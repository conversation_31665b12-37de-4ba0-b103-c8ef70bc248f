# SparkEventLogParser 使用示例

## 快速开始

### 1. 基本使用示例

```java
import com.jd.spark.eventlog.SparkEventLogAnalyzer;
import com.jd.spark.eventlog.model.SparkApplicationResourceSummary;
import com.jd.spark.eventlog.model.ExecutorResourceInfo;

import java.io.File;

public class BasicUsageExample {
    public static void main(String[] args) {
        // 创建分析器
        SparkEventLogAnalyzer analyzer = new SparkEventLogAnalyzer();
        
        try {
            // 分析单个事件日志文件
            File eventLogFile = new File("/path/to/application_123456789_0001.lz4");
            SparkApplicationResourceSummary summary = analyzer.analyzeEventLog(eventLogFile);
            
            // 打印基本信息
            System.out.println("=== Spark应用资源使用分析结果 ===");
            System.out.println("应用ID: " + summary.getApplicationId());
            System.out.println("应用名称: " + summary.getApplicationName());
            System.out.println("Spark版本: " + summary.getSparkVersion());
            System.out.println("运行时间: " + (summary.getApplicationDurationMs() / 1000.0) + " 秒");
            
            // 打印资源配置
            System.out.println("\n=== 资源配置 ===");
            System.out.println("Executor数量: " + summary.getExecutorCount());
            System.out.println("总核数: " + summary.getTotalCores());
            
            if (summary.getDriverInfo() != null) {
                System.out.println("Driver核数: " + summary.getDriverInfo().getCores());
                System.out.println("Driver内存: " + summary.getDriverInfo().getMemoryMB() + " MB");
            }
            
            // 打印资源使用量
            System.out.println("\n=== 资源使用量 ===");
            System.out.println("Executor总使用量: " + formatResourceUsage(summary.getExecutorTotalResourceUsage()));
            System.out.println("Driver使用量: " + formatResourceUsage(summary.getDriverResourceUsage()));
            System.out.println("AM使用量: " + formatResourceUsage(summary.getApplicationMasterResourceUsage()));
            System.out.println("总使用量: " + formatResourceUsage(summary.getTotalResourceUsage()));
            
            // 打印Executor详细信息
            System.out.println("\n=== Executor详细信息 ===");
            for (ExecutorResourceInfo executor : summary.getExecutorInfos()) {
                System.out.printf("Executor %s: %d核, 存活%d秒, 使用量=%s%n",
                    executor.getExecutorId(),
                    executor.getCores(),
                    executor.getAliveTimeMs() / 1000,
                    formatResourceUsage(executor.getResourceUsage()));
            }
            
        } catch (Exception e) {
            System.err.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            analyzer.close();
        }
    }
    
    private static String formatResourceUsage(long resourceUsageMs) {
        double coreHours = resourceUsageMs / (1000.0 * 3600.0);
        return String.format("%.2f 核·小时", coreHours);
    }
}
```

### 2. 批量处理示例

```java
import com.jd.spark.eventlog.SparkEventLogAnalyzer;
import com.jd.spark.eventlog.model.SparkApplicationResourceSummary;

import java.io.File;
import java.util.List;

public class BatchProcessingExample {
    public static void main(String[] args) {
        SparkEventLogAnalyzer analyzer = new SparkEventLogAnalyzer(8); // 使用8个线程
        
        try {
            // 分析目录下的所有事件日志文件
            File eventLogDir = new File("/path/to/spark/eventlogs/");
            List<SparkApplicationResourceSummary> summaries = 
                analyzer.analyzeEventLogsInDirectory(eventLogDir, true);
            
            System.out.println("成功分析 " + summaries.size() + " 个应用");
            
            // 统计总资源使用量
            long totalResourceUsage = 0;
            int totalExecutors = 0;
            
            for (SparkApplicationResourceSummary summary : summaries) {
                totalResourceUsage += summary.getTotalResourceUsage();
                totalExecutors += summary.getExecutorCount();
                
                System.out.printf("%s: %s, %d个Executor, %.2f核·小时%n",
                    summary.getApplicationId(),
                    summary.getApplicationName(),
                    summary.getExecutorCount(),
                    summary.getTotalResourceUsage() / (1000.0 * 3600.0));
            }
            
            System.out.println("\n=== 汇总统计 ===");
            System.out.println("总应用数: " + summaries.size());
            System.out.println("总Executor数: " + totalExecutors);
            System.out.printf("总资源使用量: %.2f 核·小时%n", 
                             totalResourceUsage / (1000.0 * 3600.0));
            
        } catch (Exception e) {
            System.err.println("批量分析失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            analyzer.close();
        }
    }
}
```

### 3. 流式处理示例

```java
import com.jd.spark.eventlog.SparkEventLogAnalyzer;
import com.jd.spark.eventlog.model.SparkApplicationResourceSummary;

import java.io.FileInputStream;
import java.io.InputStream;

public class StreamProcessingExample {
    public static void main(String[] args) {
        SparkEventLogAnalyzer analyzer = new SparkEventLogAnalyzer();
        
        try {
            // 从输入流分析事件日志
            InputStream inputStream = new FileInputStream("/path/to/application_123.lz4");
            SparkApplicationResourceSummary summary = 
                analyzer.analyzeEventLog(inputStream, "application_123.lz4");
            
            System.out.println("应用ID: " + summary.getApplicationId());
            System.out.println("总资源使用量: " + summary.getTotalResourceUsage() + " 核·毫秒");
            
        } catch (Exception e) {
            System.err.println("流式分析失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            analyzer.close();
        }
    }
}
```

## 运行示例

### 编译项目
```bash
mvn clean compile -pl SparkEventLogParser
```

### 运行示例程序
```bash
# 使用内置的示例程序
java -cp SparkEventLogParser/target/classes:SparkEventLogParser/target/SparkEventLogParser-1.0-SNAPSHOT.jar \
  com.jd.spark.eventlog.example.SparkEventLogAnalyzerExample /path/to/eventlog.lz4

# 或者分析整个目录
java -cp SparkEventLogParser/target/classes:SparkEventLogParser/target/SparkEventLogParser-1.0-SNAPSHOT.jar \
  com.jd.spark.eventlog.example.SparkEventLogAnalyzerExample /path/to/eventlogs/
```

## 输出示例

```
=== 分析单个事件日志文件 ===
文件: /path/to/application_1234567890_0001.lz4

=== 应用资源使用汇总 ===
应用ID: application_1234567890_0001
应用名称: TestSparkApp
Spark版本: 3.4.0
运行时间: 16分钟40秒

--- 资源配置 ---
Executor数量: 2
总核数: 11
Driver核数: 2
Driver内存: 2048 MB
AM核数: 1
AM内存: 512 MB

--- 资源使用量 ---
Executor总使用量: 6.22 核·小时
Driver使用量: 0.56 核·小时
AM使用量: 0.28 核·小时
总使用量: 7.06 核·小时

--- Executor详细信息 ---
Executor 1: 4核, 存活13分钟20秒, 使用量=0.89 核·小时
Executor 2: 4核, 存活12分钟30秒, 使用量=0.83 核·小时
```

## 注意事项

1. **内存使用**: 处理大文件时，建议增加JVM堆内存：`-Xmx4g`
2. **线程池**: 批量处理时可以调整线程池大小以获得更好的性能
3. **超时设置**: 对于很大的文件，可以设置超时时间避免长时间等待
4. **压缩格式**: 支持LZ4、ZSTD、GZIP和无压缩格式
5. **异常处理**: 对于损坏或不完整的事件日志，程序会尽力解析并提供兜底处理
