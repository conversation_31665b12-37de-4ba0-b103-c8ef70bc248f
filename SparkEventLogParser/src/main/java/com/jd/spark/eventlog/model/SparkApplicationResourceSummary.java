package com.jd.spark.eventlog.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Spark应用资源使用汇总信息
 */
public class SparkApplicationResourceSummary implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 应用ID */
    private String applicationId;
    
    /** 应用名称 */
    private String applicationName;
    
    /** 应用启动时间 */
    private long applicationStartTime;
    
    /** 应用结束时间 */
    private long applicationEndTime;
    
    /** 应用总运行时间(毫秒) */
    private long applicationDurationMs;
    
    /** Spark版本 */
    private String sparkVersion;
    
    /** Driver资源信息 */
    private DriverResourceInfo driverInfo;
    
    /** ApplicationMaster资源信息 */
    private ApplicationMasterResourceInfo applicationMasterInfo;
    
    /** 所有Executor资源信息列表 */
    private List<ExecutorResourceInfo> executorInfos;
    
    /** 总资源使用量(核·毫秒) */
    private long totalResourceUsage;
    
    /** Executor总资源使用量(核·毫秒) */
    private long executorTotalResourceUsage;
    
    /** Driver资源使用量(核·毫秒) */
    private long driverResourceUsage;
    
    /** ApplicationMaster资源使用量(核·毫秒) */
    private long applicationMasterResourceUsage;
    
    public SparkApplicationResourceSummary() {
        this.executorInfos = new ArrayList<>();
    }
    
    public SparkApplicationResourceSummary(String applicationId, String applicationName) {
        this();
        this.applicationId = applicationId;
        this.applicationName = applicationName;
    }
    
    /**
     * 计算总资源使用量
     */
    public void calculateTotalResourceUsage() {
        // 计算应用总运行时间
        this.applicationDurationMs = this.applicationEndTime - this.applicationStartTime;
        
        // 计算Driver资源使用量
        if (this.driverInfo != null) {
            this.driverInfo.calculateResourceUsage();
            this.driverResourceUsage = this.driverInfo.getResourceUsage();
        }
        
        // 计算ApplicationMaster资源使用量
        if (this.applicationMasterInfo != null) {
            this.applicationMasterInfo.calculateResourceUsage();
            this.applicationMasterResourceUsage = this.applicationMasterInfo.getResourceUsage();
        }
        
        // 计算所有Executor资源使用量
        this.executorTotalResourceUsage = 0;
        for (ExecutorResourceInfo executorInfo : this.executorInfos) {
            executorInfo.calculateResourceUsage(this.applicationEndTime);
            this.executorTotalResourceUsage += executorInfo.getResourceUsage();
        }
        
        // 计算总资源使用量
        this.totalResourceUsage = this.executorTotalResourceUsage + this.driverResourceUsage + this.applicationMasterResourceUsage;
    }
    
    /**
     * 添加Executor信息
     */
    public void addExecutorInfo(ExecutorResourceInfo executorInfo) {
        this.executorInfos.add(executorInfo);
    }
    
    /**
     * 获取Executor数量
     */
    public int getExecutorCount() {
        return this.executorInfos.size();
    }
    
    /**
     * 获取总核数
     */
    public int getTotalCores() {
        int totalCores = 0;
        
        if (this.driverInfo != null) {
            totalCores += this.driverInfo.getCores();
        }
        
        if (this.applicationMasterInfo != null) {
            totalCores += this.applicationMasterInfo.getCores();
        }
        
        for (ExecutorResourceInfo executorInfo : this.executorInfos) {
            totalCores += executorInfo.getCores();
        }
        
        return totalCores;
    }
    
    // Getters and Setters
    public String getApplicationId() {
        return applicationId;
    }
    
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }
    
    public String getApplicationName() {
        return applicationName;
    }
    
    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }
    
    public long getApplicationStartTime() {
        return applicationStartTime;
    }
    
    public void setApplicationStartTime(long applicationStartTime) {
        this.applicationStartTime = applicationStartTime;
    }
    
    public long getApplicationEndTime() {
        return applicationEndTime;
    }
    
    public void setApplicationEndTime(long applicationEndTime) {
        this.applicationEndTime = applicationEndTime;
    }
    
    public long getApplicationDurationMs() {
        return applicationDurationMs;
    }
    
    public void setApplicationDurationMs(long applicationDurationMs) {
        this.applicationDurationMs = applicationDurationMs;
    }
    
    public String getSparkVersion() {
        return sparkVersion;
    }
    
    public void setSparkVersion(String sparkVersion) {
        this.sparkVersion = sparkVersion;
    }
    
    public DriverResourceInfo getDriverInfo() {
        return driverInfo;
    }
    
    public void setDriverInfo(DriverResourceInfo driverInfo) {
        this.driverInfo = driverInfo;
    }
    
    public ApplicationMasterResourceInfo getApplicationMasterInfo() {
        return applicationMasterInfo;
    }
    
    public void setApplicationMasterInfo(ApplicationMasterResourceInfo applicationMasterInfo) {
        this.applicationMasterInfo = applicationMasterInfo;
    }
    
    public List<ExecutorResourceInfo> getExecutorInfos() {
        return executorInfos;
    }
    
    public void setExecutorInfos(List<ExecutorResourceInfo> executorInfos) {
        this.executorInfos = executorInfos;
    }
    
    public long getTotalResourceUsage() {
        return totalResourceUsage;
    }
    
    public void setTotalResourceUsage(long totalResourceUsage) {
        this.totalResourceUsage = totalResourceUsage;
    }
    
    public long getExecutorTotalResourceUsage() {
        return executorTotalResourceUsage;
    }
    
    public void setExecutorTotalResourceUsage(long executorTotalResourceUsage) {
        this.executorTotalResourceUsage = executorTotalResourceUsage;
    }
    
    public long getDriverResourceUsage() {
        return driverResourceUsage;
    }
    
    public void setDriverResourceUsage(long driverResourceUsage) {
        this.driverResourceUsage = driverResourceUsage;
    }
    
    public long getApplicationMasterResourceUsage() {
        return applicationMasterResourceUsage;
    }
    
    public void setApplicationMasterResourceUsage(long applicationMasterResourceUsage) {
        this.applicationMasterResourceUsage = applicationMasterResourceUsage;
    }
    
    @Override
    public String toString() {
        return "SparkApplicationResourceSummary{" +
                "applicationId='" + applicationId + '\'' +
                ", applicationName='" + applicationName + '\'' +
                ", applicationDurationMs=" + applicationDurationMs +
                ", sparkVersion='" + sparkVersion + '\'' +
                ", executorCount=" + getExecutorCount() +
                ", totalCores=" + getTotalCores() +
                ", totalResourceUsage=" + totalResourceUsage +
                ", executorTotalResourceUsage=" + executorTotalResourceUsage +
                ", driverResourceUsage=" + driverResourceUsage +
                ", applicationMasterResourceUsage=" + applicationMasterResourceUsage +
                '}';
    }
}
