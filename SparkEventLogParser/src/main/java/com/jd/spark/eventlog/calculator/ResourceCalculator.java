package com.jd.spark.eventlog.calculator;

import com.jd.spark.eventlog.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 资源使用量计算器
 */
public class ResourceCalculator {
    
    private static final Logger logger = LoggerFactory.getLogger(ResourceCalculator.class);
    
    /**
     * 计算应用的总资源使用量
     * 
     * @param summary 应用资源汇总信息
     */
    public static void calculateTotalResourceUsage(SparkApplicationResourceSummary summary) {
        if (summary == null) {
            logger.warn("应用资源汇总信息为空，无法计算资源使用量");
            return;
        }
        
        logger.info("开始计算应用 {} 的资源使用量", summary.getApplicationId());
        
        // 验证应用时间信息
        validateApplicationTiming(summary);
        
        // 计算应用总运行时间
        calculateApplicationDuration(summary);
        
        // 计算Driver资源使用量
        calculateDriverResourceUsage(summary);
        
        // 计算ApplicationMaster资源使用量
        calculateApplicationMasterResourceUsage(summary);
        
        // 计算所有Executor资源使用量
        calculateExecutorResourceUsage(summary);
        
        // 计算总资源使用量
        calculateTotalUsage(summary);
        
        logger.info("完成计算应用 {} 的资源使用量: 总计 {} 核·毫秒", 
                   summary.getApplicationId(), summary.getTotalResourceUsage());
    }
    
    /**
     * 验证应用时间信息
     */
    private static void validateApplicationTiming(SparkApplicationResourceSummary summary) {
        if (summary.getApplicationStartTime() <= 0) {
            logger.warn("应用 {} 的启动时间无效: {}", 
                       summary.getApplicationId(), summary.getApplicationStartTime());
        }
        
        if (summary.getApplicationEndTime() <= 0) {
            logger.warn("应用 {} 的结束时间无效: {}，将使用当前时间作为兜底", 
                       summary.getApplicationId(), summary.getApplicationEndTime());
            summary.setApplicationEndTime(System.currentTimeMillis());
        }
        
        if (summary.getApplicationEndTime() <= summary.getApplicationStartTime()) {
            logger.warn("应用 {} 的结束时间 {} 早于或等于启动时间 {}，将使用启动时间+1小时作为兜底", 
                       summary.getApplicationId(), summary.getApplicationEndTime(), summary.getApplicationStartTime());
            summary.setApplicationEndTime(summary.getApplicationStartTime() + 3600000); // 1小时
        }
    }
    
    /**
     * 计算应用总运行时间
     */
    private static void calculateApplicationDuration(SparkApplicationResourceSummary summary) {
        long duration = summary.getApplicationEndTime() - summary.getApplicationStartTime();
        summary.setApplicationDurationMs(duration);
        
        logger.debug("应用 {} 运行时间: {} 毫秒 ({} 分钟)", 
                    summary.getApplicationId(), duration, duration / 60000.0);
    }
    
    /**
     * 计算Driver资源使用量
     */
    private static void calculateDriverResourceUsage(SparkApplicationResourceSummary summary) {
        DriverResourceInfo driverInfo = summary.getDriverInfo();
        
        if (driverInfo == null) {
            logger.warn("应用 {} 缺少Driver信息，创建默认Driver信息", summary.getApplicationId());
            driverInfo = createDefaultDriverInfo(summary);
            summary.setDriverInfo(driverInfo);
        }
        
        // 设置Driver的启动和结束时间（如果未设置）
        if (driverInfo.getStartTime() <= 0) {
            driverInfo.setStartTime(summary.getApplicationStartTime());
        }
        
        if (driverInfo.getEndTime() <= 0) {
            driverInfo.setEndTime(summary.getApplicationEndTime());
        }
        
        // 计算Driver资源使用量
        driverInfo.calculateResourceUsage();
        summary.setDriverResourceUsage(driverInfo.getResourceUsage());
        
        logger.debug("Driver资源使用量: {} 核 × {} 毫秒 = {} 核·毫秒", 
                    driverInfo.getCores(), driverInfo.getAliveTimeMs(), driverInfo.getResourceUsage());
    }
    
    /**
     * 计算ApplicationMaster资源使用量
     */
    private static void calculateApplicationMasterResourceUsage(SparkApplicationResourceSummary summary) {
        ApplicationMasterResourceInfo amInfo = summary.getApplicationMasterInfo();
        
        if (amInfo == null) {
            logger.warn("应用 {} 缺少ApplicationMaster信息，创建默认AM信息", summary.getApplicationId());
            amInfo = createDefaultApplicationMasterInfo(summary);
            summary.setApplicationMasterInfo(amInfo);
        }
        
        // 设置AM的启动和结束时间（如果未设置）
        if (amInfo.getStartTime() <= 0) {
            amInfo.setStartTime(summary.getApplicationStartTime());
        }
        
        if (amInfo.getEndTime() <= 0) {
            amInfo.setEndTime(summary.getApplicationEndTime());
        }
        
        // 计算AM资源使用量
        amInfo.calculateResourceUsage();
        summary.setApplicationMasterResourceUsage(amInfo.getResourceUsage());
        
        logger.debug("ApplicationMaster资源使用量: {} 核 × {} 毫秒 = {} 核·毫秒", 
                    amInfo.getCores(), amInfo.getAliveTimeMs(), amInfo.getResourceUsage());
    }
    
    /**
     * 计算所有Executor资源使用量
     */
    private static void calculateExecutorResourceUsage(SparkApplicationResourceSummary summary) {
        List<ExecutorResourceInfo> executorInfos = summary.getExecutorInfos();
        long totalExecutorUsage = 0;
        int completedExecutors = 0;
        int incompleteExecutors = 0;
        
        for (ExecutorResourceInfo executorInfo : executorInfos) {
            // 处理未完成的Executor（使用应用结束时间作为兜底）
            if (!executorInfo.isCompleted() || executorInfo.getEndTime() <= 0) {
                logger.debug("Executor {} 未正常结束，使用应用结束时间 {} 作为兜底", 
                           executorInfo.getExecutorId(), summary.getApplicationEndTime());
                executorInfo.setEndTime(summary.getApplicationEndTime());
                incompleteExecutors++;
            } else {
                completedExecutors++;
            }
            
            // 验证Executor时间的合理性
            validateExecutorTiming(executorInfo, summary);
            
            // 计算Executor资源使用量
            executorInfo.calculateResourceUsage(summary.getApplicationEndTime());
            totalExecutorUsage += executorInfo.getResourceUsage();
            
            logger.debug("Executor {} 资源使用量: {} 核 × {} 毫秒 = {} 核·毫秒", 
                        executorInfo.getExecutorId(), executorInfo.getCores(), 
                        executorInfo.getAliveTimeMs(), executorInfo.getResourceUsage());
        }
        
        summary.setExecutorTotalResourceUsage(totalExecutorUsage);
        
        logger.info("Executor资源使用量汇总: 总计 {} 个Executor，其中 {} 个正常完成，{} 个使用兜底时间，总使用量 {} 核·毫秒", 
                   executorInfos.size(), completedExecutors, incompleteExecutors, totalExecutorUsage);
    }
    
    /**
     * 验证Executor时间的合理性
     */
    private static void validateExecutorTiming(ExecutorResourceInfo executorInfo, SparkApplicationResourceSummary summary) {
        // 检查启动时间是否合理
        if (executorInfo.getStartTime() < summary.getApplicationStartTime()) {
            logger.warn("Executor {} 的启动时间 {} 早于应用启动时间 {}，调整为应用启动时间", 
                       executorInfo.getExecutorId(), executorInfo.getStartTime(), summary.getApplicationStartTime());
            executorInfo.setStartTime(summary.getApplicationStartTime());
        }
        
        // 检查结束时间是否合理
        if (executorInfo.getEndTime() > summary.getApplicationEndTime()) {
            logger.warn("Executor {} 的结束时间 {} 晚于应用结束时间 {}，调整为应用结束时间", 
                       executorInfo.getExecutorId(), executorInfo.getEndTime(), summary.getApplicationEndTime());
            executorInfo.setEndTime(summary.getApplicationEndTime());
        }
        
        // 检查存活时间是否合理
        if (executorInfo.getEndTime() <= executorInfo.getStartTime()) {
            logger.warn("Executor {} 的结束时间 {} 早于或等于启动时间 {}，调整结束时间为启动时间+1分钟", 
                       executorInfo.getExecutorId(), executorInfo.getEndTime(), executorInfo.getStartTime());
            executorInfo.setEndTime(executorInfo.getStartTime() + 60000); // 1分钟
        }
    }
    
    /**
     * 计算总资源使用量
     */
    private static void calculateTotalUsage(SparkApplicationResourceSummary summary) {
        long totalUsage = summary.getExecutorTotalResourceUsage() + 
                         summary.getDriverResourceUsage() + 
                         summary.getApplicationMasterResourceUsage();
        
        summary.setTotalResourceUsage(totalUsage);
        
        logger.info("总资源使用量计算完成: Executor={} + Driver={} + AM={} = {} 核·毫秒", 
                   summary.getExecutorTotalResourceUsage(), 
                   summary.getDriverResourceUsage(), 
                   summary.getApplicationMasterResourceUsage(), 
                   totalUsage);
    }
    
    /**
     * 创建默认Driver信息
     */
    private static DriverResourceInfo createDefaultDriverInfo(SparkApplicationResourceSummary summary) {
        DriverResourceInfo driverInfo = new DriverResourceInfo();
        driverInfo.setCores(1); // 默认1核
        driverInfo.setMemoryMB(1024); // 默认1GB
        driverInfo.setStartTime(summary.getApplicationStartTime());
        driverInfo.setEndTime(summary.getApplicationEndTime());
        
        logger.info("为应用 {} 创建默认Driver信息: 1核, 1GB内存", summary.getApplicationId());
        
        return driverInfo;
    }
    
    /**
     * 创建默认ApplicationMaster信息
     */
    private static ApplicationMasterResourceInfo createDefaultApplicationMasterInfo(SparkApplicationResourceSummary summary) {
        ApplicationMasterResourceInfo amInfo = new ApplicationMasterResourceInfo();
        amInfo.setCores(1); // 默认1核
        amInfo.setMemoryMB(512); // 默认512MB
        amInfo.setStartTime(summary.getApplicationStartTime());
        amInfo.setEndTime(summary.getApplicationEndTime());
        
        logger.info("为应用 {} 创建默认ApplicationMaster信息: 1核, 512MB内存", summary.getApplicationId());
        
        return amInfo;
    }
}
