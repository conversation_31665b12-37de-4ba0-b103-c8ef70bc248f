package com.jd.spark.eventlog.example;

import com.jd.spark.eventlog.SparkEventLogAnalyzer;
import com.jd.spark.eventlog.model.ExecutorResourceInfo;
import com.jd.spark.eventlog.model.SparkApplicationResourceSummary;

import java.io.File;
import java.util.List;

/**
 * Spark事件日志分析器使用示例
 */
public class SparkEventLogAnalyzerExample {
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("使用方法: java SparkEventLogAnalyzerExample <event_log_file_or_directory>");
            System.out.println("示例:");
            System.out.println("  java SparkEventLogAnalyzerExample /path/to/application_123456789_0001.lz4");
            System.out.println("  java SparkEventLogAnalyzerExample /path/to/spark/eventlogs/");
            return;
        }
        
        String path = args[0];
        File file = new File(path);
        
        if (!file.exists()) {
            System.err.println("文件或目录不存在: " + path);
            return;
        }
        
        SparkEventLogAnalyzer analyzer = new SparkEventLogAnalyzer();
        
        try {
            if (file.isFile()) {
                // 分析单个文件
                analyzeSingleFile(analyzer, file);
            } else if (file.isDirectory()) {
                // 分析目录下的所有文件
                analyzeDirectory(analyzer, file);
            }
        } catch (Exception e) {
            System.err.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            analyzer.close();
        }
    }
    
    /**
     * 分析单个文件
     */
    private static void analyzeSingleFile(SparkEventLogAnalyzer analyzer, File file) throws Exception {
        System.out.println("=== 分析单个事件日志文件 ===");
        System.out.println("文件: " + file.getAbsolutePath());
        
        SparkApplicationResourceSummary summary = analyzer.analyzeEventLog(file);
        printSummary(summary);
    }
    
    /**
     * 分析目录
     */
    private static void analyzeDirectory(SparkEventLogAnalyzer analyzer, File directory) {
        System.out.println("=== 分析目录下的事件日志文件 ===");
        System.out.println("目录: " + directory.getAbsolutePath());
        
        List<SparkApplicationResourceSummary> summaries = analyzer.analyzeEventLogsInDirectory(directory, true);
        
        System.out.println("\n成功分析 " + summaries.size() + " 个应用:");
        
        long totalResourceUsage = 0;
        for (SparkApplicationResourceSummary summary : summaries) {
            printSummary(summary);
            totalResourceUsage += summary.getTotalResourceUsage();
            System.out.println("----------------------------------------");
        }
        
        System.out.println("\n=== 汇总统计 ===");
        System.out.println("总应用数: " + summaries.size());
        System.out.println("总资源使用量: " + formatResourceUsage(totalResourceUsage));
    }
    
    /**
     * 打印应用资源使用汇总信息
     */
    private static void printSummary(SparkApplicationResourceSummary summary) {
        System.out.println("\n=== 应用资源使用汇总 ===");
        System.out.println("应用ID: " + summary.getApplicationId());
        System.out.println("应用名称: " + summary.getApplicationName());
        System.out.println("Spark版本: " + summary.getSparkVersion());
        System.out.println("运行时间: " + formatDuration(summary.getApplicationDurationMs()));
        
        System.out.println("\n--- 资源配置 ---");
        System.out.println("Executor数量: " + summary.getExecutorCount());
        System.out.println("总核数: " + summary.getTotalCores());
        
        if (summary.getDriverInfo() != null) {
            System.out.println("Driver核数: " + summary.getDriverInfo().getCores());
            System.out.println("Driver内存: " + summary.getDriverInfo().getMemoryMB() + " MB");
        }
        
        if (summary.getApplicationMasterInfo() != null) {
            System.out.println("AM核数: " + summary.getApplicationMasterInfo().getCores());
            System.out.println("AM内存: " + summary.getApplicationMasterInfo().getMemoryMB() + " MB");
        }
        
        System.out.println("\n--- 资源使用量 ---");
        System.out.println("Executor总使用量: " + formatResourceUsage(summary.getExecutorTotalResourceUsage()));
        System.out.println("Driver使用量: " + formatResourceUsage(summary.getDriverResourceUsage()));
        System.out.println("AM使用量: " + formatResourceUsage(summary.getApplicationMasterResourceUsage()));
        System.out.println("总使用量: " + formatResourceUsage(summary.getTotalResourceUsage()));
        
        // 打印Executor详细信息
        if (!summary.getExecutorInfos().isEmpty()) {
            System.out.println("\n--- Executor详细信息 ---");
            for (ExecutorResourceInfo executor : summary.getExecutorInfos()) {
                System.out.printf("Executor %s: %d核, %s, 使用量=%s%n",
                    executor.getExecutorId(),
                    executor.getCores(),
                    formatDuration(executor.getAliveTimeMs()),
                    formatResourceUsage(executor.getResourceUsage()));
            }
        }
    }
    
    /**
     * 格式化持续时间
     */
    private static String formatDuration(long durationMs) {
        if (durationMs < 0) {
            return "未知";
        }
        
        long seconds = durationMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
    
    /**
     * 格式化资源使用量
     */
    private static String formatResourceUsage(long resourceUsageMs) {
        if (resourceUsageMs < 0) {
            return "未知";
        }
        
        // 转换为核·小时
        double coreHours = resourceUsageMs / (1000.0 * 3600.0);
        
        if (coreHours >= 1.0) {
            return String.format("%.2f 核·小时", coreHours);
        } else {
            // 转换为核·分钟
            double coreMinutes = resourceUsageMs / (1000.0 * 60.0);
            return String.format("%.2f 核·分钟", coreMinutes);
        }
    }
}
