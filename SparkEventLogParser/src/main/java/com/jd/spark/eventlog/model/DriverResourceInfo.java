package com.jd.spark.eventlog.model;

import java.io.Serializable;

/**
 * Driver资源使用信息
 */
public class DriverResourceInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** Driver主机名 */
    private String host;
    
    /** Driver核数 */
    private int cores;
    
    /** Driver内存大小(MB) */
    private long memoryMB;
    
    /** Driver启动时间戳 */
    private long startTime;
    
    /** Driver结束时间戳 */
    private long endTime;
    
    /** 计算得出的存活时间(毫秒) */
    private long aliveTimeMs;
    
    /** 计算得出的资源使用量(核数 × 存活时间，单位：核·毫秒) */
    private long resourceUsage;
    
    public DriverResourceInfo() {
    }
    
    public DriverResourceInfo(String host, int cores, long memoryMB, long startTime, long endTime) {
        this.host = host;
        this.cores = cores;
        this.memoryMB = memoryMB;
        this.startTime = startTime;
        this.endTime = endTime;
        calculateResourceUsage();
    }
    
    /**
     * 计算存活时间和资源使用量
     */
    public void calculateResourceUsage() {
        this.aliveTimeMs = this.endTime - this.startTime;
        this.resourceUsage = this.cores * this.aliveTimeMs;
    }
    
    // Getters and Setters
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public int getCores() {
        return cores;
    }
    
    public void setCores(int cores) {
        this.cores = cores;
    }
    
    public long getMemoryMB() {
        return memoryMB;
    }
    
    public void setMemoryMB(long memoryMB) {
        this.memoryMB = memoryMB;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
    
    public long getAliveTimeMs() {
        return aliveTimeMs;
    }
    
    public void setAliveTimeMs(long aliveTimeMs) {
        this.aliveTimeMs = aliveTimeMs;
    }
    
    public long getResourceUsage() {
        return resourceUsage;
    }
    
    public void setResourceUsage(long resourceUsage) {
        this.resourceUsage = resourceUsage;
    }
    
    @Override
    public String toString() {
        return "DriverResourceInfo{" +
                "host='" + host + '\'' +
                ", cores=" + cores +
                ", memoryMB=" + memoryMB +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", aliveTimeMs=" + aliveTimeMs +
                ", resourceUsage=" + resourceUsage +
                '}';
    }
}
