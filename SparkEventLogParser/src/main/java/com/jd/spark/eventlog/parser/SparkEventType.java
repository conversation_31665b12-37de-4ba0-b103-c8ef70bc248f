package com.jd.spark.eventlog.parser;

/**
 * Spark事件类型枚举
 */
public enum SparkEventType {
    
    /** 应用启动事件 */
    APPLICATION_START("SparkListenerApplicationStart"),
    
    /** 应用结束事件 */
    APPLICATION_END("SparkListenerApplicationEnd"),
    
    /** Executor添加事件 */
    EXECUTOR_ADDED("SparkListenerExecutorAdded"),
    
    /** Executor移除事件 */
    EXECUTOR_REMOVED("SparkListenerExecutorRemoved"),
    
    /** 任务开始事件 */
    TASK_START("SparkListenerTaskStart"),
    
    /** 任务结束事件 */
    TASK_END("SparkListenerTaskEnd"),
    
    /** Stage开始事件 */
    STAGE_SUBMITTED("SparkListenerStageSubmitted"),
    
    /** Stage完成事件 */
    STAGE_COMPLETED("SparkListenerStageCompleted"),
    
    /** Job开始事件 */
    JOB_START("SparkListenerJobStart"),
    
    /** Job结束事件 */
    JOB_END("SparkListenerJobEnd"),
    
    /** 环境更新事件 */
    ENVIRONMENT_UPDATE("SparkListenerEnvironmentUpdate"),
    
    /** 块管理器添加事件 */
    BLOCK_MANAGER_ADDED("SparkListenerBlockManagerAdded"),
    
    /** 块管理器移除事件 */
    BLOCK_MANAGER_REMOVED("SparkListenerBlockManagerRemoved"),
    
    /** 日志开始事件 */
    LOG_START("SparkListenerLogStart"),
    
    /** 未知事件类型 */
    UNKNOWN("Unknown");
    
    private final String eventName;
    
    SparkEventType(String eventName) {
        this.eventName = eventName;
    }
    
    public String getEventName() {
        return eventName;
    }
    
    /**
     * 根据事件名称获取事件类型
     */
    public static SparkEventType fromEventName(String eventName) {
        if (eventName == null) {
            return UNKNOWN;
        }
        
        for (SparkEventType type : values()) {
            if (type.eventName.equals(eventName)) {
                return type;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 判断是否为资源相关事件
     */
    public boolean isResourceRelated() {
        switch (this) {
            case APPLICATION_START:
            case APPLICATION_END:
            case EXECUTOR_ADDED:
            case EXECUTOR_REMOVED:
            case ENVIRONMENT_UPDATE:
                return true;
            default:
                return false;
        }
    }
}
