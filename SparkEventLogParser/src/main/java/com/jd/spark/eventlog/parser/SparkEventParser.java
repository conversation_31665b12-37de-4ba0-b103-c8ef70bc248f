package com.jd.spark.eventlog.parser;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.spark.eventlog.compression.CompressionUtils;
import com.jd.spark.eventlog.model.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * Spark事件日志解析器
 */
public class SparkEventParser {
    
    private static final Logger logger = LoggerFactory.getLogger(SparkEventParser.class);
    
    /** 默认Driver核数 */
    private static final int DEFAULT_DRIVER_CORES = 1;
    
    /** 默认ApplicationMaster核数 */
    private static final int DEFAULT_AM_CORES = 1;
    
    /**
     * 解析Spark事件日志文件
     * 
     * @param eventLogFile 事件日志文件
     * @return 应用资源使用汇总信息
     * @throws IOException 如果读取文件失败
     */
    public SparkApplicationResourceSummary parseEventLog(File eventLogFile) throws IOException {
        logger.info("开始解析Spark事件日志文件: {}", eventLogFile.getAbsolutePath());
        
        try (InputStream inputStream = CompressionUtils.createAutoDecompressedInputStream(eventLogFile);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            
            return parseEventLog(reader, eventLogFile.getName());
        }
    }
    
    /**
     * 解析Spark事件日志输入流
     * 
     * @param inputStream 输入流
     * @param fileName 文件名（用于日志记录）
     * @return 应用资源使用汇总信息
     * @throws IOException 如果读取流失败
     */
    public SparkApplicationResourceSummary parseEventLog(InputStream inputStream, String fileName) throws IOException {
        logger.info("开始解析Spark事件日志流: {}", fileName);
        
        try (InputStream decompressedStream = CompressionUtils.createAutoDecompressedInputStream(inputStream, fileName);
             BufferedReader reader = new BufferedReader(new InputStreamReader(decompressedStream, "UTF-8"))) {
            
            return parseEventLog(reader, fileName);
        }
    }
    
    /**
     * 解析Spark事件日志
     * 
     * @param reader 读取器
     * @param fileName 文件名
     * @return 应用资源使用汇总信息
     * @throws IOException 如果读取失败
     */
    private SparkApplicationResourceSummary parseEventLog(BufferedReader reader, String fileName) throws IOException {
        SparkApplicationResourceSummary summary = new SparkApplicationResourceSummary();
        Map<String, ExecutorResourceInfo> executorMap = new HashMap<>();
        
        String line;
        int lineNumber = 0;
        
        while ((line = reader.readLine()) != null) {
            lineNumber++;
            
            if (StringUtils.isBlank(line)) {
                continue;
            }
            
            try {
                JSONObject eventJson = JSON.parseObject(line);
                String eventType = eventJson.getString("Event");
                
                if (eventType == null) {
                    continue;
                }
                
                SparkEventType sparkEventType = SparkEventType.fromEventName(eventType);
                
                // 只处理资源相关的事件
                if (!sparkEventType.isResourceRelated()) {
                    continue;
                }
                
                switch (sparkEventType) {
                    case APPLICATION_START:
                        parseApplicationStart(eventJson, summary);
                        break;
                        
                    case APPLICATION_END:
                        parseApplicationEnd(eventJson, summary);
                        break;
                        
                    case EXECUTOR_ADDED:
                        parseExecutorAdded(eventJson, executorMap);
                        break;
                        
                    case EXECUTOR_REMOVED:
                        parseExecutorRemoved(eventJson, executorMap);
                        break;
                        
                    case ENVIRONMENT_UPDATE:
                        parseEnvironmentUpdate(eventJson, summary);
                        break;
                        
                    default:
                        // 忽略其他事件类型
                        break;
                }
                
            } catch (Exception e) {
                logger.warn("解析第{}行事件失败: {}, 错误: {}", lineNumber, line, e.getMessage());
            }
        }
        
        // 将executor信息添加到汇总中
        for (ExecutorResourceInfo executorInfo : executorMap.values()) {
            summary.addExecutorInfo(executorInfo);
        }
        
        // 计算总资源使用量
        summary.calculateTotalResourceUsage();
        
        logger.info("完成解析Spark事件日志，应用ID: {}, Executor数量: {}, 总资源使用量: {} 核·毫秒", 
                   summary.getApplicationId(), summary.getExecutorCount(), summary.getTotalResourceUsage());
        
        return summary;
    }
    
    /**
     * 解析应用启动事件
     */
    private void parseApplicationStart(JSONObject eventJson, SparkApplicationResourceSummary summary) {
        String appId = eventJson.getString("App ID");
        String appName = eventJson.getString("App Name");
        Long timestamp = eventJson.getLong("Timestamp");
        String sparkVersion = eventJson.getString("Spark Version");
        
        summary.setApplicationId(appId);
        summary.setApplicationName(appName);
        summary.setSparkVersion(sparkVersion);
        
        if (timestamp != null) {
            summary.setApplicationStartTime(timestamp);
        }
        
        logger.debug("解析到应用启动事件: appId={}, appName={}, sparkVersion={}", appId, appName, sparkVersion);
    }
    
    /**
     * 解析应用结束事件
     */
    private void parseApplicationEnd(JSONObject eventJson, SparkApplicationResourceSummary summary) {
        Long timestamp = eventJson.getLong("Timestamp");
        
        if (timestamp != null) {
            summary.setApplicationEndTime(timestamp);
        }
        
        logger.debug("解析到应用结束事件: timestamp={}", timestamp);
    }
    
    /**
     * 解析Executor添加事件
     */
    private void parseExecutorAdded(JSONObject eventJson, Map<String, ExecutorResourceInfo> executorMap) {
        Long timestamp = eventJson.getLong("Timestamp");
        String executorId = eventJson.getString("Executor ID");
        JSONObject executorInfo = eventJson.getJSONObject("Executor Info");

        if (executorInfo == null || timestamp == null || executorId == null) {
            return;
        }

        String host = executorInfo.getString("Host");
        Integer totalCores = executorInfo.getInteger("Total Cores");
        Long maxMemory = executorInfo.getLong("Maximum Memory");

        if (totalCores != null) {
            ExecutorResourceInfo resourceInfo = new ExecutorResourceInfo(
                executorId, host, totalCores, maxMemory != null ? maxMemory / (1024 * 1024) : 0, timestamp);

            executorMap.put(executorId, resourceInfo);

            logger.debug("解析到Executor添加事件: executorId={}, host={}, cores={}, memory={}MB",
                        executorId, host, totalCores, maxMemory != null ? maxMemory / (1024 * 1024) : 0);
        }
    }
    
    /**
     * 解析Executor移除事件
     */
    private void parseExecutorRemoved(JSONObject eventJson, Map<String, ExecutorResourceInfo> executorMap) {
        Long timestamp = eventJson.getLong("Timestamp");
        String executorId = eventJson.getString("Executor ID");
        String reason = eventJson.getString("Reason");
        
        if (executorId != null && timestamp != null) {
            ExecutorResourceInfo resourceInfo = executorMap.get(executorId);
            if (resourceInfo != null) {
                resourceInfo.setEndTime(timestamp);
                resourceInfo.setFailureReason(reason);
                
                logger.debug("解析到Executor移除事件: executorId={}, reason={}", executorId, reason);
            }
        }
    }
    
    /**
     * 解析环境更新事件（用于获取Driver和AM配置信息）
     */
    private void parseEnvironmentUpdate(JSONObject eventJson, SparkApplicationResourceSummary summary) {
        JSONObject sparkProperties = eventJson.getJSONObject("Spark Properties");
        
        if (sparkProperties != null) {
            // 尝试获取Driver配置
            parseDriverConfig(sparkProperties, summary);
            
            // 尝试获取ApplicationMaster配置
            parseApplicationMasterConfig(sparkProperties, summary);
        }
    }
    
    /**
     * 解析Driver配置
     */
    private void parseDriverConfig(JSONObject sparkProperties, SparkApplicationResourceSummary summary) {
        // 获取Driver核数
        int driverCores = DEFAULT_DRIVER_CORES;
        String driverCoresStr = sparkProperties.getString("spark.driver.cores");
        if (StringUtils.isNotBlank(driverCoresStr)) {
            try {
                driverCores = Integer.parseInt(driverCoresStr);
            } catch (NumberFormatException e) {
                logger.warn("无法解析Driver核数: {}", driverCoresStr);
            }
        }
        
        // 获取Driver内存
        long driverMemoryMB = 0;
        String driverMemoryStr = sparkProperties.getString("spark.driver.memory");
        if (StringUtils.isNotBlank(driverMemoryStr)) {
            driverMemoryMB = parseMemoryString(driverMemoryStr);
        }
        
        // 创建Driver资源信息
        DriverResourceInfo driverInfo = new DriverResourceInfo();
        driverInfo.setCores(driverCores);
        driverInfo.setMemoryMB(driverMemoryMB);
        driverInfo.setStartTime(summary.getApplicationStartTime());
        driverInfo.setEndTime(summary.getApplicationEndTime());
        
        summary.setDriverInfo(driverInfo);
        
        logger.debug("解析到Driver配置: cores={}, memory={}MB", driverCores, driverMemoryMB);
    }
    
    /**
     * 解析ApplicationMaster配置
     */
    private void parseApplicationMasterConfig(JSONObject sparkProperties, SparkApplicationResourceSummary summary) {
        // 获取AM核数
        int amCores = DEFAULT_AM_CORES;
        String amCoresStr = sparkProperties.getString("spark.yarn.am.cores");
        if (StringUtils.isNotBlank(amCoresStr)) {
            try {
                amCores = Integer.parseInt(amCoresStr);
            } catch (NumberFormatException e) {
                logger.warn("无法解析ApplicationMaster核数: {}", amCoresStr);
            }
        }
        
        // 获取AM内存
        long amMemoryMB = 0;
        String amMemoryStr = sparkProperties.getString("spark.yarn.am.memory");
        if (StringUtils.isNotBlank(amMemoryStr)) {
            amMemoryMB = parseMemoryString(amMemoryStr);
        }
        
        // 创建ApplicationMaster资源信息
        ApplicationMasterResourceInfo amInfo = new ApplicationMasterResourceInfo();
        amInfo.setCores(amCores);
        amInfo.setMemoryMB(amMemoryMB);
        amInfo.setStartTime(summary.getApplicationStartTime());
        amInfo.setEndTime(summary.getApplicationEndTime());
        
        summary.setApplicationMasterInfo(amInfo);
        
        logger.debug("解析到ApplicationMaster配置: cores={}, memory={}MB", amCores, amMemoryMB);
    }
    
    /**
     * 解析内存字符串，支持g、m、k等单位
     */
    private long parseMemoryString(String memoryStr) {
        if (StringUtils.isBlank(memoryStr)) {
            return 0;
        }
        
        memoryStr = memoryStr.toLowerCase().trim();
        
        try {
            if (memoryStr.endsWith("g")) {
                return Long.parseLong(memoryStr.substring(0, memoryStr.length() - 1)) * 1024;
            } else if (memoryStr.endsWith("m")) {
                return Long.parseLong(memoryStr.substring(0, memoryStr.length() - 1));
            } else if (memoryStr.endsWith("k")) {
                return Long.parseLong(memoryStr.substring(0, memoryStr.length() - 1)) / 1024;
            } else {
                // 默认按字节处理，转换为MB
                return Long.parseLong(memoryStr) / (1024 * 1024);
            }
        } catch (NumberFormatException e) {
            logger.warn("无法解析内存字符串: {}", memoryStr);
            return 0;
        }
    }
}
