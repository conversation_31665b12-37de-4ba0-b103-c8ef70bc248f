package com.jd.spark.eventlog.compression;

/**
 * 压缩类型枚举
 */
public enum CompressionType {
    
    /** 无压缩 */
    NONE("none", ""),
    
    /** LZ4压缩 */
    LZ4("lz4", ".lz4"),
    
    /** ZSTD压缩 */
    ZSTD("zstd", ".zst"),
    
    /** GZIP压缩 */
    GZIP("gzip", ".gz");
    
    private final String name;
    private final String extension;
    
    CompressionType(String name, String extension) {
        this.name = name;
        this.extension = extension;
    }
    
    public String getName() {
        return name;
    }
    
    public String getExtension() {
        return extension;
    }
    
    /**
     * 根据文件名检测压缩类型
     */
    public static CompressionType detectFromFileName(String fileName) {
        if (fileName == null) {
            return NONE;
        }
        
        String lowerFileName = fileName.toLowerCase();
        
        if (lowerFileName.endsWith(".lz4")) {
            return LZ4;
        } else if (lowerFileName.endsWith(".zst") || lowerFileName.endsWith(".zstd")) {
            return ZSTD;
        } else if (lowerFileName.endsWith(".gz")) {
            return GZIP;
        } else {
            return NONE;
        }
    }
    
    /**
     * 根据文件内容的魔数检测压缩类型
     */
    public static CompressionType detectFromMagicBytes(byte[] header) {
        if (header == null || header.length < 2) {
            return NONE;
        }

        // LZ4 magic number: 0x184D2204
        if (header.length >= 4 &&
            (header[0] & 0xFF) == 0x04 &&
            (header[1] & 0xFF) == 0x22 &&
            (header[2] & 0xFF) == 0x4D &&
            (header[3] & 0xFF) == 0x18) {
            return LZ4;
        }

        // ZSTD magic number: 0xFD2FB528
        if (header.length >= 4 &&
            (header[0] & 0xFF) == 0x28 &&
            (header[1] & 0xFF) == 0xB5 &&
            (header[2] & 0xFF) == 0x2F &&
            (header[3] & 0xFF) == 0xFD) {
            return ZSTD;
        }

        // GZIP magic number: 0x1F8B
        if (header.length >= 2 &&
            (header[0] & 0xFF) == 0x1F &&
            (header[1] & 0xFF) == 0x8B) {
            return GZIP;
        }

        return NONE;
    }
}
