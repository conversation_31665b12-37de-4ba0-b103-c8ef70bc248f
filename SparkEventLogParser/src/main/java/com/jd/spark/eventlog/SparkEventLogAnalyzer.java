package com.jd.spark.eventlog;

import com.jd.spark.eventlog.calculator.ResourceCalculator;
import com.jd.spark.eventlog.model.SparkApplicationResourceSummary;
import com.jd.spark.eventlog.parser.SparkEventParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * Spark事件日志分析器 - 主要API接口
 */
public class SparkEventLogAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(SparkEventLogAnalyzer.class);
    
    private final SparkEventParser eventParser;
    private final ExecutorService executorService;
    
    /**
     * 构造函数
     */
    public SparkEventLogAnalyzer() {
        this.eventParser = new SparkEventParser();
        this.executorService = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors());
    }
    
    /**
     * 构造函数，指定线程池大小
     * 
     * @param threadPoolSize 线程池大小
     */
    public SparkEventLogAnalyzer(int threadPoolSize) {
        this.eventParser = new SparkEventParser();
        this.executorService = Executors.newFixedThreadPool(threadPoolSize);
    }
    
    /**
     * 分析单个Spark事件日志文件
     * 
     * @param eventLogFile 事件日志文件
     * @return 应用资源使用汇总信息
     * @throws IOException 如果文件读取失败
     */
    public SparkApplicationResourceSummary analyzeEventLog(File eventLogFile) throws IOException {
        if (eventLogFile == null || !eventLogFile.exists()) {
            throw new IllegalArgumentException("事件日志文件不存在: " + eventLogFile);
        }
        
        if (!eventLogFile.isFile()) {
            throw new IllegalArgumentException("指定路径不是文件: " + eventLogFile);
        }
        
        logger.info("开始分析事件日志文件: {}", eventLogFile.getAbsolutePath());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 解析事件日志
            SparkApplicationResourceSummary summary = eventParser.parseEventLog(eventLogFile);
            
            // 计算资源使用量
            ResourceCalculator.calculateTotalResourceUsage(summary);
            
            long endTime = System.currentTimeMillis();
            logger.info("完成分析事件日志文件: {}，耗时: {} 毫秒", 
                       eventLogFile.getAbsolutePath(), endTime - startTime);
            
            return summary;
            
        } catch (Exception e) {
            logger.error("分析事件日志文件失败: " + eventLogFile.getAbsolutePath(), e);
            throw e;
        }
    }
    
    /**
     * 分析Spark事件日志输入流
     * 
     * @param inputStream 输入流
     * @param fileName 文件名（用于日志记录）
     * @return 应用资源使用汇总信息
     * @throws IOException 如果流读取失败
     */
    public SparkApplicationResourceSummary analyzeEventLog(InputStream inputStream, String fileName) throws IOException {
        if (inputStream == null) {
            throw new IllegalArgumentException("输入流不能为空");
        }
        
        logger.info("开始分析事件日志流: {}", fileName);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 解析事件日志
            SparkApplicationResourceSummary summary = eventParser.parseEventLog(inputStream, fileName);
            
            // 计算资源使用量
            ResourceCalculator.calculateTotalResourceUsage(summary);
            
            long endTime = System.currentTimeMillis();
            logger.info("完成分析事件日志流: {}，耗时: {} 毫秒", fileName, endTime - startTime);
            
            return summary;
            
        } catch (Exception e) {
            logger.error("分析事件日志流失败: " + fileName, e);
            throw e;
        }
    }
    
    /**
     * 批量分析多个事件日志文件
     * 
     * @param eventLogFiles 事件日志文件列表
     * @return 应用资源使用汇总信息列表
     */
    public List<SparkApplicationResourceSummary> analyzeEventLogs(List<File> eventLogFiles) {
        if (eventLogFiles == null || eventLogFiles.isEmpty()) {
            logger.warn("事件日志文件列表为空");
            return new ArrayList<>();
        }
        
        logger.info("开始批量分析 {} 个事件日志文件", eventLogFiles.size());
        
        long startTime = System.currentTimeMillis();
        List<Future<SparkApplicationResourceSummary>> futures = new ArrayList<>();
        
        // 提交所有分析任务
        for (File file : eventLogFiles) {
            Future<SparkApplicationResourceSummary> future = executorService.submit(() -> {
                try {
                    return analyzeEventLog(file);
                } catch (Exception e) {
                    logger.error("分析文件失败: " + file.getAbsolutePath(), e);
                    return null;
                }
            });
            futures.add(future);
        }
        
        // 收集结果
        List<SparkApplicationResourceSummary> results = new ArrayList<>();
        for (int i = 0; i < futures.size(); i++) {
            try {
                SparkApplicationResourceSummary summary = futures.get(i).get();
                if (summary != null) {
                    results.add(summary);
                }
            } catch (Exception e) {
                logger.error("获取分析结果失败: " + eventLogFiles.get(i).getAbsolutePath(), e);
            }
        }
        
        long endTime = System.currentTimeMillis();
        logger.info("完成批量分析，成功分析 {} / {} 个文件，总耗时: {} 毫秒", 
                   results.size(), eventLogFiles.size(), endTime - startTime);
        
        return results;
    }
    
    /**
     * 批量分析多个事件日志文件（带超时）
     * 
     * @param eventLogFiles 事件日志文件列表
     * @param timeoutMinutes 超时时间（分钟）
     * @return 应用资源使用汇总信息列表
     */
    public List<SparkApplicationResourceSummary> analyzeEventLogs(List<File> eventLogFiles, int timeoutMinutes) {
        if (eventLogFiles == null || eventLogFiles.isEmpty()) {
            logger.warn("事件日志文件列表为空");
            return new ArrayList<>();
        }
        
        logger.info("开始批量分析 {} 个事件日志文件，超时时间: {} 分钟", eventLogFiles.size(), timeoutMinutes);
        
        long startTime = System.currentTimeMillis();
        List<Future<SparkApplicationResourceSummary>> futures = new ArrayList<>();
        
        // 提交所有分析任务
        for (File file : eventLogFiles) {
            Future<SparkApplicationResourceSummary> future = executorService.submit(() -> {
                try {
                    return analyzeEventLog(file);
                } catch (Exception e) {
                    logger.error("分析文件失败: " + file.getAbsolutePath(), e);
                    return null;
                }
            });
            futures.add(future);
        }
        
        // 收集结果（带超时）
        List<SparkApplicationResourceSummary> results = new ArrayList<>();
        for (int i = 0; i < futures.size(); i++) {
            try {
                SparkApplicationResourceSummary summary = futures.get(i).get(timeoutMinutes, TimeUnit.MINUTES);
                if (summary != null) {
                    results.add(summary);
                }
            } catch (TimeoutException e) {
                logger.error("分析文件超时: " + eventLogFiles.get(i).getAbsolutePath());
                futures.get(i).cancel(true);
            } catch (Exception e) {
                logger.error("获取分析结果失败: " + eventLogFiles.get(i).getAbsolutePath(), e);
            }
        }
        
        long endTime = System.currentTimeMillis();
        logger.info("完成批量分析，成功分析 {} / {} 个文件，总耗时: {} 毫秒", 
                   results.size(), eventLogFiles.size(), endTime - startTime);
        
        return results;
    }
    
    /**
     * 分析指定目录下的所有事件日志文件
     * 
     * @param directory 目录
     * @param recursive 是否递归搜索子目录
     * @return 应用资源使用汇总信息列表
     */
    public List<SparkApplicationResourceSummary> analyzeEventLogsInDirectory(File directory, boolean recursive) {
        if (directory == null || !directory.exists() || !directory.isDirectory()) {
            throw new IllegalArgumentException("目录不存在或不是目录: " + directory);
        }
        
        List<File> eventLogFiles = findEventLogFiles(directory, recursive);
        logger.info("在目录 {} 中找到 {} 个事件日志文件", directory.getAbsolutePath(), eventLogFiles.size());
        
        return analyzeEventLogs(eventLogFiles);
    }
    
    /**
     * 查找目录下的事件日志文件
     */
    private List<File> findEventLogFiles(File directory, boolean recursive) {
        List<File> eventLogFiles = new ArrayList<>();
        
        File[] files = directory.listFiles();
        if (files == null) {
            return eventLogFiles;
        }
        
        for (File file : files) {
            if (file.isFile() && isEventLogFile(file)) {
                eventLogFiles.add(file);
            } else if (file.isDirectory() && recursive) {
                eventLogFiles.addAll(findEventLogFiles(file, recursive));
            }
        }
        
        return eventLogFiles;
    }
    
    /**
     * 判断是否为事件日志文件
     */
    private boolean isEventLogFile(File file) {
        String fileName = file.getName().toLowerCase();
        
        // 检查文件名模式
        if (fileName.startsWith("application_") || fileName.contains("eventlog")) {
            return true;
        }
        
        // 检查文件扩展名
        return fileName.endsWith(".log") || 
               fileName.endsWith(".lz4") || 
               fileName.endsWith(".zst") || 
               fileName.endsWith(".gz");
    }
    
    /**
     * 关闭分析器，释放资源
     */
    public void close() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 获取分析器状态信息
     */
    public String getStatus() {
        return String.format("SparkEventLogAnalyzer[threadPool=%s, isShutdown=%s]", 
                           executorService.getClass().getSimpleName(), 
                           executorService.isShutdown());
    }
}
