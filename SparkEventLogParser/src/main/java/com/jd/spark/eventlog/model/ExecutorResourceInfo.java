package com.jd.spark.eventlog.model;

import java.io.Serializable;

/**
 * Executor资源使用信息
 */
public class ExecutorResourceInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** Executor ID */
    private String executorId;
    
    /** Executor主机名 */
    private String host;
    
    /** Executor核数 */
    private int cores;
    
    /** Executor内存大小(MB) */
    private long memoryMB;
    
    /** Executor启动时间戳 */
    private long startTime;
    
    /** Executor结束时间戳，如果为-1表示未结束或未记录 */
    private long endTime;
    
    /** 是否已完成 */
    private boolean completed;
    
    /** 失败原因，如果有的话 */
    private String failureReason;
    
    /** 计算得出的存活时间(毫秒) */
    private long aliveTimeMs;
    
    /** 计算得出的资源使用量(核数 × 存活时间，单位：核·毫秒) */
    private long resourceUsage;
    
    public ExecutorResourceInfo() {
        this.endTime = -1;
        this.completed = false;
    }
    
    public ExecutorResourceInfo(String executorId, String host, int cores, long memoryMB, long startTime) {
        this();
        this.executorId = executorId;
        this.host = host;
        this.cores = cores;
        this.memoryMB = memoryMB;
        this.startTime = startTime;
    }
    
    /**
     * 计算存活时间和资源使用量
     * @param applicationEndTime 应用结束时间，用于兜底
     */
    public void calculateResourceUsage(long applicationEndTime) {
        long actualEndTime = this.endTime > 0 ? this.endTime : applicationEndTime;
        this.aliveTimeMs = actualEndTime - this.startTime;
        this.resourceUsage = this.cores * this.aliveTimeMs;
    }
    
    // Getters and Setters
    public String getExecutorId() {
        return executorId;
    }
    
    public void setExecutorId(String executorId) {
        this.executorId = executorId;
    }
    
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public int getCores() {
        return cores;
    }
    
    public void setCores(int cores) {
        this.cores = cores;
    }
    
    public long getMemoryMB() {
        return memoryMB;
    }
    
    public void setMemoryMB(long memoryMB) {
        this.memoryMB = memoryMB;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(long endTime) {
        this.endTime = endTime;
        this.completed = true;
    }
    
    public boolean isCompleted() {
        return completed;
    }
    
    public void setCompleted(boolean completed) {
        this.completed = completed;
    }
    
    public String getFailureReason() {
        return failureReason;
    }
    
    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }
    
    public long getAliveTimeMs() {
        return aliveTimeMs;
    }
    
    public void setAliveTimeMs(long aliveTimeMs) {
        this.aliveTimeMs = aliveTimeMs;
    }
    
    public long getResourceUsage() {
        return resourceUsage;
    }
    
    public void setResourceUsage(long resourceUsage) {
        this.resourceUsage = resourceUsage;
    }
    
    @Override
    public String toString() {
        return "ExecutorResourceInfo{" +
                "executorId='" + executorId + '\'' +
                ", host='" + host + '\'' +
                ", cores=" + cores +
                ", memoryMB=" + memoryMB +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", completed=" + completed +
                ", aliveTimeMs=" + aliveTimeMs +
                ", resourceUsage=" + resourceUsage +
                '}';
    }
}
