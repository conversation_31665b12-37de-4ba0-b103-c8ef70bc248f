package com.jd.spark.eventlog.compression;

import com.github.luben.zstd.ZstdInputStream;
import net.jpountz.lz4.LZ4FrameInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.zip.GZIPInputStream;

/**
 * 压缩文件处理工具类
 */
public class CompressionUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(CompressionUtils.class);
    
    /**
     * 创建解压缩输入流
     * 
     * @param inputStream 原始输入流
     * @param compressionType 压缩类型
     * @return 解压缩后的输入流
     * @throws IOException 如果创建输入流失败
     */
    public static InputStream createDecompressedInputStream(InputStream inputStream, CompressionType compressionType) 
            throws IOException {
        
        switch (compressionType) {
            case LZ4:
                return new LZ4FrameInputStream(inputStream);
                
            case ZSTD:
                return new ZstdInputStream(inputStream);
                
            case GZIP:
                return new GZIPInputStream(inputStream);
                
            case NONE:
            default:
                return inputStream;
        }
    }
    
    /**
     * 自动检测压缩类型并创建解压缩输入流
     * 
     * @param file 文件
     * @return 解压缩后的输入流
     * @throws IOException 如果创建输入流失败
     */
    public static InputStream createAutoDecompressedInputStream(File file) throws IOException {
        // 首先根据文件名检测
        CompressionType typeFromName = CompressionType.detectFromFileName(file.getName());
        
        // 读取文件头部字节进行二次确认
        byte[] header = new byte[8];
        try (FileInputStream fis = new FileInputStream(file)) {
            int bytesRead = fis.read(header);
            if (bytesRead > 0) {
                CompressionType typeFromMagic = CompressionType.detectFromMagicBytes(header);
                
                // 如果魔数检测结果与文件名检测结果不一致，优先使用魔数检测结果
                if (typeFromMagic != CompressionType.NONE && typeFromMagic != typeFromName) {
                    logger.warn("文件 {} 的压缩类型检测不一致：文件名检测为 {}，魔数检测为 {}，使用魔数检测结果", 
                               file.getName(), typeFromName, typeFromMagic);
                    typeFromName = typeFromMagic;
                }
            }
        }
        
        logger.info("检测到文件 {} 的压缩类型为: {}", file.getName(), typeFromName);
        
        FileInputStream fileInputStream = new FileInputStream(file);
        return createDecompressedInputStream(fileInputStream, typeFromName);
    }
    
    /**
     * 自动检测压缩类型并创建解压缩输入流
     * 
     * @param inputStream 输入流
     * @param fileName 文件名（用于辅助检测）
     * @return 解压缩后的输入流
     * @throws IOException 如果创建输入流失败
     */
    public static InputStream createAutoDecompressedInputStream(InputStream inputStream, String fileName) 
            throws IOException {
        
        // 使用BufferedInputStream以支持mark/reset
        BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
        
        // 标记当前位置
        bufferedInputStream.mark(8);
        
        // 读取文件头部字节
        byte[] header = new byte[8];
        int bytesRead = bufferedInputStream.read(header);
        
        // 重置到标记位置
        bufferedInputStream.reset();
        
        CompressionType compressionType = CompressionType.NONE;
        
        if (bytesRead > 0) {
            // 首先尝试从魔数检测
            compressionType = CompressionType.detectFromMagicBytes(header);
            
            // 如果魔数检测失败，尝试从文件名检测
            if (compressionType == CompressionType.NONE && fileName != null) {
                compressionType = CompressionType.detectFromFileName(fileName);
            }
        }
        
        logger.info("检测到输入流的压缩类型为: {} (文件名: {})", compressionType, fileName);
        
        return createDecompressedInputStream(bufferedInputStream, compressionType);
    }
    
    /**
     * 检查文件是否为压缩文件
     * 
     * @param file 文件
     * @return 如果是压缩文件返回true
     */
    public static boolean isCompressedFile(File file) {
        CompressionType type = CompressionType.detectFromFileName(file.getName());
        return type != CompressionType.NONE;
    }
    
    /**
     * 获取解压缩后的文件名
     * 
     * @param compressedFileName 压缩文件名
     * @return 解压缩后的文件名
     */
    public static String getDecompressedFileName(String compressedFileName) {
        if (compressedFileName == null) {
            return null;
        }
        
        CompressionType type = CompressionType.detectFromFileName(compressedFileName);
        if (type != CompressionType.NONE) {
            String extension = type.getExtension();
            if (compressedFileName.endsWith(extension)) {
                return compressedFileName.substring(0, compressedFileName.length() - extension.length());
            }
        }
        
        return compressedFileName;
    }
}
