package com.jd.spark.eventlog.compression;

import org.junit.Test;
import static org.junit.Assert.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * CompressionUtils测试类
 */
public class CompressionUtilsTest {
    
    @Test
    public void testDetectCompressionTypeFromFileName() {
        // 测试LZ4文件
        assertEquals(CompressionType.LZ4, CompressionType.detectFromFileName("application_123.lz4"));
        assertEquals(CompressionType.LZ4, CompressionType.detectFromFileName("test.LZ4"));
        
        // 测试ZSTD文件
        assertEquals(CompressionType.ZSTD, CompressionType.detectFromFileName("application_123.zst"));
        assertEquals(CompressionType.ZSTD, CompressionType.detectFromFileName("test.zstd"));
        
        // 测试GZIP文件
        assertEquals(CompressionType.GZIP, CompressionType.detectFromFileName("application_123.gz"));
        
        // 测试无压缩文件
        assertEquals(CompressionType.NONE, CompressionType.detectFromFileName("application_123"));
        assertEquals(CompressionType.NONE, CompressionType.detectFromFileName("test.log"));
        assertEquals(CompressionType.NONE, CompressionType.detectFromFileName(null));
    }
    
    @Test
    public void testDetectCompressionTypeFromMagicBytes() {
        // 测试LZ4魔数
        byte[] lz4Magic = {0x04, 0x22, 0x4D, 0x18};
        assertEquals(CompressionType.LZ4, CompressionType.detectFromMagicBytes(lz4Magic));
        
        // 测试ZSTD魔数
        byte[] zstdMagic = {0x28, (byte)0xB5, 0x2F, (byte)0xFD};
        assertEquals(CompressionType.ZSTD, CompressionType.detectFromMagicBytes(zstdMagic));
        
        // 测试GZIP魔数
        byte[] gzipMagic = {0x1F, (byte)0x8B};
        assertEquals(CompressionType.GZIP, CompressionType.detectFromMagicBytes(gzipMagic));
        
        // 测试无效魔数
        byte[] invalidMagic = {0x00, 0x01, 0x02, 0x03};
        assertEquals(CompressionType.NONE, CompressionType.detectFromMagicBytes(invalidMagic));
        
        // 测试空数组
        assertEquals(CompressionType.NONE, CompressionType.detectFromMagicBytes(null));
        assertEquals(CompressionType.NONE, CompressionType.detectFromMagicBytes(new byte[0]));
    }
    
    @Test
    public void testCreateDecompressedInputStream() throws IOException {
        // 测试无压缩
        byte[] testData = "test data".getBytes();
        InputStream inputStream = new ByteArrayInputStream(testData);
        InputStream result = CompressionUtils.createDecompressedInputStream(inputStream, CompressionType.NONE);
        assertSame(inputStream, result);
    }
    
    @Test
    public void testGetDecompressedFileName() {
        assertEquals("application_123", CompressionUtils.getDecompressedFileName("application_123.lz4"));
        assertEquals("application_123", CompressionUtils.getDecompressedFileName("application_123.zst"));
        assertEquals("application_123", CompressionUtils.getDecompressedFileName("application_123.gz"));
        assertEquals("application_123", CompressionUtils.getDecompressedFileName("application_123"));
        assertNull(CompressionUtils.getDecompressedFileName(null));
    }
    
    @Test
    public void testIsCompressedFile() {
        assertTrue(CompressionUtils.isCompressedFile(new java.io.File("test.lz4")));
        assertTrue(CompressionUtils.isCompressedFile(new java.io.File("test.zst")));
        assertTrue(CompressionUtils.isCompressedFile(new java.io.File("test.gz")));
        assertFalse(CompressionUtils.isCompressedFile(new java.io.File("test.log")));
        assertFalse(CompressionUtils.isCompressedFile(new java.io.File("test")));
    }
}
