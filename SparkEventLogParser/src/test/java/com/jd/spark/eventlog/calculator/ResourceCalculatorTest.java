package com.jd.spark.eventlog.calculator;

import com.jd.spark.eventlog.model.*;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * ResourceCalculator测试类
 */
public class ResourceCalculatorTest {
    
    private SparkApplicationResourceSummary summary;
    
    @Before
    public void setUp() {
        summary = new SparkApplicationResourceSummary("app_123", "TestApp");
        summary.setApplicationStartTime(1000000L);
        summary.setApplicationEndTime(2000000L);
    }
    
    @Test
    public void testCalculateBasicResourceUsage() {
        // 设置Driver信息
        DriverResourceInfo driverInfo = new DriverResourceInfo("driver-host", 2, 1024, 1000000L, 2000000L);
        summary.setDriverInfo(driverInfo);
        
        // 设置ApplicationMaster信息
        ApplicationMasterResourceInfo amInfo = new ApplicationMasterResourceInfo("am-host", 1, 512, 1000000L, 2000000L);
        summary.setApplicationMasterInfo(amInfo);
        
        // 添加Executor信息
        ExecutorResourceInfo executor1 = new ExecutorResourceInfo("1", "host1", 4, 1024, 1100000L);
        executor1.setEndTime(1900000L);
        summary.addExecutorInfo(executor1);
        
        // 计算资源使用量
        ResourceCalculator.calculateTotalResourceUsage(summary);
        
        // 验证应用运行时间
        assertEquals(1000000L, summary.getApplicationDurationMs());
        
        // 验证Driver资源使用量: 2核 × 1000000毫秒 = 2000000
        assertEquals(2000000L, summary.getDriverResourceUsage());
        
        // 验证AM资源使用量: 1核 × 1000000毫秒 = 1000000
        assertEquals(1000000L, summary.getApplicationMasterResourceUsage());
        
        // 验证Executor资源使用量: 4核 × 800000毫秒 = 3200000
        assertEquals(3200000L, summary.getExecutorTotalResourceUsage());
        
        // 验证总资源使用量
        assertEquals(6200000L, summary.getTotalResourceUsage());
    }
    
    @Test
    public void testCalculateResourceUsageWithIncompleteExecutor() {
        // 添加未完成的Executor
        ExecutorResourceInfo executor1 = new ExecutorResourceInfo("1", "host1", 2, 1024, 1100000L);
        // 不设置结束时间
        summary.addExecutorInfo(executor1);
        
        // 计算资源使用量
        ResourceCalculator.calculateTotalResourceUsage(summary);
        
        // 验证Executor使用应用结束时间作为兜底
        // 存活时间 = 2000000 - 1100000 = 900000毫秒
        // 资源使用量 = 2核 × 900000毫秒 = 1800000
        assertEquals(1800000L, summary.getExecutorTotalResourceUsage());
        assertEquals(2000000L, executor1.getEndTime()); // 应该被设置为应用结束时间
    }
    
    @Test
    public void testCalculateResourceUsageWithMissingDriverInfo() {
        // 不设置Driver信息
        
        // 计算资源使用量
        ResourceCalculator.calculateTotalResourceUsage(summary);
        
        // 验证创建了默认Driver信息
        assertNotNull(summary.getDriverInfo());
        assertEquals(1, summary.getDriverInfo().getCores()); // 默认1核
        assertEquals(1024, summary.getDriverInfo().getMemoryMB()); // 默认1GB
        
        // 验证Driver资源使用量: 1核 × 1000000毫秒 = 1000000
        assertEquals(1000000L, summary.getDriverResourceUsage());
    }
    
    @Test
    public void testCalculateResourceUsageWithMissingAMInfo() {
        // 不设置ApplicationMaster信息
        
        // 计算资源使用量
        ResourceCalculator.calculateTotalResourceUsage(summary);
        
        // 验证创建了默认AM信息
        assertNotNull(summary.getApplicationMasterInfo());
        assertEquals(1, summary.getApplicationMasterInfo().getCores()); // 默认1核
        assertEquals(512, summary.getApplicationMasterInfo().getMemoryMB()); // 默认512MB
        
        // 验证AM资源使用量: 1核 × 1000000毫秒 = 1000000
        assertEquals(1000000L, summary.getApplicationMasterResourceUsage());
    }
    
    @Test
    public void testCalculateResourceUsageWithInvalidApplicationEndTime() {
        // 设置无效的应用结束时间
        summary.setApplicationEndTime(0);
        
        // 计算资源使用量
        ResourceCalculator.calculateTotalResourceUsage(summary);
        
        // 验证应用结束时间被修正
        assertTrue(summary.getApplicationEndTime() > summary.getApplicationStartTime());
    }
    
    @Test
    public void testCalculateResourceUsageWithInvalidExecutorTiming() {
        // 添加时间异常的Executor
        ExecutorResourceInfo executor1 = new ExecutorResourceInfo("1", "host1", 2, 1024, 500000L); // 启动时间早于应用启动时间
        executor1.setEndTime(3000000L); // 结束时间晚于应用结束时间
        summary.addExecutorInfo(executor1);
        
        ExecutorResourceInfo executor2 = new ExecutorResourceInfo("2", "host2", 4, 1024, 1500000L);
        executor2.setEndTime(1400000L); // 结束时间早于启动时间
        summary.addExecutorInfo(executor2);
        
        // 计算资源使用量
        ResourceCalculator.calculateTotalResourceUsage(summary);
        
        // 验证Executor1的时间被修正
        assertEquals(1000000L, executor1.getStartTime()); // 调整为应用启动时间
        assertEquals(2000000L, executor1.getEndTime()); // 调整为应用结束时间
        
        // 验证Executor2的结束时间被修正
        assertTrue(executor2.getEndTime() > executor2.getStartTime());
    }
    
    @Test
    public void testCalculateResourceUsageWithMultipleExecutors() {
        // 添加多个Executor
        ExecutorResourceInfo executor1 = new ExecutorResourceInfo("1", "host1", 2, 1024, 1100000L);
        executor1.setEndTime(1800000L);
        summary.addExecutorInfo(executor1);
        
        ExecutorResourceInfo executor2 = new ExecutorResourceInfo("2", "host2", 4, 1024, 1200000L);
        executor2.setEndTime(1900000L);
        summary.addExecutorInfo(executor2);
        
        ExecutorResourceInfo executor3 = new ExecutorResourceInfo("3", "host3", 2, 1024, 1300000L);
        // executor3没有结束时间，应该使用应用结束时间
        summary.addExecutorInfo(executor3);
        
        // 计算资源使用量
        ResourceCalculator.calculateTotalResourceUsage(summary);
        
        // 验证各个Executor的资源使用量
        // Executor1: 2核 × 700000毫秒 = 1400000
        assertEquals(1400000L, executor1.getResourceUsage());
        
        // Executor2: 4核 × 700000毫秒 = 2800000
        assertEquals(2800000L, executor2.getResourceUsage());
        
        // Executor3: 2核 × 700000毫秒 = 1400000 (使用应用结束时间)
        assertEquals(1400000L, executor3.getResourceUsage());
        assertEquals(2000000L, executor3.getEndTime()); // 应该被设置为应用结束时间
        
        // 验证总Executor资源使用量
        assertEquals(5600000L, summary.getExecutorTotalResourceUsage());
    }
    
    @Test
    public void testCalculateResourceUsageWithNullSummary() {
        // 测试空汇总对象
        ResourceCalculator.calculateTotalResourceUsage(null);
        // 应该不抛出异常
    }
}
