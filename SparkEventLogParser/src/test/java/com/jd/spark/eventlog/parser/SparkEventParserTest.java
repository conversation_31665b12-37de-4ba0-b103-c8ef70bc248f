package com.jd.spark.eventlog.parser;

import com.jd.spark.eventlog.model.SparkApplicationResourceSummary;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * SparkEventParser测试类
 */
public class SparkEventParserTest {
    
    private SparkEventParser parser;
    
    @Before
    public void setUp() {
        parser = new SparkEventParser();
    }
    
    @Test
    public void testParseApplicationStartEvent() throws IOException {
        String eventLog = "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_123\",\"App Name\":\"TestApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = parser.parseEventLog(inputStream, "test.log");
        
        assertEquals("application_123", summary.getApplicationId());
        assertEquals("TestApp", summary.getApplicationName());
        assertEquals("3.4.0", summary.getSparkVersion());
        assertEquals(1000000L, summary.getApplicationStartTime());
    }
    
    @Test
    public void testParseApplicationEndEvent() throws IOException {
        String eventLog = "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_123\",\"App Name\":\"TestApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}\n" +
                         "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = parser.parseEventLog(inputStream, "test.log");
        
        assertEquals("application_123", summary.getApplicationId());
        assertEquals(1000000L, summary.getApplicationStartTime());
        assertEquals(2000000L, summary.getApplicationEndTime());
    }
    
    @Test
    public void testParseExecutorAddedEvent() throws IOException {
        String eventLog = "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_123\",\"App Name\":\"TestApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}\n" +
                         "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1100000,\"Executor ID\":\"1\",\"Executor Info\":{\"Host\":\"host1\",\"Total Cores\":4,\"Maximum Memory\":2147483648}}\n" +
                         "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";

        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = parser.parseEventLog(inputStream, "test.log");

        assertEquals(1, summary.getExecutorCount());
        assertEquals("1", summary.getExecutorInfos().get(0).getExecutorId());
        assertEquals("host1", summary.getExecutorInfos().get(0).getHost());
        assertEquals(4, summary.getExecutorInfos().get(0).getCores());
        assertEquals(2048, summary.getExecutorInfos().get(0).getMemoryMB()); // 2GB in MB
        assertEquals(1100000L, summary.getExecutorInfos().get(0).getStartTime());
    }
    
    @Test
    public void testParseExecutorRemovedEvent() throws IOException {
        String eventLog = "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_123\",\"App Name\":\"TestApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}\n" +
                         "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1100000,\"Executor ID\":\"1\",\"Executor Info\":{\"Host\":\"host1\",\"Total Cores\":4,\"Maximum Memory\":2147483648}}\n" +
                         "{\"Event\":\"SparkListenerExecutorRemoved\",\"Timestamp\":1900000,\"Executor ID\":\"1\",\"Reason\":\"Command exited with code 1\"}\n" +
                         "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";

        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = parser.parseEventLog(inputStream, "test.log");

        assertEquals(1, summary.getExecutorCount());
        assertEquals(1900000L, summary.getExecutorInfos().get(0).getEndTime());
        assertEquals("Command exited with code 1", summary.getExecutorInfos().get(0).getFailureReason());
        assertTrue(summary.getExecutorInfos().get(0).isCompleted());
    }
    
    @Test
    public void testParseEnvironmentUpdateEvent() throws IOException {
        String eventLog = "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_123\",\"App Name\":\"TestApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}\n" +
                         "{\"Event\":\"SparkListenerEnvironmentUpdate\",\"Spark Properties\":{\"spark.driver.cores\":\"2\",\"spark.driver.memory\":\"2g\",\"spark.yarn.am.cores\":\"1\",\"spark.yarn.am.memory\":\"512m\"}}\n" +
                         "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = parser.parseEventLog(inputStream, "test.log");
        
        // 验证Driver配置
        assertNotNull(summary.getDriverInfo());
        assertEquals(2, summary.getDriverInfo().getCores());
        assertEquals(2048, summary.getDriverInfo().getMemoryMB()); // 2g = 2048MB
        
        // 验证ApplicationMaster配置
        assertNotNull(summary.getApplicationMasterInfo());
        assertEquals(1, summary.getApplicationMasterInfo().getCores());
        assertEquals(512, summary.getApplicationMasterInfo().getMemoryMB());
    }
    
    @Test
    public void testParseCompleteEventLog() throws IOException {
        String eventLog = 
            "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_123\",\"App Name\":\"TestApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}\n" +
            "{\"Event\":\"SparkListenerEnvironmentUpdate\",\"Spark Properties\":{\"spark.driver.cores\":\"2\",\"spark.driver.memory\":\"1g\",\"spark.yarn.am.cores\":\"1\",\"spark.yarn.am.memory\":\"512m\"}}\n" +
            "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1100000,\"Executor ID\":\"1\",\"Executor Info\":{\"Host\":\"host1\",\"Total Cores\":4,\"Maximum Memory\":2147483648}}\n" +
            "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1200000,\"Executor ID\":\"2\",\"Executor Info\":{\"Host\":\"host2\",\"Total Cores\":4,\"Maximum Memory\":2147483648}}\n" +
            "{\"Event\":\"SparkListenerExecutorRemoved\",\"Timestamp\":1900000,\"Executor ID\":\"1\",\"Reason\":\"Command exited with code 1\"}\n" +
            "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = parser.parseEventLog(inputStream, "test.log");
        
        // 验证基本信息
        assertEquals("application_123", summary.getApplicationId());
        assertEquals("TestApp", summary.getApplicationName());
        assertEquals("3.4.0", summary.getSparkVersion());
        
        // 验证时间信息
        assertEquals(1000000L, summary.getApplicationStartTime());
        assertEquals(2000000L, summary.getApplicationEndTime());
        
        // 验证Executor信息
        assertEquals(2, summary.getExecutorCount());
        
        // 验证Driver和AM信息
        assertNotNull(summary.getDriverInfo());
        assertNotNull(summary.getApplicationMasterInfo());
        
        // 验证总核数: Driver(2) + AM(1) + Executor1(4) + Executor2(4) = 11
        assertEquals(11, summary.getTotalCores());
        
        // 验证资源使用量计算
        assertTrue(summary.getTotalResourceUsage() > 0);
    }
    
    @Test
    public void testParseInvalidJson() throws IOException {
        String eventLog = "invalid json line\n" +
                         "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_123\",\"App Name\":\"TestApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}\n" +
                         "another invalid line\n" +
                         "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = parser.parseEventLog(inputStream, "test.log");
        
        // 应该能够正确解析有效的JSON行，忽略无效行
        assertEquals("application_123", summary.getApplicationId());
        assertEquals(1000000L, summary.getApplicationStartTime());
        assertEquals(2000000L, summary.getApplicationEndTime());
    }
    
    @Test
    public void testParseEmptyEventLog() throws IOException {
        String eventLog = "";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = parser.parseEventLog(inputStream, "test.log");
        
        // 应该返回一个空的汇总对象
        assertNotNull(summary);
        assertNull(summary.getApplicationId());
        assertEquals(0, summary.getExecutorCount());
    }
}
