package com.jd.spark.eventlog;

import com.jd.spark.eventlog.model.SparkApplicationResourceSummary;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * SparkEventLogAnalyzer集成测试
 */
public class SparkEventLogAnalyzerIntegrationTest {
    
    private SparkEventLogAnalyzer analyzer;
    
    @Before
    public void setUp() {
        analyzer = new SparkEventLogAnalyzer();
    }
    
    @After
    public void tearDown() {
        if (analyzer != null) {
            analyzer.close();
        }
    }
    
    @Test
    public void testAnalyzeCompleteEventLog() throws IOException {
        String eventLog = 
            "{\"Event\":\"SparkListenerLogStart\",\"Spark Version\":\"3.4.0\"}\n" +
            "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_1234567890_0001\",\"App Name\":\"TestSparkApp\",\"Timestamp\":1000000,\"User\":\"testuser\",\"Spark Version\":\"3.4.0\"}\n" +
            "{\"Event\":\"SparkListenerEnvironmentUpdate\",\"Spark Properties\":{\"spark.app.id\":\"application_1234567890_0001\",\"spark.app.name\":\"TestSparkApp\",\"spark.driver.cores\":\"2\",\"spark.driver.memory\":\"2g\",\"spark.executor.cores\":\"4\",\"spark.executor.memory\":\"4g\",\"spark.yarn.am.cores\":\"1\",\"spark.yarn.am.memory\":\"512m\"}}\n" +
            "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1100000,\"Executor ID\":\"1\",\"Executor Info\":{\"Host\":\"worker1.example.com\",\"Total Cores\":4,\"Maximum Memory\":4294967296}}\n" +
            "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1200000,\"Executor ID\":\"2\",\"Executor Info\":{\"Host\":\"worker2.example.com\",\"Total Cores\":4,\"Maximum Memory\":4294967296}}\n" +
            "{\"Event\":\"SparkListenerExecutorRemoved\",\"Timestamp\":1900000,\"Executor ID\":\"1\",\"Reason\":\"Command exited with code 0\"}\n" +
            "{\"Event\":\"SparkListenerExecutorRemoved\",\"Timestamp\":1950000,\"Executor ID\":\"2\",\"Reason\":\"Command exited with code 0\"}\n" +
            "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = analyzer.analyzeEventLog(inputStream, "test_event_log.json");
        
        // 验证基本信息
        assertEquals("application_1234567890_0001", summary.getApplicationId());
        assertEquals("TestSparkApp", summary.getApplicationName());
        assertEquals("3.4.0", summary.getSparkVersion());
        assertEquals(1000000L, summary.getApplicationStartTime());
        assertEquals(2000000L, summary.getApplicationEndTime());
        assertEquals(1000000L, summary.getApplicationDurationMs());
        
        // 验证Driver信息
        assertNotNull(summary.getDriverInfo());
        assertEquals(2, summary.getDriverInfo().getCores());
        assertEquals(2048, summary.getDriverInfo().getMemoryMB()); // 2g = 2048MB
        assertEquals(2000000L, summary.getDriverResourceUsage()); // 2核 × 1000000毫秒
        
        // 验证ApplicationMaster信息
        assertNotNull(summary.getApplicationMasterInfo());
        assertEquals(1, summary.getApplicationMasterInfo().getCores());
        assertEquals(512, summary.getApplicationMasterInfo().getMemoryMB());
        assertEquals(1000000L, summary.getApplicationMasterResourceUsage()); // 1核 × 1000000毫秒
        
        // 验证Executor信息
        assertEquals(2, summary.getExecutorCount());
        assertEquals(2, summary.getExecutorInfos().size());
        
        // 验证Executor1
        assertEquals("1", summary.getExecutorInfos().get(0).getExecutorId());
        assertEquals("worker1.example.com", summary.getExecutorInfos().get(0).getHost());
        assertEquals(4, summary.getExecutorInfos().get(0).getCores());
        assertEquals(4096, summary.getExecutorInfos().get(0).getMemoryMB()); // 4GB = 4096MB
        assertEquals(1100000L, summary.getExecutorInfos().get(0).getStartTime());
        assertEquals(1900000L, summary.getExecutorInfos().get(0).getEndTime());
        assertEquals(3200000L, summary.getExecutorInfos().get(0).getResourceUsage()); // 4核 × 800000毫秒
        
        // 验证Executor2
        assertEquals("2", summary.getExecutorInfos().get(1).getExecutorId());
        assertEquals("worker2.example.com", summary.getExecutorInfos().get(1).getHost());
        assertEquals(4, summary.getExecutorInfos().get(1).getCores());
        assertEquals(4096, summary.getExecutorInfos().get(1).getMemoryMB());
        assertEquals(1200000L, summary.getExecutorInfos().get(1).getStartTime());
        assertEquals(1950000L, summary.getExecutorInfos().get(1).getEndTime());
        assertEquals(3000000L, summary.getExecutorInfos().get(1).getResourceUsage()); // 4核 × 750000毫秒
        
        // 验证总资源使用量
        assertEquals(6200000L, summary.getExecutorTotalResourceUsage()); // 3200000 + 3000000
        assertEquals(9200000L, summary.getTotalResourceUsage()); // 6200000 + 2000000 + 1000000
        
        // 验证总核数
        assertEquals(11, summary.getTotalCores()); // Driver(2) + AM(1) + Executor1(4) + Executor2(4)
    }
    
    @Test
    public void testAnalyzeEventLogWithIncompleteExecutor() throws IOException {
        String eventLog = 
            "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_test\",\"App Name\":\"TestApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}\n" +
            "{\"Event\":\"SparkListenerEnvironmentUpdate\",\"Spark Properties\":{\"spark.driver.cores\":\"1\",\"spark.driver.memory\":\"1g\",\"spark.yarn.am.cores\":\"1\",\"spark.yarn.am.memory\":\"512m\"}}\n" +
            "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1100000,\"Executor ID\":\"1\",\"Executor Info\":{\"Host\":\"worker1\",\"Total Cores\":2,\"Maximum Memory\":2147483648}}\n" +
            "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1200000,\"Executor ID\":\"2\",\"Executor Info\":{\"Host\":\"worker2\",\"Total Cores\":2,\"Maximum Memory\":2147483648}}\n" +
            "{\"Event\":\"SparkListenerExecutorRemoved\",\"Timestamp\":1800000,\"Executor ID\":\"1\",\"Reason\":\"Normal exit\"}\n" +
            // Executor2没有移除事件，应该使用应用结束时间作为兜底
            "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = analyzer.analyzeEventLog(inputStream, "test_incomplete.json");
        
        // 验证基本信息
        assertEquals("application_test", summary.getApplicationId());
        assertEquals(2, summary.getExecutorCount());
        
        // 验证Executor1（正常结束）
        assertEquals(1800000L, summary.getExecutorInfos().get(0).getEndTime());
        assertEquals(1400000L, summary.getExecutorInfos().get(0).getResourceUsage()); // 2核 × 700000毫秒
        
        // 验证Executor2（使用应用结束时间作为兜底）
        assertEquals(2000000L, summary.getExecutorInfos().get(1).getEndTime()); // 应该被设置为应用结束时间
        assertEquals(1600000L, summary.getExecutorInfos().get(1).getResourceUsage()); // 2核 × 800000毫秒
        
        // 验证总Executor资源使用量
        assertEquals(3000000L, summary.getExecutorTotalResourceUsage()); // 1400000 + 1600000
    }
    
    @Test
    public void testAnalyzeEventLogWithMissingConfiguration() throws IOException {
        String eventLog = 
            "{\"Event\":\"SparkListenerApplicationStart\",\"App ID\":\"application_minimal\",\"App Name\":\"MinimalApp\",\"Timestamp\":1000000,\"Spark Version\":\"3.4.0\"}\n" +
            // 没有环境更新事件，应该使用默认配置
            "{\"Event\":\"SparkListenerExecutorAdded\",\"Timestamp\":1100000,\"Executor ID\":\"1\",\"Executor Info\":{\"Host\":\"worker1\",\"Total Cores\":4,\"Maximum Memory\":4294967296}}\n" +
            "{\"Event\":\"SparkListenerApplicationEnd\",\"Timestamp\":2000000}";
        
        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = analyzer.analyzeEventLog(inputStream, "test_minimal.json");
        
        // 验证基本信息
        assertEquals("application_minimal", summary.getApplicationId());
        assertEquals("MinimalApp", summary.getApplicationName());
        
        // 验证默认Driver配置
        assertNotNull(summary.getDriverInfo());
        assertEquals(1, summary.getDriverInfo().getCores()); // 默认1核
        assertEquals(1024, summary.getDriverInfo().getMemoryMB()); // 默认1GB
        
        // 验证默认AM配置
        assertNotNull(summary.getApplicationMasterInfo());
        assertEquals(1, summary.getApplicationMasterInfo().getCores()); // 默认1核
        assertEquals(512, summary.getApplicationMasterInfo().getMemoryMB()); // 默认512MB
        
        // 验证Executor信息
        assertEquals(1, summary.getExecutorCount());
        assertEquals(4, summary.getExecutorInfos().get(0).getCores());
        
        // 验证总资源使用量计算
        assertTrue(summary.getTotalResourceUsage() > 0);
    }
    
    @Test
    public void testAnalyzeEmptyEventLog() throws IOException {
        String eventLog = "";

        InputStream inputStream = new ByteArrayInputStream(eventLog.getBytes());
        SparkApplicationResourceSummary summary = analyzer.analyzeEventLog(inputStream, "empty.json");

        // 应该返回一个有效的汇总对象，但信息很少
        assertNotNull(summary);
        assertNull(summary.getApplicationId());
        assertEquals(0, summary.getExecutorCount());

        // 对于空事件日志，ResourceCalculator会创建默认的Driver和AM信息，
        // 并使用当前时间作为兜底，所以总资源使用量不会是0
        assertTrue("空事件日志的总资源使用量应该大于0（因为有默认的Driver和AM）",
                  summary.getTotalResourceUsage() > 0);
    }
}
