package com.jd.spark.eventlog.model;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * SparkApplicationResourceSummary测试类
 */
public class SparkApplicationResourceSummaryTest {
    
    private SparkApplicationResourceSummary summary;
    
    @Before
    public void setUp() {
        summary = new SparkApplicationResourceSummary("app_123", "TestApp");
        summary.setApplicationStartTime(1000000L);
        summary.setApplicationEndTime(2000000L);
    }
    
    @Test
    public void testBasicProperties() {
        assertEquals("app_123", summary.getApplicationId());
        assertEquals("TestApp", summary.getApplicationName());
        assertEquals(1000000L, summary.getApplicationStartTime());
        assertEquals(2000000L, summary.getApplicationEndTime());
    }
    
    @Test
    public void testAddExecutorInfo() {
        ExecutorResourceInfo executor1 = new ExecutorResourceInfo("1", "host1", 2, 1024, 1100000L);
        ExecutorResourceInfo executor2 = new ExecutorResourceInfo("2", "host2", 4, 2048, 1200000L);
        
        summary.addExecutorInfo(executor1);
        summary.addExecutorInfo(executor2);
        
        assertEquals(2, summary.getExecutorCount());
        assertEquals(2, summary.getExecutorInfos().size());
        assertTrue(summary.getExecutorInfos().contains(executor1));
        assertTrue(summary.getExecutorInfos().contains(executor2));
    }
    
    @Test
    public void testGetTotalCores() {
        // 添加Driver信息
        DriverResourceInfo driverInfo = new DriverResourceInfo("driver-host", 1, 1024, 1000000L, 2000000L);
        summary.setDriverInfo(driverInfo);
        
        // 添加ApplicationMaster信息
        ApplicationMasterResourceInfo amInfo = new ApplicationMasterResourceInfo("am-host", 1, 512, 1000000L, 2000000L);
        summary.setApplicationMasterInfo(amInfo);
        
        // 添加Executor信息
        ExecutorResourceInfo executor1 = new ExecutorResourceInfo("1", "host1", 2, 1024, 1100000L);
        ExecutorResourceInfo executor2 = new ExecutorResourceInfo("2", "host2", 4, 2048, 1200000L);
        summary.addExecutorInfo(executor1);
        summary.addExecutorInfo(executor2);
        
        // 总核数 = Driver(1) + AM(1) + Executor1(2) + Executor2(4) = 8
        assertEquals(8, summary.getTotalCores());
    }
    
    @Test
    public void testCalculateTotalResourceUsage() {
        // 设置Driver信息
        DriverResourceInfo driverInfo = new DriverResourceInfo("driver-host", 2, 1024, 1000000L, 2000000L);
        summary.setDriverInfo(driverInfo);
        
        // 设置ApplicationMaster信息
        ApplicationMasterResourceInfo amInfo = new ApplicationMasterResourceInfo("am-host", 1, 512, 1000000L, 2000000L);
        summary.setApplicationMasterInfo(amInfo);
        
        // 添加Executor信息
        ExecutorResourceInfo executor1 = new ExecutorResourceInfo("1", "host1", 4, 1024, 1100000L);
        executor1.setEndTime(1900000L);
        summary.addExecutorInfo(executor1);
        
        // 计算总资源使用量
        summary.calculateTotalResourceUsage();
        
        // 验证应用运行时间
        assertEquals(1000000L, summary.getApplicationDurationMs());
        
        // 验证Driver资源使用量: 2核 × 1000000毫秒 = 2000000
        assertEquals(2000000L, summary.getDriverResourceUsage());
        
        // 验证AM资源使用量: 1核 × 1000000毫秒 = 1000000
        assertEquals(1000000L, summary.getApplicationMasterResourceUsage());
        
        // 验证Executor资源使用量: 4核 × 800000毫秒 = 3200000
        assertEquals(3200000L, summary.getExecutorTotalResourceUsage());
        
        // 验证总资源使用量: 2000000 + 1000000 + 3200000 = 6200000
        assertEquals(6200000L, summary.getTotalResourceUsage());
    }
    
    @Test
    public void testCalculateResourceUsageWithIncompleteExecutor() {
        // 添加未完成的Executor（没有结束时间）
        ExecutorResourceInfo executor1 = new ExecutorResourceInfo("1", "host1", 2, 1024, 1100000L);
        // 不设置结束时间，模拟未正常结束的情况
        summary.addExecutorInfo(executor1);
        
        // 计算总资源使用量
        summary.calculateTotalResourceUsage();
        
        // 验证Executor使用应用结束时间作为兜底
        // 存活时间 = 2000000 - 1100000 = 900000毫秒
        // 资源使用量 = 2核 × 900000毫秒 = 1800000
        assertEquals(1800000L, summary.getExecutorTotalResourceUsage());
    }
    
    @Test
    public void testToString() {
        summary.setSparkVersion("3.4.0");
        
        ExecutorResourceInfo executor = new ExecutorResourceInfo("1", "host1", 2, 1024, 1100000L);
        summary.addExecutorInfo(executor);
        
        summary.calculateTotalResourceUsage();
        
        String result = summary.toString();
        assertTrue(result.contains("app_123"));
        assertTrue(result.contains("TestApp"));
        assertTrue(result.contains("3.4.0"));
        assertTrue(result.contains("executorCount=1"));
    }
}
