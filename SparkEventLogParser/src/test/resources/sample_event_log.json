{"Event":"SparkListenerLogStart","Spark Version":"3.4.0"}
{"Event":"SparkListenerApplicationStart","App ID":"application_1234567890_0001","App Name":"TestSparkApp","Timestamp":1000000,"User":"testuser","Spark Version":"3.4.0"}
{"Event":"SparkListenerEnvironmentUpdate","JVM Information":{"Java Version":"1.8.0_281 (Oracle Corporation)","Java Home":"/usr/lib/jvm/java-8-oracle","Scala Version":"version 2.12.17"},"Spark Properties":{"spark.app.id":"application_1234567890_0001","spark.app.name":"TestSparkApp","spark.driver.cores":"2","spark.driver.memory":"2g","spark.executor.cores":"4","spark.executor.memory":"4g","spark.yarn.am.cores":"1","spark.yarn.am.memory":"512m"},"Hadoop Properties":{},"System Properties":{},"Classpath Entries":{}}
{"Event":"SparkListenerExecutorAdded","Timestamp":1100000,"Executor ID":"1","Executor Info":{"Host":"worker1.example.com","Total Cores":4,"Maximum Memory":4294967296}}
{"Event":"SparkListenerExecutorAdded","Timestamp":1200000,"Executor ID":"2","Executor Info":{"Host":"worker2.example.com","Total Cores":4,"Maximum Memory":4294967296}}
{"Event":"SparkListenerJobStart","Job ID":0,"Submission Time":1300000,"Stage Infos":[{"Stage ID":0,"Stage Attempt ID":0,"Stage Name":"collect at TestApp.scala:10","Number of Tasks":2,"RDD Info":[]}],"Properties":{}}
{"Event":"SparkListenerStageSubmitted","Stage Info":{"Stage ID":0,"Stage Attempt ID":0,"Stage Name":"collect at TestApp.scala:10","Number of Tasks":2,"RDD Info":[],"Parent IDs":[],"Details":"","Submission Time":1300000}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":0,"Index":0,"Attempt":0,"Launch Time":1310000,"Executor ID":"1","Host":"worker1.example.com","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskStart","Stage ID":0,"Stage Attempt ID":0,"Task Info":{"Task ID":1,"Index":1,"Attempt":0,"Launch Time":1310000,"Executor ID":"2","Host":"worker2.example.com","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":0,"Failed":false,"Killed":false,"Accumulables":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ResultTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":0,"Index":0,"Attempt":0,"Launch Time":1310000,"Executor ID":"1","Host":"worker1.example.com","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1320000,"Failed":false,"Killed":false,"Accumulables":[]},"Task Metrics":{"Executor Deserialize Time":100,"Executor Deserialize CPU Time":50,"Executor Run Time":9000,"Executor CPU Time":8000,"Result Size":1000,"JVM GC Time":100,"Result Serialization Time":50,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Bytes Written":0,"Write Time":0,"Records Written":0},"Input Metrics":{"Bytes Read":1000,"Records Read":10},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerTaskEnd","Stage ID":0,"Stage Attempt ID":0,"Task Type":"ResultTask","Task End Reason":{"Reason":"Success"},"Task Info":{"Task ID":1,"Index":1,"Attempt":0,"Launch Time":1310000,"Executor ID":"2","Host":"worker2.example.com","Locality":"PROCESS_LOCAL","Speculative":false,"Getting Result Time":0,"Finish Time":1325000,"Failed":false,"Killed":false,"Accumulables":[]},"Task Metrics":{"Executor Deserialize Time":100,"Executor Deserialize CPU Time":50,"Executor Run Time":14000,"Executor CPU Time":13000,"Result Size":1000,"JVM GC Time":100,"Result Serialization Time":50,"Memory Bytes Spilled":0,"Disk Bytes Spilled":0,"Shuffle Read Metrics":{"Remote Blocks Fetched":0,"Local Blocks Fetched":0,"Fetch Wait Time":0,"Remote Bytes Read":0,"Remote Bytes Read To Disk":0,"Local Bytes Read":0,"Total Records Read":0},"Shuffle Write Metrics":{"Bytes Written":0,"Write Time":0,"Records Written":0},"Input Metrics":{"Bytes Read":1000,"Records Read":10},"Output Metrics":{"Bytes Written":0,"Records Written":0},"Updated Blocks":[]}}
{"Event":"SparkListenerStageCompleted","Stage Info":{"Stage ID":0,"Stage Attempt ID":0,"Stage Name":"collect at TestApp.scala:10","Number of Tasks":2,"RDD Info":[],"Parent IDs":[],"Details":"","Submission Time":1300000,"Completion Time":1325000,"Failure Reason":"","Accumulables":[]}}
{"Event":"SparkListenerJobEnd","Job ID":0,"Completion Time":1325000,"Job Result":{"Result":"JobSucceeded"}}
{"Event":"SparkListenerExecutorRemoved","Timestamp":1900000,"Executor ID":"1","Reason":"Command exited with code 0"}
{"Event":"SparkListenerExecutorRemoved","Timestamp":1950000,"Executor ID":"2","Reason":"Command exited with code 0"}
{"Event":"SparkListenerApplicationEnd","Timestamp":2000000}
