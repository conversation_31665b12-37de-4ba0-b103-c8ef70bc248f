# Spark Event Log Parser

一个用于解析Spark事件日志文件并计算资源使用量的Java库。

## 功能特性

- **多版本支持**: 支持Spark 2.4、3.0、3.4版本生成的事件日志
- **多压缩格式**: 支持LZ4、ZSTD、GZIP压缩格式以及无压缩的事件日志文件
- **资源计算**: 计算Executor、Driver、ApplicationMaster的核数×存活时间
- **异常处理**: 对于未记录完成时间的Executor，使用应用完成时间作为兜底
- **批量处理**: 支持单个文件和批量文件处理
- **线程安全**: 支持多线程并发处理

## 核心组件

### 数据模型
- `ExecutorResourceInfo`: Executor资源使用信息
- `DriverResourceInfo`: Driver资源使用信息  
- `ApplicationMasterResourceInfo`: ApplicationMaster资源使用信息
- `SparkApplicationResourceSummary`: 应用资源使用汇总信息

### 解析器
- `SparkEventParser`: 核心事件日志解析器
- `CompressionUtils`: 压缩文件处理工具

### 计算器
- `ResourceCalculator`: 资源使用量计算器

### API接口
- `SparkEventLogAnalyzer`: 主要API接口

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.jd</groupId>
    <artifactId>SparkEventLogParser</artifactId>
    <version>1.0-SNAPSHOT</version>
</dependency>
```

### 2. 基本使用

```java
import com.jd.spark.eventlog.SparkEventLogAnalyzer;
import com.jd.spark.eventlog.model.SparkApplicationResourceSummary;

// 创建分析器
SparkEventLogAnalyzer analyzer = new SparkEventLogAnalyzer();

try {
    // 分析单个文件
    File eventLogFile = new File("/path/to/application_123456789_0001.lz4");
    SparkApplicationResourceSummary summary = analyzer.analyzeEventLog(eventLogFile);
    
    // 打印结果
    System.out.println("应用ID: " + summary.getApplicationId());
    System.out.println("总资源使用量: " + summary.getTotalResourceUsage() + " 核·毫秒");
    System.out.println("Executor数量: " + summary.getExecutorCount());
    
} finally {
    analyzer.close();
}
```

### 3. 批量处理

```java
// 分析目录下的所有事件日志文件
File directory = new File("/path/to/spark/eventlogs/");
List<SparkApplicationResourceSummary> summaries = analyzer.analyzeEventLogsInDirectory(directory, true);

for (SparkApplicationResourceSummary summary : summaries) {
    System.out.println(summary.getApplicationId() + ": " + summary.getTotalResourceUsage() + " 核·毫秒");
}
```

### 4. 使用示例程序

```bash
# 编译项目
mvn clean compile

# 分析单个文件
java -cp target/classes com.jd.spark.eventlog.example.SparkEventLogAnalyzerExample /path/to/application_123.lz4

# 分析目录
java -cp target/classes com.jd.spark.eventlog.example.SparkEventLogAnalyzerExample /path/to/eventlogs/
```

## 资源计算说明

### 计算公式
- **Executor资源使用量** = Executor核数 × Executor存活时间(毫秒)
- **Driver资源使用量** = Driver核数 × 应用运行时间(毫秒)  
- **ApplicationMaster资源使用量** = AM核数 × 应用运行时间(毫秒)
- **总资源使用量** = Executor总使用量 + Driver使用量 + AM使用量

### 异常处理
1. **Executor未正常结束**: 使用应用结束时间作为Executor结束时间
2. **缺少Driver配置**: 使用默认配置(1核, 1GB内存)
3. **缺少AM配置**: 使用默认配置(1核, 512MB内存)
4. **时间异常**: 自动修正不合理的时间戳

## 支持的事件类型

- `SparkListenerApplicationStart`: 应用启动事件
- `SparkListenerApplicationEnd`: 应用结束事件
- `SparkListenerExecutorAdded`: Executor添加事件
- `SparkListenerExecutorRemoved`: Executor移除事件
- `SparkListenerEnvironmentUpdate`: 环境配置更新事件

## 压缩格式支持

| 格式 | 文件扩展名 | 魔数检测 |
|------|------------|----------|
| LZ4  | .lz4       | ✓        |
| ZSTD | .zst, .zstd| ✓        |
| GZIP | .gz        | ✓        |
| 无压缩| .log, 其他 | -        |

## 配置参数

### 默认值
- Driver默认核数: 1
- ApplicationMaster默认核数: 1
- Driver默认内存: 1GB
- ApplicationMaster默认内存: 512MB

### 线程池配置
```java
// 自定义线程池大小
SparkEventLogAnalyzer analyzer = new SparkEventLogAnalyzer(8);
```

## 测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=SparkEventParserTest
```

## 性能建议

1. **批量处理**: 对于大量文件，使用批量处理API以获得更好的性能
2. **线程池大小**: 根据CPU核数和IO特性调整线程池大小
3. **内存管理**: 处理大文件时注意JVM堆内存设置
4. **超时设置**: 对于可能很大的文件，设置合适的超时时间

## 故障排除

### 常见问题

1. **文件读取失败**
   - 检查文件路径和权限
   - 确认文件格式是否支持

2. **解析结果不准确**
   - 检查事件日志文件是否完整
   - 确认Spark版本兼容性

3. **内存不足**
   - 增加JVM堆内存: `-Xmx4g`
   - 使用流式处理避免加载整个文件到内存

### 日志级别
```properties
# 启用DEBUG日志查看详细解析过程
logging.level.com.jd.spark.eventlog=DEBUG
```

## 版本历史

- **1.0-SNAPSHOT**: 初始版本，支持基本的事件日志解析和资源计算功能

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用内部许可证，仅供京东内部使用。
