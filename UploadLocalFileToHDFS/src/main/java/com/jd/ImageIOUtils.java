package com.jd;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Iterator;

public class ImageIOUtils {

    public static void main(String[] args) throws IOException {
        File file = new File("/Users/<USER>/Desktop/n03018349_4028.JPEG");
//        File file = new File("/Users/<USER>/Desktop/iShot_2023-04-22_08.14.34.png");
        ImageInputStream imageInputStream = ImageIO.createImageInputStream(file);
        Iterator<ImageReader> imageReaders = ImageIO.getImageReaders(imageInputStream);
        while(imageReaders.hasNext()){
            ImageReader reader = imageReaders.next();
            reader.setInput(imageInputStream);
            BufferedImage read = reader.read(0);
            // BufferedImage@2d928643: type = 6
            // ColorModel: #pixelBits = 32
            // numComponents = 4 color space = java.awt.color.ICC_ColorSpace@5025a98f
            // transparency = 3 has alpha = true
            // isAlphaPre = false ByteInterleavedRaster:
            // width = 3520 height = 838
            // #numDataElements 4 dataOff[0] = 3
            System.out.println("read = " + read);
            // 虽然同样会会抛出IO异常
            String imageName = reader.getFormatName();
            System.out.println("imageName = " + imageName);
            reader.dispose();
        }
    }
}
