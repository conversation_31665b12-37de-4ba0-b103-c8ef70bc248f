package com.jd;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageInputStream;

import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.concurrent.*;

/**
ps -ef|grep UploadLocalFileToHDFS | grep -v grep | awk '{print $2}' | xargs kill
export JAVA_HOME=/software/servers/jdk1.8.0_221/
export PATH=$JAVA_HOME/bin:$PATH
export UMPPREFIX=/home/<USER>/zhangchen/upload/umpLog/
nohup java -jar UploadLocalFileToHDFS-1.0-SNAPSHOT-jar-with-dependencies.jar &> upload.log &
 */
public class Upload {

    static void getFile(File file, Operation operation) {
        if (file != null) {
            for (File listFile : file.listFiles()) {
                if (listFile.isDirectory()) {
                    getFile(listFile, operation);
                }
                operation.m1(listFile);
            }
        }
    }

    interface Operation {
        void m1(File file);
    }

    public static void main(String[] args) {
        Profiler.registerJVMInfo("UploadLocalFileToHDFS");

        System.setProperty("ump.app_name", "SparkMonitorApp");

        String localFilePath = args.length == 0 ? "/home/<USER>/zhangchen/train" : args[0];
        String hdfsFilePath = args.length == 0 ? "hdfs://ns17/tmp/wuguoxiao_jpeg_6" : args[1];
        int threadPoolSize = args.length == 0 ? 10 : Integer.parseInt(args[2]);
        String coreSite = args.length == 0 ? "/software/conf/cairne/mart_sc/bdp_jmart_dapb_union.bdp_jmart_dapb_union_formal/hadoop_conf/core-site.xml" : args[3];
        String hdfsSite = args.length == 0 ? "/software/conf/cairne/mart_sc/bdp_jmart_dapb_union.bdp_jmart_dapb_union_formal/hadoop_conf/hdfs-site.xml" : args[4];
        String defaultFS = args.length == 0 ? "hdfs://ns17" : args[5];

        ExecutorService myExecutor = Executors.newFixedThreadPool(threadPoolSize);

        Configuration conf = new Configuration();
        System.out.println("before conf.toString() = " + conf.toString());
        conf.addResource(new Path(coreSite));
        conf.addResource(new Path(hdfsSite));
        System.out.println("after conf.toString() = " + conf.toString());
        conf.set("fs.hdfs.impl", "org.apache.hadoop.hdfs.DistributedFileSystem");
        conf.set("fs.defaultFS", defaultFS);

        final int[] i = {0};
        Operation operation = new Operation() {
            @Override
            public void m1(File file) {
                long start = System.currentTimeMillis();
                myExecutor.submit(() -> {
                    try (FileSystem fs = FileSystem.newInstance(conf)) {
                        System.out.println(Thread.currentThread().getName() + " file.getName() = " + file.getName());
                        CallerInfo schedulerCaller = Profiler.registerInfo("UploadLocalFileToHDFS.submit", "SparkMonitorApp", false, true);
                        Path hdfsPath = new Path(hdfsFilePath + "/" + file.getPath().substring(localFilePath.length()));
                        String pathString = "file://" + file.getPath();
                        System.out.println(Thread.currentThread().getName() + " local file = " + pathString);
                        Path localPath = new Path(pathString);
                        //

                        //
                        fs.copyFromLocalFile(localPath, hdfsPath);
                        System.out.println(Thread.currentThread().getName() + " localPath = " + localPath + " hdfsPath = " + hdfsPath);
                        Profiler.registerInfoEnd(schedulerCaller);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
                System.out.println((i[0]++) + " file = " + file + " elapsed " + (System.currentTimeMillis() - start));
            }
        };

        getFile(new File(localFilePath), operation);

        myExecutor.shutdown();
    }
}
