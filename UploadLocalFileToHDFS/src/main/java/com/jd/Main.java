package com.jd;

import java.util.concurrent.ArrayBlockingQueue;

public class Main {
    public static void main(String[] args) {
        ArrayBlockingQueue arrayBlockingQueue = new ArrayBlockingQueue(10);
        for (int i = 0; i < 20; i++) {
            System.out.println("brfore" + i);
            try {
                arrayBlockingQueue.put(i);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            System.out.println("after " + i);

        }
    }
}
