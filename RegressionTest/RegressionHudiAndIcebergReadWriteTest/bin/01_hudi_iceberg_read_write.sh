#!/bin/sh

export JDHXXXXX_CLUSTER_NAME=10k;
export JDHXXXXX_USER=mart_scr;
export JDHXXXXX_QUEUE=bdp_jmart_dapb_union.bdp_jmart_dapb_union_formal;
export TEAM_USER=mart_dapb_spark;
source /software/servers/env/env.sh

set -e
set -x

if [ -d "$1" ]; then
  echo "Info: $1 is a directory."
  export SPARK_HOME=$1
fi

pyspark_version_str=`grep "__version__" $SPARK_HOME/python/pyspark/version.py | awk -F= '{print $2}'`

submit_iceberg() {
  local hudi=$1
  spark-submit --master $master --conf spark.datalake.enabled=true \
  --conf spark.sql.storeAssignmentPolicy=ANSI \
  $hudi \
  --class com.jd.spark.regression.IcebergReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar
}

submit_spark() {
  local master=$1

  # iceberg case
  if [[ ${pyspark_version_str} =~ "3.0" ]]; then
    submit_iceberg ""
    submit_iceberg "--conf spark.hudi.enabled=true"
    submit_iceberg "--conf spark.hudi_pure.enabled=true"
  fi

  # hudi case
  spark-submit --master $master --conf spark.hudi.enabled=true \
  --class com.jd.spark.regression.HudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

  spark-submit --master $master --conf spark.hudi.enabled=true \
  --conf spark.datalake.enabled=true \
  --conf spark.sql.storeAssignmentPolicy=ANSI \
  --class com.jd.spark.regression.HudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

  spark-submit --master $master --conf spark.hudi.enabled=true \
  --conf spark.hudi_pure.enabled=true \
  --class com.jd.spark.regression.HudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

  spark-submit --master $master --conf spark.hudi.enabled=true \
  --conf spark.datalake.enabled=true \
  --conf spark.sql.storeAssignmentPolicy=ANSI \
  --conf spark.hudi_pure.enabled=true \
  --class com.jd.spark.regression.HudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

  # hudi_pure test
  spark-submit --master $master --conf spark.hudi_pure.enabled=true \
  --class com.jd.spark.regression.HudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

  spark-submit --master $master --conf spark.hudi_pure.enabled=true \
  --conf spark.datalake.enabled=true \
  --conf spark.sql.storeAssignmentPolicy=ANSI \
  --class com.jd.spark.regression.HudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

  spark-submit --master $master --conf spark.hudi_pure.enabled=true \
  --conf spark.hudi.enabled=true \
  --class com.jd.spark.regression.HudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

  spark-submit --master $master --conf spark.hudi_pure.enabled=true \
  --conf spark.hudi.enabled=true \
  --conf spark.datalake.enabled=true \
  --conf spark.sql.storeAssignmentPolicy=ANSI \
  --class com.jd.spark.regression.HudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

  # iceberg case
  if [[ ${pyspark_version_str} =~ "3.0" ]]; then
    # hudi + iceberg test
    spark-submit --master $master --conf spark.hudi.enabled=true --conf spark.datalake.enabled=true \
    --conf spark.sql.storeAssignmentPolicy=ANSI \
    --class com.jd.spark.regression.IcebergHudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

    # hudi_pure + iceberg test
    spark-submit --master $master --conf spark.hudi_pure.enabled=true --conf spark.datalake.enabled=true \
    --conf spark.sql.storeAssignmentPolicy=ANSI \
    --class com.jd.spark.regression.IcebergHudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar

    # hudi + hudi_pure + iceberg test
    spark-submit --master $master --conf spark.hudi.enabled=true \
    --conf spark.hudi_pure.enabled=true --conf spark.datalake.enabled=true \
    --conf spark.sql.storeAssignmentPolicy=ANSI \
    --class com.jd.spark.regression.IcebergHudiReadWriteTest RegressionHudiAndIcebergReadWriteTest-1.0-SNAPSHOT.jar
  fi
}

submit_spark "local"