package com.jd.spark.regression


import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession

object IcebergHudiReadWriteTest {
  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .appName("Iceberg Hudi Read-Write App")
      .enableHiveSupport()
      .getOrCreate()

    spark.sql("select * from dev.wgx_test_iceberg").show

    spark.sql("insert into dev.wgx_test_iceberg values(3, 'CC')")

    spark.sql("select * from dev.wgx_test_iceberg").show

    spark.sql("CALL iceberg_catalog.system.remove_orphan_files(table => 'dev.wgx_test_iceberg', dry_run => true)")

    spark.sql("drop table if exists tmp.spark_temp_hudi_table_10k_20250101")

    spark.sql(
      """
    CREATE TABLE tmp.spark_temp_hudi_table_10k_20250101 (
      id INT,
      name STRING,
      age INT
    ) USING hudi
    OPTIONS (
      type = 'cow',
      primaryKey = 'id',
      preCombineField = 'age'
    )
    """)

    spark.sql(
      """
    INSERT INTO tmp.spark_temp_hudi_table_10k_20250101 VALUES
      (1, 'Alice', 30),
      (2, 'Bob', 25),
      (3, 'Cathy', 27),
      (4, 'David', 32),
      (5, 'Eva', 29),
      (6, 'Frank', 31),
      (7, 'Grace', 28),
      (8, 'Hank', 26),
      (9, 'Ivy', 33),
      (10, 'Jack', 24)
    """)

    spark.sql("SELECT * FROM tmp.spark_temp_hudi_table_10k_20250101 WHERE age > 25").show

    spark.stop
  }
}
