package com.jd.spark.regression

import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession

object IcebergReadWriteTest {
  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .appName("Iceberg Read-Write App")
      .enableHiveSupport()
      .getOrCreate()

    spark.sql("select * from dev.wgx_test_iceberg").show

    spark.sql("insert into dev.wgx_test_iceberg values(3, 'CC')")

    spark.sql("select * from dev.wgx_test_iceberg").show

    spark.sql("CALL iceberg_catalog.system.remove_orphan_files(table => 'dev.wgx_test_iceberg', dry_run => true)")

    spark.close()
  }
}
