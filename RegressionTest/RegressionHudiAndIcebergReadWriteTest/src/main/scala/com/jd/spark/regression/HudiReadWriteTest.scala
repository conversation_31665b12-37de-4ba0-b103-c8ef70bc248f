package com.jd.spark.regression

import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession

object HudiReadWriteTest {

  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .appName("Hudi Read-Write App")
      .enableHiveSupport()
      .getOrCreate()

    spark.sql("drop table if exists tmp.spark_temp_hudi_table_10k_20250101")

    spark.sql(
      """
  CREATE TABLE tmp.spark_temp_hudi_table_10k_20250101 (
    id INT,
    name STRING,
    age INT
  ) USING hudi
  OPTIONS (
    type = 'cow',
    primaryKey = 'id',
    preCombineField = 'age'
  )
  """)

    spark.sql(
      """
  INSERT INTO tmp.spark_temp_hudi_table_10k_20250101 VALUES
    (1, '<PERSON>', 30),
    (2, '<PERSON>', 25),
    (3, '<PERSON>', 27),
    (4, '<PERSON>', 32),
    (5, '<PERSON>', 29),
    (6, '<PERSON>', 31),
    (7, '<PERSON>', 28),
    (8, '<PERSON>', 26),
    (9, '<PERSON>', 33),
    (10, '<PERSON>', 24)
  """)

    spark.sql("<PERSON>LE<PERSON> * F<PERSON>M tmp.spark_temp_hudi_table_10k_20250101 WHERE age > 25").show

    spark.stop
  }
}
