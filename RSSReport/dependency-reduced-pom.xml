<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.jd.bdp.engine</groupId>
  <artifactId>RSSReport</artifactId>
  <version>1.0-SNAPSHOT</version>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <transformers>
                <transformer>
                  <mainClass>com.jd.bdp.engine.RSSReport</mainClass>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>scala-maven-plugin</artifactId>
        <version>3.2.2</version>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <scalaVersion>${scala.version}</scalaVersion>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-core_2.12</artifactId>
      <version>3.4.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-sql_2.12</artifactId>
      <version>3.4.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.scalatest</groupId>
      <artifactId>scalatest_2.12</artifactId>
      <version>3.2.15</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>scalatest-core_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-featurespec_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-flatspec_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-freespec_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-funsuite_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-funspec_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-propspec_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-refspec_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-wordspec_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-diagrams_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-matchers-core_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-shouldmatchers_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scalatest-mustmatchers_2.12</artifactId>
          <groupId>org.scalatest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <scala.version>2.12.11</scala.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <spark.version>3.4.0</spark.version>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
</project>
