#!/bin/bash

# 调度任务地址：http://dp.jd.com/buffalo/task/detail?env=prod&taskId=1884040

export JDHXXXXX_CLUSTER_NAME=cairne;
export JDHXXXXX_USER=dd_edw;
export JDHXXXXX_QUEUE=bdp_jdw_dd_edw_bdp_spark;
export TEAM_USER=dd_edw_system_optimization;
source /software/servers/env/env.sh

# 脚本地址： https://oss.jd.com/bucket/moneta/list/buffalo/
\rm RSSReport-1.0-SNAPSHOT.jar
wget http://storage.jd.local/moneta/buffalo/RSSReport-1.0-SNAPSHOT.jar

# 容器地址
export SPARK_HOME=/buffalo/bdp-app/spark_3.4
# 物理机地址
# export SPARK_HOME=/software/servers/cairne/dd_edw/spark_3.4

spark-submit --queue root.bdp_jdw_dd_edw_test --class com.jd.bdp.engine.RSSReport RSSReport-1.0-SNAPSHOT.jar