package com.jd.bdp.engine

import org.apache.spark.sql.SparkSession

/**
 */
object RSSReport {

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.shuffle.partitions", "1000")
      .config("spark.sql.adaptive.shuffle.targetPostShuffleInputSize", "41943040")
      .config("spark.dynamicAllocation.initialExecutors", "200")
      .config("spark.dynamicAllocation.minExecutors", "200")
      .config("spark.dynamicAllocation.maxExecutors", "2000")
      .config("spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version", 2)
      .appName("RSS Report App")
      .enableHiveSupport()
      .getOrCreate()

    val detailDF = spark.sql(
      """
        |SELECT
        |	b.erp,
        |	b.taskid,
        |    c.buffaloenvactiondefid,
        |	b.logid,
        |	b.jobsource,
        |	b.cluster,
        |    b.queue,
        |	CAST(SUBSTR(b.starttime_date, 12, 2) AS INT) AS start_hour,
        |	CAST((
        |			CASE
        |				WHEN SUBSTR(b.endtime_date, 12, 2) = ''
        |				THEN '-1'
        |				ELSE SUBSTR(b.endtime_date, 12, 2)
        |			END) AS INT) AS end_hour,
        |	b.duration / 1000 AS durationSecond,
        |	(instr(c.spark_config_all, 'spark.shuffle.manager -> remote') > 0
        |	AND instr(c.spark_config_all, 'spark.shuffle.rss.enabled -> true') > 0) AS isRSS,
        | 	(instr(c.spark_config_all, 'spark.shuffle.manager -> sort') > 0
        |	AND instr(c.spark_config_all, 'spark.shuffle.rss.enabled -> true') > 0) AS rssError,
        |	b.appSparkVersion,
        |	a.ns,
        |	a.appid,
        |	a.appattemptid,
        |	SUM(inputBytes / 1024 / 1024 ) / 1024 AS sum_input_GB,
        |	MAX(inputBytes / 1024 / 1024 ) / 1024 AS max_input_GB,
        |	SUM(shuffleWriteBytes / 1024 / 1024) / 1024 / 1024 AS sum_shuffle_TB,
        |	MAX(shuffleWriteBytes / 1024 / 1024) / 1024 / 1024 AS max_shuffle_TB,
        |	a.dt
        |FROM
        |	fdm.fdm_spark_stageinfo_di AS a
        |INNER JOIN fdm.fdm_spark_appinfo_di AS b
        |ON
        |	a.appid = b.appid
        |	AND a.appattemptid = b.appattemptid
        |	AND a.dt = b.dt
        |INNER JOIN fdm.fdm_spark_environmentinfo_di AS c
        |ON
        |	a.appid = c.appid
        |	AND a.appattemptid = c.appattemptid
        |	AND a.dt = c.dt
        |WHERE
        |	a.dt >= sysdate( - 2)
        |	AND b.dt >= sysdate( - 2)
        |	AND c.dt >= sysdate( - 2)
        |GROUP BY
        |	b.erp,
        |	a.dt,
        |	b.taskid,
        |    c.buffaloenvactiondefid,
        |	b.logid,
        |	b.jobsource,
        |	b.cluster,
        |    b.queue,
        |	CAST(SUBSTR(b.starttime_date, 12, 2) AS INT),
        |	CAST((
        |			CASE
        |				WHEN SUBSTR(b.endtime_date, 12, 2) = ''
        |				THEN '-1'
        |				ELSE SUBSTR(b.endtime_date, 12, 2)
        |			END) AS INT),
        |	b.duration,
        |	(instr(c.spark_config_all, 'spark.shuffle.manager -> remote') > 0
        |	AND instr(c.spark_config_all, 'spark.shuffle.rss.enabled -> true') > 0),
        | (instr(c.spark_config_all, 'spark.shuffle.manager -> sort') > 0
        |	AND instr(c.spark_config_all, 'spark.shuffle.rss.enabled -> true') > 0),
        |	b.appSparkVersion,
        |	a.ns,
        |	a.appid,
        |	a.appattemptid
        |""".stripMargin)

    detailDF.groupBy("dt").count().show(100)

    val tableName = "rss_heavy_application_detail"
    detailDF.repartition(10).write.format("jdbc")
      .option("url", "jdbc:mysql://*************:3306/unified_metadata_pre?characterEncoding=utf8")
      .option("dbtable", tableName)
      .option("driver", "com.mysql.jdbc.Driver")
      .option("user", "salt")
      .option("password", "salt")
      .mode("overwrite")
      .save()

    spark.close()
  }
}
