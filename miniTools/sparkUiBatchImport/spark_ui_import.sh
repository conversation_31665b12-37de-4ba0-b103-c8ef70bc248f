#!/bin/bash

# 这个脚本主要用来读取data_l2.txt文件，重构对应application_id的sparkUI数据
#  ssh bdp_client@10.198.194.194
#  cd /home/<USER>/xiaowei12
#  /home/<USER>/xiaowei12/10k.sh 用来导入10k集群的任务SPARkUI
#  /home/<USER>/xiaowei12/hope.sh 用来导入hope集群的任务SPARkUI
#  /home/<USER>/xiaowei12/tyrande.sh 用来导入tyrande集群的任务SPARkUI
# 由于广告集市的eventLog，会写入tyrande集群，因此hope.sh和tyrande.sh需要都试一下



if [[ $# < 1 ]]
then 
  echo "必须输入一个参数: input data name"
  exit 1
fi


data_path=$1
for line in `cat /home/<USER>/xiaowei12/${data_path}`
do
    app_id=$(echo $line  | awk -F ','  '{print $1}')
    cluster=$(echo $line  | awk -F ','  '{print $5}')
    engine=$(echo $line  | awk -F ','  '{print $6}')
    spark_ui=$(echo $line  | awk -F ','  '{print $7}')
    if [ -n "$spark_ui" ];then
        echo $line
        continue
    fi

    if [[  $engine == "MAPREDUCE" ]];then
        echo $line
        continue
    fi
    #echo "==== start import spark ui for: $app_id in $cluster  ========="

    if [[ $cluster == *"10k"* ]]
    then
       final_url=$(sh /home/<USER>/xiaowei12/10k.sh $app_id 2023-09-08)
    elif [[ $cluster == *"hope"* ]]
    then
       final_url=$(sh /home/<USER>/xiaowei12/hope.sh $app_id 2023-09-08) 
       if [[ "$final_url" == "" ]]
       then
          final_url=$(sh /home/<USER>/xiaowei12/tyrande.sh $app_id 2023-09-08)
       fi
    elif [[ $cluster == *"tyrande"* ]]
    then
       final_url=$(sh /home/<USER>/xiaowei12/tyrande.sh $app_id 2023-09-08)
       if [[ "$final_url" == "" ]]
       then
           final_url=$(sh /home/<USER>/xiaowei12/hope.sh $app_id 2023-09-08)
       fi
    else
        echo "$line,error_cluster"
    fi

    echo "$line,$final_url"
done 