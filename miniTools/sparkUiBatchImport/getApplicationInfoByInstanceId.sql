-- 根据buffalo任务实例id查询对应JobID, 任务实例id， 运行日志id， buffalo任务id， 集群
SELECT
    r1.id,              
    r1.task_instance_id,
    r1.log_id, 
    r2.task_id, 
    r1.cluster 
FROM
    (
        SELECT
            r.id,
            split(r.name, ',') [3] AS task_instance_id,
            split(r.name, ',') [4] AS log_id,
            r.cluster
        FROM
            (
                SELECT
                    id,
                    name,
                    cluster
                FROM
                    fdm.fdm_m99_cluster_rt_job_info_di
                WHERE
                        dt = '2023-09-08'
                  AND name regexp
                      '3552903376230418310|3552903333777770502'
                GROUP BY
                    id,
                    name,
                    cluster
            )
                r
    )
        r1
        LEFT OUTER JOIN
    (
        SELECT
            task_id,
            task_instance_id
        FROM
            gdm.gdm_m99_task_run_log
        WHERE
                dt = '2023-09-08'
        GROUP BY
            task_id,
            task_instance_id
    )
        r2
    ON
            r1.task_instance_id = r2.task_instance_id