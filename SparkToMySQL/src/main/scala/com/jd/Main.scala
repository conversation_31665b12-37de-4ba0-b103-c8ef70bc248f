package com.jd

import org.apache.spark.internal.Logging
import org.apache.spark.sql.SparkSession

object Main extends Logging {

  def main(args: Array[String]): Unit = {

    val jimUrl = "jim://2941132917553898407/10466"

    import com.jd.jim.cli.ReloadableJimClientFactory
    import com.jd.jim.cli.serializer.DefaultObjectSerializer
//    val configClientFactory = new ConfigLongPollingClientFactory("http://cfs.jim.jd.local/")
    val factory = new ReloadableJimClientFactory
    // default 0
    factory.setConfigId("0")
    // netty IO线程池数量，一般情况下设置为2效果最佳，针对吞吐要求高的情况，可以根据不同的客户端CPU配置和集群规模建议测试后进行调整
    factory.setIoThreadPoolSize(2)
    //默认16k
    factory.setObjectSerializer(new DefaultObjectSerializer(16 * 1024))
    factory.setJimUrl(jimUrl)
    //流量控制，该队列由请求和响应两部组成，当队列瞬时达到50000，此时会提示超出队列长度，可以根据业务的流量进行调整，特别针对异步和pipeline调用
    factory.setRequestQueueSize(50000)

//    factory.setConfigClient(configClientFactory.create)

    val client = factory.getClient


    val spark = SparkSession
      .builder()
      .appName(s"buffaloId:")
      .enableHiveSupport()
      .getOrCreate()

    val df = spark.sql(
      """
        |SELECT
        |	taskid,
        |	a.dt,
        |	logid,
        |	SUM(a.duration) AS sum_duration,
        |	sum(inputBytes / 1024 ) / 1024 / 1024 as inputGB,
        |	sum(shuffleReadBytes / 1024 ) / 1024 / 1024 as shuffleReadGB
        |FROM
        |	fdm.fdm_spark_appinfo_di AS a
        |INNER JOIN fdm.fdm_spark_environmentinfo_di AS b
        |ON
        |	a.appid = b.appid
        |	AND a.appattemptid = b.appattemptid
        |INNER JOIN fdm.fdm_spark_stageinfo_di AS c
        |ON
        |	a.appid = c.appid
        |	AND a.appattemptid = c.appattemptid
        |WHERE
        |	a.dt = '2022-11-26'
        |	AND b.dt = '2022-11-26'
        |	AND a.jobsource = 'BUFFALO4'
        |GROUP BY
        |	taskid,
        |	a.dt,
        |	logid
        |""".stripMargin)
    df.cache()
    df.foreach(row => {
      val taskId = row.getString(0)
      val dt = row.getString(1)
      val logid = row.get(2)
      val duration = row.getString(3)
      val inputGB = row.getString(4)
      val shuffleReadGB = row.getString(5)
      System.err.println(s"taskId = $taskId dt = $dt logid = $logid")
      val data = new java.util.HashMap[String, String]
      data.put("duration", duration)
      data.put("inputGB", inputGB)
      data.put("shuffleReadGB", shuffleReadGB)
      client.hMSet(taskId + "_" + logid, data)
    })


    var startTime = System.currentTimeMillis()

    spark.close()
    System.exit(-1)
  }
}
