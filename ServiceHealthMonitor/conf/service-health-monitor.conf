# 服务健康监控配置文件
# 此文件包含服务健康检查的所有配置参数

# ================================
# 服务健康检查配置
# ================================

# 需要监控的服务端点列表（逗号分隔）
# 默认值: http://11.11.17.174:18082,http://11.11.17.174:18081
spark.service.health.endpoints=http://11.11.17.174:18082,http://11.11.17.174:18081

# HTTP请求超时时间（秒）
# 默认值: 10
spark.service.health.timeout.seconds=10

# 健康检查间隔时间（分钟）
# 默认值: 1
spark.service.health.check.interval.minutes=1

# 期望的响应内容（用于判断服务是否健康）
# 默认值: History Server
spark.service.health.expected.content=History Server

# ================================
# Pushgateway配置
# ================================

# Pushgateway服务地址
# 默认值: http://baizepg-hadoop.jd.local
spark.service.health.pushgateway.url=http://baizepg-hadoop.jd.local

# Pushgateway服务ID
# 默认值: 55600fc1-5749-4db3-b5d9-0e39ce9472e2
spark.service.health.pushgateway.service.id=55600fc1-5749-4db3-b5d9-0e39ce9472e2

# ================================
# 使用示例
# ================================

# 1. 监控不同的服务端点:
# spark.service.health.endpoints=http://server1:8080,http://server2:8080,http://server3:8080

# 2. 调整超时时间为30秒:
# spark.service.health.timeout.seconds=30

# 3. 每5分钟检查一次:
# spark.service.health.check.interval.minutes=5

# 4. 检查不同的响应内容:
# spark.service.health.expected.content=Application is running

# 5. 使用不同的Pushgateway:
# spark.service.health.pushgateway.url=http://my-pushgateway.example.com:9091
# spark.service.health.pushgateway.service.id=my-custom-service-id
