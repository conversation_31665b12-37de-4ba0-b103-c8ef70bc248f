# 服务健康监控模块 (ServiceHealthMonitor)

## 功能概述

ServiceHealthMonitor是一个基于Spark的服务健康检查监控模块，主要功能包括：

1. **定时健康检查**: 每1分钟对指定的服务端点执行HTTP GET请求
2. **智能健康判断**: 检查HTTP响应状态码和响应内容是否包含指定字符串
3. **监控数据上报**: 将检查结果推送到Apache Pushgateway
4. **灵活配置**: 支持通过Spark配置参数自定义监控行为

## 监控指标设计

### 指标名称和含义

| 指标名称 | 含义 | 值说明 |
|---------|------|--------|
| `service_health_status` | 服务健康状态 | 0=健康，1=异常 |
| `service_response_time_ms` | 服务响应时间 | 响应时间（毫秒） |

### 标签设计

| 标签名称 | 说明 | 示例值 |
|---------|------|--------|
| `endpoint` | 完整的服务端点URL | `http://************:18082` |
| `host` | 服务主机地址 | `************` |
| `port` | 服务端口号 | `18082` |
| `service_type` | 服务类型 | `history_server` |

### 健康判断规则

- **健康条件**: HTTP状态码为2xx且响应内容包含"History Server"
- **异常条件**: HTTP请求失败、状态码非2xx或响应内容不包含期望字符串

## 配置参数

| 配置项 | 说明 | 默认值 |
|-------|------|--------|
| `spark.service.health.endpoints` | 监控的服务端点列表（逗号分隔） | `http://************:18082,http://************:18081` |
| `spark.service.health.timeout.seconds` | HTTP请求超时时间（秒） | `10` |
| `spark.service.health.check.interval.minutes` | 健康检查间隔（分钟） | `1` |
| `spark.service.health.expected.content` | 期望的响应内容 | `History Server` |
| `spark.service.health.pushgateway.url` | Pushgateway地址 | `http://baizepg-hadoop.jd.local` |
| `spark.service.health.pushgateway.service.id` | Pushgateway服务ID | `55600fc1-5749-4db3-b5d9-0e39ce9472e2` |

## 编译和打包

```bash
# 进入模块目录
cd ServiceHealthMonitor

# 编译项目
mvn clean compile

# 打包项目
mvn clean package
```

编译成功后，会在`target`目录下生成JAR文件。

## 使用方法

### 1. 定时监控模式（推荐）

```bash
# 使用spark-submit启动定时监控
spark-submit \
  --class Main \
  --master local[2] \
  --conf spark.service.health.endpoints="http://************:18082,http://************:18081" \
  --conf spark.service.health.timeout.seconds=10 \
  --conf spark.service.health.check.interval.minutes=1 \
  --conf spark.service.health.expected.content="History Server" \
  --conf spark.service.health.pushgateway.url="http://baizepg-hadoop.jd.local" \
  --conf spark.service.health.pushgateway.service.id="55600fc1-5749-4db3-b5d9-0e39ce9472e2" \
  target/ServiceHealthMonitor-1.0-SNAPSHOT.jar monitor
```

### 2. 单次执行模式

```bash
# 只执行一次健康检查
spark-submit \
  --class Main \
  --master local[2] \
  --conf spark.service.health.endpoints="http://************:18082,http://************:18081" \
  target/ServiceHealthMonitor-1.0-SNAPSHOT.jar once
```

### 3. 测试模式

```bash
# 测试Pushgateway连接
spark-submit \
  --class Main \
  --master local[2] \
  --conf spark.service.health.pushgateway.url="http://baizepg-hadoop.jd.local" \
  --conf spark.service.health.pushgateway.service.id="55600fc1-5749-4db3-b5d9-0e39ce9472e2" \
  target/ServiceHealthMonitor-1.0-SNAPSHOT.jar test
```

### 4. 使用配置文件

```bash
# 使用配置文件启动
spark-submit \
  --class Main \
  --master local[2] \
  --properties-file conf/service-health-monitor.conf \
  target/ServiceHealthMonitor-1.0-SNAPSHOT.jar monitor
```

## 监控数据格式

推送到Pushgateway的数据格式符合OpenTSDB标准：

```json
[
  {
    "metric": "service_health_status",
    "timestamp": 1586490128123,
    "value": 0,
    "tags": {
      "endpoint": "http://************:18082",
      "host": "************",
      "port": "18082",
      "service_type": "history_server"
    }
  },
  {
    "metric": "service_response_time_ms",
    "timestamp": 1586490128123,
    "value": 245,
    "tags": {
      "endpoint": "http://************:18082",
      "host": "************",
      "port": "18082",
      "service_type": "history_server"
    }
  }
]
```

## 日志说明

程序运行时会输出详细的日志信息：

- **INFO级别**: 正常的监控活动和结果摘要
- **WARN级别**: 服务健康检查失败的警告
- **ERROR级别**: 系统异常和推送失败的错误

## 故障排查

### 1. HTTP连接超时
- 检查网络连接
- 调整`spark.service.health.timeout.seconds`参数
- 确认服务端点URL正确

### 2. Pushgateway推送失败
- 检查Pushgateway地址是否正确
- 确认网络连接正常
- 验证服务ID是否有效

### 3. 响应内容检查失败
- 确认期望内容字符串正确
- 检查服务是否正常返回预期内容
- 查看详细的错误日志

## 扩展说明

### 添加新的监控端点
修改配置参数`spark.service.health.endpoints`，添加新的URL即可。

### 自定义健康检查逻辑
可以修改`HealthChecker.scala`中的`checkEndpoint`方法来实现更复杂的健康检查逻辑。

### 添加新的监控指标
可以在`PushgatewayUtil.scala`中的`convertToMetrics`方法中添加新的指标类型。

## 依赖说明

- **Spark 3.0.2**: 核心计算引擎
- **OkHttp 4.9.3**: HTTP客户端库
- **Jackson 2.12.3**: JSON序列化库
- **Scala 2.12.10**: 编程语言
