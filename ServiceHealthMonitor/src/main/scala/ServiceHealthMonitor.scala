import org.apache.spark.internal.Logging
import org.apache.spark.sql.SparkSession

import java.util.concurrent.{Executors, ScheduledExecutorService, TimeUnit}
import scala.util.{Failure, Success, Try}

/**
 * 服务健康监控主类
 * 负责定时执行健康检查并上报监控数据
 */
class ServiceHealthMonitor(spark: SparkSession) extends Logging {

  private val conf = new ConfUtil(spark)
  private val healthChecker = new HealthChecker(conf)
  private val pushgatewayUtil = new PushgatewayUtil(conf)
  
  // 定时任务调度器
  private var scheduler: Option[ScheduledExecutorService] = None
  
  /**
   * 执行一次健康检查
   */
  def executeHealthCheck(): Unit = {
    logInfo("=== 开始执行服务健康检查 ===")
    
    try {
      // 执行健康检查
      val results = healthChecker.checkAllEndpoints()
      
      // 打印检查结果摘要
      printHealthCheckSummary(results)
      
      // 推送监控数据
      val pushSuccess = pushgatewayUtil.pushHealthCheckResults(results)
      if (pushSuccess) {
        logInfo("监控数据推送成功")
      } else {
        logError("监控数据推送失败")
      }
      
    } catch {
      case ex: Exception =>
        logError("执行健康检查时发生异常", ex)
    }
    
    logInfo("=== 服务健康检查执行完成 ===")
  }

  /**
   * 打印健康检查结果摘要
   * @param results 检查结果列表
   */
  private def printHealthCheckSummary(results: List[HealthCheckResult]): Unit = {
    logInfo("健康检查结果摘要:")
    results.foreach { result =>
      val status = if (result.isHealthy) "✓ 健康" else "✗ 异常"
      val errorInfo = result.errorMessage.map(msg => s" ($msg)").getOrElse("")
      logInfo(s"  ${result.endpoint} [$status] 响应时间: ${result.responseTime}ms$errorInfo")
    }
  }

  /**
   * 启动定时监控
   */
  def startScheduledMonitoring(): Unit = {
    val intervalMinutes = conf.checkIntervalMinutes
    logInfo(s"启动定时健康检查，间隔: ${intervalMinutes}分钟")
    
    // 打印配置信息
    conf.printConfig()
    
    // 创建定时任务调度器
    val scheduledExecutor = Executors.newScheduledThreadPool(1)
    scheduler = Some(scheduledExecutor)
    
    // 立即执行一次健康检查
    executeHealthCheck()
    
    // 定时执行健康检查
    scheduledExecutor.scheduleAtFixedRate(
      new Runnable {
        override def run(): Unit = {
          Try {
            executeHealthCheck()
          } match {
            case Success(_) => // 成功，无需处理
            case Failure(ex) => logError("定时健康检查任务执行失败", ex)
          }
        }
      },
      intervalMinutes, // 初始延迟
      intervalMinutes, // 执行间隔
      TimeUnit.MINUTES
    )
    
    logInfo("定时健康检查已启动")
  }

  /**
   * 停止定时监控
   */
  def stopScheduledMonitoring(): Unit = {
    scheduler.foreach { executor =>
      logInfo("正在停止定时健康检查...")
      executor.shutdown()
      try {
        if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
          executor.shutdownNow()
        }
        logInfo("定时健康检查已停止")
      } catch {
        case _: InterruptedException =>
          executor.shutdownNow()
          Thread.currentThread().interrupt()
      }
    }
    scheduler = None
  }

  /**
   * 关闭监控器，释放资源
   */
  def close(): Unit = {
    stopScheduledMonitoring()
    healthChecker.close()
    pushgatewayUtil.close()
    logInfo("服务健康监控器已关闭")
  }

  /**
   * 执行测试推送
   */
  def testPushgateway(): Unit = {
    logInfo("测试Pushgateway连接...")
    val success = pushgatewayUtil.pushTestData()
    if (success) {
      logInfo("Pushgateway连接测试成功")
    } else {
      logError("Pushgateway连接测试失败")
    }
  }
}
