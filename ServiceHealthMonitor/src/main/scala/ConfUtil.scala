import ConfUtil._
import org.apache.spark.sql.SparkSession

object ConfUtil {
  // 服务健康检查配置键
  private val SERVICE_ENDPOINTS_KEY = "spark.service.health.endpoints"
  private val SERVICE_TIMEOUT_SECONDS_KEY = "spark.service.health.timeout.seconds"
  private val SERVICE_CHECK_INTERVAL_MINUTES_KEY = "spark.service.health.check.interval.minutes"
  private val SERVICE_EXPECTED_CONTENT_KEY = "spark.service.health.expected.content"
  
  // Pushgateway配置键
  private val PUSHGATEWAY_URL_KEY = "spark.service.health.pushgateway.url"
  private val PUSHGATEWAY_SERVICE_ID_KEY = "spark.service.health.pushgateway.service.id"
  
  // 默认配置值
  private val SERVICE_ENDPOINTS_DEFAULT = "http://11.11.17.174:18082,http://11.11.17.174:18081"
  private val SERVICE_TIMEOUT_SECONDS_DEFAULT = "10"
  private val SERVICE_CHECK_INTERVAL_MINUTES_DEFAULT = "1"
  private val SERVICE_EXPECTED_CONTENT_DEFAULT = "History Server"
  
  private val PUSHGATEWAY_URL_DEFAULT = "http://baizepg-hadoop.jd.local"
  private val PUSHGATEWAY_SERVICE_ID_DEFAULT = "55600fc1-5749-4db3-b5d9-0e39ce9472e2"
}

/**
 * 服务健康检查配置工具类
 * 负责管理所有配置参数的读取和解析
 */
class ConfUtil(spark: SparkSession) {
  private def conf = spark.conf

  /**
   * 获取需要监控的服务端点列表
   * @return 服务端点URL列表
   */
  def serviceEndpoints: List[String] = {
    conf.get(SERVICE_ENDPOINTS_KEY, SERVICE_ENDPOINTS_DEFAULT)
      .split(",")
      .map(_.trim)
      .filter(_.nonEmpty)
      .toList
  }

  /**
   * 获取HTTP请求超时时间（秒）
   * @return 超时时间
   */
  def timeoutSeconds: Int = {
    conf.get(SERVICE_TIMEOUT_SECONDS_KEY, SERVICE_TIMEOUT_SECONDS_DEFAULT).toInt
  }

  /**
   * 获取健康检查间隔时间（分钟）
   * @return 检查间隔
   */
  def checkIntervalMinutes: Int = {
    conf.get(SERVICE_CHECK_INTERVAL_MINUTES_KEY, SERVICE_CHECK_INTERVAL_MINUTES_DEFAULT).toInt
  }

  /**
   * 获取期望的响应内容
   * @return 期望包含的字符串
   */
  def expectedContent: String = {
    conf.get(SERVICE_EXPECTED_CONTENT_KEY, SERVICE_EXPECTED_CONTENT_DEFAULT)
  }

  /**
   * 获取Pushgateway URL
   * @return Pushgateway地址
   */
  def pushgatewayUrl: String = {
    conf.get(PUSHGATEWAY_URL_KEY, PUSHGATEWAY_URL_DEFAULT)
  }

  /**
   * 获取Pushgateway服务ID
   * @return 服务ID
   */
  def pushgatewayServiceId: String = {
    conf.get(PUSHGATEWAY_SERVICE_ID_KEY, PUSHGATEWAY_SERVICE_ID_DEFAULT)
  }

  /**
   * 打印当前配置信息（用于调试）
   */
  def printConfig(): Unit = {
    println(s"服务端点: ${serviceEndpoints.mkString(", ")}")
    println(s"超时时间: ${timeoutSeconds}秒")
    println(s"检查间隔: ${checkIntervalMinutes}分钟")
    println(s"期望内容: $expectedContent")
    println(s"Pushgateway URL: $pushgatewayUrl")
    println(s"服务ID: $pushgatewayServiceId")
  }
}
