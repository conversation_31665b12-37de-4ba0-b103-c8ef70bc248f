import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import okhttp3.{MediaType, OkHttpClient, Request, RequestBody}
import org.apache.spark.internal.Logging

import java.util.concurrent.TimeUnit
import scala.collection.mutable
import scala.util.{Failure, Success, Try}

/**
 * 监控指标数据类
 * @param metric 指标名称
 * @param timestamp 时间戳（毫秒）
 * @param value 指标值（0=健康，1=异常）
 * @param tags 标签信息
 */
case class MetricData(
  metric: String,
  timestamp: Long,
  value: Int,
  tags: Map[String, String]
)

/**
 * Pushgateway上报工具类
 * 负责将监控数据推送到Apache Pushgateway
 */
class PushgatewayUtil(conf: ConfUtil) extends Logging {

  // JSON序列化工具
  private val objectMapper = new ObjectMapper()
  objectMapper.registerModule(DefaultScalaModule)

  // HTTP客户端
  private val httpClient = new OkHttpClient.Builder()
    .connectTimeout(30, TimeUnit.SECONDS)
    .readTimeout(30, TimeUnit.SECONDS)
    .writeTimeout(30, TimeUnit.SECONDS)
    .build()

  // JSON媒体类型
  private val JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8")

  /**
   * 将健康检查结果转换为监控指标
   * @param results 健康检查结果列表
   * @return 监控指标列表
   */
  def convertToMetrics(results: List[HealthCheckResult]): List[MetricData] = {
    results.map { result =>
      MetricData(
        metric = "service_health_status",
        timestamp = result.timestamp,
        value = if (result.isHealthy) 0 else 1, // 0=健康，1=异常
        tags = Map(
          "endpoint" -> result.endpoint,
          "host" -> result.host,
          "instance" -> result.host,
          "port" -> result.port,
          "service_type" -> "history_server",
          "deploymentServices" -> "sparkjh"
        )
      )
    } ++ results.map { result =>
      // 添加响应时间指标
      MetricData(
        metric = "service_response_time_ms",
        timestamp = result.timestamp,
        value = result.responseTime.toInt,
        tags = Map(
          "endpoint" -> result.endpoint,
          "host" -> result.host,
          "instance" -> result.host,
          "port" -> result.port,
          "service_type" -> "history_server",
          "deploymentServices" -> "sparkjh"
        )
      )
    }
  }

  /**
   * 推送监控数据到Pushgateway
   * @param metrics 监控指标列表
   * @return 是否推送成功
   */
  def pushMetrics(metrics: List[MetricData]): Boolean = {
    if (metrics.isEmpty) {
      logWarning("没有监控数据需要推送")
      return true
    }

    try {
      // 构建请求URL
      val url = s"${conf.pushgatewayUrl}/api/put?service_id=${conf.pushgatewayServiceId}&data_fmt=opentsdb"
      
      // 将指标数据转换为JSON
      val jsonData = objectMapper.writeValueAsString(metrics)
      
      logInfo(s"准备推送 ${metrics.size} 个监控指标到 Pushgateway")
      logDebug(s"推送URL: $url")
      logDebug(s"推送数据: $jsonData")
      
      // 构建HTTP POST请求
      val requestBody = RequestBody.create(JSON_MEDIA_TYPE, jsonData)
      val request = new Request.Builder()
        .url(url)
        .post(requestBody)
        .addHeader("Content-Type", "application/json")
        .build()
      
      // 执行请求
      val response = httpClient.newCall(request).execute()
      
      try {
        if (response.isSuccessful) {
          logInfo(s"监控数据推送成功，HTTP状态码: ${response.code()}")
          true
        } else {
          val responseBody = response.body().string()
          logError(s"监控数据推送失败，HTTP状态码: ${response.code()}，响应内容: $responseBody")
          false
        }
      } finally {
        response.close()
      }
      
    } catch {
      case ex: Exception =>
        logError(s"推送监控数据到Pushgateway时发生异常: ${ex.getMessage}", ex)
        false
    }
  }

  /**
   * 推送健康检查结果
   * @param results 健康检查结果列表
   * @return 是否推送成功
   */
  def pushHealthCheckResults(results: List[HealthCheckResult]): Boolean = {
    val metrics = convertToMetrics(results)
    pushMetrics(metrics)
  }

  /**
   * 生成测试数据并推送（用于测试）
   */
  def pushTestData(): Boolean = {
    val testMetrics = List(
      MetricData(
        metric = "service_health_test",
        timestamp = System.currentTimeMillis(),
        value = 0,
        tags = Map(
          "host" -> "test.example.com",
          "port" -> "8080",
          "type" -> "test"
        )
      )
    )
    
    logInfo("推送测试数据到Pushgateway")
    pushMetrics(testMetrics)
  }

  /**
   * 关闭HTTP客户端资源
   */
  def close(): Unit = {
    httpClient.dispatcher().executorService().shutdown()
    httpClient.connectionPool().evictAll()
  }
}
