import okhttp3.{OkHttpClient, Request}
import org.apache.spark.internal.Logging

import java.net.{URI, URL}
import java.util.concurrent.TimeUnit
import scala.util.{Failure, Success, Try}

/**
 * 健康检查结果数据类
 * @param endpoint 服务端点URL
 * @param host 主机地址
 * @param port 端口号
 * @param isHealthy 是否健康（true=健康，false=异常）
 * @param responseTime 响应时间（毫秒）
 * @param errorMessage 错误信息（如果有）
 * @param timestamp 检查时间戳（毫秒）
 */
case class HealthCheckResult(
  endpoint: String,
  host: String,
  port: String,
  isHealthy: Boolean,
  responseTime: Long,
  errorMessage: Option[String],
  timestamp: Long
)

/**
 * 服务健康检查器
 * 负责执行HTTP GET请求并检查响应内容
 */
class HealthChecker(conf: ConfUtil) extends Logging {

  // 创建HTTP客户端，配置超时时间
  private val httpClient: OkHttpClient = new OkHttpClient.Builder()
    .connectTimeout(conf.timeoutSeconds, TimeUnit.SECONDS)
    .readTimeout(conf.timeoutSeconds, TimeUnit.SECONDS)
    .writeTimeout(conf.timeoutSeconds, TimeUnit.SECONDS)
    .build()

  /**
   * 执行单个端点的健康检查
   * @param endpoint 服务端点URL
   * @return 健康检查结果
   */
  def checkEndpoint(endpoint: String): HealthCheckResult = {
    val startTime = System.currentTimeMillis()
    val timestamp = startTime
    
    try {
      // 解析URL获取主机和端口信息
      val url = new URL(endpoint)
      val host = url.getHost
      val port = if (url.getPort == -1) url.getDefaultPort.toString else url.getPort.toString
      
      logInfo(s"开始检查服务端点: $endpoint")
      
      // 构建HTTP GET请求
      val request = new Request.Builder()
        .url(endpoint)
        .get()
        .build()
      
      // 执行请求
      val response = httpClient.newCall(request).execute()
      val responseTime = System.currentTimeMillis() - startTime
      
      try {
        val responseBody = response.body().string()
        val isHealthy = response.isSuccessful && responseBody.contains(conf.expectedContent)
        
        if (isHealthy) {
          logInfo(s"服务端点 $endpoint 健康检查通过，响应时间: ${responseTime}ms")
        } else {
          val reason = if (!response.isSuccessful) {
            s"HTTP状态码: ${response.code()}"
          } else {
            s"响应内容不包含期望字符串: '${conf.expectedContent}'"
          }
          logWarning(s"服务端点 $endpoint 健康检查失败: $reason，响应时间: ${responseTime}ms")
        }
        
        HealthCheckResult(
          endpoint = endpoint,
          host = host,
          port = port,
          isHealthy = isHealthy,
          responseTime = responseTime,
          errorMessage = if (isHealthy) None else Some(s"HTTP ${response.code()} 或内容检查失败"),
          timestamp = timestamp
        )
      } finally {
        response.close()
      }
      
    } catch {
      case ex: Exception =>
        val responseTime = System.currentTimeMillis() - startTime
        val errorMsg = s"请求异常: ${ex.getMessage}"
        logError(s"服务端点 $endpoint 健康检查异常: $errorMsg，响应时间: ${responseTime}ms", ex)
        
        // 尝试解析URL获取主机和端口，如果失败则使用默认值
        val (host, port) = Try {
          val url = new URL(endpoint)
          (url.getHost, if (url.getPort == -1) url.getDefaultPort.toString else url.getPort.toString)
        }.getOrElse(("unknown", "unknown"))
        
        HealthCheckResult(
          endpoint = endpoint,
          host = host,
          port = port,
          isHealthy = false,
          responseTime = responseTime,
          errorMessage = Some(errorMsg),
          timestamp = timestamp
        )
    }
  }

  /**
   * 执行所有配置端点的健康检查
   * @return 所有端点的健康检查结果列表
   */
  def checkAllEndpoints(): List[HealthCheckResult] = {
    val endpoints = conf.serviceEndpoints
    logInfo(s"开始检查 ${endpoints.size} 个服务端点")
    
    val results = endpoints.map(checkEndpoint)
    
    val healthyCount = results.count(_.isHealthy)
    val unhealthyCount = results.length - healthyCount
    logInfo(s"健康检查完成: 健康 $healthyCount 个，异常 $unhealthyCount 个")
    
    results
  }

  /**
   * 关闭HTTP客户端资源
   */
  def close(): Unit = {
    httpClient.dispatcher().executorService().shutdown()
    httpClient.connectionPool().evictAll()
  }
}
