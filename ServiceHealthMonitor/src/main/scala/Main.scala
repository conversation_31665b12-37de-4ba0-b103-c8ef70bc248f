import org.apache.spark.sql.SparkSession

/**
 * 服务健康监控主程序入口
 */
object Main {

  def main(args: Array[String]): Unit = {
    // 解析命令行参数
    val mode = if (args.length > 0) args(0) else "monitor"
    
    // 创建Spark会话
    val spark = SparkSession.builder()
      .appName("ServiceHealthMonitor")
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .getOrCreate()
    
    // 设置日志级别
    spark.sparkContext.setLogLevel("INFO")
    
    val monitor = new ServiceHealthMonitor(spark)
    
    try {
      mode.toLowerCase match {
        case "monitor" =>
          // 默认模式：启动定时监控
          println("启动服务健康监控...")
          monitor.startScheduledMonitoring()
          
          // 添加关闭钩子，确保程序优雅退出
          Runtime.getRuntime.addShutdownHook(new Thread(() => {
            println("正在关闭服务健康监控...")
            monitor.close()
            spark.stop()
          }))
          
          // 保持程序运行
          while (!Thread.currentThread().isInterrupted) {
            Thread.sleep(10000) // 每10秒检查一次
          }
          
        case "once" =>
          // 单次执行模式：只执行一次健康检查
          println("执行单次健康检查...")
          monitor.executeHealthCheck()
          
        case "test" =>
          // 测试模式：测试Pushgateway连接
          println("测试Pushgateway连接...")
          monitor.testPushgateway()
          
        case _ =>
          println("使用方法:")
          println("  monitor  - 启动定时监控（默认）")
          println("  once     - 执行单次健康检查")
          println("  test     - 测试Pushgateway连接")
          System.exit(1)
      }
      
    } catch {
      case ex: Exception =>
        println(s"程序执行异常: ${ex.getMessage}")
        ex.printStackTrace()
        System.exit(1)
    } finally {
      if (mode != "monitor") {
        monitor.close()
        spark.stop()
      }
    }
  }
}
