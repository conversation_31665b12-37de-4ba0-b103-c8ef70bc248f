#!/bin/bash

# 服务健康监控启动脚本
# 使用方法: ./start-monitor.sh [monitor|once|test]

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 默认配置
DEFAULT_MODE="monitor"
DEFAULT_MASTER="local[2]"
DEFAULT_JAR="$PROJECT_DIR/target/ServiceHealthMonitor-1.0-SNAPSHOT.jar"
DEFAULT_CONF="$PROJECT_DIR/conf/service-health-monitor.conf"

# 解析命令行参数
MODE=${1:-$DEFAULT_MODE}
MASTER=${SPARK_MASTER:-$DEFAULT_MASTER}
JAR_FILE=${JAR_FILE:-$DEFAULT_JAR}
CONF_FILE=${CONF_FILE:-$DEFAULT_CONF}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境
check_environment() {
    print_info "检查运行环境..."
    
    # 检查Spark是否安装
    if ! command -v spark-submit &> /dev/null; then
        print_error "spark-submit 命令未找到，请确保Spark已正确安装并配置PATH"
        exit 1
    fi
    
    # 检查JAR文件是否存在
    if [ ! -f "$JAR_FILE" ]; then
        print_error "JAR文件不存在: $JAR_FILE"
        print_info "请先运行 'mvn clean package' 编译项目"
        exit 1
    fi
    
    # 检查配置文件是否存在
    if [ ! -f "$CONF_FILE" ]; then
        print_warn "配置文件不存在: $CONF_FILE"
        print_info "将使用默认配置参数"
        CONF_FILE=""
    fi
    
    print_info "环境检查通过"
}

# 构建spark-submit命令
build_spark_command() {
    local cmd="spark-submit"
    cmd="$cmd --class Main"
    cmd="$cmd --master $MASTER"
    cmd="$cmd --name ServiceHealthMonitor-$MODE"
    
    # 添加配置文件（如果存在）
    if [ -n "$CONF_FILE" ]; then
        cmd="$cmd --properties-file $CONF_FILE"
    fi
    
    # 添加默认配置（如果没有配置文件）
    if [ -z "$CONF_FILE" ]; then
        cmd="$cmd --conf spark.service.health.endpoints=http://11.11.17.174:18082,http://11.11.17.174:18081"
        cmd="$cmd --conf spark.service.health.timeout.seconds=10"
        cmd="$cmd --conf spark.service.health.check.interval.minutes=1"
        cmd="$cmd --conf spark.service.health.expected.content='History Server'"
        cmd="$cmd --conf spark.service.health.pushgateway.url=http://baizepg-hadoop.jd.local"
        cmd="$cmd --conf spark.service.health.pushgateway.service.id=55600fc1-5749-4db3-b5d9-0e39ce9472e2"
    fi
    
    cmd="$cmd $JAR_FILE $MODE"
    echo "$cmd"
}

# 显示使用帮助
show_help() {
    echo "服务健康监控启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [模式]"
    echo ""
    echo "模式选项:"
    echo "  monitor  - 启动定时监控（默认）"
    echo "  once     - 执行单次健康检查"
    echo "  test     - 测试Pushgateway连接"
    echo "  help     - 显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  SPARK_MASTER  - Spark master地址（默认: local[2]）"
    echo "  JAR_FILE      - JAR文件路径（默认: target/ServiceHealthMonitor-1.0-SNAPSHOT.jar）"
    echo "  CONF_FILE     - 配置文件路径（默认: conf/service-health-monitor.conf）"
    echo ""
    echo "示例:"
    echo "  $0 monitor                    # 启动定时监控"
    echo "  $0 once                       # 执行单次检查"
    echo "  $0 test                       # 测试连接"
    echo "  SPARK_MASTER=yarn $0 monitor  # 在YARN上运行"
}

# 主函数
main() {
    print_info "=== 服务健康监控启动脚本 ==="
    
    # 处理帮助命令
    if [ "$MODE" = "help" ] || [ "$MODE" = "-h" ] || [ "$MODE" = "--help" ]; then
        show_help
        exit 0
    fi
    
    # 验证模式参数
    if [[ ! "$MODE" =~ ^(monitor|once|test)$ ]]; then
        print_error "无效的模式: $MODE"
        show_help
        exit 1
    fi
    
    # 检查环境
    check_environment
    
    # 构建并执行命令
    print_info "启动模式: $MODE"
    print_info "Spark Master: $MASTER"
    print_info "JAR文件: $JAR_FILE"
    if [ -n "$CONF_FILE" ]; then
        print_info "配置文件: $CONF_FILE"
    fi
    
    local spark_cmd=$(build_spark_command)
    print_info "执行命令: $spark_cmd"
    print_info "=== 开始执行 ==="
    
    # 执行spark-submit命令
    eval "$spark_cmd"
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_info "程序执行完成"
    else
        print_error "程序执行失败，退出码: $exit_code"
    fi
    
    exit $exit_code
}

# 执行主函数
main "$@"
