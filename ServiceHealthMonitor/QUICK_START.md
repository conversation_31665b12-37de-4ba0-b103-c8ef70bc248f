# 服务健康监控模块 - 快速开始指南

## 🚀 快速启动

### 1. 编译项目
```bash
cd ServiceHealthMonitor
mvn clean package
```

### 2. 运行模式

#### 单次健康检查（推荐先测试）
```bash
./bin/start-monitor.sh once
```

#### 测试Pushgateway连接
```bash
./bin/start-monitor.sh test
```

#### 启动定时监控
```bash
./bin/start-monitor.sh monitor
```

## 📊 监控指标说明

### 指标名称
- `service_health_status`: 服务健康状态（0=健康，1=异常）
- `service_response_time_ms`: 服务响应时间（毫秒）

### 标签信息
- `endpoint`: 完整的服务端点URL
- `host`: 服务主机地址
- `port`: 服务端口号
- `service_type`: 服务类型（固定为"history_server"）

## ⚙️ 配置自定义

### 方式1：修改配置文件
编辑 `conf/service-health-monitor.conf` 文件：
```properties
# 监控的服务端点（逗号分隔）
spark.service.health.endpoints=http://your-server1:8080,http://your-server2:8080

# 超时时间（秒）
spark.service.health.timeout.seconds=15

# 检查间隔（分钟）
spark.service.health.check.interval.minutes=2

# 期望的响应内容
spark.service.health.expected.content=Your Expected Content

# Pushgateway配置
spark.service.health.pushgateway.url=http://your-pushgateway:9091
spark.service.health.pushgateway.service.id=your-service-id
```

### 方式2：使用环境变量
```bash
# 自定义Spark Master
SPARK_MASTER=yarn ./bin/start-monitor.sh monitor

# 使用自定义JAR文件
JAR_FILE=/path/to/your/jar ./bin/start-monitor.sh once

# 使用自定义配置文件
CONF_FILE=/path/to/your/config ./bin/start-monitor.sh monitor
```

### 方式3：直接使用spark-submit
```bash
spark-submit \
  --class Main \
  --master local[2] \
  --conf spark.service.health.endpoints="http://server1:8080,http://server2:8080" \
  --conf spark.service.health.timeout.seconds=15 \
  --conf spark.service.health.check.interval.minutes=2 \
  target/ServiceHealthMonitor-1.0-SNAPSHOT.jar once
```

## 📝 推送数据格式示例

推送到Pushgateway的JSON数据格式：
```json
[
  {
    "metric": "service_health_status",
    "timestamp": 1586490128123,
    "value": 0,
    "tags": {
      "endpoint": "http://************:18082",
      "host": "************",
      "port": "18082",
      "service_type": "history_server"
    }
  },
  {
    "metric": "service_response_time_ms",
    "timestamp": 1586490128123,
    "value": 245,
    "tags": {
      "endpoint": "http://************:18082",
      "host": "************",
      "port": "18082",
      "service_type": "history_server"
    }
  }
]
```

## 🔧 故障排查

### 常见问题

1. **编译失败**
   - 确保Java 8+和Maven已安装
   - 检查网络连接，确保能访问Maven仓库

2. **HTTP连接超时**
   - 检查服务端点URL是否正确
   - 增加超时时间配置
   - 确认网络连通性

3. **Pushgateway推送失败**
   - 验证Pushgateway地址是否正确
   - 检查服务ID是否有效
   - 确认网络连接正常

4. **响应内容检查失败**
   - 确认期望内容字符串正确
   - 手动访问服务端点检查响应内容
   - 查看详细日志了解具体错误

### 日志级别
- **INFO**: 正常监控活动和结果
- **WARN**: 服务健康检查失败警告
- **ERROR**: 系统异常和推送失败错误

## 📈 监控建议

1. **生产环境部署**
   - 建议使用YARN或Kubernetes部署
   - 配置适当的资源限制
   - 设置监控告警

2. **性能优化**
   - 根据服务数量调整并发度
   - 合理设置检查间隔
   - 监控程序自身的资源使用

3. **高可用性**
   - 部署多个监控实例
   - 配置故障转移机制
   - 定期检查监控程序状态

## 🎯 下一步

1. 根据实际需求调整配置参数
2. 在测试环境验证监控效果
3. 配置Pushgateway的可视化面板
4. 设置基于监控数据的告警规则
