package com.jd

import org.apache.spark.sql.SparkSession

/**
export SPARK_CONF_DIR=/home/<USER>/bdp_jmart_dapb_union.bdp_jmart_dapb_union_formal/spark_conf
export SPARK_HOME=${SPARK_HOME}_3.0
export DATALAKE_ENABLED=true

nohup spark-submit \
--master yarn --class com.jd.HdfsToIceberg \
--queue root.bdp_jdw_dd_edw_bdp_spark \
--conf spark.executor.memory=24g \
--conf spark.datalake.enabled=true \
--conf spark.sql.storeAssignmentPolicy=ANSI \
--conf spark.speculation=true \
HdfsToIceberg-1.0-SNAPSHOT.jar &> toIceberg.log &
 */
object HdfsToIceberg {
  def main(args: Array[String]): Unit = {

    val loadPath = if (args != null && args.length > 0) args(0) else "hdfs://ns17/tmp/wuguoxiao_jpeg/train/train/*/*.JPEG"
    val tableName = if (args != null && args.length > 2) args(1) else "dev.spark_team_wgx_110w_jpeg_samples_04"

    val spark = SparkSession.builder()
      .appName("HDFS_TO_ICEBERG")
      .enableHiveSupport()
      .getOrCreate()

    spark.sql(s"drop table if exists ${tableName}")
    spark.read.format("binaryFile").load(loadPath)
      .repartition(1000)
      .write.format("iceberg")
      .mode("overwrite")
      .saveAsTable(tableName)

    spark.close()
  }
}
