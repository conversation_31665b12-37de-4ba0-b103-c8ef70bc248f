#/bin/bash
set -x
set -e

source "$(dirname "$0")"/printHostEnv.sh

if [ ! -z "${NOTE}" ]; then
    if [[ "${NOTE}" =~ "JCPT_206_68" ]]; then
        JCPT_206_68="True"
    elif [[ "${NOTE}" =~ "JCPT_53_164" ]]; then
        JCPT_53_164="True"
    elif [[ "${NOTE}" =~ "MIGRATE_60_130" ]]; then
        MIGRATE_60_130="True"
    elif [[ "${NOTE}" =~ "deploy all nodes" ]]; then
        JCPT_206_68="True"
        JCPT_53_164="True"
        MIGRATE_60_130="True"
    fi
fi

if [ ! -z "${REMOTE_DEBUG}" ]; then
    REMOTE_DEBUG=" && ${REMOTE_DEBUG} "
fi

sh SparkAPM/CICD/UploadLocalFileToHDFS.sh
sh SparkAPM/CICD/HdfsToIceberg.sh

cd Data-GC

mvn clean package

dataGCDir=`pwd`

ls -l

cd ..

cd SparkAPM/TroubleshootingPlatform

curl "http://storage.jd.local/moneta-private/authenticate.yaml?Expires=3786756008&AccessKey=9IwSbgaxVScf6KDk&Signature=GNOYW9NnYQINAKxqqqpM%2BaE4q0o%3D" \
    -o src/main/resources/authenticate.yaml

mvn dependency:tree
mvn clean package -DskipTests

if [ "${EXECUTE_TESTS}" = "True" ]; then
    export APP_DOMAIN=jcpt.jd.com
    mvn test
fi

cd target/
mv SparkTroubleshooting ROOT
tar -cf ROOT.tar.gz ROOT/*

(
cat <<EOF
#!/bin/sh
set -x
set -e

export JDHXXXXX_USER=dd_edw
export JDHXXXXX_CLUSTER_NAME=10k
export JDHXXXXX_QUEUE=bdp_jdw_dd_edw_union.bdp_jdw_dd_edw_rir
source /software/servers/env/env.sh

export JRE_HOME=/software/servers/jdk1.8.0_121/
export PATH=\$JAVA_HOME/bin:\$PATH

\rm -rf webapps/*
\rm -rf logs/*
\rm -rf work/Catalina/localhost/*

pids=\`jps | grep Bootstrap | awk '{print \$1}'\`

for pid in \$pids
do
    kill -9 \$pid
done
EOF
) > shutdownTomcat.sh

(
cat <<EOF
#!/bin/sh
set -x
set -e

export JRE_HOME=/software/servers/jdk1.8.0_121/
export PATH=$JAVA_HOME/bin:$PATH

mv ROOT.tar.gz webapps
cd webapps
tar -xf ROOT.tar.gz
cd ..

./bin/startup.sh
EOF
) > startTomcat.sh

if [ "${JCPT_206_68}" = "True" ]; then
    scp -o "StrictHostKeyChecking no" shutdownTomcat.sh root@*************:/root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47/
    scp -o "StrictHostKeyChecking no" startTomcat.sh root@*************:/root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47/
    scp -o "StrictHostKeyChecking no" ROOT.tar.gz root@*************:/root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47/
    sleep 1
    ssh -o "StrictHostKeyChecking no" root@************* "cd /root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47 && sh shutdownTomcat.sh"
    sleep 5
    ssh -o "StrictHostKeyChecking no" root@************* "cd /root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47/ ${REMOTE_DEBUG} && export SCHEDULER_MIGRATE_ENABLED=false; export SCHEDULER_XBP_ENABLED=true && export APP_DOMAIN=jcpt.jd.com && sh startTomcat.sh"
fi

if [ "${JCPT_53_164}" = "True" ]; then
    scp -o "StrictHostKeyChecking no" shutdownTomcat.sh root@*************:/root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47/
    scp -o "StrictHostKeyChecking no" startTomcat.sh root@*************:/root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47/
    scp -o "StrictHostKeyChecking no" ROOT.tar.gz root@*************:/root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47/
    sleep 1
    ssh -o "StrictHostKeyChecking no" root@************* "cd /root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47 && sh shutdownTomcat.sh"
    sleep 5
    ssh -o "StrictHostKeyChecking no" root@************* "cd /root/wuguoxiao/wuguoxiao/apache-tomcat-8.5.47/ ${REMOTE_DEBUG} && export SCHEDULER_MIGRATE_ENABLED=false; export SCHEDULER_XBP_ENABLED=false && export APP_DOMAIN=test.jcpt.jd.com && sh startTomcat.sh"
fi

if [ "${MIGRATE_60_130}" = "True" ]; then
    scp -o "StrictHostKeyChecking no" shutdownTomcat.sh root@*************:/root/tomcat-8/
    scp -o "StrictHostKeyChecking no" startTomcat.sh root@*************:/root/tomcat-8/
    scp -o "StrictHostKeyChecking no" ROOT.tar.gz root@*************:/root/tomcat-8/
    sleep 1
    ssh -o "StrictHostKeyChecking no" root@************* "cd /root/tomcat-8/ && sh shutdownTomcat.sh"
    sleep 10
    ssh -o "StrictHostKeyChecking no" root@************* "cd /root/tomcat-8/ ${REMOTE_DEBUG} && export SCHEDULER_MIGRATE_ENABLED=true; export SCHEDULER_XBP_ENABLED=false && export APP_DOMAIN=yfb.jcpt.jd.com && sh startTomcat.sh"
fi

