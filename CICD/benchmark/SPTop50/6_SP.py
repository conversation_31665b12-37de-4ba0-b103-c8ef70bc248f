#!/usr/bin/env python3

# buffaloID: 507288

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = zst.stat_ct
stat_begin = zst.stat_begin
lnk_rlat_day_str = zst.lnk_rlat_day_str

dest_table_name = 'benchmark_app_zs_z0504_itm_tval'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
SELECT
  t2.shop_typ_cd AS shop_typ_cd, --商家类型
  t2.itm_scnd_catg_cd AS itm_scnd_catg_cd, --商品二级分类代码
  t2.itm_thrd_catg_cd AS itm_thrd_catg_cd, --商品三级分类代码
  t2.chan_cd AS chan_cd, --渠道编号
  """ + stat_ct + """ AS stat_ct,
  """ + zst.data_day_int + """ AS stat_ct_stamp,
  t2.itm_id AS itm_id, --商品id
  t2.itm_nm AS itm_nm, --商品名称
  t2.shop_id AS shop_id, --店铺编号
  t2.ord_amt AS ord_amt, --成交金额
  t2.uv AS uv, --访客数
  t2.follow_usr_qty AS follow_usr_qty,
  t2.srch_clck_num AS srch_clck_num, --搜索点击量
  ROW_NUMBER() OVER(PARTITION BY t2.shop_typ_cd, t2.itm_thrd_catg_cd, t2.chan_cd, t2.itm_scnd_catg_cd ORDER BY ord_amt DESC,t2.itm_id DESC) AS ord_amt_rank, --成交金额排名
  ROW_NUMBER() OVER(PARTITION BY t2.shop_typ_cd, t2.itm_thrd_catg_cd, t2.chan_cd, t2.itm_scnd_catg_cd ORDER BY uv DESC,t2.itm_id DESC) AS uv_rank, --访客数排名
  t2.ord_qty AS ord_qty, --成交单量
  t2.uv2ord AS uv2ord, -- 成交转化率
  ROW_NUMBER() OVER(PARTITION BY t2.shop_typ_cd, t2.itm_thrd_catg_cd, t2.chan_cd, t2.if_n, t2.itm_scnd_catg_cd ORDER BY ord_amt DESC,t2.itm_id DESC) AS new_ord_amt_rank, --新品成交金额排名 （排序加上itm_id是为了避免数据倾斜，对最终排序结果无影响）
  ROW_NUMBER() OVER(PARTITION BY t2.shop_typ_cd, t2.itm_thrd_catg_cd, t2.chan_cd, t2.if_n, t2.itm_scnd_catg_cd ORDER BY uv DESC,t2.itm_id DESC) AS uv_rank, --新品访客数排名
  if_n AS is_new_pro,  --是否新品
  uv_rate
FROM
  (
    SELECT
      t.shop_typ_cd AS shop_typ_cd, --商家类型
      t.itm_frst_catg_cd AS itm_frst_catg_cd, --商品一级分类代码
      cast(t.itm_scnd_catg_cd as string) AS itm_scnd_catg_cd, --商品二级分类代码
      t.itm_thrd_catg_cd AS itm_thrd_catg_cd, --商品三级分类代码
      t.chan_cd AS chan_cd, --渠道编号
      t.shop_id AS shop_id, --店铺编号
      t.itm_id AS itm_id, --商品id
      t.itm_nm AS itm_nm, --商品名称
      t.ord_amt AS ord_amt, --订单金额
      t.ord_qty AS ord_qty, --订单量
      t.uv AS uv, --访客数
      t.follow_usr_qty AS follow_usr_qty, --商品关注人数
      t.srch_clck_num AS srch_clck_num, --搜索点击量
--      t1.std_ord_amt AS std_ord_amt, --28天标准差-订单金额
--      t1.avg_ord_amt AS avg_ord_amt, --28天平均值-订单金额
--      t1.std_ord_qty AS std_ord_qty, --28天标准差-订单量
--      t1.avg_ord_qty AS avg_ord_qty, --28天平均值-订单量
--      t1.std_uv AS std_uv, --28天标准差--访客数
--      t1.avg_uv AS avg_uv, --28天平均值--访客数
--      t1.std_srch_clck_num AS std_srch_clck_num, --28天标准差-搜索点击量
--      t1.avg_srch_clck_num AS avg_srch_clck_num, --28天平均值-搜索点击量
--      CAST(GETINDEX(t.ord_amt, t1.std_ord_amt, t1.avg_ord_amt) AS DOUBLE) AS index_ord_amt,
--      CAST(GETINDEX(t.ord_qty, t1.std_ord_qty, t1.avg_ord_qty) AS DOUBLE) AS index_ord_qty,
--      CAST(GETINDEX(t.uv, t1.std_uv, t1.avg_uv) AS double) AS index_uv,
--      CAST(GETINDEX(t.srch_clck_num, t1.std_srch_clck_num, t1.avg_srch_clck_num) AS DOUBLE) AS index_srch_clck_num,
--      CAST(GETINDEX(t.uv2ord, t1.std_uv2ord, t1.avg_uv2ord) AS DOUBLE) AS index_uv2ord, -- 成交转化率指数
      CASE WHEN xt.item_id IS NULL THEN 0 ELSE 1 END AS if_n,
      t.uv/uv_all.uv as uv_rate,
      t.uv2ord
    FROM
      (
        SELECT
          shop_typ_cd,
          itm_frst_catg_cd,
          itm_scnd_catg_cd,
          itm_thrd_catg_cd,
          chan_cd,
          shop_id,
          itm_id,
          itm_nm,
          ord_amt,
          ord_qty,
          uv,
          follow_usr_qty,
          srch_clck_num,
          ord_cust_num,
          uv2ord
        FROM
          retail_benchmark_10t.benchmark_adm_s14_zs_itm_list_base
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND stat_ct_cd = '""" + stat_ct_cd + """'
          AND shop_id not in ('10278978')   --剔除店铺
      ) t
    left join
      ( select
          shop_typ_cd,
          shop_id,
          itm_frst_catg_cd,
          itm_scnd_catg_cd,
          itm_thrd_catg_cd,
          chan_cd,
          itm_id,
          uv
        from retail_benchmark_10t.benchmark_adm_s14_zs_itm_list_base
        where dt = '""" + zst.data_day_str + """'
          and stat_ct_cd = '""" + stat_ct_cd + """'
          and chan_cd=99
      ) uv_all
    on t.shop_typ_cd=uv_all.shop_typ_cd
    and t.shop_id=uv_all.shop_id
    and t.itm_frst_catg_cd=uv_all.itm_frst_catg_cd
    and t.itm_scnd_catg_cd=uv_all.itm_scnd_catg_cd
    and t.itm_thrd_catg_cd=uv_all.itm_thrd_catg_cd
    and t.itm_id=uv_all.itm_id
    
    LEFT OUTER JOIN
      (
        SELECT
          item_id
        FROM
          retail_benchmark_10t.benchmark_gdm_m03_item_sku_fea_info_da
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND cloth_sign_new_lst_dt >= '""" + stat_begin + """'
          AND cloth_sign_new_lst_dt <= '""" + zst.data_day_str + """'
        GROUP BY
          item_id
      )
      xt
    ON
      t.itm_id = xt.item_id
  )
  t2
  distribute by itm_id,shop_typ_cd,itm_scnd_catg_cd,itm_thrd_catg_cd,chan_cd
  ;

"""
zst.exec_sql(schema_name="retail_benchmark_10t", table_name=dest_table_name, sql=sql, lzo_compress=False,
             lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             exec_engine='spark', retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
