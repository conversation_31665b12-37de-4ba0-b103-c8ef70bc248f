#!/usr/bin/env python3

# buffaloID: 503061

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

print(stat_ct_cd)
print(stat_ct)
print(stat_begin)

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;
INSERT OVERWRITE TABLE retail_benchmark_10t.benchmark_adm_m04_zs_ord_list_mid_new PARTITION (dt='""" + insertDay + """')
SELECT t.shop_typ_cd      AS shop_typ_cd,
       t.itm_frst_catg_cd AS itm_frst_catg_cd,
       t.itm_scnd_catg_cd AS itm_scnd_catg_cd,
       t.itm_thrd_catg_cd AS itm_thrd_catg_cd,
       t.chan_cd          AS chan_cd,
       t.shop_id          AS shop_id,
       t.brand_cd         AS brand_cd,
       t.itm_id           AS itm_id,
       t2.itm_nm          AS itm_nm
       ,t2.main_brand_cd       --主品牌ID
       ,t2.main_brand_nm       --主品牌全名称
       ,t2.itm_typ             --商品型号
       ,t2.itm_num,             --商品货号
       t.par_sale_ord_id  AS par_sale_ord_id,
       t.sale_ord_id      AS sale_ord_id,
       t.sale_qty         AS sale_qty,
       t.ord_amt          AS ord_amt,
       t.usr_log_acct     AS usr_log_acct
  FROM (SELECT /*+mapjoin(t3)*/CASE
                 WHEN t1.shop_typ = 'POP' THEN
                  0
                 ELSE
                  1
               END AS shop_typ_cd,
               t1.itm_frst_catg_cd AS itm_frst_catg_cd,
               t1.itm_scnd_catg_cd AS itm_scnd_catg_cd,
               t1.itm_thrd_catg_cd AS itm_thrd_catg_cd,
               t1.chan_cd AS chan_cd,
               t1.shop_id AS shop_id,
               t1.brand_cd AS brand_cd,
               t1.itm_id AS itm_id,
               t1.par_sale_ord_id AS par_sale_ord_id,
               t1.sale_ord_id as sale_ord_id,
               SUM(t1.sale_qty) AS sale_qty,
               SUM(t1.ord_amt) AS ord_amt,
               t1.usr_log_acct AS usr_log_acct
          FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det t1
         INNER JOIN (SELECT *
                      FROM retail_benchmark_10t.benchmark_dim_item_gen_third_cate
                     WHERE valid_flag = '1'             -- and otc_utc_flag = 1
                      and item_gen_third_cate_id not in ('13235', '13237', '13236', '13238')
                       ) t3
            ON t1.itm_frst_catg_cd = t3.item_gen_first_cate_id
           AND t1.itm_scnd_catg_cd = t3.item_gen_second_cate_id
           AND t1.itm_thrd_catg_cd = t3.item_gen_third_cate_id
         WHERE t1.dt = '""" + zst.data_day_str + """'
           AND t1.shop_id > 0
           AND t1.itm_frst_catg_cd > 0
           AND t1.chan_cd <> 10
           AND t1.is_deal_ord = 1
           AND NOT (itm_frst_catg_cd = 737 and substring(ord_flg,70,1) = '6')
           AND NOT (itm_scnd_catg_cd = 653 and substring(ord_flg,70,1) = '6')  ---屏蔽通讯分销业务数据
         GROUP BY CASE
                    WHEN t1.shop_typ = 'POP' THEN
                     0
                    ELSE
                     1
                  END,
                  t1.itm_frst_catg_cd,
                  t1.itm_scnd_catg_cd,
                  t1.itm_thrd_catg_cd,
                  t1.chan_cd,
                  t1.shop_id,
                  t1.brand_cd,
                  t1.itm_id,
                  t1.par_sale_ord_id,
                  t1.sale_ord_id,
                  t1.usr_log_acct) t
  LEFT OUTER JOIN (SELECT  itm_id AS itm_id    --SPU商品编号
                          ,itm_nm AS itm_nm    --SPU商品名称
                          ,main_brand_nm       --主品牌全名称
                          ,main_brand_cd       --主品牌ID
                          ,itm_typ             --商品型号
                          ,itm_num             --商品货号
                     FROM retail_benchmark_10t.benchmark_adm_m03_zs_spu_info_new
                    where dt = '""" + zst.data_day_str + """') t2
    ON t.itm_id = t2.itm_id
;

"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_adm_m04_zs_ord_list_mid_new', sql=sql,
             exec_engine='spark', retry_with_hive=False, spark_args=["--conf spark.sql.adaptive.enabled=true",
                                                                     "--conf spark.sql.hive.mergeFiles=true",
                                                                     "--conf spark.sql.adaptive.repartition.enabled=true"])
