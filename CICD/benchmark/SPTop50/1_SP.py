#!/usr/bin/env python3

# buffaloID: 371957

import datetime
from ZSTask import ZSTask

zst = ZSTask()
stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

insertDay = datetime.date.today().strftime("%Y-%m-%d")

dest_table_name = 'benchmark_adm_m14_zs_mobile_shop_traffics'

sql = """
USE retail_benchmark_10t;
INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """',shop_typ )
select
  gdm_mobile.sessn_id, -----会话编号
  gdm_mobile.seq_num, -----访问序号
  gdm_mobile.req_tm, -----请求时间
  gdm_mobile.chan_cd, -----渠道代码
  gdm_mobile.shop_id, -----店铺编号
  gdm_mobile.shop_typ_cd, -----店铺类型代码
  gdm_mobile.itm_sku_id, -----SKU商品编号
  gdm_mobile.itm_sku_nm, -----SKU商品名称
  gdm_mobile.itm_id, -----SPU商品编号
  gdm_mobile.itm_nm, -----SPU商品名称
  gdm_mobile.brand_cd, -----品牌代码
  gdm_mobile.itm_frst_catg_cd, -----商品一级分类代码
  gdm_mobile.itm_scnd_catg_cd, -----商品二级分类代码
  gdm_mobile.itm_thrd_catg_cd, -----商品三级分类代码
  gdm_mobile.brws_uniq_id, -----浏览器唯一编号
  gdm_mobile.usr_log_acct, -----用户账号
  gdm_mobile.usr_vst_ip, -----用户访问IP
  gdm_mobile.url_typ_cd, -----店铺页面类型代码
  gdm_mobile.url_attr_val, -----页面属性值
  gdm_mobile.url, -----页面链接
  gdm_mobile.req_parm, -----页面请求参数
  gdm_mobile.zs_url_thrd_catg_cd, -----智商页面三级分类代码
  gdm_mobile.tm_on_page, -----单线程方式页面访问停留时长
  gdm_mobile.ref_req_tm, -----上一页请求时间
  gdm_mobile.ref_seq_num, -----上一页访问序号
  gdm_mobile.ref_shop_id, -----上一页店铺编号
  gdm_mobile.ref_itm_sku_id, -----上一页SKU商品编号
  gdm_mobile.ref_itm_sku_nm, -----上一页SKU商品名称
  gdm_mobile.ref_itm_id, -----上一页SPU商品编号
  gdm_mobile.ref_itm_nm, -----上一页SPU商品名称
  gdm_mobile.ref_brand_cd, -----上一页品牌代码
  gdm_mobile.ref_itm_frst_catg_cd, -----上一页商品一级分类代码
  gdm_mobile.ref_itm_scnd_catg_cd, -----上一页商品二级分类代码
  gdm_mobile.ref_itm_thrd_catg_cd, -----上一页商品三级分类代码
  gdm_mobile.ref_url, -----上一页页面链接
  gdm_mobile.ref_req_parm, -----上一页页面请求参数
  gdm_mobile.ref_zs_url_frst_catg_cd, ----上一页智商页面一级分类代码
  gdm_mobile.ref_zs_url_scnd_catg_cd, ----上一页智商页面二级分类代码
  gdm_mobile.ref_zs_url_thrd_catg_cd, ----上一页智商页面三级分类代码
  gdm_mobile.nxt_req_tm, -----下一页请求时间
  gdm_mobile.nxt_seq_num, -----下一页访问序号
  gdm_mobile.nxt_shop_id, -----下一页店铺编号
  gdm_mobile.nxt_itm_sku_id, -----下一页SKU商品编号
  gdm_mobile.nxt_itm_sku_nm, -----下一页SKU商品名称
  gdm_mobile.nxt_itm_id, -----下一页SPU商品编号
  gdm_mobile.nxt_itm_nm, -----下一页SPU商品名称
  gdm_mobile.nxt_brand_cd, -----下一页品牌代码
  gdm_mobile.nxt_itm_frst_catg_cd, -----下一页商品一级分类代码
  gdm_mobile.nxt_itm_scnd_catg_cd, -----下一页商品二级分类代码
  gdm_mobile.nxt_itm_thrd_catg_cd, -----下一页商品三级分类代码
  gdm_mobile.nxt_url, -----下一页页面链接
  gdm_mobile.nxt_req_parm, -----下一页页面请求参数
  gdm_mobile.nxt_zs_url_frst_catg_cd, ----下一页智商页面一级分类代码
  gdm_mobile.nxt_zs_url_scnd_catg_cd, ----下一页智商页面二级分类代码
  gdm_mobile.nxt_zs_url_thrd_catg_cd, ----下一页智商页面三级分类代码
  gdm_mobile.app_ver, -----应用版本号
  gdm_mobile.os, -----操作系统版本号
  gdm_mobile.log_id, -----日志编号
  gdm_mobile.url_app, -----移动APP应用程序对应URL
  gdm_mobile.shop_sessn_id, -----店铺会话编号
  gdm_mobile.shop_frst_req_flg, -----店铺会话中的首次请求标志
  gdm_mobile.shop_lst_req_flg, -----店铺会话中的最后请求标志
  gdm_mobile.shop_sessn_src_url, -----店铺会话来源页面链接
  gdm_mobile.shop_sessn_src_req_parm, -----店铺会话来源页面参数
  gdm_mobile.shop_sessn_src_zs_url_frst_catg_cd, ------店铺会话来源智商页面一级分类代码
  gdm_mobile.shop_sessn_src_zs_url_scnd_catg_cd, ------店铺会话来源智商页面二级分类代码
  gdm_mobile.shop_sessn_src_zs_url_thrd_catg_cd, ------店铺会话来源智商页面三级分类代码
  gdm_mobile.shop_sessn_par_seq_num, -----店铺会话父访问序号
  gdm_mobile.idfa, ----IOS广告标示符
  gdm_mobile.imei, ----手机IMEI
  gdm_mobile.jda, ----广告部jda
  gdm_mobile.open_id, ----微信手Q的用户标识
  gdm_mobile.ad_uuid, ----广告跳微信手Q专用uuid
  gdm_mobile.report_time,
  gdm_mobile.chan_type, -----微信渠道chan_type:1微信、2手Q、3浏览器、5微信小程序
  gdm_mobile.whitelist_flag, -----1是域名白名单，0是域名黑名单
  gdm_mobile.blacklist_flag as blacklist_flag, ----剔刷黑名单：1垃圾流量，0是正常流量
  gdm_mobile.shop_typ as shop_typ
from
  (
    select
      gdm.sessn_id, -----会话编号
      gdm.seq_num, -----访问序号
      gdm.req_tm, -----请求时间
      gdm.chan_cd, -----渠道代码
      gdm.shop_id, -----店铺编号
      gdm.shop_typ_cd, -----店铺类型代码
      gdm.itm_sku_id, -----SKU商品编号
      gdm.itm_sku_nm, -----SKU商品名称
      gdm.itm_id, -----SPU商品编号
      gdm.itm_nm, -----SPU商品名称
      gdm.brand_cd, -----品牌代码
      gdm.itm_frst_catg_cd, -----商品一级分类代码
      gdm.itm_scnd_catg_cd, -----商品二级分类代码
      gdm.itm_thrd_catg_cd, -----商品三级分类代码
      gdm.brws_uniq_id, -----浏览器唯一编号
      gdm.usr_log_acct, -----用户账号
      gdm.usr_vst_ip, -----用户访问IP
      gdm.url_typ_cd, -----店铺页面类型代码
      gdm.url_attr_val, -----页面属性值
      gdm.url, -----页面链接
      gdm.req_parm, -----页面请求参数
      gdm.zs_url_thrd_catg_cd, -----智商页面三级分类代码
      gdm.tm_on_page, -----单线程方式页面访问停留时长
      gdm.ref_req_tm, -----上一页请求时间
      gdm.ref_seq_num, -----上一页访问序号
      gdm.ref_shop_id, -----上一页店铺编号
      gdm.ref_itm_sku_id, -----上一页SKU商品编号
      gdm.ref_itm_sku_nm, -----上一页SKU商品名称
      gdm.ref_itm_id, -----上一页SPU商品编号
      gdm.ref_itm_nm, -----上一页SPU商品名称
      gdm.ref_brand_cd, -----上一页品牌代码
      gdm.ref_itm_frst_catg_cd, -----上一页商品一级分类代码
      gdm.ref_itm_scnd_catg_cd, -----上一页商品二级分类代码
      gdm.ref_itm_thrd_catg_cd, -----上一页商品三级分类代码
      gdm.ref_url, -----上一页页面链接
      gdm.ref_req_parm, -----上一页页面请求参数
      gdm.ref_zs_url_frst_catg_cd, ----上一页智商页面一级分类代码
      gdm.ref_zs_url_scnd_catg_cd, ----上一页智商页面二级分类代码
      gdm.ref_zs_url_thrd_catg_cd, ----上一页智商页面三级分类代码
      gdm.nxt_req_tm, -----下一页请求时间
      gdm.nxt_seq_num, -----下一页访问序号
      gdm.nxt_shop_id, -----下一页店铺编号
      gdm.nxt_itm_sku_id, -----下一页SKU商品编号
      gdm.nxt_itm_sku_nm, -----下一页SKU商品名称
      gdm.nxt_itm_id, -----下一页SPU商品编号
      gdm.nxt_itm_nm, -----下一页SPU商品名称
      gdm.nxt_brand_cd, -----下一页品牌代码
      gdm.nxt_itm_frst_catg_cd, -----下一页商品一级分类代码
      gdm.nxt_itm_scnd_catg_cd, -----下一页商品二级分类代码
      gdm.nxt_itm_thrd_catg_cd, -----下一页商品三级分类代码
      gdm.nxt_url, -----下一页页面链接
      gdm.nxt_req_parm, -----下一页页面请求参数
      gdm.nxt_zs_url_frst_catg_cd, ----下一页智商页面一级分类代码
      gdm.nxt_zs_url_scnd_catg_cd, ----下一页智商页面二级分类代码
      gdm.nxt_zs_url_thrd_catg_cd, ----下一页智商页面三级分类代码
      gdm.app_ver, -----应用版本号
      gdm.os, -----操作系统版本号
      gdm.log_id, -----日志编号
      gdm.url_app, -----移动APP应用程序对应URL
      gdm.shop_sessn_id, -----店铺会话编号
      gdm.shop_frst_req_flg, -----店铺会话中的首次请求标志
      gdm.shop_lst_req_flg, -----店铺会话中的最后请求标志
      gdm.shop_sessn_src_url, -----店铺会话来源页面链接
      gdm.shop_sessn_src_req_parm, -----店铺会话来源页面参数
      gdm.shop_sessn_src_zs_url_frst_catg_cd, ------店铺会话来源智商页面一级分类代码
      gdm.shop_sessn_src_zs_url_scnd_catg_cd, ------店铺会话来源智商页面二级分类代码
      gdm.shop_sessn_src_zs_url_thrd_catg_cd, ------店铺会话来源智商页面三级分类代码
      gdm.shop_sessn_par_seq_num, -----店铺会话父访问序号
      gdm.idfa, ----IOS广告标示符
      gdm.imei, ----手机IMEI
      gdm.jda, ----广告部jda
      gdm.open_id, ----微信手Q的用户标识
      gdm.ad_uuid, ----广告跳微信手Q专用uuid
      gdm.report_time,
      gdm.chan_type, -----微信渠道chan_type:1微信、2手Q、3浏览器、5微信小程序
      coalesce(gdm.whitelist_flag, '0') as whitelist_flag, -----1是域名白名单，0是域名黑名单
      case
        when gdm.chan_cd = 2
          and uuid_cheat.uuid is not null ---APP保留白名单
        then 0
        when gdm.chan_cd = 1
          and uuid_cheat.uuid is null
        then 0
        else 1
      end as blacklist_flag, ----剔刷黑名单：1垃圾流量，0是正常流量
      gdm.shop_typ
    from
      (
        select
          *
        from
          retail_benchmark_10t.benchmark_gdm_m14_zs_mobile_shop_traffics
        where
          dt = '""" + zst.data_day_str + """'
          and chan_cd = 1
          and chan_type = 8
          and os in ('M-M','WEIXIN-M')
      )
      gdm
    left join
      (

        select
          lower(mba_muid) as uuid   --转小写
        from
          retail_benchmark_10t.benchmark_anti_m_tishua_result   --新版m端剔刷表
        where
          dt = '""" + zst.data_day_str + """'
      )
      uuid_cheat
    on
      nvl(lower(gdm.brws_uniq_id),concat('hive-',rand()))= uuid_cheat.uuid  ---防止数据倾斜
    
    union all
    
    select
      sessn_id, -----会话编号
      seq_num, -----访问序号
      req_tm, -----请求时间
      chan_cd, -----渠道代码
      shop_id, -----店铺编号
      shop_typ_cd, -----店铺类型代码
      itm_sku_id, -----SKU商品编号
      itm_sku_nm, -----SKU商品名称
      itm_id, -----SPU商品编号
      itm_nm, -----SPU商品名称
      brand_cd, -----品牌代码
      itm_frst_catg_cd, -----商品一级分类代码
      itm_scnd_catg_cd, -----商品二级分类代码
      itm_thrd_catg_cd, -----商品三级分类代码
      brws_uniq_id, -----浏览器唯一编号
      usr_log_acct, -----用户账号
      usr_vst_ip, -----用户访问IP
      url_typ_cd, -----店铺页面类型代码
      url_attr_val, -----页面属性值
      url, -----页面链接
      req_parm, -----页面请求参数
      zs_url_thrd_catg_cd, -----智商页面三级分类代码
      tm_on_page, -----单线程方式页面访问停留时长
      ref_req_tm, -----上一页请求时间
      ref_seq_num, -----上一页访问序号
      ref_shop_id, -----上一页店铺编号
      ref_itm_sku_id, -----上一页SKU商品编号
      ref_itm_sku_nm, -----上一页SKU商品名称
      ref_itm_id, -----上一页SPU商品编号
      ref_itm_nm, -----上一页SPU商品名称
      ref_brand_cd, -----上一页品牌代码
      ref_itm_frst_catg_cd, -----上一页商品一级分类代码
      ref_itm_scnd_catg_cd, -----上一页商品二级分类代码
      ref_itm_thrd_catg_cd, -----上一页商品三级分类代码
      ref_url, -----上一页页面链接
      ref_req_parm, -----上一页页面请求参数
      ref_zs_url_frst_catg_cd, ----上一页智商页面一级分类代码
      ref_zs_url_scnd_catg_cd, ----上一页智商页面二级分类代码
      ref_zs_url_thrd_catg_cd, ----上一页智商页面三级分类代码
      nxt_req_tm, -----下一页请求时间
      nxt_seq_num, -----下一页访问序号
      nxt_shop_id, -----下一页店铺编号
      nxt_itm_sku_id, -----下一页SKU商品编号
      nxt_itm_sku_nm, -----下一页SKU商品名称
      nxt_itm_id, -----下一页SPU商品编号
      nxt_itm_nm, -----下一页SPU商品名称
      nxt_brand_cd, -----下一页品牌代码
      nxt_itm_frst_catg_cd, -----下一页商品一级分类代码
      nxt_itm_scnd_catg_cd, -----下一页商品二级分类代码
      nxt_itm_thrd_catg_cd, -----下一页商品三级分类代码
      nxt_url, -----下一页页面链接
      nxt_req_parm, -----下一页页面请求参数
      nxt_zs_url_frst_catg_cd, ----下一页智商页面一级分类代码
      nxt_zs_url_scnd_catg_cd, ----下一页智商页面二级分类代码
      nxt_zs_url_thrd_catg_cd, ----下一页智商页面三级分类代码
      app_ver, -----应用版本号
      os, -----操作系统版本号
      log_id, -----日志编号
      url_app, -----移动APP应用程序对应URL
      shop_sessn_id, -----店铺会话编号
      shop_frst_req_flg, -----店铺会话中的首次请求标志
      shop_lst_req_flg, -----店铺会话中的最后请求标志
      shop_sessn_src_url, -----店铺会话来源页面链接
      shop_sessn_src_req_parm, -----店铺会话来源页面参数
      shop_sessn_src_zs_url_frst_catg_cd, ------店铺会话来源智商页面一级分类代码
      shop_sessn_src_zs_url_scnd_catg_cd, ------店铺会话来源智商页面二级分类代码
      shop_sessn_src_zs_url_thrd_catg_cd, ------店铺会话来源智商页面三级分类代码
      shop_sessn_par_seq_num, -----店铺会话父访问序号
      idfa, ----IOS广告标示符
      imei, ----手机IMEI
      jda, ----广告部jda
      open_id, ----微信手Q的用户标识
      ad_uuid, ----广告跳微信手Q专用uuid
      report_time,
      chan_type, -----微信渠道chan_type:1微信、2手Q、3浏览器、5微信小程序
      coalesce(whitelist_flag, '0') as whitelist_flag, -----1是域名白名单，0是域名黑名单
      0 as blacklist_flag, ----剔刷黑名单：1垃圾流量，0是正常流量
      shop_typ
    from
      retail_benchmark_10t.benchmark_gdm_m14_zs_mobile_shop_traffics
    where
      dt = '""" + zst.data_day_str + """'
      and ( chan_cd = 2 or 
                      ( chan_cd in( 3, 4)  --app渠道不提刷，微信手q也不剔刷
      and chan_type in (1,2,5) ----只取微信1 手Q2 微信小程序5
                      ) )
      
  )
  gdm_mobile
    """
zst.exec_sql(schema_name="retail_benchmark_10t", table_name=dest_table_name, sql=sql,
             exec_engine='spark', retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
