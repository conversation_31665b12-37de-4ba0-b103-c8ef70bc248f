#!/usr/bin/env python3

# buffaloID: 516161

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = zst.stat_ct
stat_begin = zst.stat_begin
lnk_rlat_day_str = zst.lnk_rlat_day_str

dest_table_name = 'benchmark_app_zs_z0504_itm_tval_top'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
SELECT
  t.shop_typ_cd as shop_typ_cd, --商家类型
  t.itm_scnd_catg_cd as itm_scnd_catg_cd, --商品二级分类代码
  t.itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级分类代码
  t.chan_cd as chan_cd, --渠道编号
  """ + stat_ct + """ as stat_ct,
  """ + zst.data_day_int + """ as stat_ct_stamp,
  t.sort_type as sort_type, --排序类型 1下单金额2转化率3访客
  t.itm_id as itm_id, --商品id
  t.itm_nm as itm_nm, --商品名称
  t.sort_rn as sort_rn, --排名
  t.shop_id as shop_id, --店铺编号
  t.ord_amt as ord_amt, --成交金额（去指数化）
  t.uv as uv, --访客数（去指数化）
  t.follow_usr_qty as follow_usr_qty, --商品关注人数
  t.srch_clck_num as srch_clck_num, -- 搜索点击次数(去指数化)
  t.ord_qty as ord_qty -- 成交单量
FROM
  (
    SELECT
      shop_typ_cd as shop_typ_cd, --商家类型
      itm_scnd_catg_cd as itm_scnd_catg_cd, --商品二级分类代码
      itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级分类代码
      chan_cd as chan_cd, --渠道编号
      1 as sort_type, --排序类型
      itm_id as itm_id, --商品id
      itm_nm as itm_nm, --商品名称
      ord_amt_rank as sort_rn, --排名
      shop_id as shop_id, --店铺编号
      ord_amt as ord_amt, --下单金额（去指数化）
      uv as uv, --访客数（去指数化）
      follow_usr_qty as follow_usr_qty, --商品关注人数
      srch_clck_num as srch_clck_num, --搜索点击量(去指数化)
      ord_qty as ord_qty -- 下单单量
    FROM
      retail_benchmark_10t.benchmark_app_zs_z0504_itm_tval
    WHERE
      dt = '""" + zst.data_day_str + """'
      AND stat_ct_cd = '""" + stat_ct_cd + """'
      AND ord_amt_rank <= 150
  
    
    UNION ALL
    
    SELECT
      shop_typ_cd as shop_typ_cd, --商家类型
      itm_scnd_catg_cd as itm_scnd_catg_cd, --商品二级分类代码
      itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级分类代码
      chan_cd as chan_cd, --渠道编号
      3 as sort_type, --排序类型
      itm_id as itm_id, --商品id
      itm_nm as itm_nm, --商品名称
      uv_rank as sort_rn, --排名
      shop_id as shop_id, --店铺编号
      ord_amt as ord_amt, --下单金额（去指数化）
      uv as uv, --访客数（去指数化）
      follow_usr_qty as follow_usr_qty, --商品关注人数
      srch_clck_num as srch_clck_num, --搜索点击量(去指数化)
      ord_qty as ord_qty -- 下单单量
    FROM
      retail_benchmark_10t.benchmark_app_zs_z0504_itm_tval
    WHERE
      dt = '""" + zst.data_day_str + """'
      AND stat_ct_cd = '""" + stat_ct_cd + """'
      AND uv_rank <= 150
    
    UNION ALL
    
    SELECT
      shop_typ_cd as shop_typ_cd, --商家类型
      itm_scnd_catg_cd as itm_scnd_catg_cd, --商品二级分类代码
      itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级分类代码
      chan_cd as chan_cd, --渠道编号
      4 as sort_type, --排序类型
      itm_id as itm_id, --商品id
      itm_nm as itm_nm, --商品名称
      new_ord_amt_rank as sort_rn, --排名
      shop_id as shop_id, --店铺编号
      ord_amt as ord_amt, --下单金额（去指数化）
      uv as uv, --访客数（去指数化）
      follow_usr_qty as follow_usr_qty, --商品关注人数
      srch_clck_num as srch_clck_num, --搜索点击量(去指数化)
      ord_qty as ord_qty -- 下单单量
    FROM
      retail_benchmark_10t.benchmark_app_zs_z0504_itm_tval
    WHERE
      dt = '""" + zst.data_day_str + """'
      AND stat_ct_cd = '""" + stat_ct_cd + """'
      AND new_ord_amt_rank <= 150
      AND is_new_pro = 1
    
    UNION ALL
    
    SELECT
      shop_typ_cd as shop_typ_cd, --商家类型
      itm_scnd_catg_cd as itm_scnd_catg_cd, --商品二级分类代码
      itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级分类代码
      chan_cd as chan_cd, --渠道编号
      5 as sort_type, --排序类型
      itm_id as itm_id, --商品id
      itm_nm as itm_nm, --商品名称
      new_uv_rank as sort_rn, --排名
      shop_id as shop_id, --店铺编号
      ord_amt as ord_amt, --下单金额（去指数化）
      uv as uv, --访客数（去指数化）
      follow_usr_qty as follow_usr_qty, --商品关注人数
      srch_clck_num as srch_clck_num, --搜索点击量(去指数化)
      ord_qty as ord_qty -- 下单单量
    FROM
      retail_benchmark_10t.benchmark_app_zs_z0504_itm_tval
    WHERE
      dt = '""" + zst.data_day_str + """'
      AND stat_ct_cd = '""" + stat_ct_cd + """'
      AND new_uv_rank <= 150
      AND is_new_pro = 1
  )
  t ;
"""
zst.exec_sql(schema_name="retail_benchmark_10t", table_name=dest_table_name, sql=sql,
             lzo_compress=False,
             lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             exec_engine='spark', retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
