#!/usr/bin/env python3

# buffaloID: 502262

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;
INSERT OVERWRITE TABLE retail_benchmark_10t.benchmark_adm_m14_zs_traffics_list_mid_new PARTITION (dt='""" + insertDay + """')
SELECT t.shop_typ_cd      AS shop_typ_cd,
       t.itm_frst_catg_cd AS itm_frst_catg_cd,
       t.itm_scnd_catg_cd AS itm_scnd_catg_cd,
       t.itm_thrd_catg_cd AS itm_thrd_catg_cd,
       t.chan_cd          AS chan_cd,
       t.shop_id          AS shop_id,
       t.brand_cd         AS brand_cd,
       t.itm_id           AS itm_id,
       t2.itm_nm          AS itm_nm,
       t.brws_uniq_id     AS brws_uniq_id,
       t.zs_url_thrd_catg_cd  AS zs_url_thrd_catg_cd,
       t.pv               AS pv,
       t.tm_on_page       AS tm_on_page,
       t2.main_brand_cd    AS main_brand_cd,
       t2.main_brand_nm AS  main_brand_nm,
       t2.itm_typ          AS itm_typ,
       t2.itm_num          AS itm_num
  FROM (SELECT /*+mapjoin(t3)*/t5.itm_frst_catg_cd AS itm_frst_catg_cd,
               t5.itm_scnd_catg_cd AS itm_scnd_catg_cd,
               t5.itm_thrd_catg_cd AS itm_thrd_catg_cd,
               t5.chan_cd AS chan_cd,
               t5.shop_id AS shop_id,
               t5.brand_cd AS brand_cd,
               CASE
                 WHEN t5.shop_typ = 'POP' THEN
                  0
                 ELSE
                  1
               END AS shop_typ_cd,
               t5.itm_id AS itm_id,
               count(1) AS pv,
         t5.zs_url_thrd_catg_cd as zs_url_thrd_catg_cd,
               t5.brws_uniq_id AS brws_uniq_id,
               SUM(CASE
                     WHEN t5.tm_on_page > 0 AND t5.tm_on_page < 1800 THEN
                      t5.tm_on_page
                     ELSE
                      0
                   END) AS tm_on_page
          FROM retail_benchmark_10t.benchmark_adm_m14_zs_pc_shop_traffics t5
         INNER JOIN (SELECT *
                      FROM retail_benchmark_10t.benchmark_dim_item_gen_third_cate
                     WHERE valid_flag = '1'             -- and otc_utc_flag = 1
                     and item_gen_third_cate_id not in ('13235', '13237', '13236', '13238')   -- 家电事业部分销类目，作屏蔽处理
                       ) t3
            ON t5.itm_frst_catg_cd = t3.item_gen_first_cate_id
           AND t5.itm_scnd_catg_cd = t3.item_gen_second_cate_id
           AND t5.itm_thrd_catg_cd = t3.item_gen_third_cate_id
         WHERE t5.dt = '""" + zst.data_day_str + """'
           AND t5.shop_id > 0
           AND t5.itm_frst_catg_cd > 0
           AND t5.blacklist_flag = 0 --- 剔刷黑名单：1垃圾流量，0是正常流量 

         GROUP BY t5.itm_frst_catg_cd,
                  t5.itm_scnd_catg_cd,
                  t5.itm_thrd_catg_cd,
                  t5.chan_cd,
                  t5.shop_id,
                  t5.brand_cd,
                  CASE
                    WHEN t5.shop_typ = 'POP' THEN
                     0
                    ELSE
                     1
                  END,
                  t5.itm_id,
                  t5.brws_uniq_id,
      t5.zs_url_thrd_catg_cd
        UNION ALL
        SELECT /*+mapjoin(t3)*/t4.itm_frst_catg_cd AS itm_frst_catg_cd,
               t4.itm_scnd_catg_cd AS itm_scnd_catg_cd,
               t4.itm_thrd_catg_cd AS itm_thrd_catg_cd,
               t4.chan_cd AS chan_cd,
               t4.shop_id AS shop_id,
               t4.brand_cd AS brand_cd,
               CASE
                 WHEN t4.shop_typ = 'POP' THEN
                  0
                 ELSE
                  1
               END AS shop_typ_cd,
               t4.itm_id AS itm_id,
               COUNT(1) AS pv,
               t4.zs_url_thrd_catg_cd as zs_url_thrd_catg_cd,
               t4.brws_uniq_id AS brws_uniq_id,
               SUM(CASE
                     WHEN t4.tm_on_page > 0 AND t4.tm_on_page < 1800 THEN
                      t4.tm_on_page
                     ELSE
                      0
                   END) AS tm_on_page
          FROM retail_benchmark_10t.benchmark_adm_m14_zs_mobile_shop_traffics t4
         INNER JOIN (SELECT *
                      FROM retail_benchmark_10t.benchmark_dim_item_gen_third_cate
                     WHERE valid_flag = '1'           -- and otc_utc_flag = 1
                     and item_gen_third_cate_id not in ('13235', '13237', '13236', '13238')
                       ) t3
            ON t4.itm_frst_catg_cd = t3.item_gen_first_cate_id
           AND t4.itm_scnd_catg_cd = t3.item_gen_second_cate_id
           AND t4.itm_thrd_catg_cd = t3.item_gen_third_cate_id
         WHERE t4.dt = '""" + zst.data_day_str + """'
           AND t4.shop_id > 0
           AND t4.itm_frst_catg_cd > 0
           AND t4.chan_cd <> 10
           AND t4.blacklist_flag = 0 --- 剔刷黑名单：1垃圾流量，0是正常流量
           AND t4.whitelist_flag = 1 -----1是域名白名单，0是域名黑名单

         group by t4.itm_frst_catg_cd,
                  t4.itm_scnd_catg_cd,
                  t4.itm_thrd_catg_cd,
                  t4.chan_cd,
                  t4.shop_id,
                  t4.brand_cd,
                  CASE
                    WHEN t4.shop_typ = 'POP' THEN
                     0
                    ELSE
                     1
                  END,
                  t4.itm_id,
                  t4.brws_uniq_id,
                  t4.zs_url_thrd_catg_cd
                  
        ----------2019年08月19日18:33:20 增加品牌小程序流量 begin ----------
         union all
        
         SELECT /*+mapjoin(t3)*/t6.itm_frst_catg_cd AS itm_frst_catg_cd,
               t6.itm_scnd_catg_cd AS itm_scnd_catg_cd,
               t6.itm_thrd_catg_cd AS itm_thrd_catg_cd,
               t6.chan_cd AS chan_cd,
               t6.shop_id AS shop_id,
               t6.brand_cd AS brand_cd,
               t6.shop_typ_cd,  --0为pop，1为b2c 
               t6.itm_id AS itm_id,
               COUNT(1) AS pv,
               t6.zs_url_thrd_catg_cd as zs_url_thrd_catg_cd,
               t6.brws_uniq_id AS brws_uniq_id,
               SUM(CASE
                     WHEN t6.tm_on_page > 0 AND t6.tm_on_page < 1800 THEN
                      t6.tm_on_page
                     ELSE
                      0
                   END) AS tm_on_page
          FROM retail_benchmark_10t.benchmark_adm_m14_zs_kepler_online_log t6
         INNER JOIN (SELECT *
                      FROM retail_benchmark_10t.benchmark_dim_item_gen_third_cate
                     WHERE valid_flag = '1'           -- and otc_utc_flag = 1
                     and item_gen_third_cate_id not in ('13235', '13237', '13236', '13238')
                       ) t3
            ON t6.itm_frst_catg_cd = t3.item_gen_first_cate_id
           AND t6.itm_scnd_catg_cd = t3.item_gen_second_cate_id
           AND t6.itm_thrd_catg_cd = t3.item_gen_third_cate_id
         WHERE t6.dt = '""" + zst.data_day_str + """'
           AND t6.shop_id > 0
           AND t6.itm_frst_catg_cd > 0
         group by t6.itm_frst_catg_cd,
                  t6.itm_scnd_catg_cd,
                  t6.itm_thrd_catg_cd,
                  t6.chan_cd,
                  t6.shop_id,
                  t6.brand_cd,
                  t6.shop_typ_cd,
                  t6.itm_id,
                  t6.brws_uniq_id,
                  t6.zs_url_thrd_catg_cd
                  
                  
     ---------增加品牌小程序流量 end -------------------  
                           
      ) t
  LEFT OUTER JOIN (SELECT 
                        itm_id AS itm_id, 
                        itm_nm AS itm_nm,
                        main_brand_cd     AS main_brand_cd,
                        main_brand_nm     AS  main_brand_nm,
                        itm_typ,
                        itm_num
                     FROM retail_benchmark_10t.benchmark_adm_m03_zs_spu_info_new
                    where dt = '""" + zst.data_day_str + """') t2
    ON t.itm_id = t2.itm_id

;

"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_adm_m14_zs_traffics_list_mid_new', sql=sql,
             exec_engine='spark', retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
