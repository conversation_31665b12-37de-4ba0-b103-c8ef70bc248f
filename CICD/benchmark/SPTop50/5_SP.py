#!/usr/bin/env python3

# buffaloID: 502847

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

print(stat_ct_cd)
print(stat_ct)
print(stat_begin)

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;
INSERT OVERWRITE TABLE retail_benchmark_10t.benchmark_adm_s14_zs_itm_list_base PARTITION(dt = '""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """')
select 
shop_typ_cd,
itm_frst_catg_cd,
itm_scnd_catg_cd,
itm_thrd_catg_cd,
chan_cd,
shop_id,
itm_id,
itm_nm,
ord_amt,
ord_qty,
uv,
follow_usr_qty,
srch_clck_num,
ord_cust_num,
uv2ord
from
  (select t1.shop_typ_cd as shop_typ_cd, --店铺类型:0-POP ,1-自营
         t1.itm_frst_catg_cd as itm_frst_catg_cd, --商品一级类目编号
         t1.itm_scnd_catg_cd as itm_scnd_catg_cd,
         t1.itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级类目编号
         t1.chan_cd as chan_cd,
         t1.shop_id as shop_id,
      --   t2.shop_nm as shop_nm,
         t1.itm_id as itm_id,
         max(t1.itm_nm) as itm_nm,
         sum(coalesce(t1.ord_amt, 0.0)) as ord_amt, --下单金额
         sum(coalesce(t1.ord_qty, 0)) as ord_qty, --下单量
         sum(coalesce(t1.uv, 0)) as uv,
         sum(coalesce(t1.follow_usr_qty, 0)) as follow_usr_qty, --店铺关注人数
         sum(coalesce(t1.srch_clck_num, 0)) as srch_clck_num, --搜索点击量 
         sum(coalesce(t1.ord_cust_num, 0)) as ord_cust_num,  --下单客户数
     case when sum(coalesce(t1.uv, 0)) > 0 
     then sum(coalesce(t1.ord_cust_num, 0))/sum(coalesce(t1.uv, 0)) else 0 end as uv2ord --成交转化率 
    from (select shop_typ_cd      as shop_typ_cd, --店铺类型:0-POP ,1-自营
                 itm_frst_catg_cd as itm_frst_catg_cd, --商品一级类目编号
                 itm_scnd_catg_cd as itm_scnd_catg_cd,
                 itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级类目编号
                 chan_cd          as chan_cd,
                 shop_id          as shop_id,
                 itm_id           as itm_id,
                 itm_nm           as itm_nm,
                 0.0              as ord_amt, --下单金额
                 0                as ord_qty, --下单量
                 uv               as uv,
                 0                as follow_usr_qty, --店铺关注人数
                 0                as srch_clck_num, --搜索点击量 
                 0                as ord_cust_num --下单客户数
            from retail_benchmark_10t.benchmark_adm_s14_zs_traffics_list_sum_new
           where dt = '""" + zst.data_day_str + """'
             and stat_ct_cd = '""" + stat_ct_cd + """'
             and sort_typ = '2'
             and itm_nm is not null
             and uv>0
             and itm_scnd_catg_cd <> 999999
          union all
          select shop_typ_cd      as shop_typ_cd, --店铺类型:0-POP ,1-自营
                 itm_frst_catg_cd as itm_frst_catg_cd, --商品一级类目编号
                 itm_scnd_catg_cd as itm_scnd_catg_cd,
                 itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级类目编号
                 chan_cd          as chan_cd,
                 shop_id          as shop_id,
                 itm_id           as itm_id,
                 itm_nm           as itm_nm,
                 ord_amt          as ord_amt, --下单金额
                 ord_qty          as ord_qty, --下单量
                 0                as uv,
                 0                as follow_usr_qty, --店铺关注人数
                 0                as srch_clck_num, --搜索点击量
                 ord_cust_num     as ord_cust_num --下单客户数 
            from retail_benchmark_10t.benchmark_adm_s04_zs_shop_ord_list_sum_new
           where dt = '""" + zst.data_day_str + """'
             and stat_ct_cd = '""" + stat_ct_cd + """'
             and sort_typ = '2'
             and itm_nm is not null
             and (ord_amt>0 or ord_qty>0)
             and itm_scnd_catg_cd <> 999999
          union all
          select shop_typ_cd      as shop_typ_cd, --店铺类型:0-POP ,1-自营
                 itm_frst_catg_cd as itm_frst_catg_cd, --商品一级类目编号
                 itm_scnd_catg_cd as itm_scnd_catg_cd,
                 itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级类目编号
                 chan_cd          as chan_cd,
                 shop_id          as shop_id,
                 itm_id           as itm_id,
                 itm_nm           as itm_nm,
                 0.0              as ord_amt, --下单金额
                 0                as ord_qty, --下单量
                 0                as uv,
                 0                as follow_usr_qty, --店铺关注人数
                 srch_clck_num    as srch_clck_num, --搜索点击量
                 0                as ord_cust_num --下单客户数 
            from retail_benchmark_10t.benchmark_adm_s14_zs_kwd_lead_list_sum_new
           where dt = '""" + zst.data_day_str + """'
             and stat_ct_cd = '""" + stat_ct_cd + """'
             and sort_typ = '2'
             and itm_nm is not null
             and srch_clck_num>0
             and itm_scnd_catg_cd <> 999999
          union all
          select shop_typ_cd      as shop_typ_cd, --店铺类型:0-POP ,1-自营
                 itm_frst_catg_cd as itm_frst_catg_cd, --商品一级类目编号
                 itm_scnd_catg_cd as itm_scnd_catg_cd,
                 itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级类目编号
                 chan_cd          as chan_cd,
                 shop_id          as shop_id,
                 itm_id           as itm_id,
                 itm_nm           as itm_nm,
                 0.0              as ord_amt, --下单金额
                 0                as ord_qty, --下单量
                 0                as uv,
                 follow_usr_qty   as follow_usr_qty, --店铺关注人数
                 0                as srch_clck_num, --搜索点击量 
                 0                as ord_cust_num --下单客户数
            from retail_benchmark_10t.benchmark_adm_s13_zs_shop_product_follow_list_sum_new
           where dt = '""" + zst.data_day_str + """'
             and stat_ct_cd = '""" + stat_ct_cd + """'
             and sort_typ = '2'
             and itm_nm is not null
             and follow_usr_qty>0
             and itm_scnd_catg_cd <> 999999
             ) t1
 --  left outer join (SELECT shop_id, MAX(shop_name) AS shop_nm
 --                   FROM retail_benchmark_10t.benchmark_fdm_pop_vender_vender_chain
 --                  WHERE start_date <= '""" + stat_begin + """'
 --                    AND end_date > '""" + zst.data_day_str + """'
 --                    AND shop_id > 0
 --                  GROUP BY shop_id) t2
 --  ON t1.shop_id = t2.shop_id
group by  t1.shop_typ_cd, --店铺类型:0-POP ,1-自营
         t1.itm_frst_catg_cd, --商品一级类目编号
         t1.itm_scnd_catg_cd,
         t1.itm_thrd_catg_cd, --商品三级类目编号
         t1.chan_cd,
         t1.shop_id ,
     --    t2.shop_nm ,
         t1.itm_id) a 
where a.shop_id not in 
(select t3.shop_id as shop_cd from retail_benchmark_10t.benchmark_gdm_m01_vender_da t3 where dt = '""" + zst.data_day_str + """' and dept_id_1 = 1460 and dept_id_3 <> 5563   --过滤新通路商家数据，new  20210402 产品：陈玉兰
union all
  select '799638' as shop_cd)    -- 过滤新通路商家数据         
;

"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_adm_s14_zs_itm_list_base', sql=sql,
             exec_engine='spark', retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
