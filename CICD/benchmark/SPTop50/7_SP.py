#!/usr/bin/env python3

# buffaloID: 503881

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

print(stat_ct_cd)
print(stat_ct)
print(stat_begin)

insertDay=datetime.date.today().strftime("%Y-%m-%d")

dest_table_name = 'benchmark_adm_s13_zs_shop_product_follow_list_sum_new'

sql = """
use retail_benchmark_10t;

from   (
select t1.shop_typ_cd      as shop_typ_cd, --店铺类型:0-POP ,1-自营
       t1.itm_frst_catg_cd as itm_frst_catg_cd, --商品一级类目编号
       t1.itm_scnd_catg_cd as itm_scnd_catg_cd, --商品二级类目编号
       t1.itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级类目编号
       t1.chan_cd          as chan_cd, --渠道类型:0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ ,10-其他 ,99-全渠道
       t1.shop_id          as shop_id, --店铺编号
       t1.brand_cd         as brand_cd, --品牌
       t1.itm_id           as itm_id, --商品编号
       t2.itm_nm           as itm_nm,
       t1.usr_log_acct,
       t1.main_brand_cd,
       t1.main_brand_nm   as main_brand_nm,
       t1.itm_typ_num,
       t1.itm_flag,
       case when t1.itm_nm is  null then 1 else  0 end  as  zengpin  ---赠品标示
  from (SELECT shop_typ_cd as shop_typ_cd, --店铺类型:0-POP ,1-自营
               itm_frst_catg_cd as itm_frst_catg_cd, --商品一级类目编号
               itm_scnd_catg_cd as itm_scnd_catg_cd, --商品二级类目编号
               itm_thrd_catg_cd as itm_thrd_catg_cd, --商品三级类目编号
               chan_cd as chan_cd, --渠道类型:0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ ,10-其他 ,99-全渠道
               shop_id as shop_id, --店铺编号
               brand_cd as brand_cd, --品牌
               itm_id as itm_id, --商品编号
               max(itm_nm) as itm_nm,
               usr_log_acct,
               main_brand_cd,
               max(main_brand_nm)   as main_brand_nm,
               case
                 when itm_typ is not null and itm_typ<>'' and  itm_typ <>'null'  then
                  itm_typ
                 else
                  itm_num
               end as itm_typ_num,
               case when main_brand_cd>0 and main_brand_cd is not null   then
               case
                 when itm_typ is not null and itm_typ<>'' and  itm_typ <>'null'  then
                  1
                 when itm_num is not null and itm_num<>'' and  itm_num <>'null'  then
                  2
               end
                   end itm_flag
          FROM retail_benchmark_10t.benchmark_adm_m04_zs_product_follow_list_mid_new
         WHERE dt <=  '""" + zst.data_day_str + """'
           AND dt >=  '""" + stat_begin + """'
         GROUP BY shop_typ_cd, --店铺类型:0-POP ,1-自营
                  itm_frst_catg_cd, --商品一级类目编号
                  itm_scnd_catg_cd, --商品二级类目编号
                  itm_thrd_catg_cd, --商品三级类目编号
                  chan_cd, --渠道类型:0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ ,10-其他 ,99-全渠道
                  shop_id, --店铺编号
                  brand_cd, --品牌
                  itm_id, --商品编号
                  usr_log_acct,
                  main_brand_cd,
               case
                 when itm_typ is not null and itm_typ<>'' and  itm_typ <>'null'  then
                  itm_typ
                 else
                  itm_num
               end ,
               case when main_brand_cd>0 and main_brand_cd is not null   then
               case
                 when itm_typ is not null and itm_typ<>'' and  itm_typ <>'null'  then
                  1
                 when itm_num is not null and itm_num<>'' and  itm_num <>'null'  then
                  2
               end
                   end) t1
  LEFT OUTER JOIN (SELECT itm_id, max(itm_nm) as itm_nm
                     FROM retail_benchmark_10t.benchmark_adm_th03_merchandise_info
                    WHERE dt = '""" + zst.data_day_str + """'
                      AND shop_id > 0
                    GROUP BY itm_id) t2
    ON (case
         when t1.itm_id is not null and t1.itm_id > 0 then
          cast(t1.itm_id as string)
         else
          cast((-rand() * 100000000) as string)
       end) = cast(t2.itm_id as string)
) t1

 INSERT OVERWRITE TABLE    """ + dest_table_name + """   PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=2,grouping_id=201) --SPU三级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,itm_thrd_catg_cd      as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
          ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                     as    itm_typ_num           --       货号/型号          ,
          ,-2                     as    itm_flag              --       货号/型号的标志
          where zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd, t1.itm_thrd_catg_cd, t1.chan_cd, t1.shop_id, t1.itm_id, t1.itm_nm

 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=2,grouping_id=202) --SPU二级类目
  select   shop_typ_cd            as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
          ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                      as    itm_typ_num           --       货号/型号          ,
          ,-2                      as    itm_flag              --       货号/型号的标志
          where zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd,              t1.chan_cd, t1.shop_id, t1.itm_id, t1.itm_nm

 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=2,grouping_id=203) --spu一级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,999999                     as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                     as    itm_typ_num           --       货号/型号          ,
          ,-2                     as    itm_flag              --       货号/型号的标志
          where zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd,                                  t1.chan_cd, t1.shop_id, t1.itm_id, t1.itm_nm
 --------------------------------------------------------------------------------------------------------------------------------------------
 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=12,grouping_id=1201) --BRAND三级类目
  select   shop_typ_cd            as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,itm_thrd_catg_cd      as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,-2                    as    shop_id               --       店铺编号           ,
           ,brand_cd              as    brand_cd              --       品牌编号           ,
           ,-2                    as    itm_id                --       商品ID             ,
           ,-2                    as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                      as    itm_typ_num           --       货号/型号          ,
          ,-2                      as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd, t1.itm_thrd_catg_cd, t1.chan_cd, t1.brand_cd

 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=12,grouping_id=1202) --BRAND二级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                     as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,-2                    as    shop_id               --       店铺编号           ,
           ,brand_cd              as    brand_cd              --       品牌编号           ,
           ,-2                    as    itm_id                --       商品ID             ,
           ,-2                    as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                      as    itm_typ_num           --       货号/型号          ,
          ,-2                      as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd,                       t1.chan_cd, t1.brand_cd

 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=12,grouping_id=1203) --BRAND一级类目
  select   shop_typ_cd            as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,999999                     as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                     as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,-2                     as    shop_id               --       店铺编号           ,
           ,brand_cd               as    brand_cd              --       品牌编号           ,
           ,-2                    as    itm_id                --       商品ID             ,
           ,-2                    as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                     as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                     as    itm_typ_num           --       货号/型号          ,
          ,-2                     as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd,                                             t1.chan_cd, t1.brand_cd
 --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

  INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=13,grouping_id=1301) --brand_shop三级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,itm_thrd_catg_cd      as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,brand_cd              as    brand_cd              --       品牌编号           ,
           ,-2                    as    itm_id                --       商品ID             ,
           ,-2                    as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                      as    itm_typ_num           --       货号/型号          ,
          ,-2                      as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd, t1.itm_thrd_catg_cd, t1.chan_cd, t1.shop_id, t1.brand_cd

    INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=13,grouping_id=1302) --brand_shop二级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,brand_cd              as    brand_cd              --       品牌编号           ,
           ,-2                    as    itm_id                --       商品ID             ,
           ,-2                     as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                      as    itm_typ_num           --       货号/型号          ,
          ,-2                      as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd,                      t1.chan_cd, t1.shop_id, t1.brand_cd

    INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=13,grouping_id=1303) --brand_shop一级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,999999                    as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,brand_cd              as    brand_cd              --       品牌编号           ,
           ,-2                    as    itm_id                --       商品ID             ,
           ,-2                    as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                     as    itm_typ_num           --       货号/型号          ,
          ,-2                      as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd,                                           t1.chan_cd, t1.shop_id, t1.brand_cd
  --------------------------------------------------------------------------------------------------------------------------------------------------------------------------
   INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=14,grouping_id=1401) --brand_shop_itm三级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,itm_thrd_catg_cd      as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,brand_cd               as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                     as    itm_typ_num           --       货号/型号          ,
          ,-2                     as    itm_flag              --       货号/型号的标志
          where zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd, t1.itm_thrd_catg_cd, t1.chan_cd, t1.shop_id,t1.brand_cd, t1.itm_id, t1.itm_nm

     INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=14,grouping_id=1402) --brand_shop_itm二级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,brand_cd               as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                     as    itm_typ_num           --       货号/型号          ,
          ,-2                     as    itm_flag              --       货号/型号的标志
          where zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd,                      t1.chan_cd, t1.shop_id,t1.brand_cd, t1.itm_id, t1.itm_nm

     INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=14,grouping_id=1403) --brand_shop_itm一级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,999999                    as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,brand_cd               as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2                      as    main_brand_cd         --       主品牌ID           ,
           ,-2                     as    main_brand_nm         --      主品牌名称         ,
          ,-2                     as    itm_typ_num           --       货号/型号          ,
          ,-2                     as    itm_flag              --       货号/型号的标志
          where zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd,                                            t1.chan_cd, t1.shop_id,t1.brand_cd, t1.itm_id, t1.itm_nm

  -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=15,grouping_id=1501) --main_brand_tm_typ_num三级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,itm_thrd_catg_cd      as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,-2                    as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,-2                     as    itm_id                --       商品ID             ,
           ,-2                     as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,main_brand_cd                        as    main_brand_cd         --       主品牌ID           ,
          ,max(main_brand_nm)                   as    main_brand_nm         --      主品牌名称         ,
          ,itm_typ_num                           as    itm_typ_num           --       货号/型号          ,
          ,min(itm_flag)                             as    itm_flag              --       货号/型号的标志
   where itm_flag is not null  and   zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd, t1.itm_thrd_catg_cd, t1.chan_cd, main_brand_cd,itm_typ_num

       INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=15,grouping_id=1502) --main_brand_tm_typ_num二级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,-2                    as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,-2                     as    itm_id                --       商品ID             ,
           ,-2                     as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,main_brand_cd                        as    main_brand_cd         --       主品牌ID           ,
          ,max(main_brand_nm)                   as    main_brand_nm         --      主品牌名称         ,
          ,itm_typ_num                           as    itm_typ_num           --       货号/型号          ,
          ,min(itm_flag)                             as    itm_flag              --       货号/型号的标志
   where itm_flag is not null   and   zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd,                      t1.chan_cd, main_brand_cd,itm_typ_num

       INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=15,grouping_id=1503) --main_brand_tm_typ_num一级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,999999                     as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                     as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,-2                    as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,-2                     as    itm_id                --       商品ID             ,
           ,-2                     as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,main_brand_cd                        as    main_brand_cd         --       主品牌ID           ,
          ,max(main_brand_nm)                   as    main_brand_nm         --      主品牌名称         ,
          ,itm_typ_num                           as    itm_typ_num           --       货号/型号          ,
          ,min(itm_flag )                            as    itm_flag              --       货号/型号的标志
   where itm_flag is not null   and   zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd,                                           t1.chan_cd, main_brand_cd,itm_typ_num

  -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
   INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=16,grouping_id=1601) --itm_ main_brand_itm_typ_num三级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,itm_thrd_catg_cd      as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,main_brand_cd          as    main_brand_cd         --       主品牌ID           ,
          ,max(main_brand_nm)     as    main_brand_nm         --      主品牌名称         ,
          ,itm_typ_num            as    itm_typ_num           --       货号/型号          ,
          ,itm_flag               as    itm_flag              --       货号/型号的标志
       where itm_flag is not null   and   zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd, t1.itm_thrd_catg_cd, t1.chan_cd, t1.shop_id, t1.itm_id, t1.itm_nm,main_brand_cd,itm_typ_num,itm_flag

 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=16,grouping_id=1602) --itm_ main_brand_itm_typ_num二级类目
  select   shop_typ_cd            as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                     as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,main_brand_cd          as    main_brand_cd         --       主品牌ID           ,
          ,max(main_brand_nm)     as    main_brand_nm         --      主品牌名称         ,
          ,itm_typ_num            as    itm_typ_num           --       货号/型号          ,
          ,itm_flag               as    itm_flag              --       货号/型号的标志
          where itm_flag is not null   and   zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd,              t1.chan_cd, t1.shop_id, t1.itm_id, t1.itm_nm,main_brand_cd,itm_typ_num,itm_flag

 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=16,grouping_id=1603) --itm_ main_brand_itm_typ_num一级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,999999                     as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,itm_id                as    itm_id                --       商品ID             ,
           ,itm_nm                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,main_brand_cd          as    main_brand_cd         --       主品牌ID           ,
          ,max(main_brand_nm)     as    main_brand_nm         --      主品牌名称         ,
          ,itm_typ_num            as    itm_typ_num           --       货号/型号          ,
          ,itm_flag               as    itm_flag              --       货号/型号的标志
      where itm_flag is not null   and   zengpin<>1
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd,                                  t1.chan_cd, t1.shop_id, t1.itm_id, t1.itm_nm,main_brand_cd,itm_typ_num,itm_flag

   -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=17,grouping_id=1701) --shop三级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,itm_thrd_catg_cd      as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,-2                as    itm_id                --       商品ID             ,
           ,-2                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2          as    main_brand_cd         --       主品牌ID           ,
          ,-2     as    main_brand_nm         --      主品牌名称         ,
          ,-2            as    itm_typ_num           --       货号/型号          ,
          ,-2               as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd, t1.itm_thrd_catg_cd, t1.chan_cd, t1.shop_id

 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=17,grouping_id=1702) --shop二级类目
  select   shop_typ_cd            as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,itm_scnd_catg_cd      as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                     as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,-2                as    itm_id                --       商品ID             ,
           ,-2                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2          as    main_brand_cd         --       主品牌ID           ,
          ,-2     as    main_brand_nm         --      主品牌名称         ,
          ,-2            as    itm_typ_num           --       货号/型号          ,
          ,-2               as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd, t1.itm_scnd_catg_cd, t1.chan_cd, t1.shop_id

 INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=17,grouping_id=1703) --shop一级类目
  select   shop_typ_cd             as    shop_typ_cd           --          商家类型       ,
           ,itm_frst_catg_cd      as    itm_frst_catg_cd      --       商品一级分类代码   ,
           ,999999                     as    itm_scnd_catg_cd      --       商品二级分类代码   ,
           ,999999                    as    itm_thrd_catg_cd      --       商品三级分类代码   ,
           ,chan_cd               as    chan_cd               --          渠道编号       ,
           ,shop_id               as    shop_id               --       店铺编号           ,
           ,-2                    as    brand_cd              --       品牌编号           ,
           ,-2                as    itm_id                --       商品ID             ,
           ,-2                as    itm_nm                --       商品名称           ,
           ,count(distinct usr_log_acct)        as    follow_usr_qty        --       关注用户数         ,
          ,-2          as    main_brand_cd         --       主品牌ID           ,
          ,-2     as    main_brand_nm         --      主品牌名称         ,
          ,-2            as    itm_typ_num           --       货号/型号          ,
          ,-2               as    itm_flag              --       货号/型号的标志
  group by t1.shop_typ_cd, t1.itm_frst_catg_cd,t1.chan_cd, t1.shop_id
   ;
 ------------------------------单独增加店铺
INSERT OVERWRITE TABLE   """ + dest_table_name + """  PARTITION (dt='""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """',sort_typ=11,grouping_id=1101)
        SELECT -2                      as shop_typ_cd, --店铺类型:0-POP ,1-自营
               -2                      as itm_frst_catg_cd, --商品一级类目编号
               -2                      as itm_scnd_catg_cd, --商品二级类目编号
               -2                      as itm_thrd_catg_cd, --商品三级类目编号
               shop_attn.chan_cd       as chan_cd, --渠道类型:0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ ,10-其他 ,99-全渠道
               shop_id                 as shop_id, --店铺编号
               -2                      as brand_cd,
               -2                      as itm_id, --商品编号
               null                    as itm_nm, --商品
               shop_attn.shop_attn_num as follow_usr_qty, --关注用户数
               -2                      as main_brand_cd ,        --       主品牌ID           ,
               -2                      as main_brand_nm,         --      主品牌名称         ,
               -2                      as itm_typ_num ,          --       货号/型号          ,
               -2                      as itm_flag              --       货号/型号的标志
          FROM (SELECT vend_mapping.shop_id              AS shop_id,
                       follow_vender_chain.chan_cd       AS chan_cd,
                       follow_vender_chain.shop_attn_num AS shop_attn_num
                  FROM (SELECT vender_id AS vender_id,
                               99 AS chan_cd,
                               COUNT(DISTINCT pin) AS shop_attn_num
                          FROM retail_benchmark_10t.benchmark_fdm_follow_vender_sns_follow_vender_chain
                         WHERE dp = 'ACTIVE' --不用开始时间和结束时间卡，因为这个数2017.03.13被初始化过
                           AND to_date(created_time) <=
                               '""" + zst.data_day_str + """'
                           AND to_date(created_time) >=
                               '""" + stat_begin + """'
                         GROUP BY vender_id) follow_vender_chain
                  LEFT OUTER JOIN (SELECT shop_id, vend_id
                                    FROM retail_benchmark_10t.benchmark_gdm_m01_zs_shop_vend_mapping
                                   WHERE dt = '""" + zst.data_day_str + """') vend_mapping
                    ON follow_vender_chain.vender_id = vend_mapping.vend_id
                UNION ALL
                SELECT shop_id AS shop_id,
                       2 AS chan_cd,
                       COUNT(DISTINCT usr_log_acct) AS shop_attn_num
                  FROM retail_benchmark_10t.benchmark_gdm_m14_zs_wireless_follow_event
                 WHERE dt <= '""" + zst.data_day_str + """'
                   AND dt >= '""" + stat_begin + """'
                   AND flw_flg = 1
                 GROUP BY shop_id) shop_attn;
"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, exec_engine='spark',
             retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true",
                         "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true",
                         "--conf spark.sql.shuffle.partitions=1200",
                         "--conf spark.driver.memory=10g",
                         "--conf spark.dynamicAllocation.maxExecutors=800"])
