#!/usr/bin/env python3

# 517629

import os, sys
import datetime

sys.path.append(os.getenv('HIVE_TASK'))

from HiveTask import HiveTask
from ZSTask import ZSTask

ht = HiveTask()
zst = ZSTask()

os.environ["HADOOP_CLIENT_OPTS"] = "-Xmx16g"

insertDay = datetime.date.today().strftime("%Y-%m-%d")

dest_table_name = 'benchmark_adm_s14_zs_shop_traffics_lead_to_ord_det'

data_day_str = zst.data_day_str

yesterday = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=1), "%Y-%m-%d")

proTable = """retail_benchmark_10t.benchmark_dim_jd_jdb_zmd_acct"""

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """')

  select
    shop_traffics.req_tm as req_tm,
    shop_traffics.chan_cd as chan_cd,
    shop_traffics.sessn_id as sessn_id,
    shop_traffics.seq_num as seq_num,
    shop_traffics.brws_uniq_id as brws_uniq_id,
    shop_traffics.usr_log_acct as usr_log_acct,
    shop_traffics.shop_id as shop_id,
    shop_traffics.shop_typ_cd as shop_typ_cd,
    shop_traffics.itm_sku_id as itm_sku_id,
    shop_traffics.itm_sku_nm as itm_sku_nm,
    shop_traffics.itm_id as itm_id,
    shop_traffics.itm_nm as itm_nm,
    shop_traffics.itm_frst_catg_cd as itm_frst_catg_cd,
    shop_traffics.itm_scnd_catg_cd as itm_scnd_catg_cd,
    shop_traffics.itm_thrd_catg_cd as itm_thrd_catg_cd,
    shop_traffics.brand_cd as brand_cd,
    shop_traffics.url_typ_cd as url_typ_cd,
    shop_traffics.url_attr_val as url_attr_val,
    shop_traffics.url,
    shop_traffics.req_parm,
    shop_traffics.shop_sessn_src_url,
    shop_traffics.shop_sessn_src_req_parm,
    shop_traffics.shop_sessn_src_zs_url_frst_catg_cd as shop_sessn_src_zs_url_frst_catg_cd,
    shop_traffics.shop_sessn_src_zs_url_scnd_catg_cd as shop_sessn_src_zs_url_scnd_catg_cd,
    shop_traffics.shop_sessn_src_zs_url_thrd_catg_cd as shop_sessn_src_zs_url_thrd_catg_cd,
    shop_ord_det.par_sale_ord_id AS par_sale_ord_id,
    shop_ord_det.sale_ord_id AS sale_ord_id,
    shop_ord_det.sale_ord_tm AS sale_ord_tm,
    SUM(shop_ord_det.ord_amt) AS ord_amt,
    SUM(shop_ord_det.sale_qty) AS sale_qty,
    case
      when shop_ord_det.usr_log_acct is not null
        and shop_ord_det.is_deal_ord = 1
        and shop_traffics.req_tm < shop_ord_det.sale_ord_tm ----浏览时间先于下单时间
        and shop_traffics.itm_sku_id = shop_ord_det.itm_sku_id
      then 1
      else 0
    end as is_ord, ---商智SKU粒度：引导成交口径
    shop_sessn_id as shop_sessn_id,
    shop_traffics.shop_sessn_par_seq_num,
    shop_traffics.ref_shop_id,
    shop_traffics.ref_itm_sku_id,
    shop_traffics.ref_itm_sku_nm,
    shop_traffics.ref_zs_url_frst_catg_cd,
    shop_traffics.ref_zs_url_scnd_catg_cd,
    shop_traffics.ref_zs_url_thrd_catg_cd,
    shop_traffics.tm_on_page,
    shop_traffics.shop_frst_req_flg,
    shop_traffics.shop_lst_req_flg,
    shop_traffics.zs_url_thrd_catg_cd,
    shop_traffics.ref_url,
    shop_traffics.ref_req_parm,
    case
      when shop_follow.usr_log_acct is not null and
          shop_traffics.req_tm < shop_follow.created_time ----浏览时间先于关注时间
      then 1
      else 0
    end as is_follow,
    shop_traffics.ref_seq_num, ---上一页访问序号
    case
      when shop_ord_det.usr_log_acct is not null
        and shop_traffics.req_tm < shop_ord_det.sale_ord_tm ----浏览时间先于下单时间
        and shop_ord_det.sz_xiadan_flag = 1 ---商智下单标记
        and shop_traffics.itm_sku_id = shop_ord_det.itm_sku_id
      then 1
      else 0
    end as is_vld_ord, -------商智SKU粒度：下单口径
    shop_ord_det.chk_acct_tm,
    shop_traffics.ref_itm_id,
    shop_traffics.ref_itm_nm,
    shop_traffics.user_site_addr,
    shop_traffics.user_site_cy_nm,
    shop_traffics.user_site_prov_nm,
    shop_traffics.user_site_city_nm,
    shop_traffics.shop_sessn_src_kwd,
    shop_traffics.shop_sessn_src_active,
    COALESCE(shop_ord_det.is_allnew_flag, - 1) AS is_allnew_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
    COALESCE(shop_ord_det.is_180new_flag, - 1) AS is_180new_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
    COALESCE(shop_ord_det.is_730new_flag, - 1) AS is_730new_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
    shop_ord_det.itm_sku_id as ord_itm_sku_id,
    case
      when shop_ord_det.usr_log_acct is not null
        and shop_ord_det.is_deal_ord = 1
        and shop_traffics.req_tm < shop_ord_det.sale_ord_tm ----浏览时间先于下单时间
      then 1
      else 0
    end as is_ord_spu, ---商智SPU粒度：引导成交口径
    case
      when shop_ord_det.usr_log_acct is not null
        and shop_traffics.req_tm < shop_ord_det.sale_ord_tm ----浏览时间先于下单时间
        and shop_ord_det.sz_xiadan_flag = 1 ---商智下单标记
      then 1
      else 0
    end as is_vld_ord_spu, ---商智SPU粒度：引导下单口径
    shop_traffics.main_sku_id
  from
    (
      select
        /*+MAPJOIN(black_acct)*/
        shop_src_traffics.*
      from
        (
          select
            sessn_id,
            seq_num,
            req_tm,
            chan_cd,
            shop_id,
            shop_typ_cd,
            itm_sku_id,
            itm_sku_nm,
            itm_id,
            itm_nm,
            brand_cd,
            itm_frst_catg_cd,
            itm_scnd_catg_cd,
            itm_thrd_catg_cd,
            zs_url_thrd_catg_cd,
            brws_uniq_id,
            usr_log_acct,
            url_typ_cd,
            url_attr_val,
            url,
            req_parm,
            ref_shop_id,
            ref_seq_num,
            ref_itm_sku_id,
            ref_itm_sku_nm,
            ref_itm_id,
            ref_itm_nm,
            ref_zs_url_frst_catg_cd,
            ref_zs_url_scnd_catg_cd,
            ref_zs_url_thrd_catg_cd,
            ref_url,
            ref_req_parm,
            tm_on_page,
            shop_sessn_id,
            shop_sessn_src_url,
            shop_sessn_src_req_parm,
            shop_sessn_par_seq_num,
            shop_frst_req_flg,
            shop_lst_req_flg,
            shop_sessn_src_zs_url_frst_catg_cd,
            shop_sessn_src_zs_url_scnd_catg_cd,
            shop_sessn_src_zs_url_thrd_catg_cd,
            user_site_addr,
            user_site_cy_nm,
            user_site_prov_nm,
            user_site_city_nm,
            shop_sessn_src_kwd,
            shop_sessn_src_active,
            main_sku_id,
            case when shop_id >0 and shop_id < 1000000000
                 then itm_id
                 else main_sku_id
            end as spu_id,
            case
                      when usr_log_acct is not null
                           and usr_log_acct != ''
                           and Upper(usr_log_acct) != 'NULL'
                          then usr_log_acct
                          else brws_uniq_id
                       end  as black_key            
          FROM
            retail_benchmark_10t.benchmark_adm_s14_zs_all_chan_shop_traffics
          WHERE
            dt <= '""" + zst.data_day_str + """'
            AND dt >= '""" + zst.data_day_str + """'
        )
        shop_src_traffics
      left join
        (
          select
            account as usr_log_acct
          from
            """ + proTable + """
          group by
            account
          
          union all
          
          select
            usr_log_acct
          from
            retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_pin_black_spu
          where
            dt = '""" + zst.data_day_str + """'
          group by
            usr_log_acct
        )
        black_acct
      on
        shop_src_traffics.black_key = black_acct.usr_log_acct
      where
        black_acct.usr_log_acct is null ----过滤掉家电京东帮专卖店账号和流量异常账号，避免数据倾斜
    )shop_traffics
  --店铺关注表
  left join (select /*+mapjoin(vend_mapping)*/ ven_follow.usr_log_acct as usr_log_acct,
                    ven_follow.vender_id,
                    coalesce(vend_mapping.shop_id, ven_follow.shop_id) as shop_id,
                    created_time
               from (select pin as usr_log_acct,
                            vender_id,
                            created_time,
                            shop_id,
                            vender_name
                       from retail_benchmark_10t.benchmark_fdm_follow_vender_sns_follow_vender_chain
                      where 
                        --dp = 'ACTIVE'
                start_date <= '""" + zst.data_day_str + """'
                AND end_date > '""" + zst.data_day_str + """'                      
                        and substr(created_time, 1, 10) =
                            '""" + zst.data_day_str + """'
                      group by pin,
                               vender_id,
                               created_time,
                               shop_id,
                               vender_name) ven_follow
               LEFT OUTER JOIN (SELECT shop_id, vend_id
                                 FROM retail_benchmark_10t.benchmark_gdm_m01_zs_shop_vend_mapping
                                WHERE dt = '""" + zst.data_day_str + """') vend_mapping
                 ON ven_follow.vender_id = vend_mapping.vend_id) shop_follow

    ON shop_traffics.usr_log_acct = shop_follow.usr_log_acct
   AND shop_traffics.shop_id = shop_follow.shop_id

    --全平台订单模型
  left join
    (
      SELECT
        dt as dt,
        shop_id as shop_id,
        CASE
          WHEN chan_cd = 3 and second_chan_cd = 31 --开普勒
          THEN 5
          ELSE chan_cd
        END AS chan_cd,
        par_sale_ord_id AS par_sale_ord_id,
        sale_ord_id AS sale_ord_id,
        sale_ord_dt as sale_ord_dt,
        --sale_ord_typ_cd AS sale_ord_typ_cd,
        --split_sts_cd    AS split_sts_cd,
        usr_log_acct AS usr_log_acct,
        is_deal_ord as is_deal_ord,
        itm_sku_id AS itm_sku_id,
        itm_id AS itm_id,
        sale_ord_tm AS sale_ord_tm,
        ord_amt AS ord_amt,
        sale_qty AS sale_qty,
        chk_acct_tm AS chk_acct_tm,
        sz_xiadan_flag AS sz_xiadan_flag,
        is_allnew_flag AS is_allnew_flag,
        is_180new_flag AS is_180new_flag,
        is_730new_flag AS is_730new_flag,
        case when shop_id >0 and shop_id < 1000000000
             then itm_id
             else main_sku_id
        end as spu_id
        
      FROM
        retail_benchmark_10t.benchmark_adm_m04_zs_oldnew_ord_det
      WHERE
        dt = '""" + zst.data_day_str + """'
        AND
        (
          is_deal_ord = 1
          or sz_xiadan_flag = 1
        )
        AND sale_ord_dt = '""" + zst.data_day_str + """'
        and substr(usr_log_acct, 1, 9) != 'forcebot_' --过滤掉压测账号
    )
    shop_ord_det
  ON
    shop_traffics.usr_log_acct = shop_ord_det.usr_log_acct
    AND (CASE
        --原WHEN shop_traffics.chan_cd = 3  AND shop_traffics.shop_sessn_src_zs_url_frst_catg_cd = 7
        --THEN 5
        --现
        WHEN shop_traffics.shop_sessn_src_zs_url_scnd_catg_cd = 6038
        THEN 5
        ELSE shop_traffics.chan_cd
       END) = shop_ord_det.chan_cd
    AND shop_traffics.shop_id = shop_ord_det.shop_id
    AND shop_traffics.spu_id = shop_ord_det.spu_id
    
  group by
    shop_traffics.req_tm,
    shop_traffics.chan_cd,
    shop_traffics.sessn_id,
    shop_traffics.seq_num,
    shop_traffics.brws_uniq_id,
    shop_traffics.usr_log_acct,
    shop_traffics.shop_id,
    shop_traffics.shop_typ_cd,
    shop_traffics.itm_sku_id,
    shop_traffics.itm_sku_nm,
    shop_traffics.itm_id,
    shop_traffics.itm_nm,
    shop_traffics.itm_frst_catg_cd,
    shop_traffics.itm_scnd_catg_cd,
    shop_traffics.itm_thrd_catg_cd,
    shop_traffics.zs_url_thrd_catg_cd,
    shop_traffics.brand_cd,
    shop_traffics.url_typ_cd,
    shop_traffics.url_attr_val,
    shop_traffics.url,
    shop_traffics.req_parm,
    shop_traffics.ref_shop_id,
    shop_traffics.ref_seq_num,
    shop_traffics.ref_itm_sku_id,
    shop_traffics.ref_itm_sku_nm,
    shop_traffics.ref_zs_url_frst_catg_cd,
    shop_traffics.ref_zs_url_scnd_catg_cd,
    shop_traffics.ref_zs_url_thrd_catg_cd,
    shop_traffics.ref_url,
    shop_traffics.ref_req_parm,
    shop_traffics.tm_on_page,
    shop_traffics.shop_sessn_id,
    shop_traffics.shop_sessn_src_url,
    shop_traffics.shop_sessn_src_req_parm,
    shop_traffics.shop_sessn_par_seq_num,
    shop_traffics.shop_frst_req_flg,
    shop_traffics.shop_lst_req_flg,
    shop_traffics.shop_sessn_src_zs_url_frst_catg_cd,
    shop_traffics.shop_sessn_src_zs_url_scnd_catg_cd,
    shop_traffics.shop_sessn_src_zs_url_thrd_catg_cd,
    shop_ord_det.par_sale_ord_id,
    shop_ord_det.sale_ord_id,
    shop_ord_det.sale_ord_tm,
    --shop_ord_det.ord_amt,
    --shop_ord_det.sale_qty,
    shop_ord_det.chk_acct_tm,
    shop_traffics.ref_itm_id,
    shop_traffics.ref_itm_nm,
    shop_traffics.user_site_addr,
    shop_traffics.user_site_cy_nm,
    shop_traffics.user_site_prov_nm,
    shop_traffics.user_site_city_nm,
    shop_traffics.shop_sessn_src_kwd,
    shop_traffics.shop_sessn_src_active,
    COALESCE(shop_ord_det.is_allnew_flag, - 1), -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
    COALESCE(shop_ord_det.is_180new_flag, - 1), -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
    COALESCE(shop_ord_det.is_730new_flag, - 1), -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
    shop_ord_det.itm_sku_id,
    case
      when shop_ord_det.usr_log_acct is not null
        and shop_ord_det.is_deal_ord = 1
        and shop_traffics.req_tm < shop_ord_det.sale_ord_tm ----浏览时间先于下单时间
      then 1
      else 0
    end, ---商智SPU粒度：引导成交口径
    case
      when shop_ord_det.usr_log_acct is not null
        and shop_traffics.req_tm < shop_ord_det.sale_ord_tm ----浏览时间先于下单时间
        and shop_ord_det.sz_xiadan_flag = 1 ---商智下单标记
      then 1
      else 0
    end,
    main_sku_id,
    case
      when shop_ord_det.usr_log_acct is not null
        and shop_ord_det.is_deal_ord = 1
        and shop_traffics.req_tm < shop_ord_det.sale_ord_tm ----浏览时间先于下单时间
        and shop_traffics.itm_sku_id = shop_ord_det.itm_sku_id
      then 1
      else 0
    end,
    case
      when shop_follow.usr_log_acct is not null and
          shop_traffics.req_tm < shop_follow.created_time ----浏览时间先于关注时间
      then 1
      else 0
    end,
    case
      when shop_ord_det.usr_log_acct is not null
        and shop_traffics.req_tm < shop_ord_det.sale_ord_tm ----浏览时间先于下单时间
        and shop_ord_det.sz_xiadan_flag = 1 ---商智下单标记
        and shop_traffics.itm_sku_id = shop_ord_det.itm_sku_id
      then 1
      else 0
    end


;
"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql
             , exec_engine='spark'
             , spark_resource_level='low'
             , retry_with_hive=False
             , spark_args=[
        "--conf spark.sql.hive.mergeFiles=true"
        , '--conf spark.sql.parser.quotedRegexColumnNames=false'
        , '--conf spark.sql.adaptive.join.enabled=true'
        , '--conf spark.sql.adaptive.enabled=true'
        , '--conf spark.executor.instances=150'
        , '--conf spark.executor.cores=4'
        , '--conf spark.executor.memory=20g'
        , '--conf spark.sql.shuffle.partitions=3000'
        , '--conf spark.shuffle.io.numConnectionsPerPeer=2'
        , '--conf spark.shuffle.io.connectionTimeout=160s'
        , '--conf spark.speculation.multiplier=3'
        , '--conf spark.dynamicAllocation.maxExecutors=1000'
        , '--conf spark.hadoop.hive.exec.orc.split.strategy=ETL'
        , "--conf spark.sql.adaptive.repartition.enabled=true"]
             )
