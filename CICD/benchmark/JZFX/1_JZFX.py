#!/usr/bin/env python3

# 487590

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

print(stat_ct_cd)
print(stat_ct)
print(stat_begin)

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;

INSERT OVERWRITE TABLE retail_benchmark_10t.benchmark_app_zs_z1701_product_compete_det_mid PARTITION(dt = '""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """')
select
  RN.shop_id as shop_id,
  RN.chan_cd as chan_cd,
  RN.pro_type,
  """ + stat_ct + """ as stat_ct,
  """ + zst.data_day_int + """ as stat_ct_stamp,
  RN.pro_id as pro_id,
  null as ord_amt_index,
  null as uv_Index,
  RN.follow_usr_qty as follow_usr_qty,
  null as add_cart_index,
  RN.ord_qty as ord_qty,
  RN.pro_nm as pro_nm,
  RN.uv_rank as uv_rank,
  RN.ord_amt_rank as ord_amt_rank,
  RN.uv as uv,
  RN.ord_amt as ord_amt,
  RN.add_usr_qty as add_cart_uv
from
  (
    select
      M.shop_id as shop_id,
      M.chan_cd as chan_cd,
      0 as pro_type,
      M.pro_id as pro_id,
      --M.ord_amt_index, --下单金额指数
      --M.uv_Index, --访客指数
      M.follow_usr_qty as follow_usr_qty, --关注人数
      --M.add_cart_index, --加购指数
      M.ord_qty as ord_qty, --下单商品件数
      M.pro_nm as pro_nm,
      ROW_NUMBER() OVER(PARTITION BY M.shop_id, M.chan_cd ORDER BY M.uv DESC,M.pro_id DESC) AS uv_rank,
      ROW_NUMBER() OVER(PARTITION BY M.shop_id, M.chan_cd ORDER BY M.ord_amt DESC,M.pro_id DESC) AS ord_amt_rank,
      M.uv, --访客数
      M.ord_amt, --成交金额
      M.add_usr_qty --加购客户数
    from
      (
        
            select
              shop_id,
              chan_cd,
              pro_id,
              max(pro_nm) as pro_nm,
              ord_qty, --下单商品件数
              ord_amt, --订单金额
              uv, --访客数
              add_usr_qty, --加购用户数
              follow_usr_qty --关注人数
            from
              retail_benchmark_10t.benchmark_app_zs_z1102_shop_item_sale_sum
            where
              dt = '""" + zst.data_day_str + """'
              and stat_ct_cd = '""" + stat_ct_cd + """'
              and shop_id not in (10278978)  --modified by ousaisai 20201019 盘货店铺剔除
            group by
              shop_id,
              chan_cd,
              pro_id,
              ord_qty,
              ord_amt,
              uv,
              add_usr_qty,
              follow_usr_qty
      
      )
      M
    join
      (
        SELECT
          itm_id AS itm_id
        FROM
          retail_benchmark_10t.benchmark_adm_th03_merchandise_info
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND shop_id > 0
          AND itm_vld_flg = 1
          AND itm_sku_sts_cd = 3001
          AND free_gds_flg = 0
          AND itm_sku_nm NOT RLIKE '非商品|邮费链接|赠品|非卖品|赠送品|5分好评返|晒单返现|不要单独拍下|请勿单独购买|勿购|勿拍|不发货|请勿评价|请勿单拍|请勿乱拍|会员保障及换新|补差|邮费补款|邮费补拍|运费补款|运费补拍|差价补拍|补邮费|补运费|补配送费|补邮专拍|配送费补款|配送费补差|配送费补拍'
        GROUP BY
          itm_id
      )
      N
    on
      M.pro_id = N.itm_id
    
    union all
    
    select
      M.shop_id as shop_id,
      M.chan_cd as chan_cd,
      1 as pro_type,
      M.pro_id as pro_id,
      --M.ord_amt_index, --下单金额指数
      --M.uv_Index, --访客指数
      M.follow_usr_qty as follow_usr_qty, --关注人数
      --M.add_cart_index, --加购指数
      M.ord_qty as ord_qty, --下单商品件数
      M.pro_nm as pro_nm,
      ROW_NUMBER() OVER(PARTITION BY M.shop_id, M.chan_cd ORDER BY M.uv DESC,M.pro_id DESC) AS uv_rank,
      ROW_NUMBER() OVER(PARTITION BY M.shop_id, M.chan_cd ORDER BY M.ord_amt DESC,M.pro_id DESC) AS ord_amt_rank,
      M.uv, --访客数
      M.ord_amt, --成交金额
      M.add_usr_qty --加购客户数
    from
      (
            select
              shop_id,
              chan_cd,
              pro_id,
              max(pro_nm) as pro_nm,
              ord_qty, --下单商品件数
              ord_amt, --订单金额
              uv, --访客数
              add_usr_qty, --加购用户数
              follow_usr_qty --关注人数
            from
              retail_benchmark_10t.benchmark_app_zs_z1102_shop_sku_sale_sum
            where
              dt = '""" + zst.data_day_str + """'
              and stat_ct_cd = '""" + stat_ct_cd + """'
              and shop_id not in (10278978)  --modified by ousaisai 20201019 盘货店铺剔除
            group by
              shop_id,
              chan_cd,
              pro_id,
              ord_qty,
              ord_amt,
              uv,
              add_usr_qty,
              follow_usr_qty

      )
      M
    join
      (
        SELECT
          itm_sku_id AS sku_id
        FROM
          retail_benchmark_10t.benchmark_adm_th03_merchandise_info
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND shop_id > 0
          AND itm_vld_flg = 1
          AND itm_sku_sts_cd = 3001
          AND free_gds_flg = 0
          AND itm_sku_nm NOT RLIKE '非商品|邮费链接|赠品|非卖品|赠送品|5分好评返|晒单返现|不要单独拍下|请勿单独购买|勿购|勿拍|不发货|请勿评价|请勿单拍|请勿乱拍|会员保障及换新|补差|邮费补款|邮费补拍|运费补款|运费补拍|差价补拍|补邮费|补运费|补配送费|补邮专拍|配送费补款|配送费补差|配送费补拍'
        GROUP BY
          itm_sku_id
      )
      N
    on
      M.pro_id = N.sku_id
  )
  RN
;

"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_app_zs_z1701_product_compete_det_mid', sql=sql,
             lzo_compress=True, lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             exec_engine='spark', retry_with_hive=False, spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true",
        "--conf spark.sql.shuffle.partitions=1500",
        "--conf spark.dynamicAllocation.maxExecutors=1200"
    ])

sql2 = """
use retail_benchmark_10t;
INSERT OVERWRITE TABLE retail_benchmark_10t.benchmark_app_zs_z1701_product_compete_det_top PARTITION(dt = '""" + insertDay + """',stat_ct_cd = '""" + stat_ct_cd + """')
select
  t.shop_id as shop_id, --商家id
  t.chan_cd as chan_cd, --渠道编号
  t.pro_type as pro_type, --商品类型 0-SPU 1-SKU
  t.sort_type as sort_type, --排序类型 0-按下单金额指数top50 1-按访客指数取top50
  """ + stat_ct + """ as stat_ct,
  """ + zst.data_day_int + """ as stat_ct_stamp,
  t.pro_id as pro_id, --商品id
  null as ord_amt_index, --下单金额（指数化）
  null as uv_index, --访客数（指数化）
  t.follow_usr_qty as follow_usr_qty, --商品关注人数
  null as add_cart_index, --加购（指数化）
  t.ord_qty as ord_qty, --下单商品件数
  t.pro_nm as pro_nm, --商品名称
  b.shop_nm as shop_nm, --店铺名称
  t.uv as uv, --访客数
  t.ord_amt as ord_amt, --c成交金额
  t.add_cart_uv as add_cart_uv --加购客户数
from
  (
    select
      t1.shop_id as shop_id,
      t1.chan_cd as chan_cd,
      t1.pro_type as pro_type,
      t1.sort_type as sort_type,
      t1.pro_id as pro_id,
      --t1.ord_amt_index as ord_amt_index,
      --t1.uv_index as uv_index,
      t1.follow_usr_qty as follow_usr_qty,
      --t1.add_cart_index as add_cart_index,
      t1.ord_qty as ord_qty,
      t1.pro_nm as pro_nm,
      t1.uv as uv,
      t1.ord_amt as ord_amt,
      t1.add_cart_uv as add_cart_uv
    from
      (
        select
          shop_id,
          chan_cd,
          pro_type,
          0 as sort_type,
          pro_id,
          --ord_amt_index,
          --uv_index,
          follow_usr_qty,
          --add_cart_index,
          ord_qty,
          pro_nm,
          uv,
          ord_amt,
          add_cart_uv
        from
          retail_benchmark_10t.benchmark_app_zs_z1701_product_compete_det_mid
        where
          dt = '""" + zst.data_day_str + """'
          and stat_ct_cd = '""" + stat_ct_cd + """'
          and ord_amt_rank <= 50
        
        union all
        
        select
          shop_id,
          chan_cd,
          pro_type,
          1 as sort_type,
          pro_id,
          --ord_amt_index,
          --uv_index,
          follow_usr_qty,
          --add_cart_index,
          ord_qty,
          pro_nm,
          uv,
          ord_amt,
          add_cart_uv
        from
          retail_benchmark_10t.benchmark_app_zs_z1701_product_compete_det_mid
        where
          dt = '""" + zst.data_day_str + """'
          and stat_ct_cd = '""" + stat_ct_cd + """'
          and uv_rank <= 50
      )
      t1
  )
  t
left outer join
  (
    select
      shop_id,
      max(shop_name) as shop_nm
    from
      retail_benchmark_10t.benchmark_fdm_pop_vender_vender_chain
    where
      start_date <= '""" + zst.data_day_str + """'
      and end_date > '""" + zst.data_day_str + """'
      and shop_id > 0
    group by
      shop_id
  )
  b
on
  t.shop_id = b.shop_id;

"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_app_zs_z1701_product_compete_det_top',
             lzo_compress=True, lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd], sql=sql2,
             exec_engine='spark', retry_with_hive=False, spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
    ])
