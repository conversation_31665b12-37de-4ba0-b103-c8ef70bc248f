#!/usr/bin/env python3

# 492741

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

dest_table_name = 'benchmark_adm_s14_zs_shop_traffics_srch_det'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE retail_benchmark_10t;
INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' , stat_ct_cd = '""" + stat_ct_cd + """' )
                    SELECT
                          shop_cart_ord.shop_id      AS shop_id
                         ,shop_cart_ord.chan_cd      AS chan_cd
                         ,shop_cart_ord.brws_uniq_id      AS brws_uniq_id
                         ,shop_cart_ord.usr_log_acct      AS usr_log_acct
                         ,shop_cart_ord.itm_sku_id   AS itm_sku_id
                         ,shop_cart_ord.itm_sku_nm   AS itm_sku_nm
                         ,shop_cart_ord.itm_id   AS itm_id
                         ,shop_cart_ord.itm_nm   AS itm_nm
                         ,itm_frst_catg_cd
                         ,itm_scnd_catg_cd
                         ,itm_thrd_catg_cd
                         ,shop_cart_ord.shop_sessn_src_kwd AS kwd
                    FROM
                         (                     SELECT shop_id AS shop_id,
                                                      chan_cd AS chan_cd,
                                                      brws_uniq_id AS brws_uniq_id,
                                                      usr_log_acct AS usr_log_acct,
                                                      shop_sessn_src_zs_url_frst_catg_cd AS shop_sessn_src_zs_url_frst_catg_cd,
                                                      shop_sessn_src_zs_url_scnd_catg_cd AS shop_sessn_src_zs_url_scnd_catg_cd,
                                                      shop_sessn_src_zs_url_thrd_catg_cd AS shop_sessn_src_zs_url_thrd_catg_cd,
                                                      shop_sessn_src_req_parm AS shop_sessn_src_req_parm,
                                                      shop_sessn_src_url as shop_sessn_src_url,
                                                      itm_frst_catg_cd,
                                                      itm_scnd_catg_cd,
                                                      itm_thrd_catg_cd,
                                                      itm_sku_id as itm_sku_id,
                                                      itm_sku_nm as itm_sku_nm,
                                                      itm_id as itm_id,
                                                      itm_nm as itm_nm,
                                                      shop_sessn_src_kwd
                                                      
                                                 FROM retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_lead_to_cart_ord_det
                                               
                                                WHERE dt <= '""" + zst.data_day_str + """'
                                                  AND dt >= '""" + stat_begin + """'
                                                  --AND shop_sessn_src_zs_url_thrd_catg_cd = (CASE WHEN chan_cd IN (1,2) THEN 20201
                                                  --                                               WHEN chan_cd IN (3,4) THEN 20303
                                                  --                                               ELSE 20301 END)
                                                  AND (shop_sessn_src_zs_url_thrd_catg_cd = (CASE WHEN chan_cd IN (1,2) THEN 20201
                                                                                                 WHEN chan_cd IN (3,4) THEN 20303
                                                                                                 ELSE 20301 END)
                                                  OR shop_sessn_src_zs_url_thrd_catg_cd in (2008001, 2008002)
                                                  )
                                                  AND itm_sku_id > 0
                                                  AND shop_id < 1000000000
                                                  AND usr_log_acct IS NOT NULL
                                                  AND usr_log_acct <> ''
                                                  AND usr_log_acct <> '-'
                                                  --AND shop_frst_req_flg = 1
                                                  AND chan_cd in (20,1,2,3,4)
                                                GROUP BY shop_id,
                                                         chan_cd,
                                                         brws_uniq_id,
                                                         usr_log_acct,
                                                         shop_sessn_src_zs_url_frst_catg_cd,
                                                         shop_sessn_src_zs_url_scnd_catg_cd,
                                                         shop_sessn_src_zs_url_thrd_catg_cd,
                                                         shop_sessn_src_req_parm,
                                                         shop_sessn_src_url,
                                                         itm_frst_catg_cd,
                                                         itm_scnd_catg_cd,
                                                         itm_thrd_catg_cd,
                                                         itm_sku_id ,
                                                         itm_sku_nm ,
                                                         itm_id ,
                                                         itm_nm ,
                                                         shop_sessn_src_kwd
                         ) shop_cart_ord
                    GROUP BY
                         shop_cart_ord.shop_id
                        ,shop_cart_ord.chan_cd
                        ,shop_cart_ord.brws_uniq_id 
                        ,shop_cart_ord.usr_log_acct
                        ,shop_cart_ord.itm_sku_id   
                        ,shop_cart_ord.itm_sku_nm  
                        ,shop_cart_ord.itm_id   
                        ,shop_cart_ord.itm_nm 
                        ,itm_frst_catg_cd
                        ,itm_scnd_catg_cd
                        ,itm_thrd_catg_cd
                        ,shop_cart_ord.shop_sessn_src_kwd
                        
                    union all
                    
                    SELECT
                          shop_cart_ord.shop_id      AS shop_id
                         ,99                         AS chan_cd
                         ,shop_cart_ord.brws_uniq_id      AS brws_uniq_id
                         ,shop_cart_ord.usr_log_acct      AS usr_log_acct
                         ,shop_cart_ord.itm_sku_id   AS itm_sku_id
                         ,shop_cart_ord.itm_sku_nm   AS itm_sku_nm
                         ,shop_cart_ord.itm_id   AS itm_id
                         ,shop_cart_ord.itm_nm   AS itm_nm
                         ,itm_frst_catg_cd
                         ,itm_scnd_catg_cd
                         ,itm_thrd_catg_cd
                         ,shop_cart_ord.shop_sessn_src_kwd  AS kwd
                    FROM
                         (                     SELECT shop_id AS shop_id,
                                                      chan_cd AS chan_cd,
                                                      brws_uniq_id AS brws_uniq_id,
                                                      usr_log_acct AS usr_log_acct,
                                                      shop_sessn_src_zs_url_frst_catg_cd AS shop_sessn_src_zs_url_frst_catg_cd,
                                                      shop_sessn_src_zs_url_scnd_catg_cd AS shop_sessn_src_zs_url_scnd_catg_cd,
                                                      shop_sessn_src_zs_url_thrd_catg_cd AS shop_sessn_src_zs_url_thrd_catg_cd,
                                                      shop_sessn_src_req_parm AS shop_sessn_src_req_parm,
                                                      shop_sessn_src_url as shop_sessn_src_url,
                                                      itm_frst_catg_cd,
                                                      itm_scnd_catg_cd,
                                                      itm_thrd_catg_cd,
                                                      itm_sku_id as itm_sku_id,
                                                      itm_sku_nm as itm_sku_nm,
                                                      itm_id as itm_id,
                                                      itm_nm as itm_nm,
                                                      shop_sessn_src_kwd
                                                      
                                                 FROM retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_lead_to_cart_ord_det
                                               
                                                WHERE dt <= '""" + zst.data_day_str + """'
                                                  AND dt >= '""" + stat_begin + """'
                                                  --AND shop_sessn_src_zs_url_thrd_catg_cd = (CASE WHEN chan_cd IN (1,2) THEN 20201
                                                  --                                               WHEN chan_cd IN (3,4) THEN 20303
                                                  --                                               ELSE 20301 END)
                                                  AND (shop_sessn_src_zs_url_thrd_catg_cd = (CASE WHEN chan_cd IN (1,2) THEN 20201
                                                                                                 WHEN chan_cd IN (3,4) THEN 20303
                                                                                                 ELSE 20301 END)
                                                  OR shop_sessn_src_zs_url_thrd_catg_cd in (2008001, 2008002)
                                                  )
                                                  AND itm_sku_id > 0
                                                  AND shop_id < 1000000000
                                                  AND usr_log_acct IS NOT NULL
                                                  AND usr_log_acct <> ''
                                                  AND usr_log_acct <> '-'
                                                  --AND shop_frst_req_flg = 1
                                                  AND chan_cd in (20,1,2,3,4)
                                                GROUP BY shop_id,
                                                         chan_cd,
                                                         brws_uniq_id,
                                                         usr_log_acct,
                                                         shop_sessn_src_zs_url_frst_catg_cd,
                                                         shop_sessn_src_zs_url_scnd_catg_cd,
                                                         shop_sessn_src_zs_url_thrd_catg_cd,
                                                         shop_sessn_src_req_parm,
                                                         shop_sessn_src_url,
                                                         itm_frst_catg_cd,
                                                         itm_scnd_catg_cd,
                                                         itm_thrd_catg_cd,
                                                         itm_sku_id ,
                                                         itm_sku_nm ,
                                                         itm_id ,
                                                         itm_nm ,
                                                         shop_sessn_src_kwd
                         ) shop_cart_ord
                    GROUP BY
                         shop_cart_ord.shop_id
                        ,shop_cart_ord.brws_uniq_id 
                        ,shop_cart_ord.usr_log_acct
                        ,shop_cart_ord.itm_sku_id   
                        ,shop_cart_ord.itm_sku_nm  
                        ,shop_cart_ord.itm_id   
                        ,shop_cart_ord.itm_nm 
                        ,itm_frst_catg_cd
                        ,itm_scnd_catg_cd
                        ,itm_thrd_catg_cd
                        ,shop_cart_ord.shop_sessn_src_kwd

"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, exec_engine='spark',
             retry_with_hive=False, spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
    ])
