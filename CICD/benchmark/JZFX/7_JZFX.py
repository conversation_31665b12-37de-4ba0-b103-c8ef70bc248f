#!/usr/bin/env python3

# 487655

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

dest_table_name = 'benchmark_app_zs_z1702_product_compete_source_notindex'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """', stat_ct_cd='""" + stat_ct_cd + """')
  SELECT t.shop_id,
         t.chan_cd,
         t.pro_type,
         t.pro_id,
         '""" + zst.stat_ct + """' AS stat_ct,
         t.shop_sessn_src_zs_url_frst_catg_cd,
         t.shop_sessn_src_zs_url_scnd_catg_cd,
         t.shop_sessn_src_zs_url_thrd_catg_cd,
         t.uv,
         t.uv / t_all.uv_all,
         t.z_ord_amt
    FROM (SELECT shop_cart_ord.shop_id AS shop_id,
                 CASE
                   WHEN GROUPING__ID IN (93, 61) THEN
                    99
                   ELSE
                    shop_cart_ord.chan_cd
                 END AS chan_cd,
                 CASE
                   WHEN GROUPING__ID IN (93, 95) THEN
                    1
                   ELSE
                    0
                 END AS pro_type,
                 CASE
                   WHEN GROUPING__ID IN (93, 95) THEN
                    shop_cart_ord.itm_sku_id
                   ELSE
                    shop_cart_ord.itm_id
                 END AS pro_id,
                 shop_cart_ord.shop_sessn_src_zs_url_frst_catg_cd,
                 shop_cart_ord.shop_sessn_src_zs_url_scnd_catg_cd,
                 shop_cart_ord.shop_sessn_src_zs_url_thrd_catg_cd,
                 COUNT(DISTINCT shop_cart_ord.brws_uniq_id) AS uv,
                 SUM(CASE
                       WHEN shop_cart_ord.is_ord = 1 THEN
                        shop_cart_ord.ord_amt
                       ELSE
                        0
                     END) AS z_ord_amt
            FROM (SELECT shop_id                            AS shop_id,
                         chan_cd                            AS chan_cd,
                         shop_sessn_src_zs_url_frst_catg_cd AS shop_sessn_src_zs_url_frst_catg_cd,
                         shop_sessn_src_zs_url_scnd_catg_cd AS shop_sessn_src_zs_url_scnd_catg_cd,
                         shop_sessn_src_zs_url_thrd_catg_cd AS shop_sessn_src_zs_url_thrd_catg_cd,
                         --shop_sessn_src_req_parm            AS shop_sessn_src_req_parm,
                         --shop_sessn_src_url                 as shop_sessn_src_url,
                         itm_sku_id                         as itm_sku_id,
                         itm_id                             as itm_id,
                         is_ord                             as is_ord,
                         sale_ord_id                        as sale_ord_id,
                         usr_log_acct                       as usr_log_acct,
                         brws_uniq_id                       as brws_uniq_id,
                         ord_amt                            as ord_amt,
                         sale_qty                           as sale_qty
                    FROM retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_lead_to_cart_ord_det
                   WHERE dt <= '""" + zst.data_day_str + """'
                     AND dt >= '""" + stat_begin + """'
                     AND itm_sku_id > 0
                     AND chan_cd in (20, 1, 2, 3, 4)
                   GROUP BY shop_id,
                            chan_cd,
                            shop_sessn_src_zs_url_frst_catg_cd,
                            shop_sessn_src_zs_url_scnd_catg_cd,
                            shop_sessn_src_zs_url_thrd_catg_cd,
                            --shop_sessn_src_req_parm,
                            --shop_sessn_src_url,
                            itm_sku_id,
                            itm_id,
                            is_ord,
                            sale_ord_id,
                            usr_log_acct,
                            brws_uniq_id,
                            ord_amt,
                            sale_qty) shop_cart_ord
           GROUP BY shop_cart_ord.shop_id,
                    shop_cart_ord.chan_cd,
                    shop_cart_ord.shop_sessn_src_zs_url_frst_catg_cd,
                    shop_cart_ord.shop_sessn_src_zs_url_scnd_catg_cd,
                    shop_cart_ord.shop_sessn_src_zs_url_thrd_catg_cd,
                    shop_cart_ord.itm_id,
                    shop_cart_ord.itm_sku_id GROUPING SETS((shop_cart_ord.shop_id, shop_cart_ord.chan_cd, shop_cart_ord.shop_sessn_src_zs_url_frst_catg_cd, shop_cart_ord.shop_sessn_src_zs_url_scnd_catg_cd, shop_cart_ord.shop_sessn_src_zs_url_thrd_catg_cd, shop_cart_ord.itm_sku_id) --95
                   ,(shop_cart_ord.shop_id, shop_cart_ord.chan_cd, shop_cart_ord.shop_sessn_src_zs_url_frst_catg_cd, shop_cart_ord.shop_sessn_src_zs_url_scnd_catg_cd, shop_cart_ord.shop_sessn_src_zs_url_thrd_catg_cd, shop_cart_ord.itm_id) --77
                   ,(shop_cart_ord.shop_id, shop_cart_ord.shop_sessn_src_zs_url_frst_catg_cd, shop_cart_ord.shop_sessn_src_zs_url_scnd_catg_cd, shop_cart_ord.shop_sessn_src_zs_url_thrd_catg_cd, shop_cart_ord.itm_sku_id) --93
                   ,(shop_cart_ord.shop_id, shop_cart_ord.shop_sessn_src_zs_url_frst_catg_cd, shop_cart_ord.shop_sessn_src_zs_url_scnd_catg_cd, shop_cart_ord.shop_sessn_src_zs_url_thrd_catg_cd, shop_cart_ord.itm_id) --61
                    )) t
    LEFT OUTER JOIN (SELECT shop_id AS shop_id,
                            CASE
                              WHEN GROUPING__ID IN (5, 9) THEN
                               99
                              ELSE
                               chan_cd
                            END AS chan_cd,
                            CASE
                              WHEN GROUPING__ID IN (9, 11) THEN
                               1
                              ELSE
                               0
                            END AS pro_type,
                            CASE
                              WHEN GROUPING__ID IN (9, 11) THEN
                               itm_sku_id
                              ELSE
                               itm_id
                            END AS pro_id,
                            COUNT(DISTINCT brws_uniq_id) AS uv_all
                       FROM retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_lead_to_cart_ord_det
                      WHERE dt <= '""" + zst.data_day_str + """'
                        AND dt >= '""" + stat_begin + """'
                        AND itm_sku_id > 0
                        AND chan_cd in (20, 1, 2, 3, 4)
                      GROUP BY shop_id,
                               chan_cd,
                               itm_id,
                               itm_sku_id GROUPING SETS((shop_id, chan_cd, itm_id) --7
                              ,(shop_id, chan_cd, itm_sku_id) --11
                              ,(shop_id, itm_id) --5
                              ,(shop_id, itm_sku_id) --9
                               )) t_all
      ON t.shop_id = t_all.shop_id
     AND t.chan_cd = t_all.chan_cd
     AND t.pro_type = t_all.pro_type
     AND t.pro_id = t_all.pro_id
;

"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, lzo_compress=True,
             lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             exec_engine='spark',
             retry_with_hive=False, spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
    ])
