#!/usr/bin/env python3

# 492739

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

dest_mid_table_name = 'benchmark_adm_s16_zs_shop_compete_loss_pin'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_mid_table_name + """ PARTITION (dt = '""" + insertDay + """')
SELECT
  /*+ MAPJOIN(compete_conf)*/
  m_shop.usr_log_acct,
  m_shop.shop_id,
  m_shop.itm_frst_catg_cd,
  m_shop.itm_scnd_catg_cd,
  m_shop.is_follow,
  m_shop.chan_addcart_list,
  compete_conf.compete_shop_list
FROM
  (  --只取当日内有浏览无订单的pin
    SELECT
      ta.usr_log_acct,
      ta.shop_id,
      ta.itm_frst_catg_cd,
      ta.itm_scnd_catg_cd,
      ta.is_follow,
      ta.chan_addcart_list,
      ta.dt
    FROM
      (
        SELECT
          traffics.usr_log_acct AS usr_log_acct,
          traffics.shop_id AS shop_id,
          traffics.itm_frst_catg_cd AS itm_frst_catg_cd,
          traffics.itm_scnd_catg_cd AS itm_scnd_catg_cd,
          IF(follow_det.usr_log_acct IS NULL, 0, 1) AS is_follow, --目前是否关注使用全渠道数据
          NVL(m_order.order_num, 0) AS order_num,
          traffics.chan_addcart_list AS chan_addcart_list,
          traffics.dt
        FROM
          (
            SELECT
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              collect_list(named_struct('chan_cd',CAST(chan_cd AS BIGINT), 'is_addcart',CAST(is_addcart AS BIGINT))) AS chan_addcart_list, --pin无订单时取流量上的渠道及各渠道对应的加购,用于算跳失
              dt
            FROM
              (
                SELECT
                  chan_cd,
                  usr_log_acct,
                  shop_id,
                  itm_frst_catg_cd,
                  itm_scnd_catg_cd,
                  MAX(is_add_cart) AS is_addcart,
                  dt
                FROM
                    retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_lead_to_cart_ord_det
                WHERE
                  dt <= '""" + zst.data_day_str + """'
                  AND dt >= '""" + stat_begin + """'
                  AND chan_cd IN(20, 1, 2, 3, 4)
                  AND shop_id IS NOT NULL
                  AND shop_id < 1000000000
                  AND usr_log_acct IS NOT NULL
                  AND usr_log_acct <> ''
                  AND usr_log_acct <> '-'
                  AND itm_frst_catg_cd <> 0
                GROUP BY
                  chan_cd,
                  usr_log_acct,
                  shop_id,
                  itm_frst_catg_cd,
                  itm_scnd_catg_cd,
                  dt
                
                UNION ALL
                
                SELECT
                  99 AS chan_cd,
                  usr_log_acct,
                  shop_id,
                  itm_frst_catg_cd,
                  itm_scnd_catg_cd,
                  MAX(is_add_cart) AS is_addcart,
                  dt
                FROM
                  retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_lead_to_cart_ord_det
                WHERE
                  dt <= '""" + zst.data_day_str + """'
                  AND dt >= '""" + stat_begin + """'
                  AND chan_cd IN(20, 1, 2, 3, 4)
                  AND shop_id IS NOT NULL
                  AND shop_id < 1000000000
                  AND usr_log_acct IS NOT NULL
                  AND usr_log_acct <> ''
                  AND usr_log_acct <> '-'
                  AND itm_frst_catg_cd <> 0
                GROUP BY
                  usr_log_acct,
                  shop_id,
                  itm_frst_catg_cd,
                  itm_scnd_catg_cd,
                  dt
              )
              tra_mid
            GROUP BY
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              dt
          )
          traffics
        LEFT OUTER JOIN
          (  --查询每个pin在每个店铺的是否有加购行为
            SELECT
              shop_id,
              usr_log_acct,
              dt
            FROM
              retail_benchmark_10t.benchmark_gdm_m13_zs_shop_product_follow_det
            WHERE
              dt <= '""" + zst.data_day_str + """'
              AND dt >= '""" + stat_begin + """'
              AND shop_id < 1000000000
            GROUP BY
              shop_id,
              usr_log_acct,
              dt
          )
          follow_det
        ON
          traffics.shop_id = follow_det.shop_id
          AND traffics.usr_log_acct = follow_det.usr_log_acct
          AND traffics.dt = follow_det.dt
        LEFT OUTER JOIN
          (  --查询每个pin在每个店铺的二级类目下的订单数
            SELECT
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              COUNT(DISTINCT sale_ord_id) AS order_num,
              dt
            FROM
              retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det
            WHERE
              dt <= '""" + zst.data_day_str + """'
              AND dt >= '""" + stat_begin + """'
              AND is_deal_ord = 1
              AND shop_id < 1000000000
            GROUP BY
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              dt
          )
          m_order
        ON
          traffics.usr_log_acct = m_order.usr_log_acct
          AND traffics.shop_id = m_order.shop_id
          AND traffics.itm_frst_catg_cd = m_order.itm_frst_catg_cd
          AND traffics.itm_scnd_catg_cd = m_order.itm_scnd_catg_cd
          AND traffics.dt = m_order.dt
      ) ta
    WHERE
      ta.order_num = 0
  )
  m_shop
INNER JOIN
  (  --关联竞店配置表
    SELECT
      currentshopid AS shop_id,
      collect_list(named_struct('shop_id',CAST(competeshopid AS BIGINT), 'shop_nm',competeshopname)) AS compete_shop_list
    FROM
      retail_benchmark_10t.benchmark_fdm_rhea_sz_user_hb_ibd_shop_compete
    WHERE
      dt = '""" + zst.data_day_str + """'
    GROUP BY
      currentshopid
  )
  compete_conf
ON
  m_shop.shop_id = CAST(compete_conf.shop_id AS BIGINT)
;

"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_mid_table_name, sql=sql, lzo_compress=True,
             lzo_index_path=['dt=' + zst.data_day_str], exec_engine='spark',
             retry_with_hive=False, spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
    ])
