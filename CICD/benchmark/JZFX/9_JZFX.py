#!/usr/bin/env python3

# 643620

import sys
import datetime
import json
from ZSTask import ZSTask

zst = ZSTask()
data_day_str = zst.data_day_str
stat_begin = zst.stat_begin
stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
data_day_int = zst.data_day_int
last_month_lastday = datetime.date(datetime.date.today().year, datetime.date.today().month, 1) - datetime.timedelta(
    days=1)  # 上月月末
last_month_firstday = str(datetime.date(last_month_lastday.year, last_month_lastday.month, 1))  # 上月第一天

dict = {}
if len(sys.argv) >= 4:
    dict = json.loads(sys.argv[3].replace('&', ';'))

insertDay=datetime.date.today().strftime("%Y-%m-%d")

dest_table_name = 'benchmark_app_zs_z1703_shop_loss_overview'

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """', stat_ct_cd='""" + stat_ct_cd + """')
select shop_id AS shop_id,
  chan_cd AS chan_cd,
  """ + zst.stat_ct + """ AS stat_ct,
  """ + zst.data_day_int + """ AS stat_ct_stamp,
  count(distinct loss_ord_cust_acct) loss_ord_cust_num,
  sum(loss_ord_amt) AS loss_ord_amt,
  count(distinct dircet_skip_cust_acct) as dircet_skip_cust_num,
  count(distinct collected_skip_cust_acct) as collected_skip_cust_num,
  count(distinct carted_skip_cust_acct) as carted_skip_cust_num,
  count(distinct cause_loss_shop) as cause_loss_shop_num
from 
(SELECT
  t.shop_id AS shop_id,
  t.chan_cd AS chan_cd,
  
  case
    when t.compete_order_num > 0
    then t.usr_log_acct
    else null
  end AS loss_ord_cust_acct,
  sum(t.compete_order_amt) AS loss_ord_amt,
  null as dircet_skip_cust_acct,
  null as collected_skip_cust_acct,
  null as carted_skip_cust_acct,
  
  case
    when t.compete_order_num > 0
    then t.compete_shop_id
    else null
  end AS cause_loss_shop
FROM
  (
    SELECT
      NVL(loss_shop.chan_cd, chan_addcart.chan_cd) AS chan_cd,
      loss_shop.usr_log_acct AS usr_log_acct,
      loss_shop.shop_id AS shop_id,
      loss_shop.itm_frst_catg_cd AS itm_frst_catg_cd,
      loss_shop.itm_scnd_catg_cd AS itm_scnd_catg_cd,
      chan_addcart.is_addcart AS is_addcart,
      loss_shop.is_follow AS is_follow,
      loss_shop.compete_shop_id AS compete_shop_id,
      loss_shop.compete_shop_nm AS compete_shop_nm,
      loss_shop.compete_order_num AS compete_order_num,
      loss_shop.compete_sale_qty AS compete_sale_qty,
      loss_shop.compete_order_amt AS compete_order_amt
    FROM
      (
        SELECT
          c_shop.chan_cd,
          co.usr_log_acct,
          co.shop_id,
          co.itm_frst_catg_cd,
          co.itm_scnd_catg_cd,
          co.is_follow,
          co.chan_addcart_list,
          co.compete_shop_id,
          co.compete_shop_nm,
          NVL(c_shop.order_num, 0) AS compete_order_num,
          NVL(c_shop.sale_qty, 0) AS compete_sale_qty,
          NVL(c_shop.order_amt, 0) AS compete_order_amt
        FROM
          (--展开竞争店铺
            SELECT
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              is_follow,
              chan_addcart_list,
              compete_shop.shop_id AS compete_shop_id,
              compete_shop.shop_nm AS compete_shop_nm,
              dt
            FROM
              retail_benchmark_10t.benchmark_adm_s16_zs_shop_compete_loss_pin t lateral view explode(compete_shop_list) b AS compete_shop
            WHERE
              dt <= '""" + zst.data_day_str + """'
              AND dt >= '""" + stat_begin + """'
          )
          co
        INNER JOIN
          (--用于算竞店订单指标
            SELECT
              chan_cd,
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              COUNT(DISTINCT sale_ord_id) AS order_num,
              SUM(sale_qty) AS sale_qty,
              SUM(ord_amt) AS order_amt,
              dt
            FROM
              retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det
            WHERE
              dt <= '""" + zst.data_day_str + """'
              AND dt >= '""" + stat_begin + """'
              AND is_deal_ord = 1
              AND shop_id < 1000000000
            GROUP BY
              chan_cd,
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              dt
            
            UNION ALL
            
            SELECT
              99 AS chan_cd,
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              COUNT(DISTINCT sale_ord_id) AS order_num,
              SUM(sale_qty) AS sale_qty,
              SUM(ord_amt) AS order_amt,
              dt
            FROM
              retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det
            WHERE
              dt <= '""" + zst.data_day_str + """'
              AND dt >= '""" + stat_begin + """'
              AND is_deal_ord = 1
              AND shop_id < 1000000000
            GROUP BY
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              dt
          )
          c_shop
        ON co.dt = c_shop.dt
          AND co.itm_frst_catg_cd = c_shop.itm_frst_catg_cd
          AND co.itm_scnd_catg_cd = c_shop.itm_scnd_catg_cd
          AND co.usr_log_acct = c_shop.usr_log_acct
          AND CAST(co.compete_shop_id AS bigint) = c_shop.shop_id
      )
      loss_shop lateral view explode(
        case
          when loss_shop.chan_cd is null
          then loss_shop.chan_addcart_list
          else array(named_struct('chan_cd', CAST(0 AS BIGINT), 'is_addcart', CAST(0 AS BIGINT)))
        end) b AS chan_addcart
  )
  t
GROUP BY
     t.chan_cd,
     t.shop_id,
     case
    when t.compete_order_num > 0
    then t.usr_log_acct
    else null
  end,
  case
    when t.compete_order_num > 0
    then t.compete_shop_id
    else null
  end
      
   
   union all

SELECT
  t.shop_id AS shop_id,
  t.chan_cd AS chan_cd,
  null AS loss_ord_cust_acct,
  0 AS loss_ord_amt,

  case
    when t.is_addcart = 0
      and t.is_follow = 0
    then t.usr_log_acct
    else null
  end AS dircet_skip_cust_acct,
  
  case
    when t.is_follow > 0
    then t.usr_log_acct
    else null
  end  AS collected_skip_cust_acct,
  
  case
    when t.is_addcart > 0
    then t.usr_log_acct
    else null
  end AS carted_skip_cust_acct,
  null AS cause_loss_shop
FROM
  (
    SELECT
      NVL(loss_shop.chan_cd, chan_addcart.chan_cd) AS chan_cd,
      loss_shop.usr_log_acct AS usr_log_acct,
      loss_shop.shop_id AS shop_id,
      loss_shop.itm_frst_catg_cd AS itm_frst_catg_cd,
      loss_shop.itm_scnd_catg_cd AS itm_scnd_catg_cd,
      chan_addcart.is_addcart AS is_addcart,
      loss_shop.is_follow AS is_follow,
      loss_shop.compete_shop_id AS compete_shop_id,
      loss_shop.compete_shop_nm AS compete_shop_nm
    FROM
      (
        SELECT
          c_shop.chan_cd,
          co.usr_log_acct,
          co.shop_id,
          co.itm_frst_catg_cd,
          co.itm_scnd_catg_cd,
          co.is_follow,
          co.chan_addcart_list,
          co.compete_shop_id,
          co.compete_shop_nm
        FROM
          (--展开竞争店铺
            SELECT
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              is_follow,
              chan_addcart_list,
              compete_shop.shop_id AS compete_shop_id,
              compete_shop.shop_nm AS compete_shop_nm,
              dt
            FROM
              retail_benchmark_10t.benchmark_adm_s16_zs_shop_compete_loss_pin t lateral view explode(compete_shop_list) b AS compete_shop
            WHERE
              dt <= '""" + zst.data_day_str + """'
              AND dt >= '""" + stat_begin + """'
          )
          co
        LEFT JOIN
          (--用于判断流失
            SELECT
              99 AS chan_cd,
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              dt
            FROM
              retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det
            WHERE
              dt <= '""" + zst.data_day_str + """'
              AND dt >= '""" + stat_begin + """'
              AND is_deal_ord = 1
              AND shop_id < 1000000000
            GROUP BY
              usr_log_acct,
              shop_id,
              itm_frst_catg_cd,
              itm_scnd_catg_cd,
              dt
          )
          c_shop
        ON co.dt = c_shop.dt
          AND co.itm_frst_catg_cd = c_shop.itm_frst_catg_cd
          AND co.itm_scnd_catg_cd = c_shop.itm_scnd_catg_cd
          AND co.usr_log_acct = c_shop.usr_log_acct
          AND CAST(co.compete_shop_id AS bigint) = c_shop.shop_id
      where c_shop.usr_log_acct is null
      )
      loss_shop lateral view explode(
        case
          when loss_shop.chan_cd is null
          then loss_shop.chan_addcart_list
          else array(named_struct('chan_cd', CAST(0 AS BIGINT), 'is_addcart', CAST(0 AS BIGINT)))
        end) b AS chan_addcart
  )
  t
GROUP BY
     t.chan_cd,
     t.shop_id,
     case
    when t.is_addcart = 0
      and t.is_follow = 0
    then t.usr_log_acct
    else null
  end,
  case
    when t.is_follow > 0
    then t.usr_log_acct
    else null
  end,
  case
    when t.is_addcart > 0
    then t.usr_log_acct
    else null
  end
      
) tt_result
group by chan_cd,shop_id

   ;

"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, lzo_compress=True,
             lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             exec_engine='spark', retry_with_hive=False, spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
            ])
