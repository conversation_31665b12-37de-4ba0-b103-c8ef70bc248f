#!/usr/bin/env python3

# 517630

import os, sys

sys.path.append(os.getenv('HIVE_TASK'))
import datetime
from ZSTask import ZSTask

zst = ZSTask()

os.environ["HADOOP_CLIENT_OPTS"] = "-Xmx16g"

dest_table_name = 'benchmark_adm_s14_zs_shop_traffics_lead_to_cart_det'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

data_day_str = zst.data_day_str

yesterday = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=1), "%Y-%m-%d")

proTable = """retail_benchmark_10t.benchmark_dim_jd_jdb_zmd_acct"""

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """')
SELECT
  req_tm,
  chan_cd,
  sessn_id,
  seq_num,
  brws_uniq_id,
  usr_log_acct,
  shop_id,
  shop_typ_cd,
  itm_sku_id,
  itm_sku_nm,
  itm_id,
  itm_nm,
  itm_frst_catg_cd,
  itm_scnd_catg_cd,
  itm_thrd_catg_cd,
  brand_cd,
  url_typ_cd,
  url_attr_val,
  url,
  req_parm,
  shop_sessn_src_url,
  shop_sessn_src_req_parm,
  shop_sessn_src_zs_url_frst_catg_cd,
  shop_sessn_src_zs_url_scnd_catg_cd,
  shop_sessn_src_zs_url_thrd_catg_cd,
  is_add_cart as is_add_cart, ----商智SKU粒度：引导加购口径
  shop_sessn_id,
  shop_sessn_par_seq_num,
  addcart_req_time,
  ref_shop_id,
  ref_itm_sku_id,
  ref_itm_sku_nm,
  ref_zs_url_frst_catg_cd,
  ref_zs_url_scnd_catg_cd,
  ref_zs_url_thrd_catg_cd,
  tm_on_page,
  shop_frst_req_flg,
  shop_lst_req_flg,
  zs_url_thrd_catg_cd,
  ref_url,
  ref_req_parm,
  ref_seq_num, ---上一页访问序号
  ref_itm_id,
  ref_itm_nm,
  user_site_addr,
  user_site_cy_nm,
  user_site_prov_nm,
  user_site_city_nm,
  shop_sessn_src_kwd,
  shop_sessn_src_active,
  cart_itm_sku_id,
  is_add_cart_spu, ---商智SPU粒度：引导加购
  main_sku_id
FROM
(
  SELECT
    shop_traffics.req_tm as req_tm,
    shop_traffics.chan_cd as chan_cd,
    shop_traffics.sessn_id as sessn_id,
    shop_traffics.seq_num as seq_num,
    shop_traffics.brws_uniq_id as brws_uniq_id,
    shop_traffics.usr_log_acct as usr_log_acct,
    shop_traffics.shop_id as shop_id,
    shop_traffics.shop_typ_cd as shop_typ_cd,
    shop_traffics.itm_sku_id as itm_sku_id,
    shop_traffics.itm_sku_nm as itm_sku_nm,
    shop_traffics.itm_id as itm_id,
    shop_traffics.itm_nm as itm_nm,
    shop_traffics.itm_frst_catg_cd as itm_frst_catg_cd,
    shop_traffics.itm_scnd_catg_cd as itm_scnd_catg_cd,
    shop_traffics.itm_thrd_catg_cd as itm_thrd_catg_cd,
    shop_traffics.brand_cd as brand_cd,
    shop_traffics.url_typ_cd as url_typ_cd,
    shop_traffics.url_attr_val as url_attr_val,
    shop_traffics.url,
    shop_traffics.req_parm,
    shop_traffics.shop_sessn_src_url,
    shop_traffics.shop_sessn_src_req_parm,
    shop_traffics.shop_sessn_src_zs_url_frst_catg_cd as shop_sessn_src_zs_url_frst_catg_cd,
    shop_traffics.shop_sessn_src_zs_url_scnd_catg_cd as shop_sessn_src_zs_url_scnd_catg_cd,
    shop_traffics.shop_sessn_src_zs_url_thrd_catg_cd as shop_sessn_src_zs_url_thrd_catg_cd,
    shop_sessn_id as shop_sessn_id,
    shop_traffics.shop_sessn_par_seq_num,
    add_cart_info.request_time as addcart_req_time,
    shop_traffics.ref_shop_id,
    shop_traffics.ref_itm_sku_id,
    shop_traffics.ref_itm_sku_nm,
    shop_traffics.ref_zs_url_frst_catg_cd,
    shop_traffics.ref_zs_url_scnd_catg_cd,
    shop_traffics.ref_zs_url_thrd_catg_cd,
    shop_traffics.tm_on_page,
    shop_traffics.shop_frst_req_flg,
    shop_traffics.shop_lst_req_flg,
    shop_traffics.zs_url_thrd_catg_cd,
    shop_traffics.ref_url,
    shop_traffics.ref_req_parm,
    shop_traffics.ref_seq_num, ---上一页访问序号
    shop_traffics.ref_itm_id,
    shop_traffics.ref_itm_nm,
    shop_traffics.user_site_addr,
    shop_traffics.user_site_cy_nm,
    shop_traffics.user_site_prov_nm,
    shop_traffics.user_site_city_nm,
    shop_traffics.shop_sessn_src_kwd,
    shop_traffics.shop_sessn_src_active,
    add_cart_info.item_sku_id as cart_itm_sku_id,
    case
      when add_cart_info.browser_uniq_id is not null
        and shop_traffics.req_tm < add_cart_info.request_time ----浏览时间先于加购时间
      then 1
      else 0
    end as is_add_cart_spu, ---商智SPU粒度：引导加购
    shop_traffics.main_sku_id,
    case
      when add_cart_info.browser_uniq_id is not null
        and shop_traffics.req_tm < add_cart_info.request_time ----浏览时间先于加购时间
        and shop_traffics.itm_sku_id = add_cart_info.item_sku_id
      then 1
      else 0
    end as is_add_cart    
  from
    (
      select
        /*+MAPJOIN(black_acct)*/
        shop_src_traffics.*
      from
        (
          select
            sessn_id,
            seq_num,
            req_tm,
            chan_cd,
            shop_id,
            shop_typ_cd,
            itm_sku_id,
            itm_sku_nm,
            itm_id,
            itm_nm,
            brand_cd,
            itm_frst_catg_cd,
            itm_scnd_catg_cd,
            itm_thrd_catg_cd,
            zs_url_thrd_catg_cd,
            brws_uniq_id,
            usr_log_acct,
            url_typ_cd,
            url_attr_val,
            url,
            req_parm,
            ref_shop_id,
            ref_seq_num,
            ref_itm_sku_id,
            ref_itm_sku_nm,
            ref_itm_id,
            ref_itm_nm,
            ref_zs_url_frst_catg_cd,
            ref_zs_url_scnd_catg_cd,
            ref_zs_url_thrd_catg_cd,
            ref_url,
            ref_req_parm,
            tm_on_page,
            shop_sessn_id,
            shop_sessn_src_url,
            shop_sessn_src_req_parm,
            shop_sessn_par_seq_num,
            shop_frst_req_flg,
            shop_lst_req_flg,
            shop_sessn_src_zs_url_frst_catg_cd,
            shop_sessn_src_zs_url_scnd_catg_cd,
            shop_sessn_src_zs_url_thrd_catg_cd,
            user_site_addr,
            user_site_cy_nm,
            user_site_prov_nm,
            user_site_city_nm,
            shop_sessn_src_kwd,
            shop_sessn_src_active,
            main_sku_id,
            case when shop_id >0 and shop_id < **********
                 then itm_id
                 else main_sku_id
            end as spu_id,
            case
                      when usr_log_acct is not null
                           and usr_log_acct != ''
                           and Upper(usr_log_acct) != 'NULL'
                          then usr_log_acct
                          else brws_uniq_id
                       end  as black_key  
          FROM
            retail_benchmark_10t.benchmark_adm_s14_zs_all_chan_shop_traffics
          WHERE
            dt <= '""" + zst.data_day_str + """'
            AND dt >= '""" + zst.data_day_str + """'
        )
        shop_src_traffics
      left join
        (
          select
            account as usr_log_acct
          from
            """ + proTable + """
          group by
            account
          
          union all
          
          select
            usr_log_acct
          from
            retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_pin_black_spu
          where
            dt = '""" + zst.data_day_str + """'
          group by
            usr_log_acct
        )
        black_acct
      on
        shop_src_traffics.black_key = black_acct.usr_log_acct
      where
        black_acct.usr_log_acct is null ----过滤掉家电京东帮专卖店账号和流量异常账号，避免数据倾斜
    )
    shop_traffics
    --全平台加购模型
  left join
    (
      select 
        MAX(request_time) as request_time,
        chan_cd,
        shop_id,
        browser_uniq_id,
        item_sku_id,
        case
          when shop_id > 0 and shop_id < **********
          then item_id
          else main_sku_id
        end as spu_id
      FROM retail_benchmark_10t.benchmark_adm_s14_zs_shop_online_log_cart_d
      where
        dt = '""" + zst.data_day_str + """'
      GROUP BY 
        chan_cd,
        shop_id,
        browser_uniq_id,
        item_sku_id,
        case
          when shop_id > 0 and shop_id < **********
          then item_id
          else main_sku_id
        end
    )
    add_cart_info ON shop_traffics.brws_uniq_id = add_cart_info.browser_uniq_id
    AND (CASE
        --原WHEN shop_traffics.chan_cd = 3  AND shop_traffics.shop_sessn_src_zs_url_frst_catg_cd = 7
        --THEN 5
        --现
          WHEN shop_traffics.shop_sessn_src_zs_url_scnd_catg_cd = 6038
          THEN 5
        ELSE shop_traffics.chan_cd 
       END) = add_cart_info.chan_cd
    AND shop_traffics.shop_id = add_cart_info.shop_id
    AND shop_traffics.spu_id = add_cart_info.spu_id
  )traf_lead_cart
WHERE is_add_cart_spu = 1
GROUP BY
  req_tm,
  chan_cd,
  sessn_id,
  seq_num,
  brws_uniq_id,
  usr_log_acct,
  shop_id,
   shop_typ_cd,
  itm_sku_id,
  itm_sku_nm,
  itm_id,
  itm_nm,
  itm_frst_catg_cd,
  itm_scnd_catg_cd,
  itm_thrd_catg_cd,
  brand_cd,
  url_typ_cd,
  url_attr_val,
  url,
  req_parm,
  shop_sessn_src_url,
  shop_sessn_src_req_parm,
  shop_sessn_src_zs_url_frst_catg_cd,
  shop_sessn_src_zs_url_scnd_catg_cd,
  shop_sessn_src_zs_url_thrd_catg_cd,
  shop_sessn_id,
  shop_sessn_par_seq_num,
  addcart_req_time,
  ref_shop_id,
  ref_itm_sku_id,
  ref_itm_sku_nm,
  ref_zs_url_frst_catg_cd,
  ref_zs_url_scnd_catg_cd,
  ref_zs_url_thrd_catg_cd,
  tm_on_page,
  shop_frst_req_flg,
  shop_lst_req_flg,
  zs_url_thrd_catg_cd,
  ref_url,
  ref_req_parm,
  ref_seq_num, ---上一页访问序号
  ref_itm_id,
  ref_itm_nm,
  user_site_addr,
  user_site_cy_nm,
  user_site_prov_nm,
  user_site_city_nm,
  shop_sessn_src_kwd,
  shop_sessn_src_active,
  cart_itm_sku_id,
  is_add_cart_spu, ---商智SPU粒度：引导加购
  main_sku_id,
  is_add_cart  

;
"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql
             , exec_engine='spark'
             , retry_with_hive=False
             , spark_resource_level='medium'
             , spark_args=['--conf spark.sql.parser.quotedRegexColumnNames=false'
        , '--conf spark.executor.instances=300'
        , '--conf spark.executor.cores=4'
        , '--conf spark.executor.memory=16g'
        , '--conf spark.sql.shuffle.partitions=1500'
        , '--conf spark.dynamicAllocation.maxExecutors=400'
        , '--conf spark.sql.autoBroadcastJoinThreshold=67108864'
        , '--conf spark.sql.hive.merge.smallfile.parallelism.v2=8'
        , "--conf spark.sql.hive.mergeFiles=true",
                           "--conf spark.sql.adaptive.enabled=true",
                           "--conf spark.sql.adaptive.repartition.enabled=true"
                           ]
             )
