#!/usr/bin/env python3

# 516578

import os
import datetime
from ZSTask import ZSTask

zst = ZSTask()

os.environ["HADOOP_CLIENT_OPTS"] = "-Xmx8g"

dest_table_name = 'benchmark_adm_s14_zs_shop_traffics_pin_black_spu'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """')
select
  *
from
  (
    select
      all_user.chan_cd,
      all_user.shop_id,
      all_user.usr_log_acct,
      all_user.spu_id,
      sum(coalesce(all_user.pv, 0)) as pv,
      sum(coalesce(all_user.ord_pv, 0)) as ord_pv,
      sum(coalesce(add_cart_ord.cart_pv, 0)) as cart_pv
    from
      (
        select
          all_user_pv.chan_cd,
          all_user_pv.shop_id,
          usr_log_acct,
          all_user_pv.spu_id,
          sum(coalesce(all_user_pv.pv, 0)) as pv,
          sum(coalesce(all_user_pv.ord_pv, 0)) as ord_pv
        from
          (----流量
            select
              case
                        when usr_log_acct is not null
                          and usr_log_acct != ''
                          and Upper(usr_log_acct) != 'NULL'
                        then usr_log_acct
                        else brws_uniq_id
                      end as usr_log_acct,
                        case 
                        --原when chan_cd = 3 and shop_sessn_src_zs_url_frst_catg_cd =7
                        --现
                    WHEN shop_sessn_src_zs_url_scnd_catg_cd = 6038   
                            then 5
                            else chan_cd
                            end as chan_cd, -- 5 开普勒
                    
              shop_id,
              case
                when shop_id > 0
                  and shop_id < 1000000000
                then itm_id
                else main_sku_id
              end as spu_id,
              count(1) as pv,
              0 as ord_pv
            from
              retail_benchmark_10t.benchmark_adm_s14_zs_all_chan_shop_traffics
            WHERE
                dt <= '""" + zst.data_day_str + """'
        AND dt >= '""" + zst.data_day_str + """'
            group by
              case
                        when usr_log_acct is not null
                          and usr_log_acct != ''
                          and Upper(usr_log_acct) != 'NULL'
                        then usr_log_acct
                        else brws_uniq_id
                      end,
                        case 
                        --原when chan_cd = 3 and shop_sessn_src_zs_url_frst_catg_cd =7
                        --现
                            WHEN shop_sessn_src_zs_url_scnd_catg_cd = 6038
                            then 5
                            else chan_cd
                            end,
              shop_id,
              case
                when shop_id > 0
                  and shop_id < 1000000000
                then itm_id
                else main_sku_id
              end
            
            union all
            
            ----订单相关
            select
              usr_log_acct,
                           case
                              when chan_cd = 3 and second_chan_cd = '31'
                              then 5
                              else chan_cd 
                           end as chan_cd,
              shop_id,
              case
                when shop_id > 0
                  and shop_id < 1000000000
                then itm_id
                else main_sku_id
              end as spu_id,
              0 as pv,
              count(1) as ord_pv
            FROM
              retail_benchmark_10t.benchmark_adm_m04_zs_oldnew_ord_det
            WHERE
              dt <= '""" + zst.data_day_str + """'
        AND dt >= '""" + zst.data_day_str + """'
              AND
              (
                is_deal_ord = 1
                or sz_xiadan_flag = 1
              )
              AND sale_ord_dt = '""" + zst.data_day_str + """'
            group by
              usr_log_acct,
                           case
                              when chan_cd = 3 and second_chan_cd = '31'
                              then 5
                              else chan_cd 
                           end,
              shop_id,
              case
                when shop_id > 0
                  and shop_id < 1000000000
                then itm_id
                else main_sku_id
              end
          )
          all_user_pv
        group by
          all_user_pv.usr_log_acct,
          all_user_pv.chan_cd,
          all_user_pv.shop_id,
          all_user_pv.spu_id
      )
      all_user
    left join
      (----加购
        select
          case
                    when user_log_acct  is not null
                      and user_log_acct  != ''
                      and Upper(user_log_acct ) != 'NULL'
                    then user_log_acct 
                    else browser_uniq_id
                  end as user_log_acct,
          chan_cd,
          case
            when shop_id > 0 and shop_id < 1000000000
            then item_id
            else main_sku_id
          end as spu_id,
          count(1) as cart_pv
        FROM retail_benchmark_10t.benchmark_adm_s14_zs_shop_online_log_cart_d
        where
          dt = '""" + zst.data_day_str + """'
        group by
          case
                    when user_log_acct  is not null
                      and user_log_acct  != ''
                      and Upper(user_log_acct ) != 'NULL'
                    then user_log_acct 
                    else browser_uniq_id
                  end,
          chan_cd,
          case
            when shop_id > 0 and shop_id < 1000000000
            then item_id
            else main_sku_id
          end  
      )
      add_cart_ord on add_cart_ord.user_log_acct = all_user.usr_log_acct
      and add_cart_ord.chan_cd = all_user.chan_cd
      and add_cart_ord.spu_id = all_user.spu_id
    group by
      all_user.chan_cd,
      all_user.shop_id,
      all_user.usr_log_acct,
      all_user.spu_id
  )
  aas
where
  pv * ord_pv * cart_pv > 7000000 ---总条数大于2kw视为黑名单账号
  or pv * ord_pv > 7000000
  or pv * cart_pv > 7000000
;


"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql
             , exec_engine='spark'
             , retry_with_hive=False
             , spark_resource_level='medium'
             , spark_args=['--conf spark.sql.parser.quotedRegexColumnNames=false'
        , '--conf spark.executor.instances=300'
        , '--conf spark.executor.cores=4'
        , '--conf spark.executor.memory=16g'
        , '--conf spark.sql.shuffle.partitions=1500'
        , '--conf spark.dynamicAllocation.maxExecutors=400'
        , '--conf spark.sql.autoBroadcastJoinThreshold=67108864'
        , '--conf spark.sql.hive.merge.smallfile.parallelism.v2=8'
        , "--conf spark.sql.hive.mergeFiles=true",
                           "--conf spark.sql.adaptive.enabled=true",
                           "--conf spark.sql.adaptive.repartition.enabled=true"
                           ]
             )
