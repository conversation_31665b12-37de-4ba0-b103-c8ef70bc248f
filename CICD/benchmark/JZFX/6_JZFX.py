#!/usr/bin/env python3

# 516577

import os
import datetime
from ZSTask import ZSTask

zst = ZSTask()

os.environ["HADOOP_CLIENT_OPTS"] = "-Xmx16g"

dest_table_name = 'benchmark_adm_s14_zs_shop_traffics_lead_to_cart_ord_det'

##zst.data_day_str = '2020-11-11'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """')

  select 
    req_tm,
    chan_cd,
    sessn_id,
    seq_num,
    brws_uniq_id,
    usr_log_acct,
    shop_id,
    shop_typ_cd,
    itm_sku_id,
    itm_sku_nm,
    itm_id,
    itm_nm,
    itm_frst_catg_cd,
    itm_scnd_catg_cd,
    itm_thrd_catg_cd,
    brand_cd,
    url_typ_cd,
    url_attr_val,
    url,
    req_parm,
    shop_sessn_src_url,
    shop_sessn_src_req_parm,
    shop_sessn_src_zs_url_frst_catg_cd,
    shop_sessn_src_zs_url_scnd_catg_cd,
    shop_sessn_src_zs_url_thrd_catg_cd,
    par_sale_ord_id,
    sale_ord_id,
    sale_ord_tm,
    ord_amt,
    sale_qty,
    is_ord,
    is_add_cart,
    shop_sessn_id,
    shop_sessn_par_seq_num,
    addcart_req_time,
    ref_shop_id,
    ref_itm_sku_id,
    ref_itm_sku_nm,
    ref_zs_url_frst_catg_cd,
    ref_zs_url_scnd_catg_cd,
    ref_zs_url_thrd_catg_cd,
    tm_on_page,
    shop_frst_req_flg,
    shop_lst_req_flg,
    zs_url_thrd_catg_cd,
    ref_url,
    ref_req_parm,
    is_follow,
    ref_seq_num,
    is_vld_ord,
    chk_acct_tm,
    ref_itm_id,
    ref_itm_nm,
    user_site_addr,
    user_site_cy_nm,
    user_site_prov_nm,
    user_site_city_nm,
    shop_sessn_src_kwd,
    all.shop_sessn_src_active as shop_sessn_src_active,
    is_allnew_flag,
    is_180new_flag,
    is_730new_flag,
    ord_itm_sku_id,
    cart_itm_sku_id,
    is_ord_spu,
    is_add_cart_spu,
    is_vld_ord_spu,
    main_sku_id,
  case when is_ord = 0 and is_add_cart = 0 and is_follow = 0 and is_vld_ord = 0 and is_ord_spu = 0 and is_add_cart_spu = 0 and is_vld_ord_spu = 0 then 0 else 1 end as sz_instr_flag --有引导效果的标记为1
  from 
  (
    SELECT
      COALESCE(traf_lead_ord.req_tm, traf_lead_cart.req_tm) as req_tm,
      COALESCE(traf_lead_ord.chan_cd, traf_lead_cart.chan_cd) as chan_cd,
      COALESCE(traf_lead_ord.sessn_id, traf_lead_cart.sessn_id) as sessn_id,
      COALESCE(traf_lead_ord.seq_num, traf_lead_cart.seq_num) as seq_num,
      COALESCE(traf_lead_ord.brws_uniq_id, traf_lead_cart.brws_uniq_id) as brws_uniq_id,
      COALESCE(traf_lead_ord.usr_log_acct, traf_lead_cart.usr_log_acct) as usr_log_acct,
      COALESCE(traf_lead_ord.shop_id, traf_lead_cart.shop_id) as shop_id,
      COALESCE(traf_lead_ord.shop_typ_cd, traf_lead_cart.shop_typ_cd) as shop_typ_cd,
      COALESCE(traf_lead_ord.itm_sku_id, traf_lead_cart.itm_sku_id) as itm_sku_id,
      COALESCE(traf_lead_ord.itm_sku_nm, traf_lead_cart.itm_sku_nm) as itm_sku_nm,
      COALESCE(traf_lead_ord.itm_id, traf_lead_cart.itm_id) as itm_id,
      COALESCE(traf_lead_ord.itm_nm, traf_lead_cart.itm_nm) as itm_nm,
      COALESCE(traf_lead_ord.itm_frst_catg_cd, traf_lead_cart.itm_frst_catg_cd) as itm_frst_catg_cd,
      COALESCE(traf_lead_ord.itm_scnd_catg_cd, traf_lead_cart.itm_scnd_catg_cd) as itm_scnd_catg_cd,
      COALESCE(traf_lead_ord.itm_thrd_catg_cd, traf_lead_cart.itm_thrd_catg_cd) as itm_thrd_catg_cd,
      COALESCE(traf_lead_ord.brand_cd, traf_lead_cart.brand_cd) as brand_cd,
      COALESCE(traf_lead_ord.url_typ_cd, traf_lead_cart.url_typ_cd) as url_typ_cd,
      COALESCE(traf_lead_ord.url_attr_val, traf_lead_cart.url_attr_val) as url_attr_val,
      COALESCE(traf_lead_ord.url, traf_lead_cart.url) as url,
      COALESCE(traf_lead_ord.req_parm, traf_lead_cart.req_parm) as req_parm,
      COALESCE(traf_lead_ord.shop_sessn_src_url, traf_lead_cart.shop_sessn_src_url) as shop_sessn_src_url,
      COALESCE(traf_lead_ord.shop_sessn_src_req_parm, traf_lead_cart.shop_sessn_src_req_parm) as shop_sessn_src_req_parm,
      COALESCE(traf_lead_ord.shop_sessn_src_zs_url_frst_catg_cd, traf_lead_cart.shop_sessn_src_zs_url_frst_catg_cd) as shop_sessn_src_zs_url_frst_catg_cd,
      COALESCE(traf_lead_ord.shop_sessn_src_zs_url_scnd_catg_cd, traf_lead_cart.shop_sessn_src_zs_url_scnd_catg_cd) as shop_sessn_src_zs_url_scnd_catg_cd,
      COALESCE(traf_lead_ord.shop_sessn_src_zs_url_thrd_catg_cd, traf_lead_cart.shop_sessn_src_zs_url_thrd_catg_cd) as shop_sessn_src_zs_url_thrd_catg_cd,
      traf_lead_ord.par_sale_ord_id AS par_sale_ord_id,
      traf_lead_ord.sale_ord_id AS sale_ord_id,
      traf_lead_ord.sale_ord_tm AS sale_ord_tm,
      traf_lead_ord.ord_amt AS ord_amt,
      traf_lead_ord.sale_qty AS sale_qty,
      COALESCE(traf_lead_ord.is_ord, '0') as is_ord, ---商智SKU粒度：引导成交口径
      COALESCE(traf_lead_cart.is_add_cart, '0') as is_add_cart, ----商智SKU粒度：引导加购口径
      COALESCE(traf_lead_ord.shop_sessn_id, traf_lead_cart.shop_sessn_id) as shop_sessn_id,
      COALESCE(traf_lead_ord.shop_sessn_par_seq_num, traf_lead_cart.shop_sessn_par_seq_num) as shop_sessn_par_seq_num,
      traf_lead_cart.addcart_req_time as addcart_req_time,
      COALESCE(traf_lead_ord.ref_shop_id, traf_lead_cart.ref_shop_id) as ref_shop_id,
      COALESCE(traf_lead_ord.ref_itm_sku_id, traf_lead_cart.ref_itm_sku_id) as ref_itm_sku_id,
      COALESCE(traf_lead_ord.ref_itm_sku_nm, traf_lead_cart.ref_itm_sku_nm) as ref_itm_sku_nm,
      COALESCE(traf_lead_ord.ref_zs_url_frst_catg_cd, traf_lead_cart.ref_zs_url_frst_catg_cd) as ref_zs_url_frst_catg_cd,
      COALESCE(traf_lead_ord.ref_zs_url_scnd_catg_cd, traf_lead_cart.ref_zs_url_scnd_catg_cd) as ref_zs_url_scnd_catg_cd,
      COALESCE(traf_lead_ord.ref_zs_url_thrd_catg_cd, traf_lead_cart.ref_zs_url_thrd_catg_cd) as ref_zs_url_thrd_catg_cd,
      COALESCE(traf_lead_ord.tm_on_page, traf_lead_cart.tm_on_page) as tm_on_page,
      COALESCE(traf_lead_ord.shop_frst_req_flg, traf_lead_cart.shop_frst_req_flg) as shop_frst_req_flg,
      COALESCE(traf_lead_ord.shop_lst_req_flg, traf_lead_cart.shop_lst_req_flg) as shop_lst_req_flg,
      COALESCE(traf_lead_ord.zs_url_thrd_catg_cd, traf_lead_cart.zs_url_thrd_catg_cd) as zs_url_thrd_catg_cd,
      COALESCE(traf_lead_ord.ref_url, traf_lead_cart.ref_url) as ref_url,
      COALESCE(traf_lead_ord.ref_req_parm, traf_lead_cart.ref_req_parm) as ref_req_parm,
      COALESCE(traf_lead_ord.is_follow, '0') as is_follow,
      COALESCE(traf_lead_ord.ref_seq_num, traf_lead_cart.ref_seq_num) as ref_seq_num, ---上一页访问序号
      COALESCE(traf_lead_ord.is_vld_ord, '0') as is_vld_ord, -------商智SKU粒度：下单口径
      traf_lead_ord.chk_acct_tm,
      COALESCE(traf_lead_ord.ref_itm_id, traf_lead_cart.ref_itm_id) as ref_itm_id,
      COALESCE(traf_lead_ord.ref_itm_nm, traf_lead_cart.ref_itm_nm) as ref_itm_nm,
      COALESCE(traf_lead_ord.user_site_addr, traf_lead_cart.user_site_addr) as user_site_addr,
      COALESCE(traf_lead_ord.user_site_cy_nm, traf_lead_cart.user_site_cy_nm) as user_site_cy_nm,
      COALESCE(traf_lead_ord.user_site_prov_nm, traf_lead_cart.user_site_prov_nm) as user_site_prov_nm,
      COALESCE(traf_lead_ord.user_site_city_nm, traf_lead_cart.user_site_city_nm) as user_site_city_nm,
      COALESCE(traf_lead_ord.shop_sessn_src_kwd, traf_lead_cart.shop_sessn_src_kwd) as shop_sessn_src_kwd,
      COALESCE(traf_lead_ord.shop_sessn_src_active, traf_lead_cart.shop_sessn_src_active) as shop_sessn_src_active,
      COALESCE(is_allnew_flag, '-1') as is_allnew_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
      COALESCE(is_180new_flag, '-1') as is_180new_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
      COALESCE(is_730new_flag, '-1') as is_730new_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
      ord_itm_sku_id,
      cart_itm_sku_id,
      COALESCE(is_ord_spu, '0') as is_ord_spu, ---商智SPU粒度：引导成交口径
      COALESCE(is_add_cart_spu, '0') as is_add_cart_spu, ---商智SPU粒度：引导加购
      COALESCE(is_vld_ord_spu, '0') as is_vld_ord_spu, ---商智SPU粒度：引导下单口径
      COALESCE(traf_lead_ord.main_sku_id, traf_lead_cart.main_sku_id) as main_sku_id
    FROM
      (
        select
          req_tm,
          chan_cd,
          sessn_id,
          seq_num,
          brws_uniq_id,
          usr_log_acct,
          shop_id,
          shop_typ_cd,
          itm_sku_id,
          itm_sku_nm,
          itm_id,
          itm_nm,
          itm_frst_catg_cd,
          itm_scnd_catg_cd,
          itm_thrd_catg_cd,
          brand_cd,
          url_typ_cd,
          url_attr_val,
          url,
          req_parm,
          shop_sessn_src_url,
          shop_sessn_src_req_parm,
          shop_sessn_src_zs_url_frst_catg_cd,
          shop_sessn_src_zs_url_scnd_catg_cd,
          shop_sessn_src_zs_url_thrd_catg_cd,
          par_sale_ord_id,
          sale_ord_id,
          sale_ord_tm,
          ord_amt,
          sale_qty,
          is_ord, ---商智SKU粒度：引导成交口径
          shop_sessn_id,
          shop_sessn_par_seq_num,
          ref_shop_id,
          ref_itm_sku_id,
          ref_itm_sku_nm,
          case when shop_sessn_src_zs_url_frst_catg_cd=1 then shop_sessn_src_zs_url_frst_catg_cd else ref_zs_url_frst_catg_cd end as ref_zs_url_frst_catg_cd,
          case when shop_sessn_src_zs_url_frst_catg_cd=1 then shop_sessn_src_zs_url_scnd_catg_cd else ref_zs_url_scnd_catg_cd end as ref_zs_url_scnd_catg_cd,
          case when shop_sessn_src_zs_url_frst_catg_cd=1 then shop_sessn_src_zs_url_thrd_catg_cd else ref_zs_url_thrd_catg_cd end as ref_zs_url_thrd_catg_cd,
          tm_on_page,
          shop_frst_req_flg,
          shop_lst_req_flg,
          zs_url_thrd_catg_cd,
          ref_url,
          ref_req_parm,
          is_follow,
          ref_seq_num, ---上一页访问序号
          is_vld_ord, -------商智SKU粒度：下单口径
          chk_acct_tm,
          ref_itm_id,
          ref_itm_nm,
          user_site_addr,
          user_site_cy_nm,
          user_site_prov_nm,
          user_site_city_nm,
          shop_sessn_src_kwd,
          shop_sessn_src_active,
          is_allnew_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
          is_180new_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
          is_730new_flag, -- 1：成交新客户 0：成交老客户 '-1'：非成交客户
          ord_itm_sku_id,
          is_ord_spu, ---商智SPU粒度：引导成交口径
          is_vld_ord_spu, ---商智SPU粒度：引导下单口径
          main_sku_id
        from retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_lead_to_ord_det
        where dt='""" + zst.data_day_str + """'
          )traf_lead_ord
    left join 
      (
        SELECT
          req_tm,
          chan_cd,
          sessn_id,
          seq_num,
          brws_uniq_id,
          usr_log_acct,
          shop_id,
           shop_typ_cd,
          itm_sku_id,
          itm_sku_nm,
          itm_id,
          itm_nm,
          itm_frst_catg_cd,
          itm_scnd_catg_cd,
          itm_thrd_catg_cd,
          brand_cd,
          url_typ_cd,
          url_attr_val,
          url,
          req_parm,
          shop_sessn_src_url,
          shop_sessn_src_req_parm,
          shop_sessn_src_zs_url_frst_catg_cd,
          shop_sessn_src_zs_url_scnd_catg_cd,
          shop_sessn_src_zs_url_thrd_catg_cd,
          is_add_cart, ----商智SKU粒度：引导加购口径
          shop_sessn_id,
          shop_sessn_par_seq_num,
          addcart_req_time,
          ref_shop_id,
          ref_itm_sku_id,
          ref_itm_sku_nm,
          case when shop_sessn_src_zs_url_frst_catg_cd=1 then shop_sessn_src_zs_url_frst_catg_cd else ref_zs_url_frst_catg_cd end as ref_zs_url_frst_catg_cd,
          case when shop_sessn_src_zs_url_frst_catg_cd=1 then shop_sessn_src_zs_url_scnd_catg_cd else ref_zs_url_scnd_catg_cd end as ref_zs_url_scnd_catg_cd,
          case when shop_sessn_src_zs_url_frst_catg_cd=1 then shop_sessn_src_zs_url_thrd_catg_cd else ref_zs_url_thrd_catg_cd end as ref_zs_url_thrd_catg_cd,
          tm_on_page,
          shop_frst_req_flg,
          shop_lst_req_flg,
          zs_url_thrd_catg_cd,
          ref_url,
          ref_req_parm,
          ref_seq_num, ---上一页访问序号
          ref_itm_id,
          ref_itm_nm,
          user_site_addr,
          user_site_cy_nm,
          user_site_prov_nm,
          user_site_city_nm,
          shop_sessn_src_kwd,
          shop_sessn_src_active,
          cart_itm_sku_id,
          is_add_cart_spu, ---商智SPU粒度：引导加购
          main_sku_id
        from retail_benchmark_10t.benchmark_adm_s14_zs_shop_traffics_lead_to_cart_det
        where dt='""" + zst.data_day_str + """'
        )traf_lead_cart 
      on traf_lead_ord.chan_cd = traf_lead_cart.chan_cd
      and traf_lead_ord.brws_uniq_id = traf_lead_cart.brws_uniq_id
      and traf_lead_ord.sessn_id = traf_lead_cart.sessn_id
      and traf_lead_ord.seq_num = traf_lead_cart.seq_num
    )all
 

  ;
"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, exec_engine='spark',
             retry_with_hive=False, spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
    ])
