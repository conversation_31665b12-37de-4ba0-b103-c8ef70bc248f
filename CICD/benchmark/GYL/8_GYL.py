#!/usr/bin/env python3

# buffaloID：712644

import datetime
from ZSTask import ZSTask

zst = ZSTask()

dest_table_name = 'benchmark_adm_s08_zs_store_sku_info_new'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE retail_benchmark_10t;
INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """')
SELECT
       shangzhi_sum.shop_id                                                        AS shop_id
      ,merchandise_info.itm_id                                                     AS itm_id
      ,merchandise_info.itm_nm                                                     AS itm_nm
      ,shangzhi_sum.itm_sku_id                                                     AS itm_sku_id
      ,merchandise_info.itm_sku_nm                                                 AS itm_sku_nm
      ,merchandise_info.itm_num                                                    AS itm_num
      ,shangzhi_sum.store_unq_id                                                   AS store_unq_id
      ,shangzhi_sum.store_unq_nm                                                   AS store_unq_nm
      ,shangzhi_sum.stk_shlf_qty                                                   AS stk_shlf_qty
      ,shangzhi_sum.stk_qty                                                        AS stk_qty
      ,shangzhi_sum.stk_qty * merchandise_info.jd_prc                              AS stk_amt
      ,shangzhi_sum.avl_stk_qty                                                    AS avl_stk_qty
      ,shangzhi_sum.ex_stk_qty                                                     AS ex_stk_qty
      ,shangzhi_sum.rtn_stk_qty                                                    AS rtn_stk_qty
      ,merchandise_info.jd_prc                                                     AS jd_prc
      ,ex_stk_info.ex_stk_ord_num                                                  AS ex_stk_ord_num
      ,ex_stk_info.ex_stk_ord_amt                                                  AS ex_stk_ord_amt
      ,ex_stk_info.sale_qty                                                        AS sale_qty

FROM  (
       SELECT
              shop_id                                                              AS shop_id
             ,item_sku_id                                                          AS itm_sku_id
             ,dim_store_num                                                        AS store_unq_id
             ,dim_store_name                                                       AS store_unq_nm
             ,shelves_qtty                                                         AS stk_shlf_qty
             ,stock_qty                                                            AS stk_qty
             ,canuse_qty                                                           AS avl_stk_qty
             ,wms_rec_qtty                                                         AS ex_stk_qty
             ,sub_qtty                                                             AS rtn_stk_qty

       FROM   retail_benchmark_10t.benchmark_app_store_shangzhi_oper_sum

       WHERE  dt = '""" + zst.data_day_str + """'
      ) shangzhi_sum

LEFT   OUTER JOIN
      (
       SELECT
              itm_id                                                               AS itm_id
             ,itm_nm                                                               AS itm_nm
             ,itm_sku_id                                                           AS itm_sku_id
             ,itm_sku_nm                                                           AS itm_sku_nm
             ,itm_num                                                              AS itm_num
             ,jd_prc                                                               AS jd_prc

       FROM   retail_benchmark_10t.benchmark_adm_th03_merchandise_basic_info

       WHERE  dt = '""" + zst.data_day_str + """'  and shop_id > 0
      ) merchandise_info
ON     shangzhi_sum.itm_sku_id = merchandise_info.itm_sku_id

LEFT   OUTER JOIN
      (
       SELECT /*+ MAPJOIN(wms_store)*/
              wms_store.dim_store_num                                              AS store_unq_id
             ,ex_stk.shop_id                                                       AS shop_id
             ,ex_stk.itm_sku_id                                                    AS itm_sku_id
             ,ex_stk.ex_stk_ord_num                                                AS ex_stk_ord_num
             ,ex_stk.ex_stk_ord_amt                                                AS ex_stk_ord_amt
             ,ex_stk.sale_qty                                                      AS sale_qty

       FROM  (
              SELECT
                     COALESCE(shangzhi_ob_det.dc_id, ord_det_sum.delv_center_num)  AS dc_id
                    ,COALESCE(shangzhi_ob_det.store_id, ord_det_sum.store_id)      AS store_id
                    ,COALESCE(shangzhi_ob_det.shop_id, ord_det_sum.pop_shop_id)    AS shop_id
                    ,COALESCE(shangzhi_ob_det.itm_sku_id, ord_det_sum.item_sku_id) AS itm_sku_id
                    ,COUNT(
                           DISTINCT
                                    CASE WHEN shangzhi_ob_det.sale_ord_id = ord_det_sum.sale_ord_id THEN shangzhi_ob_det.sale_ord_id
                                         ELSE NULL
                                    END
                          )                                                        AS ex_stk_ord_num
                    ,SUM(
                         CASE WHEN shangzhi_ob_det.sale_ord_id = ord_det_sum.sale_ord_id THEN COALESCE(ord_det_sum.after_prefr_amount - ord_det_sum.pop_shop_jq_pay_amount - ord_det_sum.pop_shop_dq_pay_amount - ord_det_sum.pop_shop_lim_sku_jq_pay_amount - ord_det_sum.pop_shop_lim_sku_dq_pay_amount - ord_det_sum.union_coupon_pay_amount + ord_det_sum.sku_freight_amount + ord_det_sum.delv_ser_fee_amount, 0)
                              ELSE 0
                         END
                        )                                                          AS ex_stk_ord_amt
                    ,SUM(
                         CASE WHEN ord_det_sum.sale_ord_valid_flag = 1 AND sale_ord_dt = '""" + zst.data_day_str + """' THEN COALESCE(ord_det_sum.sale_qtty, 0)
                              ELSE 0
                         END
                        )                                                          AS sale_qty
                    ,COUNT(
                           CASE WHEN shangzhi_ob_det.shop_id > 0 THEN 1
                                ELSE NULL
                           END
                          )                                                        AS res_flg

              FROM  (
                     SELECT
                            goods_no                                               AS itm_sku_id
                           ,ord_id                                                 AS sale_ord_id
                           ,shop_id                                                AS shop_id
                           ,distribute_no                                          AS dc_id
                           ,warehouse_no                                           AS store_id

                     FROM   retail_benchmark_10t.benchmark_app_store_shangzhi_shop_ob
                     WHERE  dt = '""" + zst.data_day_str + """'
                     AND    shop_id > 0
                     GROUP BY goods_no,ord_id,shop_id,distribute_no,warehouse_no
                    ) shangzhi_ob_det

              FULL   OUTER JOIN --计算出库订单以及销售件数使用，只需关联一遍订单明细表
                    (
                     SELECT
                            *
                     FROM   retail_benchmark_10t.benchmark_gdm_m04_ord_det_sum

                     WHERE  dt >= '""" + zst.data_day_str + """'
                     AND    pop_shop_id BETWEEN 0 AND 1000000000
                    ) ord_det_sum
                --  ON     shangzhi_ob_det.dc_id = ord_det_sum.delv_center_num
                -- AND    shangzhi_ob_det.store_id = ord_det_sum.store_id
              ON    shangzhi_ob_det.itm_sku_id = ord_det_sum.item_sku_id
              AND    shangzhi_ob_det.sale_ord_id = ord_det_sum.sale_ord_id

              GROUP  BY COALESCE(shangzhi_ob_det.dc_id, ord_det_sum.delv_center_num)
                       ,COALESCE(shangzhi_ob_det.store_id, ord_det_sum.store_id)
                       ,COALESCE(shangzhi_ob_det.shop_id, ord_det_sum.pop_shop_id)
                       ,COALESCE(shangzhi_ob_det.itm_sku_id, ord_det_sum.item_sku_id)
             ) ex_stk

       LEFT   OUTER JOIN
             (
              SELECT
                     *
              FROM   retail_benchmark_10t.benchmark_dim_wms_store
             ) wms_store
       ON     ex_stk.dc_id = wms_store.delv_center_num
       AND    ex_stk.store_id = wms_store.store_id
      ) ex_stk_info
ON     shangzhi_sum.store_unq_id = ex_stk_info.store_unq_id
AND    shangzhi_sum.shop_id = ex_stk_info.shop_id
AND    shangzhi_sum.itm_sku_id = ex_stk_info.itm_sku_id
;
"""

zst.exec_sql(schema_name="retail_benchmark_10t", table_name=dest_table_name, sql=sql, exec_engine='spark',
             retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true",
                         "--conf spark.sql.shuffle.partitions=2500",
                         "--conf spark.dynamicAllocation.maxExecutors=1000",
                         "--conf spark.driver.memory=12g"
                         ])
