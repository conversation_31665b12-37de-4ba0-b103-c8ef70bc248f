#!/usr/bin/env python3

# buffaloID: 696106

import sys
import os


sys.path.append(os.getenv('HIVE_TASK'))
from HiveTask import HiveTask
from datetime import *
from string import *

ht = HiveTask()

insertDay=date.today().strftime("%Y-%m-%d")

# 定义开始、结束时间段转换，对合并动态分区小文件使用
def getDay(begin_date, end_date):
    date_list = []
    start_date = datetime.strptime(begin_date, "%Y-%m-%d")
    end_date = datetime.strptime(end_date, "%Y-%m-%d")
    while start_date <= end_date:
        date_str = start_date.strftime("%Y-%m-%d")
        date_list.append(date_str)
        start_date += timedelta(days=1)
    return date_list


# 执行脚本获取库名及表名
db_name = 'retail_benchmark_10t'
tb_name = 'benchmark_app_order_so1_so_status'

# 日期传参 根据产值偏移量控制传参
one_day = ht.oneday(0)
today = ht.oneday(1)
data_369 = ht.oneday(-369)
data_370 = ht.oneday(-370)

####目标表加工SQL
sql = """
use """ + db_name + """;

from
(
select start_date,
       change_code,
       id,
       so_id,
       so_status,
       operate_time,
       operate_user,
       source,
       create_time,
       update_time,
       create_user,
       update_user,
       yn,
       ts,
       version,
       reserve1,
       reserve2,
       test,
       gid,
       '2' as sys_source
from retail_benchmark_10t.benchmark_fdm_eclp_so1_so_status_chain
where dp = 'ACTIVE'

union all

select '' as start_date,
       '' as change_code,
       id,
       regexp_replace(order_no,'ESL','') as so_id,
       order_status as so_status,
       operation_time as operate_time,
       '' as operate_user,
       operation_source as source,
       create_time,
       update_time,
       create_user,
       update_user,
       yn,
       ts,
       null as version,
       '' as reserve1,
       '' as reserve2,
       null as test,
       null as gid,
       '1' as sys_source
from retail_benchmark_10t.benchmark_fdm_cp_order_0_t_order_status_flow
where dt >= '""" + data_370 + """'
and order_group = 'Outbound'
and tenant_id = '1000'

) x

insert overwrite table """ + tb_name + """ partition (dp = 'ACTIVE', dt = '""" + insertDay +"""')
select *
where  substr(create_time,1,10) >= '""" + data_369 + """'

insert overwrite table """ + tb_name + """ partition (dp = 'HISTORY', dt = '""" + insertDay + """')
select *
where substr(create_time,1,10) = '""" + data_370 + """'
;
"""
# 目标表加工
# print(sql_1)
ht.exec_sql(schema_name=db_name, sql=sql, table_name=tb_name, exec_engine='spark', retry_with_hive=False,
            spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                        "--conf spark.sql.adaptive.repartition.enabled=true"])
