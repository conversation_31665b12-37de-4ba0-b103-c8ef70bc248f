#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# buffaloID: 734784

import sys
import os
import datetime

sys.path.append(os.getenv('HIVE_TASK'))
from HiveTask import HiveTask

ht = HiveTask()

insertDay = datetime.date.today().strftime("%Y-%m-%d")

db2 = 'retail_benchmark_10t'

start_date_str = ht.data_day_str

sql1 = """
  use retail_benchmark_10t;
  drop table  if exists retail_benchmark_10t.benchmark_app_eclp_so1_so_main_chain_2;
  create table  benchmark_app_eclp_so1_so_main_chain_2 as
  select
     start_date
    ,id
    ,distribution_no
    ,erp_warehouse_no
    ,sp_so_no
    ,create_time
    ,yn
    ,dp
    ,dt
    ,end_date
  from
  retail_benchmark_10t.benchmark_fdm_eclp_so1_so_main_chain
  where
    dt>='""" + start_date_str + """'
    and sp_id ='1' --销售平台 --销售平台来源（1京东/2天猫/3苏宁/4亚马逊/5ChinaSkin/6其它）
  """
ht.exec_sql(schema_name=db2, table_name='benchmark_app_eclp_so1_so_main_chain_2', sql=sql1, exec_engine='spark',
            retry_with_hive=False,
            spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                        "--conf spark.sql.adaptive.repartition.enabled=true"]
            )
