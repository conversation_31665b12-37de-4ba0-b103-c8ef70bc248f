#!/usr/bin/env python3

# 706058

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = zst.stat_ct
stat_begin = zst.stat_begin

dest_table_name = 'benchmark_app_zs_z1601_store_overview_new'

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """', stat_ct_cd='""" + stat_ct_cd + """')
SELECT
       shop_id                                                              AS shop_id
      ,'""" + stat_ct + """'                                                AS stat_ct
      ,""" + zst.data_day_int + """                                         AS data_dt
      ,SUM(stk_shlf_qty)                                                    AS stk_shlf_qty
      ,SUM(stk_qty)                                                         AS stk_qty
      ,SUM(avl_stk_qty)                                                     AS avl_stk_qty
      ,SUM(stk_amt)                                                         AS stk_amt
      ,SUM(
           CASE WHEN dt IN ('""" + stat_begin + """', '""" + zst.data_day_str + """') THEN avl_stk_qty
                ELSE 0
           END
          )
       * (DATEDIFF('""" + zst.data_day_str + """', '""" + stat_begin + """') + 1)
       / SUM(sale_qty)
       * 2                                                                  AS stk_turn_days
      ,SUM(ex_stk_qty)                                                      AS ex_stk_qty
      ,SUM(rtn_stk_qty)                                                     AS rtn_stk_qty
      ,SUM(ex_stk_ord_num)                                                  AS ex_stk_ord_num
      ,SUM(ex_stk_ord_amt)                                                  AS ex_stk_ord_amt
      ,SUM(sale_qty)                                                        AS sale_qty

FROM  (
       SELECT
              *

       FROM   retail_benchmark_10t.benchmark_app_zs_z1601_store_detail_new

       WHERE  dt >= '""" + stat_begin + """'
       AND    dt <= '""" + zst.data_day_str + """'
       AND    stat_ct_cd = 'day'
      ) store_detail

GROUP  BY shop_id
;
"""

zst.exec_sql(schema_name="retail_benchmark_10t", table_name=dest_table_name, sql=sql, exec_engine='spark',
             retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
