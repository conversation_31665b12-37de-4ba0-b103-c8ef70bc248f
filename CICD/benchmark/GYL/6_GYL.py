#!/usr/bin/env python3

# buffaloID：706061

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;
insert overwrite table  retail_benchmark_10t.benchmark_app_store_shangzhi_shop_ob  partition (dt='""" + insertDay + """')
 select exp_date,
    distribute_no,
    warehouse_no,
    goods_no,
    ord_id,
    t2.shop_id as seller_no,
    t2.shop_name as seller_name,
    sum(sale_qtty) as sale_qtty
from (

    SELECT  substr(t1.creatdate,1,10) as exp_date,
         t1.orgid as distribute_no,  -- 配送中心
         t1.sid as warehouse_no,    -- 仓库
         t3.wareid goods_no,
         t1.orderid as ord_id,
         sum(t3.num) as sale_qtty
    from (
        --出管表出库件数
        select * from retail_benchmark_10t.benchmark_fdm_pek_jd_chuguan_chain
                 where start_date <= '""" + zst.data_day_str + """'
                   and end_date   >  '""" + zst.data_day_str + """'
                   and substr(creatdate,1,10) ='""" + zst.data_day_str + """'
           and typeid in('201','202')

           )t1

    join(
        select * from retail_benchmark_10t.benchmark_fdm_pek_jd_qingdan_chain
                 where start_date <= '""" + zst.data_day_str + """'
                   and end_date   >  '""" + zst.data_day_str + """'
                   AND LENGTH(wareid) IN(10, 11, 14) --限制POP Sku

           )t3
         on coalesce(t1.kdanhao, rand()) = coalesce(t3.kdanhao, -rand(10))

  group  by substr(t1.creatdate,1,10) ,
         t1.orgid,
         t1.sid,
         t3.wareid ,
         t1.orderid

  union all

   --sop出库件数
    SELECT
          SUBSTR(ob_time, 1, 10) AS exp_date,
          distribution_no AS distribute_no,
          erp_warehouse_no AS warehouse_no,
          isv_goods_no AS goods_no,
          sp_so_no AS ord_id,
          SUM(apply_outstore_qty) AS sale_qtty
        FROM
          (
            select
              *
            from
              retail_benchmark_10t.benchmark_app_vsc_order_detail_forzhuge_v2
            where
              dt >= date_sub('""" + zst.data_day_str + """', 10)
              AND SUBSTR(create_time, 1, 10) >= date_sub('""" + zst.data_day_str + """', 5)
              and so_status not IN (10009, 10028, 10060)
              and SUBSTR(ob_time, 1, 10) = '""" + zst.data_day_str + """'
          )
          t1
        GROUP BY
          SUBSTR(ob_time, 1, 10),
          distribution_no,
          erp_warehouse_no,
          isv_goods_no,
          sp_so_no

    ) tmp

left join (

      select item_sku_id,
             coalesce(shop_id,pop_vender_id, '无') as shop_id,
                   coalesce(shop_name,pop_vender_name, '无') as shop_name
            from  retail_benchmark_10t.benchmark_gdm_m03_item_sku_act
             where dt='""" + zst.data_day_str + """'
        and item_third_cate_cd not in (1009,874,1137,10011,10970,2643,1446,10968,6881,10010,466,463,512,6980,12258,1195,10969,982,12360,12365)
        and (shop_id is not null or  pop_vender_id is not null)
        and (shop_id<>'' or pop_vender_id<>'')
        and shop_name not like '%测试%'
        and pop_vender_name not like '%测试%'

        ) t2
  on coalesce(tmp.goods_no, rand()) = coalesce(t2.item_sku_id, -rand(10))

where sale_qtty>0
  and sale_qtty is not null
  and shop_id is  not  null
  and shop_name is not null
  and shop_id<>''

   group by exp_date,
      distribute_no,
      warehouse_no,
      goods_no,
      ord_id,
      t2.shop_id,
      t2.shop_name
;
"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_app_store_shangzhi_shop_ob', sql=sql,
             exec_engine='spark', retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true",
                         "--conf spark.sql.shuffle.partitions=2000",
                         "--conf spark.dynamicAllocation.maxExecutors=900",
                         "--conf spark.driver.memory=12g"]
             )
