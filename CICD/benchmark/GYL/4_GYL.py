#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# !/usr/bin/env python3

# buffaloID: 82462

import sys
import os
import datetime

sys.path.append(os.getenv('HIVE_TASK'))
from HiveTask import HiveTask

ht = HiveTask()

insertDay=datetime.date.today().strftime("%Y-%m-%d")

start_date_str=ht.data_day_str

db = 'retail_benchmark_10t'

sql1 = """
INSERT overwrite TABLE retail_benchmark_10t.benchmark_app_vsc_order_detail_forzhuge partition (dt = '""" + insertDay + """')
SELECT
  SUBSTR(so_main.create_time, 1, 10) AS op_date, --订单时间
  so_main.so_no, --ECLP订单编号
  so_main.seller_no, --商家编号
  so_main.seller_name, --商家名称
  so_main.dept_no, --事业部编号
  so_main.dept_name, --事业部名称
  so_main.warehouse_no, --库房编码
  so_main.warehouse_name, --库房名称
  so_main.distribution_no, --配送中心编号(外部)
  so_main.erp_warehouse_no, --库房编号(外部)
  so_main.isv_so_no, --ISV出库单号
  so_main.sp_id, --销售平台ID
  so_main.sp_no, --销售平台编号
  so_main.sp_name, --销售平台
  so_main.sp_so_no, --销售平台单号
  so_main.group_order_no, --团单号
  so_main.parent_id, --父单id
  so_main.so_status, --订单状态
  so_main.shop_id, --店铺Id
  so_main.shop_no, --店铺编号
  so_main.shop_name, --店铺名称
  so_main.station_no, --站点编号
  so_main.station_name, --站点名称
  so_main.shipper_no, --承运商编码
  so_main.shipper_name, --承运商名称
  so_main.way_bill, --运单号
  so_main.addr_province, --收货地址-省
  so_main.addr_city, --收货地址-市
  so_main.addr_county, --收货地址-县
  so_main.addr_town, --收货地址-镇
  so_main.consignee_addr, --收货人地址
  so_item.goods_no, --商品编码
  so_item.isv_goods_no, --ISV商品编号
  so_item.seller_goods_sign, --商家商品标识
  so_item.goods_name, --商品名称
  so_item.price, --商品单价
  so_item.line_cnt, --行项数
  so_item.apply_outstore_qty, --下单数量
  so_item.real_outtore_qty, --实际发货数量
  sort_array(status_key) AS status_key, --状态集合
  so_status.status_map, --状态键值
  so_status.status_map[10010] AS ord_init_time, --订单初始化时间
  so_status.status_map[10017] AS check_time, --复核时间      --增加字段
  so_status.status_map[10018] AS package_time, --打包时间
  so_status.status_map[10019] AS ob_time, --出库（发货）时间
  so_status.status_map[10032] AS arrival_sorting_time, --到分拣时间
  so_status.status_map[10033] AS arrival_site_time, --到站点时间
  so_status.status_map[10034] AS sign_time, --签收（妥投）时间
  so_status.status_map[10035] AS refuse_time, --拒收时间
  CASE
    WHEN so_main.so_status IN(10009, 10028, 10060)
    THEN COALESCE(so_status.status_map[10009], so_status.status_map[10028], so_status.status_map[10060], so_main.update_time)
    ELSE NULL
  END AS cancel_time, --取消时间

  so_main.order_amount, --订单金额
  so_main.so_weight, --订单重量（kg）
  so_main.so_volume, --订单总体积(单位cm3)
  so_main.so_err_status, --订单异常状态
  so_main.err_type, --订单异常类型
  so_main.so_type, --订单类型
  so_main.so_mark, --订单标记位
  so_main.create_time, --创建时间
  so_main.update_time, --更新时间
  so_main.isv_so_type, --外部订单类型
  so_main.edi_remark, --外部标识信息
  so_main.bd_owner_no, --青龙业主号
  so_main.so_source, --订单来源
  so_main.sp_create_time, --销售平台下单时间
  so_main.consignee_remark --买家留言

FROM
  (
    SELECT
    *
    FROM
      retail_benchmark_10t.benchmark_app_order_so1_so_main
    WHERE
      dp = 'ACTIVE'
      AND yn = '1'
      AND SUBSTR(sp_create_time, 1, 10) >= date_sub('""" + start_date_str + """', 45)
      AND SUBSTR(sp_create_time, 1, 10) <= '""" + start_date_str + """'
      and (sp_name in ('京东商城','京东POP旗舰店','京东男鞋店','京东女鞋店','京东供销','京东童鞋店','京东运动店','汽后(京东商城)','京东POP服饰','京东POP食品','京东到家','京东POP家电','京东佳佰','京东POP','半亩抖音京东快递COD','京东国际跨境（零关税）体验馆','京东综合自营店','京东','京东POP数码','京东POP箱包','斯凯奇东区京东店','京东POP宠物','斯凯奇北区京东店','京东POP礼品','斯凯奇京东童鞋店','斯凯奇南区京东店','京东自营严选','网易考拉京东华南电商仓') or warehouse_name like '%京喜%')
  ) so_main
left JOIN
  (
            SELECT
              so_id,
              goods_no,
              goods_name,
              isv_goods_no,
              seller_goods_sign,
        max(price) as price, --取max
              sum(if(dept_real_out_qty is null or dept_real_out_qty = 0, real_outtore_qty, dept_real_out_qty)) as real_outtore_qty, --汇总逻辑修改
              sum(if(dept_apply_out_qty is null or dept_apply_out_qty = 0, apply_outstore_qty, dept_apply_out_qty)) as apply_outstore_qty, --汇总逻辑修改
              COUNT(1) line_cnt
            FROM
              retail_benchmark_10t.benchmark_app_order_so1_so_item A
            WHERE
              dp = 'ACTIVE'
              AND yn = '1'
              AND SUBSTR(create_time, 1, 10) >= date_sub('""" + start_date_str + """', 60)
              AND SUBSTR(create_time, 1, 10) <= '""" + start_date_str + """'
            GROUP BY
              A.so_id,
              A.goods_no,
              A.goods_name,
              A.isv_goods_no,
              A.seller_goods_sign
  ) so_item
ON
  so_item.so_id = so_main.id
LEFT JOIN
  (
    SELECT
      so_id,
      str_to_map(concat_ws(',', collect_set(concat(so_status, ':', operate_time))), ',', ':') status_map,
      collect_set(cast(so_status as int)) status_key
    FROM
      retail_benchmark_10t.benchmark_app_order_so1_so_status
    WHERE
      dp = 'ACTIVE'
      AND yn = 1
      --AND so_status IN('10009','10010', '10018', '10019', '10034', '10035', '10032', '10033', '10028','10060','10016','10017','10020')
      ----10010订单初始化,10018 货品已打包,10019交接发货,10032分拣验收,10033站点验收,10034妥投 10035拒收 10028删单 10016拣货 10017复核 10020包裹出库
      AND SUBSTR(create_time, 1, 10) >= date_sub('""" + start_date_str + """', 60)
      AND SUBSTR(create_time, 1, 10) <= date_add('""" + start_date_str + """', 90)
    GROUP BY
      so_id
  )
  so_status
ON
  so_main.id = so_status.so_id
;

    """
ht.exec_sql(
    schema_name=db,
    table_name='benchmark_app_vsc_order_detail_forzhuge',
    sql=sql1,
    retry_with_hive=False,
    exec_engine='spark',
    spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                "--conf spark.sql.adaptive.repartition.enabled=true"]
)


