#!/usr/bin/env python3

# buffaloID: 696713

import sys
import os
import datetime

sys.path.append(os.getenv('HIVE_TASK'))
from HiveTask import HiveTask

ht = HiveTask()

insertDay = datetime.date.today().strftime("%Y-%m-%d")

befor_369_days_str=ht.oneday(-369)
befor_370_days_str=ht.oneday(-370)

# 执行脚本获取库名及表名
db = 'retail_benchmark_10t'
tb = 'benchmark_app_order_so1_so_main'

sql1 = """
    use """ + db + """;
    from(
        SELECT
          start_date
          ,change_code
          ,cast(id as string) id
          ,cast(parent_id as string) parent_id
          ,so_no
          ,isv_so_no
          ,sp_so_no
          ,so_source
          ,sp_id
          ,sp_no
          ,sp_name
          ,bd_sp_no
          ,sp_create_time
          ,seller_id
          ,seller_no
          ,seller_name
          ,seller_remark
          ,dept_id
          ,dept_no
          ,dept_name
          ,shop_id
          ,shop_no
          ,shop_name
          ,bd_owner_no
          ,isv_shop_no
          ,way_bill
          ,station_no
          ,station_name
          ,warehouse_id
          ,warehouse_no
          ,warehouse_name
          ,distribution_no
          ,org_no
          ,erp_warehouse_no
          ,shipper_id
          ,shipper_no
          ,shipper_name
          ,expect_date
          ,consignee
          ,consignee_addr
          ,addr_province
          ,addr_city
          ,addr_county
          ,addr_town
          ,consignee_postcode
          ,consignee_mobile
          ,consignee_phone
          ,consignee_email
          ,consignee_remark
          ,aftersales_name
          ,aftersales_mobile
          ,aftersales_addr
          ,so_status
          ,trans_type
          ,so_err_status
          ,so_mark
          ,so_split_flag
          ,receivable
          ,guarantee_value
          ,pin_account
          ,create_time
          ,update_time
          ,create_user
          ,update_user
          ,yn
          ,ts
          ,version
          ,reserve1
          ,reserve2
          ,test
          ,sorting_center_no
          ,sorting_center_name
          ,isv_source
          ,isv_source_name
          ,so_type
          ,so_weight
          ,so_volume
          ,piece_type
          ,insured_price_rule
          ,gift_type
          ,gift_no
          ,batch_no
          ,group_order_no
          ,is_merge
          ,sorting_road
          ,expect_delivery_date
          ,isv_so_type
          ,so_third_site
          ,user_name
          ,user_nick_name
          ,waybill_remark
          ,hybris
          ,seller_factory_no
          ,seller_factory_name
          ,err_type
          ,pause_time
          ,gid
          ,batch_qty
          ,transaction_source
          ,edi_remark
          ,station_type
          ,merge_key
          ,order_amount
          ,transport_type
          ,settlement_type
          ,target_warehouse_id
          ,client_name
          ,client_no
          ,yun_fee
          ,discount
          ,1 as data_sourse
          -- ,dp
          -- ,dt
          -- ,end_date
        FROM retail_benchmark_10t.benchmark_fdm_eclp_so1_so_main_chain
      where dp = 'ACTIVE'
        and substr(create_time,1,10) >= '""" + befor_370_days_str + """'

        union all
        ------------------------订单中心主表------------------------
        select
          main.start_date
          ,main.change_code
          ,substr(main.order_no,4) as id
          ,substr(main.parent_order_no,4) as parent_id
          ,main.order_no as so_no
          ,main.isv_so_no
          ,cust.sp_so_no
          ,extend.so_source
          ,cust.sp_id
          ,null as sp_no
          ,cust.sp_name
          ,sp.bd_sp_no
          ,main.sp_create_time
          ,dept.seller_id
          ,seller.seller_no
          ,seller.seller_name
          ,clob.seller_remark
          ,dept.dept_id
          ,main.dept_no
          ,main.dept_name
          ,cast(substr(shop_no,4) as bigint) as shop_id
          ,cust.shop_no
          ,cust.shop_name
          ,wayv.bd_owner_no
          ,cust.isv_shop_no
          ,rela.way_bill
          ,waym.station_no
          ,waym.station_name
          ,ware.warehouse_id
          ,node.warehouse_no
          ,node.warehouse_name
          ,ware.distribution_no
          ,ware.org_no
          ,ware.erp_warehouse_no
          ,cast(substr(cpship.shipper_no,4) as bigint) as shipper_id
          ,cpship.shipper_no
          ,cpship.shipper_name
          ,cpship.expect_date
          ,consign.consignee
          ,consign.consignee_addr
          ,consign.addr_province
          ,consign.addr_city
          ,consign.addr_county
          ,consign.addr_town
          ,consign.consignee_postcode
          ,consign.consignee_mobile
          ,consign.consignee_phone
          ,conex.consignee_email
          ,clob.consignee_remark
          ,null as aftersales_name
          ,null as aftersales_mobile
          ,null as aftersales_addr
          ,main.so_status
          ,1 as trans_type
          ,concat(
            coalesce(main.cancel_status,'0')
            ,coalesce(clob.yc,'0')
            ,'0' ,'0'
            ,coalesce(main.intercept_type,'0')
            ,coalesce(main.intercept_type,'0')
            ,'0' ,'0' ,'0' ,'0'
          ) as so_err_status
          ,clob.so_mark
          ,0 as so_split_flag
          ,solu.receivable
          ,solu.guarantee_value
          ,clob.pin_account
          ,main.create_time
          ,main.update_time
          ,main.create_user
          ,main.update_user
          ,main.yn
          ,main.ts
          ,null as version
          ,null as reserve1
          ,null as reserve2
          ,0 as test
          ,null as sorting_center_no
          ,null as sorting_center_name
          ,null as isv_source
          ,null as isv_source_name
          ,extend.so_type
          ,main.so_weight
          ,main.so_volume
          ,ware.piece_type
          ,null as insured_price_rule
          ,clob.gift_type
          ,clob.gift_no
          ,null as batch_no
          ,null as group_order_no
          ,null as is_merge
          ,wayv.sorting_road
          ,cpship.expect_delivery_date
          ,null as isv_so_type
          ,null as so_third_site
          ,null as user_name
          ,null as user_nick_name
          ,null as waybill_remark
          ,null as hybris
          ,null as seller_factory_no
          ,null as seller_factory_name
          ,null as err_type
          ,null as pause_time
          ,null as gid
          ,null as batch_qty
          ,null as transaction_source
          ,null as edi_remark
          ,waym.station_type
          ,null as merge_key
          ,clob.order_amount
          ,null as transport_type
          ,null as settlement_type
          ,null as target_warehouse_id
          ,null as client_name
          ,null as client_no
          ,null as yun_fee
          ,null as discount
        ,2 as data_sourse
          -- ,main.dp
          -- ,main.dt
          -- ,main.end_date
        from (
        --------------------------订单中心主表---------------------
          select
            start_date
            ,change_code
            ,order_id
            ,order_no
            ,parent_order_no
            ,customer_order_no as isv_so_no
            ,fulfillment_account_no as dept_no
            ,fulfillment_account_name as dept_name
            ,channel_create_time as sp_create_time
            ,order_status_custom as so_status
            ,recheck_weight as so_weight
            ,recheck_volume as so_volume
            ,case intercept_type when 2 then '1'
                when 1 then '2'
            else intercept_type end as intercept_type
            ,cancel_status
            ,received_time as create_time
            ,update_time
            ,create_user
            ,update_user
            ,yn
            ,ts
            -- ,dp
            -- ,dt
            -- ,end_date
          from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_main_chain
          where order_group = 'Outbound'
            and sync_source = 1
        --    and sync_source = 2
        --    and business_unit = 'cn_jdl_sc-sync-eclp'
            and tenant_id = '1000'
            and substr(create_time,1,10) >= '""" + befor_370_days_str + """'
          and dp = 'ACTIVE'
        ) main
        ------------------------订单拓展信息------------------------
        left join (
          select order_no
            ,attr6 as so_source
            ,attr1 as so_type
          from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_extend_chain
          where tenant_id = '1000'
            and order_group = 'Outbound'
            and dp = 'ACTIVE'
        ) extend
          on main.order_no = extend.order_no
        ------------------------客户渠道信息------------------------
        left join (
          select order_no
            ,channel_no as sp_id
            ,channel_name as sp_name
            ,second_level_channel as shop_no
            ,second_level_channel_name as shop_name
            ,channel_shop_no as isv_shop_no
            ,channel_order_no as sp_so_no
          from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_customer_channel_chain
          where tenant_id = '1000'
            and order_group = 'Outbound'
            and dp = 'ACTIVE'
        ) cust
          on main.order_no = cust.order_no
        ------------------------青龙销售平台------------------------
        left join (
          select id,bd_seller_no as bd_sp_no
          from retail_benchmark_10t.benchmark_fdm_eclp_master_sp_source_chain
          where dp='ACTIVE'
                and yn=1
            group by id,bd_seller_no
        ) sp
          on cust.sp_id = sp.id
        ------------------------eclp事业部主表------------------------
        left join (
          select id as dept_id,dept_no,seller_id
          from retail_benchmark_10t.benchmark_fdm_eclp_master_dept_chain
          where dp='ACTIVE'
                and yn=1
        ) dept
          on main.dept_no = dept.dept_no
        ------------------------eclp商家主表------------------------
        left join (
          select id,seller_no,seller_name
          from retail_benchmark_10t.benchmark_fdm_eclp_master_seller_chain
            where dp='ACTIVE'
                and yn=1
        ) seller
          on dept.seller_id = seller.id
        ------------------------eclp店铺主表------------------------
        -- left join (
          -- select id as shop_id,shop_no
          -- from retail_benchmark_10t.benchmark_fdm_eclp_master_shop_chain
          -- group by id,shop_no
        -- ) shop
          -- on cust.shop_no = shop.shop_no
        ------------------------订单关联单信息------------------------
        left join (
          select order_no
            ,ref_order_no as way_bill
          from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_relation_chain
          where ref_order_type = 50000
            and ref_order_sub_type = 1
            and order_group = 'Outbound'
            and tenant_id = '1000'
              and dp = 'ACTIVE'
              and yn = 1
        ) rela
          on main.order_no = rela.order_no
        ------------------------运单拓展表------------------------
        left join (
          select waybill_code
            ,customer_code as bd_owner_no
            ,road_code as sorting_road
            ,old_site_id
          from retail_benchmark_10t.benchmark_fdm_bd_waybill_v_waybill_e_chain
          where dt = date_sub('""" + ht.data_day_str + """',1)
              and first_time > '2021-10-15'
        ) wayv
          on rela.way_bill = wayv.waybill_code
        ------------------------青龙站点表------------------------
        left join (
          select id
            ,site_code as station_no
            ,site_name as station_name
            ,site_type as station_type
          from retail_benchmark_10t.benchmark_fdm_basic_ql_base_site_chain
          where dp = 'ACTIVE'
        ) waym
          on wayv.old_site_id = waym.id
        ------------------------订单中心仓节点------------------------
        left join (
          select order_no
            ,actual_node_no as warehouse_no
            ,actual_node_name as warehouse_name
          from retail_benchmark_10t.benchmark_fdm_cp_order_0_t_order_logistics_nodes_chain
          where node_classification = 1 and node_usage = 1
            and tenant_id = '1000'
            and order_group = 'Outbound'
              and yn = 1
          group by order_no
              ,actual_node_no
            ,actual_node_name
        ) node
          on main.order_no = node.order_no
        ------------------------仓库主表------------------------
        left join (
          select id as warehouse_id
            ,erp_warehouse_no
            ,warehouse_no
            ,distribution_no
            ,org_no
            ,type as piece_type
          from retail_benchmark_10t.benchmark_fdm_eclp_master_warehouse_chain
            where dp='ACTIVE'
                and yn = 1
        ) ware
          on node.warehouse_no = ware.warehouse_no
        ------------------------订单中心承运商表------------------------
        left join (
          select order_no
            ,shipper_no
            ,shipper_name
            -- ,last_station_no as station_no
            -- ,last_station_name as station_name
            -- ,last_station_type as station_type
            ,expect_delivery_start_time as expect_date
            ,plan_delivery_time as expect_delivery_date
          from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_shipment_chain
          where tenant_id = '1000'
            and order_group = 'Outbound'
            and dp = 'ACTIVE'
              and yn = 1
        ) cpship
          on main.order_no = cpship.order_no
        ------------------------eclp承运商表------------------------
        -- left join (
          -- select id as shipper_id,shipper_no
          -- from retail_benchmark_10t.benchmark_fdm_eclp_master_ship_chain
          -- where create_time > '2021-09-15'
          -- group by id,shipper_no
        -- ) ship
        --  on cpship.shipper_no = ship.shipper_no
        ----------------------订单策略表------------------------
        -- left join (
          -- select order_no
            -- ,attr1 as expect_date
          -- from retail_benchmark_10t.benchmark_fdm_cp_order_0_t_order_smart_pattern_chain
          -- where tenant_id = '1000'
            -- and order_group = 'Outbound'
            -- and attr1 <> ''
            -- and dp = 'ACTIVE'
              -- and yn = 1
        -- ) pattern
          -- on main.order_no = pattern.order_no
        ------------------------订单收发信息------------------------
        left join (
          select order_no
            ,consignee_name as consignee
            ,consignee_address as consignee_addr
            ,consignee_province_no as addr_province
            ,consignee_city_no as addr_city
            ,consignee_county_no as addr_county
            ,consignee_town_no as addr_town
            ,consignee_zip_code as consignee_postcode
            ,consignee_mobile
            ,consignee_phone
          from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_consign_chain
          where tenant_id = '1000'
            and order_group = 'Outbound'
            and dp = 'ACTIVE'
              and yn = 1
        ) consign
          on main.order_no = consign.order_no
        ------------------------收发拓展信息------------------------
        left join (
          select order_no,consignee_email
          from retail_benchmark_10t.benchmark_fdm_cp_order_0_t_order_consign_extend_chain
          where tenant_id = '1000'
            and order_group = 'Outbound'
            and dp = 'ACTIVE'
              and yn = 1
        ) conex
          on main.order_no = conex.order_no
        ------------------------订单大字段------------------------
        left join (
          select order_no
            ,ordermark as so_mark
            ,get_json_object(ecp_order,'$.buyerRemark') as consignee_remark
            ,get_json_object(ecp_order,'$.buyerNo') as pin_account
            ,get_json_object(ecp_order,'$.sellerRemark') as seller_remark
            ,get_json_object(ecp_order,'$.orderAmount') as order_amount
            ,if(get_json_object(ecp_order,'$.orderGiftType') = 2,1,0) as gift_type
            ,get_json_object(ecp_order,'$.orderGiftRelationList') as gift_no
            ,get_json_object(attr1,'$.fulfillmentExceptionStatus') as yc
          from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_extend_clob_chain
          where tenant_id = '1000'
            and order_group = 'Outbound'
              and dp = 'ACTIVE'
              and yn = 1
        ) clob
          on main.order_no = clob.order_no
        ------------------------订单解决方案------------------------
        left join (
          select order_no
            ,get_json_object(product_attrs,'$.shouldPayMoney') as receivable
            ,get_json_object(product_attrs,'$.guaranteeMoney') as guarantee_value
          from retail_benchmark_10t.benchmark_fdm_cp_order_0_t_order_solution_chain
          where tenant_id = '1000'
            and order_group = 'Outbound'
              and dp = 'ACTIVE'
              and yn = 1
        ) solu
          on main.order_no = solu.order_no
    ) t

    insert overwrite table """ + tb + """ partition (dp = 'ACTIVE', dt = '""" + insertDay + """', end_date = '""" + befor_369_days_str + """')
    select *
    where  substr(create_time,1,10) >= '""" + befor_369_days_str + """'

    insert overwrite table """ + tb + """ partition (dp = 'EXPIRED', dt = '""" + insertDay + """', end_date = '""" + befor_370_days_str + """')
    select *
    where substr(create_time,1,10) = '""" + befor_370_days_str + """'
    ;

    """
ht.exec_sql(
    schema_name=db,
    table_name=tb,
    sql=sql1,
    retry_with_hive=False,
    exec_engine='spark',
    spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                "--conf spark.sql.adaptive.repartition.enabled=true",
                "--conf spark.isLoadHivercFile=false"]
)