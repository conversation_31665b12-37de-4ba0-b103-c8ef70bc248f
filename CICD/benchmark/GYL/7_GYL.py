#!/usr/bin/env python3

# buffaloID：712658

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;
INSERT  overwrite TABLE retail_benchmark_10t.benchmark_app_store_shangzhi_oper_sum partition  (dt = '""" + insertDay + """')
SELECT
  exp_date,
  d.dim_store_name,
  d.dim_store_num,
  b.shop_id AS seller_no,
  b.shop_name AS seller_name,
  A.goods_no,
  SUM(shelves_qtty),
  SUM(stock_qty),
  SUM(canuse_qty),
  SUM(wms_rec_qtty),
  SUM(sub_qtty)
FROM
  (
     --sop采购入库
    SELECT
      SUBSTR(t1.update_time, 1, 10) AS exp_date,
      t3.sp_goods_no AS goods_no,
      distribution_no AS distribute_no,
      warehouse_no AS warehouse_no,
      SUM(real_instore_qty) AS shelves_qtty,
      0 AS stock_qty,
      0 AS canuse_qty,
      0 AS wms_rec_qtty,
      0 AS sub_qtty
    FROM
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_app_eclp_po1_po_main_chain
        WHERE
          dp = 'ACTIVE'
          AND po_status = '70'
          AND yn = 1
      )
      t1
    JOIN
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_fdm_eclp_po1_po_item_wms_chain
        WHERE
          start_date <= '""" + zst.data_day_str + """'
          AND end_date > '""" + zst.data_day_str + """'
          AND yn = 1
          AND SUBSTR(update_time, 1, 10) = '""" + zst.data_day_str + """'
      )
      t2
    ON
      t1.id = t2.po_id
    JOIN
      (
        SELECT
          goods_no,
          MAX(sp_goods_no) AS sp_goods_no
        FROM
          retail_benchmark_10t.benchmark_app_eclp_goods1_shop_goods_chain
        WHERE
          dp = 'ACTIVE'
          AND yn = 1
        GROUP BY
          goods_no
      )
      t3
    ON
      t2.goods_no = t3.goods_no
    GROUP BY
      SUBSTR(t1.update_time, 1, 10),
      t3.sp_goods_no,
      distribution_no,
      warehouse_no

    UNION ALL

    --出管采购入库

    SELECT
      SUBSTR(t1.creatdate, 1, 10) AS exp_date,
      t3.wareid goods_no,
      t1.orgid AS distribute_no, -- 配送中心
      t1.sid AS warehouse_no, -- 仓库
      SUM(t3.num) AS shelves_qtty,
      0 AS stock_qty,
      0 AS canuse_qty,
      0 AS wms_rec_qtty,
      0 AS sub_qtty
    FROM
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_fdm_pek_jd_chuguan_chain
        WHERE
          start_date <= '""" + zst.data_day_str + """'
          AND end_date > '""" + zst.data_day_str + """'
          AND typeid = '101'
          AND SUBSTR(creatdate, 1, 10) = '""" + zst.data_day_str + """'
      )
      t1
    JOIN
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_fdm_pek_jd_qingdan_chain
        WHERE
          start_date <= '""" + zst.data_day_str + """'
          AND end_date > '""" + zst.data_day_str + """'
          AND LENGTH(wareid) IN(10, 11, 14) --限制POP Sku
      )
      t3
    ON
      t1.kdanhao = t3.kdanhao
    GROUP BY
      SUBSTR(t1.creatdate, 1, 10),
      t1.orgid,
      t1.sid,
      t3.wareid

    UNION ALL
    --sop退货入库

    SELECT
      SUBSTR(t1.update_time, 1, 10) AS exp_date,
      t2.sp_goods_no AS goods_no,
      t3.distribution_no AS distribute_no,
      t3.erp_warehouse_no AS warehouse_no,
      0 AS shelves_qtty,
      0 AS stock_qty,
      0 AS canuse_qty,
      0 AS wms_rec_qtty,
      SUM(real_instore_qty) AS sub_qtty
    FROM
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_app_eclp_rtw1_rtw_main_chain
        WHERE
          dp = 'ACTIVE'
          AND status = '200'
          AND SUBSTR(update_time, 1, 10) = '""" + zst.data_day_str + """'
          AND yn = 1
      )
      t1
    JOIN
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_app_eclp_rtw1_rtw_item_chain
        WHERE
          dp = 'ACTIVE'
          AND yn = '1'
          AND SUBSTR(update_time, 1, 10) = '""" + zst.data_day_str + """'
      )
      t2
    ON
      t1.rtw_no = t2.rtw_no
    LEFT JOIN
      (
        SELECT
          distribution_no,
          erp_warehouse_no,
          warehouse_no
        FROM
          retail_benchmark_10t.benchmark_fdm_eclp_master_warehouse_chain
        WHERE
          dp = 'ACTIVE'
          AND yn = 1
        GROUP BY
          distribution_no,
          erp_warehouse_no,
          warehouse_no
      )
      t3
    ON
      t1.warehouse_no = t3.warehouse_no
    GROUP BY
      SUBSTR(t1.update_time, 1, 10),
      t2.sp_goods_no,
      t3.distribution_no,
      t3.erp_warehouse_no

    UNION ALL

    --出管退货入库

    SELECT
      SUBSTR(t1.creatdate, 1, 10) AS exp_date,
      t3.wareid goods_no,
      t1.orgid AS distribute_no, -- 配送中心
      t1.sid AS warehouse_no, -- 仓库
      0 AS shelves_qtty,
      0 AS stock_qty,
      0 AS canuse_qty,
      0 AS wms_rec_qtty,
      SUM(ABS(t3.num)) AS sub_qtty
    FROM
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_fdm_pek_jd_chuguan_chain
        WHERE
          start_date <= '""" + zst.data_day_str + """'
          AND end_date > '""" + zst.data_day_str + """'
          AND typeid IN('301', '302')
          AND SUBSTR(creatdate, 1, 10) >= '""" + zst.data_day_str + """'
      )
      t1
    JOIN
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_fdm_pek_jd_qingdan_chain
        WHERE
          start_date <= '""" + zst.data_day_str + """'
          AND end_date > '""" + zst.data_day_str + """'
          AND LENGTH(wareid) IN(10, 11, 14) --限制POP Sku
      )
      t3
    ON
      t1.kdanhao = t3.kdanhao
    GROUP BY
      SUBSTR(t1.creatdate, 1, 10),
      t1.orgid,
      t1.sid,
      t3.wareid

    UNION ALL

    --sop出库件数
    SELECT
          SUBSTR(ob_time, 1, 10) AS exp_date,
          isv_goods_no AS goods_no,
          distribution_no AS distribute_no,
          erp_warehouse_no AS warehouse_no,
          0 AS shelves_qtty,
          0 AS stock_qty,
          0 AS canuse_qty,
          SUM(apply_outstore_qty) AS wms_rec_qtty,
          0 AS sub_qtty
        FROM
          (
            select
              *
            from
              retail_benchmark_10t.benchmark_app_vsc_order_detail_forzhuge_v2
            where
              dt >= date_sub('""" + zst.data_day_str + """', 10)
              AND SUBSTR(create_time, 1, 10) >= date_sub('""" + zst.data_day_str + """', 5)
              and so_status not IN (10009, 10028, 10060)
              and SUBSTR(ob_time, 1, 10) = '""" + zst.data_day_str + """'
          )
          t1
        GROUP BY
          SUBSTR(ob_time, 1, 10),
          distribution_no,
          erp_warehouse_no,
          isv_goods_no

    UNION ALL

    --出管表出库件数
    SELECT
      SUBSTR(t1.creatdate, 1, 10) AS exp_date,
      t3.wareid goods_no,
      t1.orgid AS distribute_no, -- 配送中心
      t1.sid AS warehouse_no, -- 仓库
      0 AS shelves_qtty,
      0 AS stock_qty,
      0 AS canuse_qty,
      SUM(ABS(t3.num)) AS wms_rec_qtty,
      0 AS sub_qtty
    FROM
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_fdm_pek_jd_chuguan_chain
        WHERE
          start_date <= '""" + zst.data_day_str + """'
          AND end_date > '""" + zst.data_day_str + """'
          AND typeid IN('201', '202')
          AND SUBSTR(creatdate, 1, 10) >= '""" + zst.data_day_str + """'
      )
      t1
    JOIN
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_fdm_pek_jd_qingdan_chain
        WHERE
          start_date <= '""" + zst.data_day_str + """'
          AND end_date > '""" + zst.data_day_str + """'
          AND LENGTH(wareid) IN(10, 11, 14) --限制POP Sku
      )
      t3
    ON
      t1.kdanhao = t3.kdanhao
    GROUP BY
      SUBSTR(t1.creatdate, 1, 10),
      t1.orgid,
      t1.sid,
      t3.wareid

    UNION ALL

    --fbp出管库存
    SELECT
      dt AS exp_date,
      sku_id AS goods_no,
      d.delv_center_num AS distribute_no,
      d.store_id AS warehouse_no,
      0 AS shelves_qtty,
      SUM(stock_qtty) AS stock_qty,
      SUM(stock_qtty + in_transit_qtty + reserve - num_order_transfer - num_nosale - num_app_booking - num_order_booking - num_inner_trans - inner_out_qtty) AS canuse_qty, --(库存-订单转移预定库存-不可售库存-申请单预定库存量-预售库存量)
      0 AS wms_rec_qtty,
      0 AS sub_qtty
    FROM
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_gdm_m08_item_stock_day_sum
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND LENGTH(sku_id) >= 10
          AND stock_qtty < 999999
          AND stock_qtty > 0
      )
      t1
    JOIN retail_benchmark_10t.benchmark_dim_wms_store d
    ON
      t1.int_org_num = d.dim_store_num
    GROUP BY
      dt,
      sku_id,
      d.delv_center_num,
      d.store_id

    UNION ALL

    --sop库存表

    SELECT
      '""" + zst.data_day_str + """' AS exp_date,
      t1.sp_goods_no AS goods_no,
      t2.distribution_no AS distribute_no,
      t2.erp_warehouse_no AS warehouse_no,
      0 AS shelves_qtty,
      SUM(stock_num) AS stock_qty, ----可用库存加上订单预占
      SUM(stock_num + occupy_num) AS canuse_qty,
      0 AS wms_rec_qtty,
      0 AS sub_qtty
    FROM
      (
        SELECT
          *
        FROM
          retail_benchmark_10t.benchmark_fdm_eclp_shopstock_shop_stock_chain
        WHERE
          start_date <= '""" + zst.data_day_str + """'
          AND end_date > '""" + zst.data_day_str + """'
          AND yn = 1
          AND stock_num > 0
      )
      t1
    LEFT JOIN
      (
        SELECT
          distribution_no,
          erp_warehouse_no,
          warehouse_no
        FROM
          retail_benchmark_10t.benchmark_fdm_eclp_master_warehouse_chain
        WHERE
          dp = 'ACTIVE'
          AND yn = 1
        GROUP BY
          distribution_no,
          erp_warehouse_no,
          warehouse_no
      )
      t2
    ON
      t1.warehouse_no = t2.warehouse_no
    GROUP BY
      t1.sp_goods_no,
      t2.distribution_no,
      t2.erp_warehouse_no
  )
  A
JOIN
  (
    SELECT
      item_sku_id,
      COALESCE(shop_id, pop_vender_id, '无') AS shop_id,
      COALESCE(shop_name, pop_vender_name, '无') AS shop_name
    FROM
      retail_benchmark_10t.benchmark_gdm_m03_item_sku_act
    WHERE
      dt = '""" + zst.data_day_str + """'
      AND item_third_cate_cd NOT IN(1009, 874, 1137, 10011, 10970, 2643, 1446, 10968, 6881, 10010, 466, 463, 512, 6980, 12258, 1195, 10969, 982, 12360, 12365)
      AND
      (
        shop_id IS NOT NULL
        OR pop_vender_id IS NOT NULL
      )
      AND
      (
        shop_id <> ''
        OR pop_vender_id <> ''
      )
      AND shop_name NOT LIKE '%测试%'
      AND pop_vender_name NOT LIKE '%测试%'
  )
  b
ON
  A.goods_no = b.item_sku_id
JOIN retail_benchmark_10t.benchmark_dim_wms_Store d
ON
  A.distribute_no = d.delv_center_num
  AND A.warehouse_no = d.store_id
WHERE
  shop_id IS NOT NULL
  AND shop_name IS NOT NULL
  AND shop_id <> ''
  AND
  (
    shelves_qtty > 0
    OR stock_qty > 0
    OR canuse_qty <> 0
    OR wms_rec_qtty > 0
    OR sub_qtty > 0
  )
GROUP BY
  exp_date,
  d.dim_store_name,
  d.dim_store_num,
  b.shop_id,
  b.shop_name,
  A.goods_no ;
"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_app_store_shangzhi_oper_sum', sql=sql,
             exec_engine='spark', retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true",
                         "--conf spark.sql.shuffle.partitions=2000",
                         "--conf spark.dynamicAllocation.maxExecutors=900",
                         "--conf spark.driver.memory=12g"
                         ])
