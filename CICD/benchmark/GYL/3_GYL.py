#!/usr/bin/env python3

# 696062

import sys
import os

sys.path.append(os.getenv('HIVE_TASK'))
from HiveTask import HiveTask
from datetime import *
from string import *

ht = HiveTask()

insertDay = date.today().strftime("%Y-%m-%d")


# 定义开始、结束时间段转换，对合并动态分区小文件使用
def getDay(begin_date, end_date):
    date_list = []
    start_date = datetime.strptime(begin_date, "%Y-%m-%d")
    end_date = datetime.strptime(end_date, "%Y-%m-%d")
    while start_date <= end_date:
        date_str = start_date.strftime("%Y-%m-%d")
        date_list.append(date_str)
        start_date += timedelta(days=1)
    return date_list


# 执行脚本获取库名及表名
db_name = 'retail_benchmark_10t'
tb_name = 'benchmark_app_order_so1_so_item'

# 日期传参 根据产值偏移量控制传参
one_day = ht.oneday(0)
today = ht.oneday(1)
data_369 = ht.oneday(-369)
data_370 = ht.oneday(-370)

####目标表加工SQL
sql = """
use """ + db_name + """;

from
(
select start_date,
       change_code,
       id,
       so_id,
       shop_goods_id,
       shop_goods_no,
       seller_goods_sign,
       shop_goods_name,
       goods_id,
       goods_no,
       goods_name,
       sp_goods_no,
       isv_goods_no,
       apply_outstore_qty,
       real_outtore_qty,
       goods_status,
       price,
       dept_id,
       create_time,
       update_time,
       create_user,
       update_user,
       yn,
       ts,
       version,
       reserve1,
       reserve2,
       test,
       so_goods_weight,
       dept_apply_out_qty,
       dept_real_out_qty,
       goods_level,
       gid,
       insure_price,
       insured_price_flag,
       print_name,
       batch_no,
       production_date,
       expiration_date,
       order_line,
       occupy_change_num,
       assigned_change_num,
       goods_amount,
       skyworth_price,
       skyworth_detail_no,
       install_company,
       real_occupy_qty,
       packaging_details,
       consignment_sp_goods_no,
       suit_id,
       so_item_mark,
       jfs_key,
       so_goods_volume,
       stock_version,
       '2' as sys_source
from retail_benchmark_10t.benchmark_fdm_eclp_so1_so_item_chain
where dp = 'ACTIVE'
and substr(create_time,1,10) >= sysdate(-370)

union all

select null as start_date,
       null as change_code,
       id,
       so_id,
       shop_goods_id,
       car_goods.shop_goods_no,
       seller_goods_sign,
       shop_goods_name,
       goods1.goods_id,
       car_goods.goods_no,
       goods_name,
       sp_goods_no,
       isv_goods_no,
       apply_outstore_qty,
       real_outtore_qty,
       goods_status,
       price,
       dept_id,
       create_time,
       update_time,
       create_user,
       update_user,
       yn,
       ts,
       null as version,
       null as reserve1,
       null as reserve2,
       null as test,
       so_goods_weight,
       null as dept_apply_out_qty,
       null as dept_real_out_qty,
       goods_level,
       null as gid,
       null as insure_price,
       null as insured_price_flag,
       null as print_name,
       null as batch_no,
       null as production_date,
       null as expiration_date,
       null as order_line,
       null as occupy_change_num,
       null as assigned_change_num,
       goods_amount,
       null as skyworth_price,
       null as skyworth_detail_no,
       null as install_company,
       real_occupy_qty,
       null as packaging_details,
       null as consignment_sp_goods_no,
       null as suit_id,
       null as so_item_mark,
       null as jfs_key,
       null as so_goods_volume,
       null as stock_version,
       '1' as sys_source
from
(
select coalesce(cargo.id,goods.id) as id,
       coalesce(cargo.so_id,goods.so_id) as so_id,
     cargo.order_no,
     cargo.tenant_id,
       goods.shop_goods_no,
       cargo.seller_goods_sign,
       goods.shop_goods_name,
       cargo.goods_no,
       cargo.goods_name,
       goods.sp_goods_no,
       cargo.isv_goods_no,
       coalesce(cargo.cargo_quantity,goods.goods_quantity) as apply_outstore_qty,
       cargo.real_outtore_qty,
       cargo.so_goods_weight,
       cargo.goods_status,
       goods.price,
       coalesce(cargo.create_time,goods.create_time) as create_time,
       coalesce(cargo.update_time,goods.update_time) as update_time,
       coalesce(cargo.create_user,goods.create_user) as create_user,
       coalesce(cargo.update_user,goods.update_user) as update_user,
       coalesce(cargo.yn,goods.yn) as yn,
       coalesce(cargo.ts,goods.ts) as ts,
       cargo.goods_level,
       goods.goods_amount,
       cargo.real_occupy_qty
from
(
select attr2 as id,
       regexp_replace(order_no,'ESL','') as so_id,
     order_no,
     tenant_id,
     attr3 as seller_goods_sign,
     ref_goods_no,
       cargo_no as goods_no,
       cargo_name as goods_name,
       isv_cargo_no as isv_goods_no,
       cargo_quantity,
       actual_production_quantity as real_outtore_qty,
       cargo_weight as so_goods_weight,
       substr(cargo_level,1,1) as goods_status,
       create_time,
       update_time,
       create_user,
       update_user,
       yn,
       ts,
       cargo_level as goods_level,
       cargo_occupy_quantity as real_occupy_qty
from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_cargo_chain
where dp = 'ACTIVE'
and substr(create_time,1,10) >= sysdate(-370)
and tenant_id = '1000'
and order_group = 'Outbound'
) cargo

full join

(
select attr2 as id,
       regexp_replace(order_no,'ESL','') as so_id,
       order_no,
       tenant_id,
       goods_no as shop_goods_no,
     goods_name as shop_goods_name,
       channel_goods_no as sp_goods_no,
       goods_unit_price as price,
     goods_quantity,
       goods_amount,
       create_time,
       update_time,
       create_user,
       update_user,
       yn,
       ts
from retail_benchmark_10t.benchmark_fdm_cp_order_0_t_order_goods_chain
where dp = 'ACTIVE'
and substr(create_time,1,10) >= sysdate(-370)
and tenant_id = '1000'
and order_group = 'Outbound'
) goods
on cargo.order_no = goods.order_no
and cargo.tenant_id = goods.tenant_id
and case when coalesce(cargo.ref_goods_no,'') = '' then concat('hive',rand()) else cargo.ref_goods_no end = case when coalesce(goods.shop_goods_no,'') = '' then concat('hive',rand()) else goods.shop_goods_no end
) car_goods

left join

(
select tenant_id,
       order_no,
       fulfillment_account_no
from retail_benchmark_10t.benchmark_fdm_cp_order_t_order_main_chain
where dp = 'ACTIVE'
and substr(create_time,1,10) >= sysdate(-370)
and tenant_id = '1000'
and order_group = 'Outbound'
) main
on car_goods.order_no = main.order_no
and car_goods.tenant_id = main.tenant_id

left join

(
select id as shop_goods_id,
       shop_goods_no
from retail_benchmark_10t.benchmark_fdm_eclp_goods1_shop_goods_chain
where dp = 'ACTIVE'
--and substr(create_time,1,10) >= sysdate(-370)
group by id,
         shop_goods_no
) shop_goods
on case when coalesce(car_goods.shop_goods_no,'') = '' then concat('hive',rand()) else car_goods.shop_goods_no end = shop_goods.shop_goods_no

left join

(select id as goods_id,
        goods_no
from retail_benchmark_10t.benchmark_fdm_eclp_goods1_goods_chain
where dp = 'ACTIVE'
--and substr(create_time,1,10) >= sysdate(-370)
group by id,
         goods_no
) goods1
on case when coalesce(car_goods.goods_no,'') = '' then concat('hive',rand()) else car_goods.goods_no end = goods1.goods_no

left join

(
select id as dept_id,
       dept_no
from retail_benchmark_10t.benchmark_fdm_eclp_master_dept_chain
where dp = 'ACTIVE'
--and substr(create_time,1,10) >= sysdate(-370)
group by id,
         dept_no
) dept
on case when coalesce(main.fulfillment_account_no,'') = '' then concat('hive',rand()) else main.fulfillment_account_no end = dept.dept_no
) x

insert overwrite table """ + tb_name + """ partition (dp = 'ACTIVE', dt = '""" + insertDay + """')
select *
where  substr(create_time,1,10) >= '""" + data_369 + """'

insert overwrite table """ + tb_name + """ partition (dp = 'HISTORY', dt = '""" + insertDay + """')
select *
where substr(create_time,1,10) = '""" + data_370 + """'
;
"""
# 目标表加工
# print(sql_1)
ht.exec_sql(schema_name=db_name, sql=sql, table_name=tb_name, exec_engine='spark', retry_with_hive=False,
            spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                        "--conf spark.sql.adaptive.repartition.enabled=true"])
