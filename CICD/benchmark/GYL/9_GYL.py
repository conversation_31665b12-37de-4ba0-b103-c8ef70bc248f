#!/usr/bin/env python3

# 706055

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = zst.stat_ct
stat_begin = zst.stat_begin

insertDay=datetime.date.today().strftime("%Y-%m-%d")

dest_table_name = 'benchmark_app_zs_z1601_store_detail_new'

if stat_ct_cd == 'day':
    sql = """
     USE retail_benchmark_10t;

     INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """', stat_ct_cd='""" + stat_ct_cd + """')
     SELECT
            store_sku_info.shop_id                                               AS shop_id
           ,'""" + stat_ct + """'                                                AS stat_ct
           ,""" + zst.data_day_int + """                                         AS data_dt
           ,store_sku_info.store_unq_id                                          AS store_unq_id
           ,store_sku_info.store_unq_nm                                          AS store_unq_nm
           ,store_sku_info.stk_shlf_qty                                          AS stk_shlf_qty
           ,store_sku_info.stk_qty                                               AS stk_qty
           ,store_sku_info.avl_stk_qty                                           AS avl_stk_qty
           ,store_sku_info.stk_amt                                               AS stk_amt
           ,store_sku_info.avl_stk_qty / store_sku_info.sale_qty * 2             AS stk_turn_days
           ,store_sku_info.ex_stk_qty                                            AS ex_stk_qty
           ,store_sku_info.rtn_stk_qty                                           AS rtn_stk_qty
           ,wms_shangzhi_ob_det.ex_stk_ord_num                                   AS ex_stk_ord_num
           ,store_sku_info.ex_stk_ord_amt                                        AS ex_stk_ord_amt
           ,store_sku_info.sale_qty                                              AS sale_qty

     FROM  (
            SELECT
                   shop_id                                                       AS shop_id
                  ,store_unq_id                                                  AS store_unq_id
                  ,MAX(store_unq_nm)                                             AS store_unq_nm
                  ,SUM(stk_shlf_qty)                                             AS stk_shlf_qty
                  ,SUM(stk_qty)                                                  AS stk_qty
                  ,SUM(avl_stk_qty)                                              AS avl_stk_qty
                  ,SUM(stk_amt)                                                  AS stk_amt
                  ,SUM(ex_stk_qty)                                               AS ex_stk_qty
                  ,SUM(rtn_stk_qty)                                              AS rtn_stk_qty
                  ,SUM(ex_stk_ord_amt)                                           AS ex_stk_ord_amt
                  ,SUM(sale_qty)                                                 AS sale_qty

            FROM   retail_benchmark_10t.benchmark_adm_s08_zs_store_sku_info_new

            WHERE  dt = '""" + zst.data_day_str + """'

            GROUP  BY shop_id, store_unq_id
           ) store_sku_info

     LEFT   OUTER JOIN
           (
            SELECT /*+ MAPJOIN(wms_store)*/
                   wms_store.store_unq_id                                        AS store_unq_id
                  ,shangzhi_ob_det.shop_id                                       AS shop_id
                  ,COUNT(DISTINCT sale_ord_id)                                   AS ex_stk_ord_num

            FROM  (
                   SELECT
                          goods_no                                               AS itm_sku_id
                         ,ord_id                                                 AS sale_ord_id
                         ,shop_id                                                AS shop_id
                         ,distribute_no                                          AS dc_id
                         ,warehouse_no                                           AS store_id

                   FROM   retail_benchmark_10t.benchmark_app_store_shangzhi_shop_ob

                   WHERE  dt = '""" + zst.data_day_str + """'
                   AND    shop_id > 0
                  ) shangzhi_ob_det

            LEFT   OUTER JOIN
                  (
                   SELECT
                          delv_center_num                                        AS dc_id
                         ,store_id                                               AS store_id
                         ,dim_store_num                                          AS store_unq_id

                   FROM   retail_benchmark_10t.benchmark_dim_wms_store
                  ) wms_store
            ON     shangzhi_ob_det.dc_id = wms_store.dc_id
            AND    shangzhi_ob_det.store_id = wms_store.store_id

            GROUP  BY wms_store.store_unq_id, shangzhi_ob_det.shop_id
           ) wms_shangzhi_ob_det
     ON     store_sku_info.shop_id = wms_shangzhi_ob_det.shop_id
     AND    store_sku_info.store_unq_id = wms_shangzhi_ob_det.store_unq_id
     ;
     """
else:
    sql = """
     USE retail_benchmark_10t;

     INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """', stat_ct_cd='""" + stat_ct_cd + """')
     SELECT
            shop_id                                                              AS shop_id
           ,'""" + stat_ct + """'                                                AS stat_ct
           ,""" + zst.data_day_int + """                                         AS data_dt
           ,store_unq_id                                                         AS store_unq_id
           ,MAX(store_unq_nm)                                                    AS store_unq_nm
           ,SUM(stk_shlf_qty)                                                    AS stk_shlf_qty
           ,SUM(stk_qty)                                                         AS stk_qty
           ,SUM(avl_stk_qty)                                                     AS avl_stk_qty
           ,SUM(stk_amt)                                                         AS stk_amt
           ,SUM(
                CASE WHEN dt IN ('""" + stat_begin + """', '""" + zst.data_day_str + """') THEN avl_stk_qty
                     ELSE 0
                END
               )
            * (DATEDIFF('""" + zst.data_day_str + """', '""" + stat_begin + """') + 1)
            / SUM(sale_qty)
            * 2                                                                  AS stk_turn_days
           ,SUM(ex_stk_qty)                                                      AS ex_stk_qty
           ,SUM(rtn_stk_qty)                                                     AS rtn_stk_qty
           ,SUM(ex_stk_ord_num)                                                  AS ex_stk_ord_num
           ,SUM(ex_stk_ord_amt)                                                  AS ex_stk_ord_amt
           ,SUM(sale_qty)                                                        AS sale_qty

     FROM  (
            SELECT
                   *

            FROM   """ + dest_table_name + """

            WHERE  dt >= '""" + stat_begin + """'
            AND    dt <= '""" + zst.data_day_str + """'
            AND    stat_ct_cd = 'day'
           ) store_detail

     GROUP  BY shop_id, store_unq_id
     ;
     """

zst.exec_sql(schema_name="retail_benchmark_10t", table_name=dest_table_name, sql=sql, exec_engine='spark',
             retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
