#!/usr/bin/env python3

# buffaloID: 502235

import datetime
from ZSTask import ZSTask
zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

insertDay=datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;
INSERT OVERWRITE TABLE retail_benchmark_10t.benchmark_adm_m03_zs_spu_info_new PARTITION (dt='""" + insertDay + """')
select
    t1.itm_id as itm_id,
    t1.itm_nm AS itm_nm,
    t1.brand_cd as brand_cd,
    t2.main_brand_id as main_brand_cd,
    t2.brand_group_name as main_brand_nm,
    t1.itm_typ as itm_typ,
    t1.itm_num as itm_num,
    t1.jd_prc AS jd_prc
from
(
  SELECT itm_id AS itm_id,
               itm_nm AS itm_nm,
               brand_cd AS brand_cd,
               itm_typ AS itm_typ,
               itm_num AS itm_num,
               AVG(jd_prc) AS jd_prc
          FROM retail_benchmark_10t.benchmark_adm_th03_merchandise_basic_info
         WHERE dt = '""" + zst.data_day_str + """'
           AND shop_id > 0
           AND free_gds_flg = 0
           AND zs_itm_sku_vld_flg = 1
           AND substring_index(regexp_replace(itm_sku_nm, '非商品|邮费链接|赠品|非卖品|赠送品|5分好评返|晒单返现|不要单独拍下|请勿单独购买|勿购|勿拍|不发货|请勿评价|请勿单拍|请勿乱拍|会员保障及换新|补差|邮费补款|邮费补拍|运费补款|运费补拍|差价补拍|补邮费|补运费|补配送费|补邮专拍|配送费补款|配送费补差|配送费补拍', 'free_gds_flg'), 'free_gds_flg', 2) 
                             = regexp_replace(itm_sku_nm, '非商品|邮费链接|赠品|非卖品|赠送品|5分好评返|晒单返现|不要单独拍下|请勿单独购买|勿购|勿拍|不发货|请勿评价|请勿单拍|请勿乱拍|会员保障及换新|补差|邮费补款|邮费补拍|运费补款|运费补拍|差价补拍|补邮费|补运费|补配送费|补邮专拍|配送费补款|配送费补差|配送费补拍', 'free_gds_flg')                      -- 赠品过滤规则，命中两个及以上识别为赠品
           AND shop_id != 799638      -- 家电事业部屏蔽需求，过滤POP测试店铺               
         GROUP BY itm_id, 
                  itm_nm, 
                  brand_cd, 
                  itm_typ, 
                  itm_num
) t1

LEFT OUTER JOIN 
   (select  brand_id,
            main_brand_id,
            brand_group_name
    from 
            retail_benchmark_10t.benchmark_fdm_forest_brands_chain  
            where start_date <= '""" + zst.data_day_str + """' 
              and end_date > '""" + zst.data_day_str + """'
   ) t2 
          on t1.brand_cd = t2.brand_id
          ;

"""
zst.exec_sql(schema_name = 'retail_benchmark_10t', table_name = 'benchmark_adm_m03_zs_spu_info_new' ,sql = sql,exec_engine='spark',retry_with_hive=False,
spark_args=[
      "--conf spark.driver.memory=10g",
      "--conf spark.executor.memory=10g",
      "--conf spark.dynamicAllocation.maxExecutors=700",
      "--conf spark.dynamicAllocation.enabled=true",
      "--conf spark.speculation=true",
      "--conf spark.shuffle.service.enabled=true",
      "--conf spark.sql.shuffle.partitions=5000",
      "--conf spark.sql.adaptive.enabled=false",
      "--conf spark.sql.hive.mergeFiles=true",
      "--conf spark.sql.broadcastTimeout=3600",
      "--conf spark.sql.autoBroadcastJoinThreshold=262144000"
])
