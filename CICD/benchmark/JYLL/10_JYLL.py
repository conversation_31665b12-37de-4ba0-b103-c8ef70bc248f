# !/usr/bin/env python3

# buffaloID: 516515

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

print(stat_ct_cd)
print(stat_ct)
print(stat_begin)

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;
INSERT OVERWRITE TABLE  retail_benchmark_10t.benchmark_adm_zs_z0304_shop_repurchase_pre PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
SELECT t4.shop_id AS shop_id,
       t4.shop_typ AS shop_typ,
       t4.stat_cycle AS stat_cycle,
       t4.main_itm_scnd_catg_cd AS main_itm_scnd_catg_cd,
       t4.main_itm_scnd_catg_nm AS main_itm_scnd_catg_nm,
       t4.shop_level AS shop_level,
       CASE
         WHEN t4.grouping__id = '7' THEN
          t4.chan_cd
         WHEN t4.grouping__id = '5' THEN
          '99'
         ELSE
          '-1'
       END AS chan_cd,
       COUNT(distinct case
               when t4.par_ord_num > 1 then
                t4.usr_log_acct
               else
                null
             end) AS repur_num,
       COUNT(distinct t4.usr_log_acct) AS cust_num
  FROM (SELECT t.shop_id                AS shop_id,
               t2.shop_typ              AS shop_typ,
               """ + stat_ct + """      AS stat_cycle,
               t2.main_itm_scnd_catg_cd AS main_itm_scnd_catg_cd,
               t2.main_itm_scnd_catg_nm AS main_itm_scnd_catg_nm,
               t2.shop_level            AS shop_level,
               t.chan_cd                AS chan_cd,
               t.usr_log_acct           AS usr_log_acct,
               --   ord_num               AS ord_num,,
               t.par_ord_num  AS par_ord_num,
               t.GROUPING__ID as GROUPING__ID
          FROM (SELECT shop_id      AS shop_id,
                       chan_cd      AS chan_cd,
                       usr_log_acct AS usr_log_acct,
                       --    COUNT(DISTINCT sale_ord_id) as ord_num,
                       COUNT(DISTINCT par_sale_ord_id) as par_ord_num,
                       GROUPING__ID as GROUPING__ID
                  FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det
                 WHERE  dt <= '""" + zst.data_day_str + """'
                 AND    dt >= '""" + stat_begin + """'
                   AND is_deal_ord = 1
                 GROUP BY shop_id,
                          chan_cd,
                          usr_log_acct 
                GROUPING SETS((shop_id, chan_cd, usr_log_acct),
                (shop_id, usr_log_acct))) t
          LEFT OUTER JOIN (SELECT shop_id,
                                 shop_typ,
                                 main_itm_scnd_catg_cd,
                                 main_itm_scnd_catg_nm,
                                 shop_level
                            FROM retail_benchmark_10t.benchmark_app_zs_z0601_shop_level_info
                           WHERE dt = '""" + zst.data_day_str + """'
                             AND stat_ct_cd = 'day') t2
            ON t.shop_id = t2.shop_id) t4
 GROUP BY t4.shop_id,
          t4.shop_typ,
          t4.stat_cycle,
          t4.main_itm_scnd_catg_cd,
          t4.main_itm_scnd_catg_nm,
          t4.shop_level,
          CASE
            WHEN t4.grouping__id = '7' THEN
             t4.chan_cd
            WHEN t4.grouping__id = '5' THEN
             '99'
            ELSE
             '-1'
          END;

"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_adm_zs_z0304_shop_repurchase_pre', sql=sql,
             exec_engine='spark', spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
    ])

sql1 = """
INSERT OVERWRITE TABLE  retail_benchmark_10t.benchmark_app_zs_z0304_shop_repurchase_rate PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
SELECT t1.shop_id AS shop_id,
       t1.shop_typ AS shop_typ,
       t1.stat_cycle AS stat_cycle,
       t1.main_itm_scnd_catg_cd AS main_itm_scnd_catg_cd,
       t1.main_itm_scnd_catg_nm AS main_itm_scnd_catg_nm,
       t1.shop_level AS shop_level,
       t1.chan_cd AS chan_cd,
       t1.repur_rate as repur_rate,
       m1.repur_rate as avg_repur_rate,
       COALESCE(m2.repur_rate, -1) as pre_avg_repur_rate
  FROM (SELECT t.shop_id AS shop_id,
               t.shop_typ AS shop_typ,
               t.stat_cycle AS stat_cycle,
               t.main_itm_scnd_catg_cd AS main_itm_scnd_catg_cd,
               t.main_itm_scnd_catg_nm AS main_itm_scnd_catg_nm,
               t.shop_level AS shop_level,
               t.chan_cd AS chan_cd,
               SUM(t.repur_num / t.cust_num) AS repur_rate
          FROM retail_benchmark_10t.benchmark_adm_zs_z0304_shop_repurchase_pre t
         WHERE dt = '""" + zst.data_day_str + """'
           AND stat_ct_cd = '""" + stat_ct_cd + """'
         GROUP BY t.shop_id,
                  t.shop_typ,
                  t.stat_cycle,
                  t.main_itm_scnd_catg_cd,
                  t.main_itm_scnd_catg_nm,
                  t.shop_level,
                  t.chan_cd) t1
  LEFT OUTER JOIN (SELECT --同行同级均值
                    shop_typ,
                    stat_cycle,
                    main_itm_scnd_catg_cd,
                    shop_level,
                    chan_cd,
                    AVG(repur_num / cust_num) AS repur_rate
                     FROM retail_benchmark_10t.benchmark_adm_zs_z0304_shop_repurchase_pre
                    WHERE dt = '""" + zst.data_day_str + """'
                      AND stat_ct_cd = '""" + stat_ct_cd + """'
                    GROUP BY shop_typ,
                             stat_cycle,
                             main_itm_scnd_catg_cd,
                             shop_level,
                             chan_cd) m1
    on t1.shop_typ = m1.shop_typ
   and t1.stat_cycle = m1.stat_cycle
   and t1.main_itm_scnd_catg_cd = m1.main_itm_scnd_catg_cd
   and t1.shop_level = m1.shop_level
   and t1.chan_cd = m1.chan_cd
  LEFT OUTER JOIN (SELECT --同行上级均值
                    shop_typ,
                    stat_cycle,
                    main_itm_scnd_catg_cd,
                    chan_cd,
                    AVG(repur_num / cust_num) AS repur_rate,
                    case when shop_level - 1 <= 0 then -1
                     else shop_level - 1
                    end as shop_up_level  --同行上级  最高级0级的 上级置为-1
                     FROM retail_benchmark_10t.benchmark_adm_zs_z0304_shop_repurchase_pre
                    WHERE dt = '""" + zst.data_day_str + """'
                      AND stat_ct_cd = '""" + stat_ct_cd + """'
                    GROUP BY shop_typ,
                             stat_cycle,
                             main_itm_scnd_catg_cd,
                             chan_cd,
                             case when shop_level - 1 <= 0 then -1
                               else shop_level - 1
                          end) m2
    on t1.shop_typ = m2.shop_typ
   and t1.stat_cycle = m2.stat_cycle
   and t1.main_itm_scnd_catg_cd = m2.main_itm_scnd_catg_cd
   and t1.shop_level = m2.shop_up_level --同行上级
   and t1.chan_cd = m2.chan_cd;

"""

zst.exec_sql(schema_name='retail_benchmark_10t', table_name='benchmark_app_zs_z0304_shop_repurchase_rate', sql=sql1,
             retry_with_hive=False,
             exec_engine='spark', lzo_compress=True,
             lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd], spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
    ])
