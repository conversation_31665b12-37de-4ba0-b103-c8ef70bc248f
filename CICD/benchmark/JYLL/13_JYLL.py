#!/usr/bin/env python3

# buffaloID: 520535

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = zst.stat_ct
stat_begin = zst.stat_begin

dest_table_name = 'benchmark_app_zs_z0601_shop_index_overview_mid'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
  USE retail_benchmark_10t;
   INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
SELECT
  ord_index.shop_id,
  ord_index.chan_cd,
  """ + stat_ct + """ stat_ct,
  """ + zst.data_day_int + """ stat_dt,
  COALESCE(ord_index.pv, 0) pv,
  COALESCE(ord_index.uv, 0) uv,
  CASE
    WHEN COALESCE(ord_index.uv, 0) = 0
    THEN 0.0
    ELSE COALESCE(ord_index.spv, 0) / COALESCE(ord_index.uv, 0)
  END AS bnc_rt, -- 跳失率
  CASE
    WHEN COALESCE(ord_index.uv, 0) = 0
    THEN 0.0
    ELSE COALESCE(ord_index.pv, 0) / COALESCE(ord_index.uv, 0)
  END AS avg_depth,
  CASE
    WHEN COALESCE(ord_index.uv, 0) = 0
    THEN 0.0
    ELSE COALESCE(ord_index.tm_on_page, 0.0) / COALESCE(ord_index.uv, 0)
  END AS avg_tm_on_page,
  COALESCE(ord_index.add_cart_cust_qty, 0) AS add_cart_cust_qty,
  CASE
    WHEN ord_index.uv = 0
    THEN 0.0
    ELSE(ord_index.add_cart_cust_qty * 1.0) / ord_index.uv
  END AS add_cart_cust_rt, --加购客户转化率
  COALESCE(ord_index.ord_cust_qty, 0) AS ord_cust_qty, ---成交客户数
  CASE
    WHEN COALESCE(ord_index.uv, 0) = 0
    THEN 0.0
    ELSE COALESCE(ord_index.ord_cust_qty, 0) * 1.0 / COALESCE(ord_index.uv, 0)
  END AS ord_cust_rt,
  COALESCE(ord_index.ord_amt, 0.0) ord_amt,
  COALESCE(ord_index.sale_qty, 0) AS sale_qty, ---成交商品件数
  COALESCE(ord_index.ord_qty, 0) AS ord_qty, ---成交单量
  CASE
    WHEN COALESCE(ord_index.ord_cust_qty, 0) = 0
    THEN 0.0
    ELSE COALESCE(ord_index.ord_amt, 0.0) / COALESCE(ord_index.ord_cust_qty, 0)
  END AS avg_cust_prc,
  COALESCE(ord_index.shop_attn_num, 0) AS shop_attn_num,
  0 itm_attn_num,
  COALESCE(rc.thirty_repur_rate, 0.0) AS thirty_repur_rate, -- 30天复购率
  COALESCE(rc.ninty_repur_rate, 0.0) AS ninty_repur_rate, -- 90天复购率
  0 in_ord_cust_rt,
  COALESCE(ord_index.intro_ord_cust_qty, 0) AS intro_ord_cust_qty, ---下单客户数
  CASE
    WHEN COALESCE(ord_index.uv, 0) = 0
    THEN 0.0
    ELSE COALESCE(ord_index.intro_ord_cust_qty, 0) / COALESCE(ord_index.uv, 0)
  END AS intro_ord_cust_rt, ---下单转化率
  COALESCE(ord_index.intro_ord_amt, 0.0) AS intro_ord_amt, ---下单金额
  COALESCE(ord_index.intro_sale_qty, 0) AS intro_sale_qty, ---下单商品件数
  COALESCE(ord_index.intro_ord_qty, 0) AS intro_ord_qty, ---下单单量
  COALESCE(ord_index.deal_ord_cust_qty, 0) AS deal_ord_cust_qty, ---下单且成交客户数
  CASE
    WHEN COALESCE(ord_index.intro_ord_cust_qty, 0) = 0
    THEN 0.0
    ELSE COALESCE(ord_index.deal_ord_cust_qty, 0) / COALESCE(ord_index.intro_ord_cust_qty)
  END AS deal_ord_cust_rt, ---下单成交转化率
  COALESCE(ord_index.uv, 0) - COALESCE(ord_index.old_uv, 0) new_uv,
  ord_index.old_uv,
  CASE
    WHEN COALESCE(ord_index.uv, 0) = 0
    THEN 0.0
    ELSE COALESCE(ord_index.ord_amt, 0) * 1.0 / COALESCE(ord_index.uv, 0)
  END AS uv_value,
  is_new_cust
FROM
  (
    SELECT
      shop_id,
      chan_cd,
      SUM(add_cart_cust_qty) AS add_cart_cust_qty,
      SUM(tm_on_page) AS tm_on_page,
      SUM(ord_qty) AS ord_qty,
      SUM(ord_cust_qty) AS ord_cust_qty,
      SUM(ord_amt) AS ord_amt,
      SUM(sale_qty) AS sale_qty,
      SUM(intro_ord_qty) AS intro_ord_qty,
      SUM(intro_ord_cust_qty) AS intro_ord_cust_qty,
      SUM(intro_ord_amt) AS intro_ord_amt,
      SUM(intro_sale_qty) AS intro_sale_qty,
      SUM(deal_ord_cust_qty) AS deal_ord_cust_qty,
      SUM(shop_attn_num) AS shop_attn_num,
      SUM(ord_cust_qty_allnew) AS ord_cust_qty_allnew,
      SUM(ord_cust_qty_allold) AS ord_cust_qty_allold,
      SUM(ord_cust_qty_180new) AS ord_cust_qty_180new,
      SUM(ord_cust_qty_180old) AS ord_cust_qty_180old,
      SUM(ord_cust_qty_730new) AS ord_cust_qty_730new,
      SUM(ord_cust_qty_730old) AS ord_cust_qty_730old,
      is_new_cust,
      SUM(uv) AS uv,
      SUM(pv) AS pv,
      SUM(old_uv) old_uv,
      SUM(new_uv) new_uv,
      SUM(spv) spv
    FROM
      (
        SELECT
          shop_id,
          chan_cd, --99-整体 ,0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ
          add_cart_cust_qty, --加购客户数
          0 tm_on_page,
          ord_qty, --成交单量
          ord_cust_qty, --成交客户数
          ord_amt, --成交金额
          sale_qty, --成交商品件数
          intro_ord_qty, --下单单量
          intro_ord_cust_qty, --下单客户数
          intro_ord_amt, --下单金额
          intro_sale_qty, --下单商品件数
          deal_ord_cust_qty, --下单且成交客户数
          0 AS shop_attn_num, --店铺关注数
          ord_cust_qty_allnew, --成交新客户数(整体)
          ord_cust_qty_allold, --成交老客户数(整体)
          ord_cust_qty_180new, --成交新客户数(180天)
          ord_cust_qty_180old, --成交老客户数(180天)
          ord_cust_qty_730new, --成交新客户数(730天)
          ord_cust_qty_730old, --成交老客户数(730天)
          NULL is_new_cust,
          0 AS uv,
          0 AS pv,
          0 old_uv,
          0 new_uv,
          0 spv
        FROM
          retail_benchmark_10t.benchmark_adm_s04_zs_shop_ord_index_mid
        WHERE
          dt >= '""" + zst.data_day_str + """'
          AND dt <= '""" + zst.data_day_str + """'
          AND stat_ct_cd = '""" + stat_ct_cd + """'
          AND shop_sessn_src_zs_url_frst_catg_cd = '999999'
          AND sort_typ = 0
        --店铺关注表
        
        UNION ALL
        
        SELECT
          shop_id,
          chan_cd, --99-整体 ,0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ
          0 add_cart_cust_qty, --加购客户数
          0 tm_on_page,
          0 ord_qty, --成交单量
          0 ord_cust_qty, --成交客户数
          0 ord_amt, --成交金额
          0 sale_qty, --成交商品件数
          0 intro_ord_qty, --下单单量
          0 intro_ord_cust_qty, --下单客户数
          0 intro_ord_amt, --下单金额
          0 intro_sale_qty, --下单商品件数
          0 deal_ord_cust_qty, --下单且成交客户数
          follow_usr_qty AS shop_attn_num, --店铺关注数
          0 ord_cust_qty_allnew, --成交新客户数(整体)
          0 ord_cust_qty_allold, --成交老客户数(整体)
          0 ord_cust_qty_180new, --成交新客户数(180天)
          0 ord_cust_qty_180old, --成交老客户数(180天)
          0 ord_cust_qty_730new, --成交新客户数(730天)
          0 ord_cust_qty_730old, --成交老客户数(730天)
          NULL is_new_cust,
          0 AS uv,
          0 AS pv,
          0 old_uv,
          0 new_uv,
          0 spv
        FROM
          retail_benchmark_10t.benchmark_adm_s13_zs_shop_product_follow_list_sum_new
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND sort_typ = 11
          AND chan_cd = 99
          AND stat_ct_cd = '""" + stat_ct_cd + """'
        
        UNION ALL
        
        SELECT
          shop_traffics.shop_id,
          shop_traffics.chan_cd, --99-整体 ,0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ
          0 add_cart_cust_qty, --加购客户数
          0 tm_on_page,
          0 ord_qty, --成交单量
          0 ord_cust_qty, --成交客户数
          0 ord_amt, --成交金额
          0 sale_qty, --成交商品件数
          0 intro_ord_qty, --下单单量
          0 intro_ord_cust_qty, --下单客户数
          0 intro_ord_amt, --下单金额
          0 intro_sale_qty, --下单商品件数
          0 deal_ord_cust_qty, --下单且成交客户数
          COUNT(DISTINCT
          CASE
            WHEN shop_follow.usr_log_acct IS NOT NULL
              AND shop_traffics.req_tm < shop_follow.created_time ----浏览时间先于关注时间
            THEN shop_traffics.usr_log_acct
            ELSE NULL
          END) AS shop_attn_num, --店铺关注数
          0 ord_cust_qty_allnew, --成交新客户数(整体)
          0 ord_cust_qty_allold, --成交老客户数(整体)
          0 ord_cust_qty_180new, --成交新客户数(180天)
          0 ord_cust_qty_180old, --成交老客户数(180天)
          0 ord_cust_qty_730new, --成交新客户数(730天)
          0 ord_cust_qty_730old, --成交老客户数(730天)
          NULL is_new_cust,
          0 AS uv,
          0 AS pv,
          0 old_uv,
          0 new_uv,
          0 spv
        FROM
          (
            SELECT
              shop_id,
              chan_cd,
              req_tm,
              usr_log_acct,
              dt
            FROM
              retail_benchmark_10t.benchmark_adm_sch_d14_zs_all_chan_shop_traffics_di
            WHERE
              dt >= '""" + zst.data_day_str + """'
              AND dt <= '""" + zst.data_day_str + """'
          )
          shop_traffics
        LEFT JOIN
          (
            SELECT
              /*+mapjoin(vend_mapping)*/
              ven_follow.usr_log_acct AS usr_log_acct,
              ven_follow.vender_id,
              COALESCE(vend_mapping.shop_id, ven_follow.shop_id) AS shop_id,
              created_time
            FROM
              (
                SELECT
                  pin AS usr_log_acct,
                  vender_id,
                  created_time,
                  shop_id,
                  vender_name
                FROM
                  retail_benchmark_10t.benchmark_fdm_follow_vender_sns_follow_vender_chain
                WHERE
                  start_date <= '""" + zst.data_day_str + """'
                  AND end_date > '""" + zst.data_day_str + """'
                  AND SUBSTR(created_time, 1, 10) >= '""" + stat_begin + """'
                  AND SUBSTR(created_time, 1, 10) <= '""" + zst.data_day_str + """'
                GROUP BY
                  pin,
                  vender_id,
                  created_time,
                  shop_id,
                  vender_name
              )
              ven_follow
            LEFT OUTER JOIN
              (
                SELECT
                  shop_id,
                  vend_id
                FROM
                  retail_benchmark_10t.benchmark_gdm_m01_zs_shop_vend_mapping
                WHERE
                  dt = '""" + zst.data_day_str + """'
              )
              vend_mapping
            ON
              ven_follow.vender_id = vend_mapping.vend_id
          )
          shop_follow ON shop_traffics.usr_log_acct = shop_follow.usr_log_acct
          AND shop_traffics.shop_id = shop_follow.shop_id
          AND shop_traffics.dt = SUBSTR(shop_follow.created_time, 1, 10)
        GROUP BY
          shop_traffics.chan_cd,
          shop_traffics.shop_id
        
        UNION ALL
        
        SELECT
          std.shop_id,
          std.chan_cd,
          0 add_cart_cust_qty,
          SUM(std.tm_on_page) tm_on_page,
          0 AS ord_qty,
          0 AS ord_cust_qty,
          0 AS ord_amt,
          0 AS sale_qty,
          0 AS intro_ord_qty,
          0 AS intro_ord_cust_qty,
          0 AS intro_ord_amt,
          0 AS intro_sale_qty,
          0 AS deal_ord_cust_qty,
          0 AS shop_attn_num,
          0 AS ord_cust_qty_allnew,
          0 AS ord_cust_qty_allold,
          0 AS ord_cust_qty_180new,
          0 AS ord_cust_qty_180old,
          0 AS ord_cust_qty_730new,
          0 AS ord_cust_qty_730old,
          NULL is_new_cust,
          COUNT(DISTINCT std.brws_uniq_id) uv,
          SUM(std.pv) AS pv,
          SUM(old_uv) AS old_uv,
          SUM(new_uv) AS new_uv,
          SUM(
            CASE
              WHEN pv = 1
              THEN 1
              ELSE 0
            END) spv
        FROM
          (
            SELECT
              std.shop_id,
              std.chan_cd,
              SUM(std.tm_on_page) tm_on_page,
              std.brws_uniq_id,
              SUM(std.upv) AS pv,
              COUNT(DISTINCT
              CASE
                WHEN shop_brws.brws_uniq_id IS NOT NULL
                  OR shop_ord.usr_log_acct IS NOT NULL
                THEN std.brws_uniq_id
                ELSE NULL
              END) AS old_uv,
              COUNT(DISTINCT
              CASE
                WHEN NOT
                  (
                    shop_brws.brws_uniq_id IS NOT NULL
                    OR shop_ord.usr_log_acct IS NOT NULL
                  )
                THEN std.brws_uniq_id
                ELSE NULL
              END) AS new_uv,
              COUNT(DISTINCT spv) AS spv -- 计算逻辑有问题，废弃
            FROM
              (
                SELECT
                  shop_id,
                  chan_cd,
                  SUM(tm_on_page) AS tm_on_page,
                  brws_uniq_id,
                  usr_log_acct,
                  concat(sessn_id, '|', seq_num) AS spv, -- 计算逻辑有问题，废弃
                  COUNT(1) AS upv
                FROM
                  retail_benchmark_10t.benchmark_adm_sch_d14_zs_all_chan_shop_traffics_di
                WHERE
                  dt >= '""" + zst.data_day_str + """'
                  AND dt <= '""" + zst.data_day_str + """'
                GROUP BY
                  shop_id,
                  brws_uniq_id,
                  usr_log_acct,
                  chan_cd,
                  concat(sessn_id, '|', seq_num)
              )
              std
            LEFT JOIN
              (
                SELECT
                  shop_id,
                  brws_uniq_id,
                  chan_cd,
                  usr_log_acct
                FROM
                  retail_benchmark_10t.benchmark_adm_s14_zs_shop_history_brws_ord_mid
                WHERE
                  dt >= '""" + zst.data_day_str + """'
                  AND dt <= '""" + zst.data_day_str + """'
                  AND dp = 'brws'
                  AND stat_ct_cd = '""" + stat_ct_cd + """'
              )
              shop_brws
            ON
              std.brws_uniq_id = shop_brws.brws_uniq_id
              AND std.shop_id = shop_brws.shop_id
              AND std.chan_cd = shop_brws.chan_cd
            LEFT JOIN
              (
                SELECT
                  shop_id,
                  brws_uniq_id,
                  chan_cd,
                  usr_log_acct
                FROM
                  retail_benchmark_10t.benchmark_adm_s14_zs_shop_history_brws_ord_mid
                WHERE
                  dt >= '""" + zst.data_day_str + """'
                  AND dt <= '""" + zst.data_day_str + """'
                  AND dp = 'ord'
                  AND stat_ct_cd = '""" + stat_ct_cd + """'
              )
              shop_ord
            ON
              std.usr_log_acct = shop_ord.usr_log_acct
              AND std.shop_id = shop_ord.shop_id
              AND std.chan_cd = shop_ord.chan_cd
            GROUP BY
              std.shop_id,
              std.chan_cd,
              std.brws_uniq_id
          )
          std
        GROUP BY
          std.shop_id,
          std.chan_cd
        
        UNION ALL
        
        SELECT
          shop_id,
          99 chan_cd,
          0 add_cart_cust_qty,
          SUM(tm_on_page) AS tm_on_page,
          0 AS ord_qty,
          0 AS ord_cust_qty,
          0 AS ord_amt,
          0 AS sale_qty,
          0 AS intro_ord_qty,
          0 AS intro_ord_cust_qty,
          0 AS intro_ord_amt,
          0 AS intro_sale_qty,
          0 AS deal_ord_cust_qty,
          0 AS shop_attn_num,
          0 AS ord_cust_qty_allnew,
          0 AS ord_cust_qty_allold,
          0 AS ord_cust_qty_180new,
          0 AS ord_cust_qty_180old,
          0 AS ord_cust_qty_730new,
          0 AS ord_cust_qty_730old,
          NULL is_new_cust,
          SUM(uv) AS uv,
          SUM(pv) AS pv,
          SUM(old_uv) old_uv,
          SUM(new_uv) new_uv,
          SUM(bnc_uv) spv
        FROM
          (
            SELECT
              std.shop_id,
              std.chan_cd,
              SUM(std.tm_on_page) tm_on_page,
              COUNT(DISTINCT std.brws_uniq_id) uv,
              SUM(std.pv) AS pv,
              SUM(old_uv) AS old_uv,
              SUM(new_uv) AS new_uv,
              SUM(
                CASE
                  WHEN pv = 1
                  THEN 1
                  ELSE 0
                END) bnc_uv
            FROM
              (
                SELECT
                  std.shop_id,
                  std.chan_cd,
                  SUM(std.tm_on_page) tm_on_page,
                  std.brws_uniq_id,
                  SUM(std.upv) AS pv,
                  COUNT(DISTINCT
                  CASE
                    WHEN shop_brws.brws_uniq_id IS NOT NULL
                      OR shop_ord.usr_log_acct IS NOT NULL
                    THEN std.brws_uniq_id
                    ELSE NULL
                  END) AS old_uv,
                  COUNT(DISTINCT
                  CASE
                    WHEN NOT
                      (
                        shop_brws.brws_uniq_id IS NOT NULL
                        OR shop_ord.usr_log_acct IS NOT NULL
                      )
                    THEN std.brws_uniq_id
                    ELSE NULL
                  END) AS new_uv
                FROM
                  (
                    SELECT
                      shop_id,
                      chan_cd,
                      SUM(tm_on_page) AS tm_on_page,
                      brws_uniq_id,
                      usr_log_acct,
                      COUNT(1) AS upv
                    FROM
                      retail_benchmark_10t.benchmark_adm_sch_d14_zs_all_chan_shop_traffics_di
                    WHERE
                      dt >= '""" + zst.data_day_str + """'
                      AND dt <= '""" + zst.data_day_str + """'
                    GROUP BY
                      shop_id,
                      brws_uniq_id,
                      usr_log_acct,
                      chan_cd
                  )
                  std
                LEFT JOIN
                  (
                    SELECT
                      shop_id,
                      brws_uniq_id,
                      chan_cd,
                      usr_log_acct
                    FROM
                      retail_benchmark_10t.benchmark_adm_s14_zs_shop_history_brws_ord_mid
                    WHERE
                      dt >= '""" + zst.data_day_str + """'
                      AND dt <= '""" + zst.data_day_str + """'
                      AND dp = 'brws'
                      AND stat_ct_cd = '""" + stat_ct_cd + """'
                  )
                  shop_brws
                ON
                  std.brws_uniq_id = shop_brws.brws_uniq_id
                  AND std.shop_id = shop_brws.shop_id
                  AND std.chan_cd = shop_brws.chan_cd
                LEFT JOIN
                  (
                    SELECT
                      shop_id,
                      brws_uniq_id,
                      chan_cd,
                      usr_log_acct
                    FROM
                      retail_benchmark_10t.benchmark_adm_s14_zs_shop_history_brws_ord_mid
                    WHERE
                      dt >= '""" + zst.data_day_str + """'
                      AND dt <= '""" + zst.data_day_str + """'
                      AND dp = 'ord'
                      AND stat_ct_cd = '""" + stat_ct_cd + """'
                  )
                  shop_ord
                ON
                  std.usr_log_acct = shop_ord.usr_log_acct
                  AND std.shop_id = shop_ord.shop_id
                  AND std.chan_cd = shop_ord.chan_cd
                GROUP BY
                  std.shop_id,
                  std.chan_cd,
                  std.brws_uniq_id
              )
              std
            GROUP BY
              std.shop_id,
              std.chan_cd
          )
          a
        GROUP BY
          shop_id
        
        UNION ALL
        
        SELECT
          shop_id,
          0 chan_cd,
          0 add_cart_cust_qty,
          SUM(tm_on_page) AS tm_on_page,
          0 AS ord_qty,
          0 AS ord_cust_qty,
          0 AS ord_amt,
          0 AS sale_qty,
          0 AS intro_ord_qty,
          0 AS intro_ord_cust_qty,
          0 AS intro_ord_amt,
          0 AS intro_sale_qty,
          0 AS deal_ord_cust_qty,
          0 AS shop_attn_num,
          0 AS ord_cust_qty_allnew,
          0 AS ord_cust_qty_allold,
          0 AS ord_cust_qty_180new,
          0 AS ord_cust_qty_180old,
          0 AS ord_cust_qty_730new,
          0 AS ord_cust_qty_730old,
          NULL is_new_cust,
          SUM(uv) AS uv,
          SUM(pv) AS pv,
          SUM(old_uv) old_uv,
          SUM(new_uv) new_uv,
          SUM(bnc_uv) spv
        FROM
          (
            SELECT
              std.shop_id,
              std.chan_cd,
              SUM(std.tm_on_page) tm_on_page,
              COUNT(DISTINCT std.brws_uniq_id) uv,
              SUM(std.pv) AS pv,
              SUM(old_uv) AS old_uv,
              SUM(new_uv) AS new_uv,
              SUM(
                CASE
                  WHEN pv = 1
                  THEN 1
                  ELSE 0
                END) bnc_uv
            FROM
              (
                SELECT
                  std.shop_id,
                  std.chan_cd,
                  SUM(std.tm_on_page) tm_on_page,
                  std.brws_uniq_id,
                  SUM(std.upv) AS pv,
                  COUNT(DISTINCT
                  CASE
                    WHEN shop_brws.brws_uniq_id IS NOT NULL
                      OR shop_ord.usr_log_acct IS NOT NULL
                    THEN std.brws_uniq_id
                    ELSE NULL
                  END) AS old_uv,
                  COUNT(DISTINCT
                  CASE
                    WHEN NOT
                      (
                        shop_brws.brws_uniq_id IS NOT NULL
                        OR shop_ord.usr_log_acct IS NOT NULL
                      )
                    THEN std.brws_uniq_id
                    ELSE NULL
                  END) AS new_uv
                FROM
                  (
                    SELECT
                      shop_id,
                      chan_cd,
                      SUM(tm_on_page) AS tm_on_page,
                      brws_uniq_id,
                      usr_log_acct,
                      COUNT(1) AS upv
                    FROM
                      retail_benchmark_10t.benchmark_adm_sch_d14_zs_all_chan_shop_traffics_di
                    WHERE
                      dt >= '""" + zst.data_day_str + """'
                      AND dt <= '""" + zst.data_day_str + """'
                      AND chan_cd <> 20
                    GROUP BY
                      shop_id,
                      brws_uniq_id,
                      usr_log_acct,
                      chan_cd
                  )
                  std
                LEFT JOIN
                  (
                    SELECT
                      shop_id,
                      brws_uniq_id,
                      chan_cd,
                      usr_log_acct
                    FROM
                      retail_benchmark_10t.benchmark_adm_s14_zs_shop_history_brws_ord_mid
                    WHERE
                      dt >= '""" + zst.data_day_str + """'
                      AND dt <= '""" + zst.data_day_str + """'
                      AND dp = 'brws'
                      AND stat_ct_cd = '""" + stat_ct_cd + """'
                      AND chan_cd <> 20
                  )
                  shop_brws
                ON
                  std.brws_uniq_id = shop_brws.brws_uniq_id
                  AND std.shop_id = shop_brws.shop_id
                  AND std.chan_cd = shop_brws.chan_cd
                LEFT JOIN
                  (
                    SELECT
                      shop_id,
                      brws_uniq_id,
                      chan_cd,
                      usr_log_acct
                    FROM
                      retail_benchmark_10t.benchmark_adm_s14_zs_shop_history_brws_ord_mid
                    WHERE
                      dt >= '""" + zst.data_day_str + """'
                      AND dt <= '""" + zst.data_day_str + """'
                      AND dp = 'ord'
                      AND stat_ct_cd = '""" + stat_ct_cd + """'
                      AND chan_cd <> 20
                  )
                  shop_ord
                ON
                  std.usr_log_acct = shop_ord.usr_log_acct
                  AND std.shop_id = shop_ord.shop_id
                  AND std.chan_cd = shop_ord.chan_cd
                GROUP BY
                  std.shop_id,
                  std.chan_cd,
                  std.brws_uniq_id
              )
              std
            GROUP BY
              std.shop_id,
              std.chan_cd
          )
          a
        GROUP BY
          shop_id
      )
      ord_index
    GROUP BY
      shop_id,
      chan_cd,
      is_new_cust
  )
  ord_index
LEFT JOIN
  (
    SELECT
      shop_id AS shop_id,
      chan_cd AS chan_cd,
      SUM(
        CASE
          WHEN stat_ct_cd = 'd30'
          THEN(repur_num * 1.0) / cust_num
          ELSE 0.0
        END) AS thirty_repur_rate,
      SUM(
        CASE
          WHEN stat_ct_cd = 'd90'
          THEN(repur_num * 1.0) / cust_num
          ELSE 0.0
        END) AS ninty_repur_rate
    FROM
      retail_benchmark_10t.benchmark_adm_zs_z0304_shop_repurchase_pre
    WHERE
      dt >= '""" + zst.data_day_str + """'
      AND dt <= '""" + zst.data_day_str + """'
    GROUP BY
      shop_id,
      chan_cd
  )
  rc
ON
  rc.shop_id = ord_index.shop_id
  AND rc.chan_cd = ord_index.chan_cd ;
  """
zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, exec_engine='spark',
             lzo_compress=False,
             retry_with_hive=False, lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             spark_args=[
                 "--conf spark.sql.hive.mergeFiles=true",
                 "--conf spark.sql.adaptive.enabled=true",
                 "--conf spark.sql.adaptive.repartition.enabled=true"
             ])
