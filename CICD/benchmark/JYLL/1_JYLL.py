#!/usr/bin/env python3

# buffaloID: 464505

import datetime
from ZSTask import ZSTask

zst = ZSTask()

dest_table_name = 'benchmark_adm_th03_merchandise_basic_info'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """ 
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION
  (dt='""" + insertDay + """', shop_typ, zs_itm_sku_vld_flg)
SELECT 
  CASE
     WHEN pro_type = 'SELF-SHOP' THEN
      item_sku_act.shop_id
     ELSE
      item_sku_act.vend_id                                   
   END                                                         AS  vend_id,          --自营为shop_id,POP为商家id 
   item_sku_act.shop_id                                        AS  shop_id,          --店铺id
   item_sku_act.shop_name                                      AS  shop_nm,          --店铺名称
   item_sku_act.main_sku_id                                    AS  main_sku_id,      --主SKU商品编号   
   item_sku_act.item_sku_id                                    AS  itm_sku_id,       --SKU商品编号
   item_sku_act.item_sku_name                                  AS  itm_sku_nm,       --SKU商品名称
   item_sku_act.item_id                                        AS  itm_id,           --SPU商品编号
   item_sku_act.item_name                                      AS  itm_nm,           --SPU商品名称
   item_sku_act.brand_cd                                       AS  brand_cd,         --品牌代码
   item_sku_act.brand_name                                     AS  brand_dsc,        --品牌描述
   item_sku_act.item_first_cate_cd                             AS  itm_frst_catg_cd, --商品一级分类代码
   item_sku_act.item_first_cate_name                           AS  itm_frst_catg_nm, --商品一级分类名称
   item_sku_act.item_second_cate_cd                            AS  itm_scnd_catg_cd, --商品二级分类代码
   item_sku_act.item_second_cate_name                          AS  itm_scnd_catg_nm, --商品二级分类名称
   item_sku_act.item_third_cate_cd                             AS  itm_thrd_catg_cd, --商品三级分类代码
   item_sku_act.item_third_cate_name                           AS  itm_thrd_catg_nm, --商品三级分类名称
   item_sku_act.shelves_tm                                     AS  itm_sku_shlf_tm,  --SKU商品上架时间
   item_sku_act.item_num                                       AS  itm_num,          --商品货号
   item_sku_act.item_type                                      AS  itm_typ,          --商品型号
   item_sku_act.free_goods_flag                                AS  free_gds_flg,     --赠品标志
   IF(DATEDIFF('""" + zst.data_day_str + """',
        TO_DATE(item_sku_act.shelves_tm)) <= 30, 1, 0)         AS  new_rlse,         --新品标志
   item_sku_price_da.jd_prc                                    AS  jd_prc,           --京东价
   item_sku_act.otc_tm                                         AS  otc_tm,           --'上柜时间'
   item_sku_act.utc_tm                                         AS  utc_tm,           --'下柜时间'
   item_sku_act.main_brand_code                                AS  main_brand_cd,    --主品牌编码
   item_sku_act.main_barndname_full                            AS  main_brandname_full, --主品牌全名称    
   item_sku_act.item_last_cate_cd                              AS  itm_last_catg_cd, --商品末级分类代码
   item_sku_act.item_last_cate_name                            AS  itm_last_catg_nm, --商品末级分类名称
   CASE
     WHEN item_sku_act.pro_type = 'POP' THEN
      'POP'
     ELSE
      'SELF'
   END                                                        AS  shop_typ,           -- 店铺类型
   item_sku_act.valid_flag                                    AS  zs_itm_sku_vld_flg  -- 商智有效标识 
    FROM ( --获取商品信息
          SELECT pop_vender_id                                                 AS  vend_id, --此处同纵横pop_vender_id
                  shop_id                                                      AS  shop_id,
                  TRIM(shop_name)                                              AS  shop_name,
                  item_sku_id                                                  AS  item_sku_id,
                  TRIM(REGEXP_REPLACE(sku_name, '[\\\\\\x00-\\\\\\x1F]', ''))  AS  item_sku_name,
                  main_sku_id                                                  AS  main_sku_id,
                  item_id                                                      AS  item_id,
                  TRIM(REGEXP_REPLACE(item_name, '[\\\\\\x00-\\\\\\x1F]', '')) AS  item_name,
                  brand_code                                                   AS  brand_cd,
                  TRIM(barndname_full)                                         AS  brand_name,
                  item_first_cate_cd                                           AS  item_first_cate_cd,
                  TRIM(item_first_cate_name)                                   AS  item_first_cate_name,
                  item_second_cate_cd                                          AS  item_second_cate_cd,
                  TRIM(item_second_cate_name)                                  AS  item_second_cate_name,
                  item_third_cate_cd                                           AS  item_third_cate_cd,
                  TRIM(item_third_cate_name)                                   AS  item_third_cate_name,
                  CASE
                    WHEN unix_timestamp(otc_tm) < unix_timestamp(utc_tm) THEN
                     utc_tm -- 数据发现上柜的商品，如果下柜时间大于上柜时间，页面上的商家时间用的是下柜时间
                    ELSE
                     otc_tm
                  END                                                          AS  shelves_tm,      -- 上柜时间指的是页面上的上架时间
                  TRIM(item_type)                                              AS  item_type,       -- 商品型号
                  free_goods_flag                                              AS  free_goods_flag, -- 此处纵横赠品定义不同
                  otc_tm                                                       AS  otc_tm,          -- 上柜时间
                  utc_tm                                                       AS  utc_tm,          -- 下柜时间
                  TRIM(item_num)                                               AS  item_num,        -- 商品货号
                  CASE
                    WHEN sku_valid_flag = 1 AND sku_status_cd = 3001 THEN
                     1
                    ELSE
                     0
                  END                                                          AS  valid_flag -- 1：有效商品 0：无效商品
                 ,
                  CASE
                    WHEN shop_id > 0 and shop_id < 1000000000 THEN
                     'POP'
                    WHEN shop_id >= 1000000000 THEN
                     'SELF-SHOP'
                    ELSE
                     'SELF-NONSHOP'
                  END                                                          AS  pro_type,
                  main_brand_code,                                             --主品牌编码
                  main_barndname_full,                                         --主品牌全名称
                  item_last_cate_cd,                                           --商品末级分类代码
                  item_last_cate_name                                          --商品末级分类名称
            FROM retail_benchmark_10t.benchmark_gdm_m03_item_sku_act
           WHERE dt = '""" + zst.data_day_str + """'
               -- AND shop_id > 0
               -- 只取页面有效商品
               --AND    sku_valid_flag = 1 -- 1：有效商品（页面商品链接还存在着，包含在售、下柜等商品  ） 0/null：无效商品（页面商品链接不存在，跳转至主页） 120：无效商品（怎么识别未知）
               --AND    sku_status_cd = 3001 -- 3000：下柜（页面显示下柜且可以查询商品信息） 3001：上柜（页面正常在售商品） 3002：可上柜（页面正常显示，但无报价） 3010：POP商品删除（页面显示下柜且查询不到商品信息） 3099：测试数据，可忽略
          ) item_sku_act
          
    LEFT OUTER JOIN
  
   ( --获取商品价格
    SELECT sku_id                                                         AS  item_sku_id,
            jd_prc                                                        AS  jd_prc,
            mkt_prc                                                       AS  mkt_prc,
            stk_prc                                                       AS  stk_prc
      FROM retail_benchmark_10t.benchmark_gdm_m03_item_sku_price_da
     WHERE dt = '""" + zst.data_day_str + """') item_sku_price_da
      ON CAST(item_sku_act.item_sku_id       as    BIGINT) = CAST(item_sku_price_da.item_sku_id        as    BIGINT)
      
"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name='dest_table_name', sql=sql, exec_engine='spark',
             retry_with_hive=False, spark_args=[
        "--conf spark.driver.memory=15g",
        "--conf spark.executor.memory=20g",
        "--conf spark.dynamicAllocation.maxExecutors=2000",
        "--conf spark.dynamicAllocation.enabled=true",
        "--conf spark.speculation=true",
        "--conf spark.shuffle.service.enabled=true",
        "--conf spark.sql.shuffle.partitions=10000",
        "--conf spark.sql.adaptive.enabled=false",
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.broadcastTimeout=3600",
        "--conf spark.sql.autoBroadcastJoinThreshold=262144000"
    ])
