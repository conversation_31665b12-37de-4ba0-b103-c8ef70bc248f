#!/usr/bin/env python3

# buffaloID: 589777

import datetime
from HiveTask import HiveTask

ht = HiveTask()

dest_db = 'retail_benchmark_10t'
dest_table_name = 'benchmark_gdm_m01_zs_shop_ord_cust_det'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE """ + dest_db + """;
INSERT OVERWRITE TABLE benchmark_gdm_m01_zs_shop_ord_cust_det PARTITION(dt = '""" + insertDay + """')
SELECT
       curr_ord_cust.shop_id               AS shop_id
      ,""" + ht.data_day_int + """        AS stat_ct
      ,curr_ord_cust.usr_log_acct          AS usr_log_acct

FROM  (
       SELECT
              shop_id                     AS shop_id
             ,usr_log_acct                AS usr_log_acct
       
       FROM   retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det
       
       WHERE  dt = '""" + ht.data_day_str + """'
       AND    is_deal_ord = 1
       AND shop_id > 0
       AND usr_log_acct is not null
       
       GROUP  BY shop_id ,usr_log_acct
      ) curr_ord_cust

LEFT   OUTER JOIN
      (
       SELECT
              shop_id                    AS shop_id
             ,usr_log_acct               AS usr_log_acct
       
       FROM   retail_benchmark_10t.benchmark_gdm_m01_zs_shop_ord_cust_det
       
       WHERE  dt < '""" + ht.data_day_str + """'  
       and shop_id > 0
       and usr_log_acct is not null
       
      ) last_ord_cust
ON     curr_ord_cust.shop_id = last_ord_cust.shop_id
AND    curr_ord_cust.usr_log_acct = last_ord_cust.usr_log_acct

WHERE  last_ord_cust.shop_id IS NULL
;
"""
ht.exec_sql(schema_name=dest_db, table_name=dest_table_name, sql=sql, exec_engine='spark', retry_with_hive=False,
            lzo_compress=True, lzo_index_path=['dt=' + ht.data_day_str], spark_args=[
        "--conf spark.sql.hive.mergeFiles=true",
        "--conf spark.sql.adaptive.enabled=true",
        "--conf spark.sql.adaptive.repartition.enabled=true"
    ])
