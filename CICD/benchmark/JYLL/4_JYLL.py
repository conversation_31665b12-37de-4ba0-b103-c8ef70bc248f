#!/usr/bin/env python3

# buffaloID: 300389

import datetime
from HiveTask import HiveTask

ht = HiveTask()

dest_db = 'retail_benchmark_10t'
dest_table_name = 'benchmark_gdm_m01_zs_shop_vend_mapping'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE """ + dest_db + """;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """')
SELECT
       shop_id                                                       AS shop_id
      ,CASE WHEN shop_id < 1000000000 THEN 0
            ELSE 1
       END                                                           AS shop_typ_cd
      ,shop_name                                                     AS shop_nm
      ,id                                                            AS vend_id
      ,vender_code                                                   AS vend_cd
      ,status                                                        AS sts
      ,col_type                                                      AS coll_typ
      ,begin_time                                                    AS cntr_begn_tm
      ,end_time                                                      AS cntr_end_tm

FROM   retail_benchmark_10t.benchmark_fdm_pop_vender_vender_chain

WHERE  start_date <= '""" + ht.data_day_str + """'
AND    end_date > '""" + ht.data_day_str + """'
AND    shop_id > 0
;
"""

ht.exec_sql(schema_name=dest_db, table_name=dest_table_name, sql=sql, exec_engine='spark', retry_with_hive=False,
            spark_args=[
                "--conf spark.sql.hive.mergeFiles=true",
                "--conf spark.sql.adaptive.enabled=true",
                "--conf spark.sql.adaptive.repartition.enabled=true"
            ])
