#!/usr/bin/env python3

# buffaloID: 517635

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

insertDay = datetime.date.today().strftime("%Y-%m-%d")

dest_table_name = 'benchmark_adm_s04_zs_shop_ord_index_mid'

if zst.stat_ct_cd == 'hour':
    sql = """
      USE retail_benchmark_10t;

      INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
      SELECT shop_id AS shop_id,
       chan_cd AS chan_cd --99-整体 ,0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ
      ,
       0 AS sort_typ --0 店铺订单 ,1 来源引导下单
      ,
       CASE
         WHEN stat_ct < 10 THEN
          CAST(CONCAT('""" + zst.data_day_int + """', '0', stat_ct) AS
               BIGINT)
         ELSE
          CAST(CONCAT('""" + zst.data_day_int + """', stat_ct) AS BIGINT)
       END AS stat_ct,
       999999 AS shop_sessn_src_zs_url_frst_catg_cd,
       999999 AS shop_sessn_src_zs_url_scnd_catg_cd,
       999999 AS shop_sessn_src_zs_url_thrd_catg_cd,
       COALESCE(ord_qty, 0) AS ord_qty,
       COALESCE(ord_cust_qty, 0) AS ord_cust_qty,
       COALESCE(ord_amt, 0.0) AS ord_amt,
       COALESCE(sale_qty, 0) AS sale_qty,
       0 AS add_cart_cust_qty,
       COALESCE(intro_ord_qty, 0) AS intro_ord_qty, ---下单单量
       COALESCE(intro_ord_cust_qty, 0) AS intro_ord_cust_qty, ---下单客户数
       COALESCE(intro_ord_amt, 0.0) AS intro_ord_amt, ---下单金额
       COALESCE(intro_sale_qty, 0) AS intro_sale_qty, ---下单商品件数
       COALESCE(deal_ord_cust_qty, 0) AS deal_ord_cust_qty, ---下单且成交客户数
       0 AS follow_usr_qty  ----店铺关注数

  FROM (SELECT shop_id AS shop_id,
               99 AS chan_cd,
               hour(sale_ord_tm) AS stat_ct,
               COUNT(DISTINCT case
                       when is_deal_ord = 1 then
                        sale_ord_id
                       else
                        null
                     end) AS ord_qty, ---成交单量
               COUNT(DISTINCT case
                       when is_deal_ord = 1 then
                        usr_log_acct
                       else
                        null
                     end) AS ord_cust_qty, ---成交客户数
               SUM(case
                     when is_deal_ord = 1 then
                      ord_amt
                     else
                      0.0
                   end) AS ord_amt, ---成交金额
               SUM(case
                     when is_deal_ord = 1 then
                      sale_qty
                     else
                      0.0
                   end) AS sale_qty, ---成交商品件数
               COUNT(DISTINCT case
                       when sz_xiadan_flag = 1
                        then
                        sale_ord_id
                       else
                        null
                     end) AS intro_ord_qty, ---下单单量
               COUNT(DISTINCT case
                       when sz_xiadan_flag = 1
                        then
                        usr_log_acct
                       else
                        null
                     end) AS intro_ord_cust_qty, ---下单客户数
               SUM(case
                     when sz_xiadan_flag = 1
                      then
                      ord_amt
                     else
                      0
                   end) AS intro_ord_amt, ---下单金额
               SUM(case
                     when sz_xiadan_flag = 1
                      then
                      sale_qty
                     else
                      0
                   end) AS intro_sale_qty, ---下单商品件数
               COUNT(DISTINCT case
                       when is_deal_ord = 1 AND chk_acct_tm IS NOT NULL AND
                            TO_DATE(chk_acct_tm) = sale_ord_dt then
                        usr_log_acct
                       else
                        null
                     end) AS deal_ord_cust_qty ---下单且成交客户数

          FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det

         WHERE dt <= '""" + zst.data_day_str + """'
           AND dt >= '""" + stat_begin + """'
           AND  ( is_deal_ord = 1 or sz_xiadan_flag = 1 )

         GROUP BY shop_id, hour(sale_ord_tm)

        UNION ALL

        SELECT shop_id AS shop_id,
               0 AS chan_cd,
               hour(sale_ord_tm) AS stat_ct,
               COUNT(DISTINCT case
                       when is_deal_ord = 1 then
                        sale_ord_id
                       else
                        null
                     end) AS ord_qty, ---成交单量
               COUNT(DISTINCT case
                       when is_deal_ord = 1 then
                        usr_log_acct
                       else
                        null
                     end) AS ord_cust_qty, ---成交客户数
               SUM(case
                     when is_deal_ord = 1 then
                      ord_amt
                     else
                      0.0
                   end) AS ord_amt, ---成交金额
               SUM(case
                     when is_deal_ord = 1 then
                      sale_qty
                     else
                      0.0
                   end) AS sale_qty, ---成交商品件数
               COUNT(DISTINCT case
                       when sz_xiadan_flag = 1
                        then
                        sale_ord_id
                       else
                        null
                     end) AS intro_ord_qty, ---下单单量
               COUNT(DISTINCT case
                       when sz_xiadan_flag = 1
                        then
                        usr_log_acct
                       else
                        null
                     end) AS intro_ord_cust_qty, ---下单客户数
               SUM(case
                     when sz_xiadan_flag = 1
                      then
                      ord_amt
                     else
                      0
                   end) AS intro_ord_amt, ---下单金额
               SUM(case
                     when sz_xiadan_flag = 1
                      then
                      sale_qty
                     else
                      0
                   end) AS intro_sale_qty, ---下单商品件数
               COUNT(DISTINCT case
                       when is_deal_ord = 1 AND chk_acct_tm IS NOT NULL AND
                            TO_DATE(chk_acct_tm) = sale_ord_dt then
                        usr_log_acct
                       else
                        null
                     end) AS deal_ord_cust_qty ---下单且成交客户数

          FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det

         WHERE dt <= '""" + zst.data_day_str + """'
           AND dt >= '""" + stat_begin + """'
           AND chan_cd <> 20
           AND  ( is_deal_ord = 1 or sz_xiadan_flag = 1 )

         GROUP BY shop_id, hour(sale_ord_tm)

        UNION ALL

        SELECT shop_id AS shop_id,
               chan_cd AS chan_cd,
               hour(sale_ord_tm) AS stat_ct,
               COUNT(DISTINCT case
                       when is_deal_ord = 1 then
                        sale_ord_id
                       else
                        null
                     end) AS ord_qty, ---成交单量
               COUNT(DISTINCT case
                       when is_deal_ord = 1 then
                        usr_log_acct
                       else
                        null
                     end) AS ord_cust_qty, ---成交客户数
               SUM(case
                     when is_deal_ord = 1 then
                      ord_amt
                     else
                      0.0
                   end) AS ord_amt, ---成交金额
               SUM(case
                     when is_deal_ord = 1 then
                      sale_qty
                     else
                      0.0
                   end) AS sale_qty, ---成交商品件数
               COUNT(DISTINCT case
                       when sz_xiadan_flag = 1
                        then
                        sale_ord_id
                       else
                        null
                     end) AS intro_ord_qty, ---下单单量
               COUNT(DISTINCT case
                       when sz_xiadan_flag = 1
                        then
                        usr_log_acct
                       else
                        null
                     end) AS intro_ord_cust_qty, ---下单客户数
               SUM(case
                     when sz_xiadan_flag = 1
                      then
                      ord_amt
                     else
                      0
                   end) AS intro_ord_amt, ---下单金额
               SUM(case
                     when sz_xiadan_flag = 1
                      then
                      sale_qty
                     else
                      0
                   end) AS intro_sale_qty, ---下单商品件数
               COUNT(DISTINCT case
                       when is_deal_ord = 1 AND chk_acct_tm IS NOT NULL AND
                            TO_DATE(chk_acct_tm) = sale_ord_dt then
                        usr_log_acct
                       else
                        null
                     end) AS deal_ord_cust_qty ---下单且成交客户数

          FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det

         WHERE dt <= '""" + zst.data_day_str + """'
           AND dt >= '""" + stat_begin + """'
           AND  ( is_deal_ord = 1 or sz_xiadan_flag = 1 )

         GROUP BY shop_id, chan_cd, hour(sale_ord_tm)) chan_ord

      ;
      """
    zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, exec_engine='spark',
                 lzo_compress=False, lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd])

elif stat_ct_cd == 'halfhour':
    sql = """
    USE retail_benchmark_10t;

    INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
        SELECT
             shop_id                                                                                                          AS shop_id
            ,chan_cd                                                                                                          AS chan_cd    --99-整体 ,0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ
            ,0                                                                                                                AS sort_typ   --0 店铺订单 ,1 来源引导下单
            ,stat_ct                                                                                                          AS stat_ct
            ,999999                                                                                                           AS shop_sessn_src_zs_url_frst_catg_cd
            ,999999                                                                                                           AS shop_sessn_src_zs_url_scnd_catg_cd
            ,999999                                                                                                           AS shop_sessn_src_zs_url_thrd_catg_cd
            ,COALESCE(ord_qty ,0)                                                                                             AS ord_qty
            ,COALESCE(ord_cust_qty ,0)                                                                                        AS ord_cust_qty
            ,COALESCE(ord_amt ,0.0)                                                                                           AS ord_amt
            ,COALESCE(sale_qty ,0)                                                                                            AS sale_qty
            ,0                                                                                                                AS add_cart_cust_qty
            ,0                                                                                                                AS intro_ord_qty
            ,0                                                                                                                AS intro_ord_cust_qty
            ,0                                                                                                                AS intro_ord_amt
            ,0                                                                                                                AS intro_sale_qty
            ,0                                                                                                                AS deal_ord_cust_qty
            ,0                                                                                                                AS follow_usr_qty
        FROM  (
                SELECT
                    shop_id                                                                                                   AS shop_id
                   ,99                                                                                                        AS chan_cd
                   ,cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint)                                          AS stat_ct
                   ,COUNT(DISTINCT sale_ord_id)                                                                               AS ord_qty
                   ,COUNT(DISTINCT usr_log_acct)                                                                              AS ord_cust_qty
                   ,SUM(ord_amt)                                                                                              AS ord_amt
                   ,SUM(sale_qty)                                                                                             AS sale_qty

                FROM   (SELECT shop_id,
               sale_ord_tm,
               sale_ord_id,
               usr_log_acct,
               ord_amt,
               sale_qty,
               case
                 when hour(sale_ord_tm) < 10 and minute(sale_ord_tm) < 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """','0',hour(sale_ord_tm),'0', minute(sale_ord_tm)) AS bigint)
                 when hour(sale_ord_tm) < 10 and minute(sale_ord_tm) >= 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """', '0', hour(sale_ord_tm), minute(sale_ord_tm)) AS bigint)
                 when hour(sale_ord_tm) >= 10 and minute(sale_ord_tm) < 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """', hour(sale_ord_tm),'0',minute(sale_ord_tm)) AS bigint)
                 else
                  CAST(CONCAT('""" + zst.data_day_int + """', hour(sale_ord_tm), minute(sale_ord_tm)) AS bigint)
               end as stat_ct
          FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det

         WHERE dt <= '""" + zst.data_day_str + """'
           AND dt >= '""" + stat_begin + """'
           AND is_deal_ord = 1) a lateral view explode(array('0030', '0100', '0130', '0200', '0230', '0300', '0330', '0400', '0430', '0500','0530', '0600',
           '0630', '0700', '0730', '0800', '0830', '0900', '0930', '1000','1030', '1100', '1130', '1200',
           '1230', '1300', '1330', '1400', '1430', '1500','1530', '1600', '1630', '1700', '1730', '1800',
           '1830', '1900', '1930', '2000', '2030', '2100', '2130', '2200', '2230', '2300', '2330', '2400')) b as date_dim
            where cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint) > stat_ct
                group by shop_id, cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint)

             UNION  ALL

             SELECT
                    shop_id                                                                                                   AS shop_id
                   ,0                                                                                                         AS chan_cd
                   ,cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint)                                          AS stat_ct
                   ,COUNT(DISTINCT sale_ord_id)                                                                               AS ord_qty
                   ,COUNT(DISTINCT usr_log_acct)                                                                              AS ord_cust_qty
                   ,SUM(ord_amt)                                                                                              AS ord_amt
                   ,SUM(sale_qty)                                                                                             AS sale_qty

             FROM  (SELECT shop_id,
               sale_ord_tm,
               sale_ord_id,
               usr_log_acct,
               ord_amt,
               sale_qty,
               case
                 when hour(sale_ord_tm) < 10 and minute(sale_ord_tm) < 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """','0',hour(sale_ord_tm),'0', minute(sale_ord_tm)) AS bigint)
                 when hour(sale_ord_tm) < 10 and minute(sale_ord_tm) >= 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """', '0', hour(sale_ord_tm), minute(sale_ord_tm)) AS bigint)
                 when hour(sale_ord_tm) >= 10 and minute(sale_ord_tm) < 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """', hour(sale_ord_tm),'0',minute(sale_ord_tm)) AS bigint)
                 else
                  CAST(CONCAT('""" + zst.data_day_int + """', hour(sale_ord_tm), minute(sale_ord_tm)) AS bigint)
               end as stat_ct
          FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det

         WHERE dt <= '""" + zst.data_day_str + """'
           AND dt >= '""" + stat_begin + """'
           AND chan_cd<>20
           AND is_deal_ord = 1) a lateral view explode(array('0030', '0100', '0130', '0200', '0230', '0300', '0330', '0400', '0430', '0500','0530', '0600',
           '0630', '0700', '0730', '0800', '0830', '0900', '0930', '1000','1030', '1100', '1130', '1200',
           '1230', '1300', '1330', '1400', '1430', '1500','1530', '1600', '1630', '1700', '1730', '1800',
           '1830', '1900', '1930', '2000', '2030', '2100', '2130', '2200', '2230', '2300', '2330', '2400')) b as date_dim
            where cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint) > stat_ct
                group by shop_id, cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint)

             UNION  ALL

             SELECT
                    shop_id                                                                                                   AS shop_id
                   ,chan_cd                                                                                                   AS chan_cd
                   ,cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint)                                          AS stat_ct
                   ,COUNT(DISTINCT sale_ord_id)                                                                               AS ord_qty
                   ,COUNT(DISTINCT usr_log_acct)                                                                              AS ord_cust_qty
                   ,SUM(ord_amt)                                                                                              AS ord_amt
                   ,SUM(sale_qty)                                                                                             AS sale_qty

             FROM   (SELECT shop_id,
               chan_cd,
               sale_ord_tm,
               sale_ord_id,
               usr_log_acct,
               ord_amt,
               sale_qty,
               case
                 when hour(sale_ord_tm) < 10 and minute(sale_ord_tm) < 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """','0',hour(sale_ord_tm),'0', minute(sale_ord_tm)) AS bigint)
                 when hour(sale_ord_tm) < 10 and minute(sale_ord_tm) >= 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """', '0', hour(sale_ord_tm), minute(sale_ord_tm)) AS bigint)
                 when hour(sale_ord_tm) >= 10 and minute(sale_ord_tm) < 10 then
                  CAST(CONCAT('""" + zst.data_day_int + """', hour(sale_ord_tm),'0',minute(sale_ord_tm)) AS bigint)
                 else
                  CAST(CONCAT('""" + zst.data_day_int + """', hour(sale_ord_tm), minute(sale_ord_tm)) AS bigint)
               end as stat_ct
          FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det

         WHERE dt <= '""" + zst.data_day_str + """'
           AND dt >= '""" + stat_begin + """'
           AND is_deal_ord = 1) a lateral view explode(array('0030', '0100', '0130', '0200', '0230', '0300', '0330', '0400', '0430', '0500','0530', '0600',
           '0630', '0700', '0730', '0800', '0830', '0900', '0930', '1000','1030', '1100', '1130', '1200',
           '1230', '1300', '1330', '1400', '1430', '1500','1530', '1600', '1630', '1700', '1730', '1800',
           '1830', '1900', '1930', '2000', '2030', '2100', '2130', '2200', '2230', '2300', '2330', '2400')) b as date_dim
           where cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint) > stat_ct
                group by shop_id,chan_cd, cast(CONCAT('""" + zst.data_day_int + """', date_dim) as bigint)) chan_ord
      ;
      """
    zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, exec_engine='spark',
                 lzo_compress=False, lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd])

else:
    sql = """
      USE retail_benchmark_10t;

      INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
      SELECT shop_id                                      AS shop_id,
             chan_cd                                      AS chan_cd,                     --99-整体 ,0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ
             sort_typ                                     AS sort_typ,                    --0 店铺订单 ,1 来源引导下单
             """ + stat_ct + """                          AS stat_ct,
             shop_sessn_src_zs_url_frst_catg_cd           AS shop_sessn_src_zs_url_frst_catg_cd,
             shop_sessn_src_zs_url_scnd_catg_cd           AS shop_sessn_src_zs_url_scnd_catg_cd,
             shop_sessn_src_zs_url_thrd_catg_cd           AS shop_sessn_src_zs_url_thrd_catg_cd,
             COALESCE(SUM(ord_qty), 0)                    AS ord_qty,                     ---成交单量
             COALESCE(SUM(ord_cust_qty), 0)               AS ord_cust_qty,                ---成交客户数
             COALESCE(SUM(ord_amt), 0.0)                  AS ord_amt,                     ---成交金额
             COALESCE(SUM(sale_qty), 0)                   AS sale_qty,                    ---成交商品件数
             COALESCE(SUM(add_cart_cust_qty), 0)          AS add_cart_cust_qty,           ---加购客户数
             COALESCE(SUM(intro_ord_qty), 0)              AS intro_ord_qty,               ---下单单量
             COALESCE(SUM(intro_ord_cust_qty), 0)         AS intro_ord_cust_qty,          ---下单客户数
             COALESCE(SUM(intro_ord_amt), 0.0)            AS intro_ord_amt,               ---下单金额
             COALESCE(SUM(intro_sale_qty), 0)             AS intro_sale_qty,              ---下单商品件数
             COALESCE(SUM(deal_ord_cust_qty), 0)          AS deal_ord_cust_qty,           ---下单且成交客户数
             COALESCE(SUM(follow_usr_qty), 0)             AS follow_usr_qty,              ----店铺关注数
             COALESCE(SUM(ord_cust_qty_allnew), 0)        AS ord_cust_qty_allnew,         ----成交新客户数(整体)
             COALESCE(SUM(ord_cust_qty_allold), 0)        AS ord_cust_qty_allold,         ----成交老客户数(整体)
             COALESCE(SUM(ord_cust_qty_180new), 0)        AS ord_cust_qty_180new,         ----成交新客户数(180天)
             COALESCE(SUM(ord_cust_qty_180old), 0)        AS ord_cust_qty_180old,         ----成交老客户数(180天)
             COALESCE(SUM(ord_cust_qty_730new), 0)        AS ord_cust_qty_730new,         ----成交新客户数(730天)
             COALESCE(SUM(ord_cust_qty_730old), 0)        AS ord_cust_qty_730old          ----成交老客户数(730天)

      FROM (SELECT shop_id AS shop_id,
                   99 AS chan_cd, --99-整体 ,0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ 20 pc
                   0 AS sort_typ, --0 店铺订单 ,1 来源引导下单
                   999999 AS shop_sessn_src_zs_url_frst_catg_cd,
                   999999 AS shop_sessn_src_zs_url_scnd_catg_cd,
                   999999 AS shop_sessn_src_zs_url_thrd_catg_cd,
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 then
                            sale_ord_id
                           else
                            null
                         end) AS ord_qty, ---成交单量
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 then
                            usr_log_acct
                           else
                            null
                         end) AS ord_cust_qty, ---成交客户数
                   SUM(case
                         when is_deal_ord = 1 then
                          ord_amt
                         else
                          0.0
                       end) AS ord_amt, ---成交金额
                   SUM(case
                         when is_deal_ord = 1 then
                          sale_qty
                         else
                          0.0
                       end) AS sale_qty, ---成交商品件数
                   COUNT(DISTINCT case
                           when sz_xiadan_flag = 1
                            then
                            sale_ord_id
                           else
                            null
                         end) AS intro_ord_qty, ---下单单量
                   COUNT(DISTINCT case
                           when sz_xiadan_flag = 1
                            then
                            usr_log_acct
                           else
                            null
                         end) AS intro_ord_cust_qty, ---下单客户数
                   SUM(case
                         when sz_xiadan_flag = 1
                          then
                          ord_amt
                         else
                          0
                       end) AS intro_ord_amt, ---下单金额
                   SUM(case
                         when sz_xiadan_flag = 1
                          then
                          sale_qty
                         else
                          0
                       end) AS intro_sale_qty, ---下单商品件数
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 AND chk_acct_tm IS NOT NULL AND
                                TO_DATE(chk_acct_tm) = sale_ord_dt then
                            usr_log_acct
                           else
                            null
                         end) AS deal_ord_cust_qty, ---下单且成交客户数
                   0 AS add_cart_cust_qty,
                   0 AS follow_usr_qty,

                   COUNT(DISTINCT case
                               when is_deal_ord = 1 and is_allnew_flag = 1 then
                                    usr_log_acct
                               else
                                    null
                          end) AS ord_cust_qty_allnew, ---成交新客户数(整体)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_allnew_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_allold, ---成交老客户数(整体)
                   COUNT(DISTINCT case
                                       when is_deal_ord = 1 and is_180new_flag = 1 then
                                            usr_log_acct
                                       else
                                            null
                                  end) AS ord_cust_qty_180new, ---成交新客户数(180天)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_180new_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_180old, ---成交老客户数(180天)
                   COUNT(DISTINCT case
                                       when is_deal_ord = 1 and is_730new_flag = 1 then
                                            usr_log_acct
                                       else
                                            null
                                  end) AS ord_cust_qty_730new, ---成交新客户数(730天)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_730new_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_730old ---成交老客户数(730天)
              FROM
              ( SELECT shop_id,
                       chan_cd,
                       is_deal_ord,
                       sz_xiadan_flag,
                       usr_log_acct,
                       ord_amt,
                       sale_qty,
                       sale_ord_id,
                       chk_acct_tm,
                       sale_ord_dt,
                       max(is_allnew_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_allnew_flag,
                       max(is_180new_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_180new_flag,
                       max(is_730new_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_730new_flag
                FROM retail_benchmark_10t.benchmark_adm_m04_zs_oldnew_ord_det
                WHERE dt <= '""" + zst.data_day_str + """'
                 AND dt >= '""" + stat_begin + """'
                 AND  ( is_deal_ord = 1 or sz_xiadan_flag = 1 )
              ) A
             GROUP BY shop_id

            UNION ALL

            SELECT shop_id AS shop_id,
                   0 AS chan_cd,
                   0 AS sort_typ,
                   999999 AS shop_sessn_src_zs_url_frst_catg_cd,
                   999999 AS shop_sessn_src_zs_url_scnd_catg_cd,
                   999999 AS shop_sessn_src_zs_url_thrd_catg_cd,
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 then
                            sale_ord_id
                           else
                            null
                         end) AS ord_qty, ---成交单量
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 then
                            usr_log_acct
                           else
                            null
                         end) AS ord_cust_qty, ---成交客户数
                   SUM(case
                         when is_deal_ord = 1 then
                          ord_amt
                         else
                          0.0
                       end) AS ord_amt, ---成交金额
                   SUM(case
                         when is_deal_ord = 1 then
                          sale_qty
                         else
                          0.0
                       end) AS sale_qty, ---成交商品件数
                   COUNT(DISTINCT case
                           when sz_xiadan_flag = 1
                            then
                            sale_ord_id
                           else
                            null
                         end) AS intro_ord_qty, ---下单单量
                   COUNT(DISTINCT case
                           when sz_xiadan_flag = 1
                            then
                            usr_log_acct
                           else
                            null
                         end) AS intro_ord_cust_qty, ---下单客户数
                   SUM(case
                         when sz_xiadan_flag = 1
                          then
                          ord_amt
                         else
                          0
                       end) AS intro_ord_amt, ---下单金额
                   SUM(case
                         when sz_xiadan_flag = 1
                          then
                          sale_qty
                         else
                          0
                       end) AS intro_sale_qty, ---下单商品件数
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 AND chk_acct_tm IS NOT NULL AND
                                TO_DATE(chk_acct_tm) = sale_ord_dt then
                            usr_log_acct
                           else
                            null
                         end) AS deal_ord_cust_qty, ---下单且成交客户数
                   0 AS add_cart_cust_qty,
                   0 AS follow_usr_qty,
                   COUNT(DISTINCT case
                               when is_deal_ord = 1 and is_allnew_flag = 1 then
                                    usr_log_acct
                               else
                                    null
                          end) AS ord_cust_qty_allnew, ---成交新客户数(整体)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_allnew_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_allold, ---成交老客户数(整体)
                   COUNT(DISTINCT case
                                       when is_deal_ord = 1 and is_180new_flag = 1 then
                                            usr_log_acct
                                       else
                                            null
                                  end) AS ord_cust_qty_180new, ---成交新客户数(180天)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_180new_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_180old, ---成交老客户数(180天)
                   COUNT(DISTINCT case
                                       when is_deal_ord = 1 and is_730new_flag = 1 then
                                            usr_log_acct
                                       else
                                            null
                                  end) AS ord_cust_qty_730new, ---成交新客户数(730天)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_730new_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_730old ---成交老客户数(730天)

              FROM
              ( SELECT shop_id,
                       chan_cd,
                       is_deal_ord,
                       sz_xiadan_flag,
                       usr_log_acct,
                       ord_amt,
                       sale_qty,
                       sale_ord_id,
                       chk_acct_tm,
                       sale_ord_dt,
                       max(is_allnew_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_allnew_flag,
                       max(is_180new_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_180new_flag,
                       max(is_730new_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_730new_flag
                FROM retail_benchmark_10t.benchmark_adm_m04_zs_oldnew_ord_det
                WHERE dt <= '""" + zst.data_day_str + """'
                 AND dt >= '""" + stat_begin + """'
                 AND chan_cd <> 20
                 AND  ( is_deal_ord = 1 or sz_xiadan_flag = 1 )
              ) A

             GROUP BY shop_id

            UNION ALL

            SELECT shop_id AS shop_id,
                   chan_cd AS chan_cd,
                   0 AS sort_typ, --0 店铺订单 ,1 来源引导下单
                   999999 AS shop_sessn_src_zs_url_frst_catg_cd,
                   999999 AS shop_sessn_src_zs_url_scnd_catg_cd,
                   999999 AS shop_sessn_src_zs_url_thrd_catg_cd,
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 then
                            sale_ord_id
                           else
                            null
                         end) AS ord_qty, ---成交单量
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 then
                            usr_log_acct
                           else
                            null
                         end) AS ord_cust_qty, ---成交客户数
                   SUM(case
                         when is_deal_ord = 1 then
                          ord_amt
                         else
                          0.0
                       end) AS ord_amt, ---成交金额
                   SUM(case
                         when is_deal_ord = 1 then
                          sale_qty
                         else
                          0.0
                       end) AS sale_qty, ---成交商品件数
                   COUNT(DISTINCT case
                           when sz_xiadan_flag = 1
                            then
                            sale_ord_id
                           else
                            null
                         end) AS intro_ord_qty, ---下单单量
                   COUNT(DISTINCT case
                           when sz_xiadan_flag = 1
                            then
                            usr_log_acct
                           else
                            null
                         end) AS intro_ord_cust_qty, ---下单客户数
                   SUM(case
                         when sz_xiadan_flag = 1
                          then
                          ord_amt
                         else
                          0
                       end) AS intro_ord_amt, ---下单金额
                   SUM(case
                         when sz_xiadan_flag = 1
                          then
                          sale_qty
                         else
                          0
                       end) AS intro_sale_qty, ---下单商品件数
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 AND chk_acct_tm IS NOT NULL AND
                                TO_DATE(chk_acct_tm) = sale_ord_dt then
                            usr_log_acct
                           else
                            null
                         end) AS deal_ord_cust_qty, ---下单且成交客户数
                   0 AS add_cart_cust_qty,
                   0 AS follow_usr_qty,
                   COUNT(DISTINCT case
                               when is_deal_ord = 1 and is_allnew_flag = 1 then
                                    usr_log_acct
                               else
                                    null
                          end) AS ord_cust_qty_allnew, ---成交新客户数(整体)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_allnew_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_allold, ---成交老客户数(整体)
                   COUNT(DISTINCT case
                                       when is_deal_ord = 1 and is_180new_flag = 1 then
                                            usr_log_acct
                                       else
                                            null
                                  end) AS ord_cust_qty_180new, ---成交新客户数(180天)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_180new_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_180old, ---成交老客户数(180天)
                   COUNT(DISTINCT case
                                       when is_deal_ord = 1 and is_730new_flag = 1 then
                                            usr_log_acct
                                       else
                                            null
                                  end) AS ord_cust_qty_730new, ---成交新客户数(730天)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_730new_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_730old ---成交老客户数(730天)

              FROM
              ( SELECT shop_id,
                       chan_cd,
                       is_deal_ord,
                       sz_xiadan_flag,
                       usr_log_acct,
                       ord_amt,
                       sale_qty,
                       sale_ord_id,
                       chk_acct_tm,
                       sale_ord_dt,
                       max(is_allnew_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_allnew_flag,
                       max(is_180new_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_180new_flag,
                       max(is_730new_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_730new_flag
                FROM retail_benchmark_10t.benchmark_adm_m04_zs_oldnew_ord_det
                WHERE dt <= '""" + zst.data_day_str + """'
                 AND dt >= '""" + stat_begin + """'
                 AND  ( is_deal_ord = 1 or sz_xiadan_flag = 1 )
              ) A

             GROUP BY shop_id,
                      chan_cd

            ) dest
          where chan_cd is not null
          group by shop_id,
                   chan_cd, --99-整体 ,0-移动整体 ,1-M ,2-APP ,3-WECHAT ,4-QQ
                   sort_typ, --0 店铺订单 ,1 来源引导下单
                   shop_sessn_src_zs_url_frst_catg_cd,
                   shop_sessn_src_zs_url_scnd_catg_cd,
                   shop_sessn_src_zs_url_thrd_catg_cd;
      """
    zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, exec_engine='spark',
                 retry_with_hive=False,
                 lzo_compress=False, lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
                 spark_args=[
                     "--conf spark.sql.hive.mergeFiles=true",
                     "--conf spark.sql.adaptive.enabled=true",
                     "--conf spark.sql.adaptive.repartition.enabled=true"
                 ])
