#!/usr/bin/env python3

# buffaloID: 706052

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = zst.stat_ct
stat_begin = zst.stat_begin
lnk_rlat_day_str = zst.lnk_rlat_day_str

dest_table_name = 'benchmark_app_zs_z1503_refund_analysis'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
use retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
SELECT
  tab1.shop_id, --店铺编号
  tab1.sort_typ, --0-SPU, 1-SKU
  tab1.chan_cd, --渠道类型：目前只有全部渠道
  """ + stat_ct + """ AS stat_ct,
  """ + zst.data_day_int + """ AS stat_dt,
  tab1.pro_id, --商品编码
  tab1.afs_reason_id, --退货原因ID
  tab1.appl_rsn_name, --退货原因名称
  COALESCE(tab1.refund_suc_num, 0) AS refund_suc_num, --成功退款必输
  COALESCE(tab1.refund_amt, 0.0) AS refund_amt, --成功退款金额
  COALESCE(tab1.avg_refund_time /(60 * 60 * 24), 0.0) AS avg_refund_time --平均退款完结时长（天）
FROM
  (
    SELECT
      b.shop_id,
      1 AS sort_typ,
      99 AS chan_cd,
      b.itm_sku_id AS pro_id,
      c.appl_rsn_cd AS afs_reason_id,
      c.appl_rsn_name,
      COUNT(afs_ser_bill_id) AS refund_suc_num,
      SUM(refund_amonut) AS refund_amt,
      SUM((unix_timestamp(proc_tm) - unix_timestamp(create_tm))) / COUNT(afs_ser_bill_id) AS avg_refund_time
    FROM
      (
        SELECT
          sku_item_id,
          NVL(appl_fst_rsn_cd, 20) AS appl_fst_rsn_cd,
          afs_ser_bill_id,
          refund_amonut,
          proc_tm,
          create_tm
        FROM
          retail_benchmark_10t.benchmark_gdm_m10_afs_ser_sum_da
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND to_date(proc_tm) BETWEEN '""" + stat_begin + """' AND '""" + zst.data_day_str + """'
          AND to_date(refund_complete_tm) BETWEEN '""" + stat_begin + """' AND '""" + zst.data_day_str + """'
          AND to_date(proc_tm) = to_date(refund_complete_tm)
          AND customer_apply_expect = 10
      )
      a
    INNER JOIN
      (
        SELECT
          shop_id,
          itm_id,
          itm_sku_id
        FROM
          retail_benchmark_10t.benchmark_adm_th03_merchandise_info
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND shop_id > 0
      )
      b
    ON
      a.sku_item_id = b.itm_sku_id
    LEFT JOIN
      (
        SELECT --退货原因维表
          appl_rsn_cd,
          appl_rsn_name,
          sys_appl_rsn_name,
          appl_rsn_lvl_type_cd,
          map_fst_chk_rsn_cd,
          appl_rsn_desc
        FROM
          retail_benchmark_10t.benchmark_dim_afs_appl_rsn
      )
      c
    ON
      a.appl_fst_rsn_cd = c.appl_rsn_cd
    GROUP BY
      b.shop_id,
      b.itm_sku_id,
      c.appl_rsn_cd,
      c.appl_rsn_name
    
    UNION ALL
    
    SELECT
      b.shop_id,
      0 AS sort_typ,
      99 AS chan_cd,
      b.itm_id AS pro_id,
      c.appl_rsn_cd AS afs_reason_id,
      c.appl_rsn_name,
      COUNT(afs_ser_bill_id) AS refund_suc_num,
      SUM(refund_amonut) AS refund_amt,
      SUM((unix_timestamp(proc_tm) - unix_timestamp(create_tm))) / COUNT(afs_ser_bill_id) AS avg_refund_time
    FROM
      (
        SELECT
          sku_item_id,
          NVL(appl_fst_rsn_cd, 20) AS appl_fst_rsn_cd,
          afs_ser_bill_id,
          refund_amonut,
          proc_tm,
          create_tm
        FROM
          retail_benchmark_10t.benchmark_gdm_m10_afs_ser_sum_da
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND to_date(proc_tm) BETWEEN '""" + stat_begin + """' AND '""" + zst.data_day_str + """'
          AND to_date(refund_complete_tm) BETWEEN '""" + stat_begin + """' AND '""" + zst.data_day_str + """'
          AND to_date(proc_tm) = to_date(refund_complete_tm)
          AND customer_apply_expect = 10
      )
      a
    INNER JOIN
      (
        SELECT
          shop_id,
          itm_id,
          itm_sku_id
        FROM
          retail_benchmark_10t.benchmark_adm_th03_merchandise_info
        WHERE
          dt = '""" + zst.data_day_str + """'
          AND shop_id > 0
      )
      b
    ON
      a.sku_item_id = b.itm_sku_id
    LEFT JOIN
      (
        SELECT --退货原因维表
          appl_rsn_cd,
          appl_rsn_name,
          sys_appl_rsn_name,
          appl_rsn_lvl_type_cd,
          map_fst_chk_rsn_cd,
          appl_rsn_desc
        FROM
          retail_benchmark_10t.benchmark_dim_afs_appl_rsn
      )
      c
    ON
      a.appl_fst_rsn_cd = c.appl_rsn_cd
    GROUP BY
      b.shop_id,
      b.itm_id,
      c.appl_rsn_cd,
      c.appl_rsn_name
  )
  tab1
"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, retry_with_hive=False,
             exec_engine='spark', lzo_compress=False,
             lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
