#!/usr/bin/env python3

# buffaloID: 507659

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

dest_table_name = 'benchmark_adm_s04_zs_shop_kepler_mid'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
    USE retail_benchmark_10t;
      INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='""" + stat_ct_cd + """')
      SELECT shop_id                                      AS shop_id,
             chan_cd                                      AS chan_cd,                     --31-开普勒品牌小程序 ,32-微信购物
             """ + stat_ct + """                          AS stat_ct,
             COALESCE(SUM(ord_qty), 0)                    AS ord_qty,                     ---成交单量
             COALESCE(SUM(ord_cust_qty), 0)               AS ord_cust_qty,                ---成交客户数
             COALESCE(SUM(ord_amt), 0.0)                  AS ord_amt,                     ---成交金额
             COALESCE(SUM(sale_qty), 0)                   AS sale_qty,                    ---成交商品件数
             COALESCE(SUM(intro_ord_qty), 0)              AS intro_ord_qty,               ---下单单量
             COALESCE(SUM(intro_ord_cust_qty), 0)         AS intro_ord_cust_qty,          ---下单客户数
             COALESCE(SUM(intro_ord_amt), 0.0)            AS intro_ord_amt,               ---下单金额
             COALESCE(SUM(intro_sale_qty), 0)             AS intro_sale_qty,              ---下单商品件数
             COALESCE(SUM(deal_ord_cust_qty), 0)          AS deal_ord_cust_qty,           ---下单且成交客户数
             COALESCE(SUM(ord_cust_qty_allnew), 0)        AS ord_cust_qty_allnew,         ----成交新客户数(整体)
             COALESCE(SUM(ord_cust_qty_allold), 0)        AS ord_cust_qty_allold,         ----成交老客户数(整体)
             COALESCE(SUM(ord_cust_qty_180new), 0)        AS ord_cust_qty_180new,         ----成交新客户数(180天)
             COALESCE(SUM(ord_cust_qty_180old), 0)        AS ord_cust_qty_180old,         ----成交老客户数(180天)
             COALESCE(SUM(ord_cust_qty_730new), 0)        AS ord_cust_qty_730new,         ----成交新客户数(730天)
             COALESCE(SUM(ord_cust_qty_730old), 0)        AS ord_cust_qty_730old,          ----成交老客户数(730天)
             COALESCE(SUM(uv), 0)        AS uv,          ----uv
             COALESCE(SUM(pv), 0)        AS pv           ----pv

      FROM (
            SELECT shop_id AS shop_id,
                   second_chan_cd AS chan_cd,
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 then
                            sale_ord_id
                           else
                            null
                         end) AS ord_qty, ---成交单量
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 then
                            usr_log_acct
                           else
                            null
                         end) AS ord_cust_qty, ---成交客户数
                   SUM(case
                         when is_deal_ord = 1 then
                          ord_amt
                         else
                          0.0
                       end) AS ord_amt, ---成交金额
                   SUM(case
                         when is_deal_ord = 1 then
                          sale_qty
                         else
                          0.0
                       end) AS sale_qty, ---成交商品件数
                   COUNT(DISTINCT case
                           when sz_xiadan_flag = 1
                            then
                            sale_ord_id
                           else
                            null
                         end) AS intro_ord_qty, ---下单单量
                   COUNT(DISTINCT case
                           when sz_xiadan_flag = 1
                            then
                            usr_log_acct
                           else
                            null
                         end) AS intro_ord_cust_qty, ---下单客户数
                   SUM(case
                         when sz_xiadan_flag = 1
                          then
                          ord_amt
                         else
                          0
                       end) AS intro_ord_amt, ---下单金额
                   SUM(case
                         when sz_xiadan_flag = 1
                          then
                          sale_qty
                         else
                          0
                       end) AS intro_sale_qty, ---下单商品件数
                   COUNT(DISTINCT case
                           when is_deal_ord = 1 AND chk_acct_tm IS NOT NULL AND
                                TO_DATE(chk_acct_tm) = sale_ord_dt then
                            usr_log_acct
                           else
                            null
                         end) AS deal_ord_cust_qty, ---下单且成交客户数
                   COUNT(DISTINCT case
                               when is_deal_ord = 1 and is_allnew_flag = 1 then
                                    usr_log_acct
                               else
                                    null
                          end) AS ord_cust_qty_allnew, ---成交新客户数(整体)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_allnew_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_allold, ---成交老客户数(整体)
                   COUNT(DISTINCT case
                                       when is_deal_ord = 1 and is_180new_flag = 1 then
                                            usr_log_acct
                                       else
                                            null
                                  end) AS ord_cust_qty_180new, ---成交新客户数(180天)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_180new_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_180old, ---成交老客户数(180天)
                   COUNT(DISTINCT case
                                       when is_deal_ord = 1 and is_730new_flag = 1 then
                                            usr_log_acct
                                       else
                                            null
                                  end) AS ord_cust_qty_730new, ---成交新客户数(730天)
                   COUNT(DISTINCT case
                     when is_deal_ord = 1 and is_730new_flag = 0 then
                      usr_log_acct
                     else
                      null
                   end) AS ord_cust_qty_730old ,---成交老客户数(730天)
                   0 as uv,
                   0 as pv

              FROM
              ( SELECT shop_id,
                       second_chan_cd,
                       is_deal_ord,
                       sz_xiadan_flag,
                       usr_log_acct,
                       ord_amt,
                       sale_qty,
                       sale_ord_id,
                       chk_acct_tm,
                       sale_ord_dt,
                       max(is_allnew_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_allnew_flag,
                       max(is_180new_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_180new_flag,
                       max(is_730new_flag) OVER(PARTITION BY shop_id,usr_log_acct) AS is_730new_flag
                FROM retail_benchmark_10t.benchmark_adm_m04_zs_oldnew_ord_det
                WHERE dt <= '""" + zst.data_day_str + """'
                 AND dt >= '""" + stat_begin + """'
                 AND  ( is_deal_ord = 1 or sz_xiadan_flag = 1 )
                 AND chan_cd = 3
              ) A

             GROUP BY shop_id,
                      second_chan_cd
            
            union all
            
            select shop_id,
                   chan_cd,
                   0 as ord_qty,
                   0 as ord_cust_qty,
                   0 as ord_amt,
                   0 as sale_qty,
                   0 as intro_ord_qty,
                   0 as intro_ord_cust_qty,
                   0 as intro_ord_amt,
                   0 as intro_sale_qty,
                   0 as deal_ord_cust_qty,
                   0 as ord_cust_qty_allnew,
                   0 as ord_cust_qty_allold,
                   0 as ord_cust_qty_180new,
                   0 as ord_cust_qty_180old,
                   0 as ord_cust_qty_730new,
                   0 as ord_cust_qty_730old,
                   count(distinct brws_uniq_id) as uv,
                   sum(upv) as pv
               from
                   (SELECT
                     shop_id AS shop_id,
                     --case when shop_sessn_src_zs_url_frst_catg_cd = 7 then 31    ---31-开普勒品牌小程序 ,32-微信购物
                    case when shop_sessn_src_zs_url_scnd_catg_cd = 6038 then 31    ---31-开普勒品牌小程序 ,32-微信购物
                          else 32 end AS chan_cd,
                     brws_uniq_id AS brws_uniq_id,
                     usr_log_acct as usr_log_acct,
                     COUNT(1) AS upv
                   FROM retail_benchmark_10t.benchmark_adm_s14_zs_all_chan_shop_traffics
                   WHERE dt <= '""" + zst.data_day_str + """'
                   AND dt >= '""" + stat_begin + """'
                   AND chan_cd = 3
                   GROUP BY
                     shop_id,
                     brws_uniq_id,
                     usr_log_acct,
                     --case when shop_sessn_src_zs_url_frst_catg_cd = 7 then 31
                     case when shop_sessn_src_zs_url_scnd_catg_cd = 6038 then 31    ---31-开普勒品牌小程序 ,32-微信购物
                          else 32 end
                   ) a
               group by shop_id,chan_cd
            ) dest
          where chan_cd is not null
      group by shop_id,
               chan_cd ---31-开普勒品牌小程序 ,32-微信购物
;
"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, retry_with_hive=False,
             exec_engine='spark', lzo_compress=False,
             lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             spark_args=["--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.enabled=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"
                         ]
             )
