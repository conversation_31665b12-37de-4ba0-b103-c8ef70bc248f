#!/usr/bin/env python3

# buffaloID: 690629

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = zst.stat_ct
stat_begin = zst.stat_begin

dest_table_name = 'benchmark_app_zs_z0601_shop_index_overview'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
  USE retail_benchmark_10t;
   INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION (dt='""" + insertDay + """' ,stat_ct_cd='day')  
   select shop_info.shop_id as shop_id,
         shop_info.chan_cd as chan_cd,
         shop_info.stat_ct as stat_ct,
         shop_info.stat_dt as stat_dt,
         shop_info.pv as pv,
         shop_info.uv as uv,
         shop_info.bnc_rt as bnc_rt,
         shop_info.avg_depth as avg_depth,
         shop_info.avg_tm_on_page as avg_tm_on_page,
         shop_info.add_cart_cust_qty as add_cart_cust_qty,
         shop_info.add_cart_cust_rt as add_cart_cust_rt,
         shop_info.ord_cust_qty as ord_cust_qty,
         shop_info.ord_cust_rt as ord_cust_rt,
         shop_info.ord_amt as ord_amt,
         shop_info.sale_qty as sale_qty,
         shop_info.ord_qty as ord_qty,
         shop_info.avg_cust_prc as avg_cust_prc,
         shop_info.shop_attn_num as shop_attn_num,
         shop_info.itm_attn_num as itm_attn_num,
         shop_info.thirty_repur_rate as thirty_repur_rate,
         shop_info.ninty_repur_rate as ninty_repur_rate,
         null as in_ord_cust_rt, --入仓转化率
         shop_info.intro_ord_cust_qty as intro_ord_cust_qty,
         shop_info.intro_ord_cust_rt as intro_ord_cust_rt,
         shop_info.intro_ord_amt as intro_ord_amt,
         shop_info.intro_sale_qty as intro_sale_qty,
         shop_info.intro_ord_qty as intro_ord_qty,
         shop_info.deal_ord_cust_qty as deal_ord_cust_qty,
         shop_info.deal_ord_cust_rt as deal_ord_cust_rt,
         shop_info.new_uv as new_uv,
         shop_info.old_uv as old_uv,
         shop_info.uv_value as uv_value,
         '' as is_new_cust 
    from (
          select dt,
                  shop_id            as shop_id,
                  chan_cd            as chan_cd,
                  stat_ct            as stat_ct,
                  stat_dt            as stat_dt,
                  pv                 as pv,
                  uv                 as uv,
                  bnc_rt             as bnc_rt,
                  avg_depth          as avg_depth,
                  avg_tm_on_page     as avg_tm_on_page,
                  add_cart_cust_qty  as add_cart_cust_qty,
                  add_cart_cust_rt   as add_cart_cust_rt,
                  ord_cust_qty       as ord_cust_qty,
                  ord_cust_rt        as ord_cust_rt,
                  ord_amt            as ord_amt,
                  sale_qty           as sale_qty,
                  ord_qty            as ord_qty,
                  avg_cust_prc       as avg_cust_prc,
                  shop_attn_num      as shop_attn_num,
                  itm_attn_num       as itm_attn_num,
                  thirty_repur_rate  as thirty_repur_rate,
                  ninty_repur_rate   as ninty_repur_rate,
                  intro_ord_cust_qty as intro_ord_cust_qty,
                  intro_ord_cust_rt  as intro_ord_cust_rt,
                  intro_ord_amt      as intro_ord_amt,
                  intro_sale_qty     as intro_sale_qty,
                  intro_ord_qty      as intro_ord_qty,
                  deal_ord_cust_qty  as deal_ord_cust_qty,
                  deal_ord_cust_rt   as deal_ord_cust_rt,
                  new_uv             as new_uv,
                  old_uv             as old_uv,
                  uv_value           as uv_value,
                  stat_ct_cd         as stat_ct_cd
            from retail_benchmark_10t.benchmark_app_zs_z0601_shop_index_overview_mid
           where dt = '""" + zst.data_day_str + """'
             and stat_ct_cd = 'day'
          ) shop_info

  union all
  
  select kepler.shop_id as shop_id,
         kepler.chan_cd as chan_cd,
         kepler.stat_ct as stat_ct,
         """ + zst.data_day_int + """       as stat_dt,
         kepler.pv as pv,
         kepler.uv as uv,
         null as bnc_rt,
         null as avg_depth,
         null as avg_tm_on_page,
         null as add_cart_cust_qty,
         null as add_cart_cust_rt,
         kepler.ord_cust_qty as ord_cust_qty,
         case when uv = 0 then 0 else 1.0*ord_cust_qty/uv end as ord_cust_rt,
         kepler.ord_amt as ord_amt,
         kepler.sale_qty as sale_qty,
         kepler.ord_qty as ord_qty,
         null as avg_cust_prc,
         null as shop_attn_num,
         null as itm_attn_num,
         null as thirty_repur_rate,
         null as ninty_repur_rate,
         null as in_ord_cust_rt, --入仓转化率
         kepler.intro_ord_cust_qty as intro_ord_cust_qty,
         case when uv = 0 then 0 else 1.0*intro_ord_cust_qty/uv end as intro_ord_cust_rt,
         kepler.intro_ord_amt as intro_ord_amt,
         kepler.intro_sale_qty as intro_sale_qty,
         kepler.intro_ord_qty as intro_ord_qty,
         kepler.deal_ord_cust_qty as deal_ord_cust_qty,
         CASE WHEN kepler.ord_cust_qty = 0 THEN 0.0
         ELSE COALESCE((kepler.deal_ord_cust_qty * 1.0) / kepler.intro_ord_cust_qty, 0.0) END AS deal_ord_cust_rt, ---下单成交转化率
         null as new_uv,
         null as old_uv,
         null as uv_value,
         '' as is_new_cust 
  from retail_benchmark_10t.benchmark_adm_s04_zs_shop_kepler_mid as kepler
  where dt='""" + zst.data_day_str + """'
  and stat_ct_cd = 'day'

  DISTRIBUTE BY shop_id, chan_cd SORT BY shop_id, chan_cd
  ;

  """
zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, retry_with_hive=False,
             exec_engine='spark', lzo_compress=False,
             lzo_index_path=['dt=' + zst.data_day_str + '/stat_ct_cd=' + stat_ct_cd],
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
