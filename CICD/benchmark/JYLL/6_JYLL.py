#!/usr/bin/env python3

# buffaloID: 589603

import datetime
from ZSTask import ZSTask

zst = ZSTask()

stat_ct_cd = zst.stat_ct_cd
stat_ct = str(zst.stat_ct)
stat_begin = zst.stat_begin

dest_table_name = 'benchmark_adm_m04_zs_oldnew_ord_det'

insertDay = datetime.date.today().strftime("%Y-%m-%d")

sql = """
USE retail_benchmark_10t;

INSERT OVERWRITE TABLE """ + dest_table_name + """ PARTITION(dt = '""" + insertDay + """',shop_typ)
SELECT vend_id                             ,    --商家编号
       ord_det.shop_id                     ,    --店铺编号
       ord_det.usr_log_acct                ,    --用户登录账号
       chan_cd                             ,    --渠道代码
       par_sale_ord_id                     ,    --父订单编号
       sale_ord_id                         ,    --订单编号
       ord_det.itm_sku_id                          ,    --SKU商品编号
       itm_sku_nm                          ,    --SKU商品名称
       itm_id                              ,    --SPU商品编号
       itm_nm                              ,    --SPU商品名称
       itm_frst_catg_cd                    ,    --商品一级分类代码
       itm_frst_catg_nm                    ,    --商品一级分类名称
       itm_scnd_catg_cd                    ,    --商品二级分类代码
       itm_scnd_catg_nm                    ,    --商品二级分类名称
       itm_thrd_catg_cd                    ,    --商品三级分类代码
       itm_thrd_catg_nm                    ,    --商品三级分类名称
       brand_cd                            ,    --品牌代码
       brand_nm                            ,    --品牌名称
       sale_ord_tm                         ,    --销售订单订购时间
       sale_ord_dt                         ,    --销售订单订购日期
       chk_acct_tm                         ,    --对账时间
       pay_tm                              ,    --付账时间
       pay_mde_cd                          ,    --支付方式代码
       pay_mde_dsc                         ,    --支付方式描述
       pay_typ_cd                          ,    --支付类型代码
       pay_typ_dsc                         ,    --支付类型描述
       sale_ord_vld_flg                    ,    --有效标志
       sku_jd_prc                          ,    --sku京东价
       sale_qty                            ,    --销售数量
       free_gds_sale_qty                   ,    --赠品销售数量
       bef_pref_unit_prc                   ,    --优惠前单价
       bef_pref_amt                        ,    --优惠前金额
       aft_pref_amt                        ,    --优惠后金额
       tot_pref_amt                        ,    --总优惠金额
       sku_freit_amt                       ,    --SKU运费金额
       ord_amt                             ,    --订单金额
       ord_tot_pref_amt                    ,    --订单总优惠金额
       ord_aft_pref_amt                    ,    --订单优惠后金额
       is_deal_ord                         ,    --是否成交订单
       deal_tm                             ,    --成交时间
       delv_center_num                     ,    --配送中心编号
       store_id                            ,    --库房编号
       cancel_flag                         ,    --取消标志
       sz_xiadan_flag                      ,    --下单标志
       CASE WHEN is_deal_ord = 1 AND allnew.usr_log_acct IS NOT NULL then 1 
            WHEN is_deal_ord = 1 AND allnew.usr_log_acct IS NULL then 0
       END AS is_allnew_flag               ,    --整体新客标识 1 新客，0 老客
       CASE WHEN is_deal_ord = 1 AND 180new.usr_log_acct IS NOT NULL then 1 
            WHEN is_deal_ord = 1 AND 180new.usr_log_acct IS NULL then 0
       END AS is_180new_flag               ,    --180天新客标识 1 新客，0 老客
       CASE WHEN is_deal_ord = 1 AND 730new.usr_log_acct IS NOT NULL then 1 
            WHEN is_deal_ord = 1 AND 730new.usr_log_acct IS NULL then 0
       END AS is_730new_flag               ,   --730天新客标识 1 新客，0 老客
       merchandise_info.main_sku_id as main_sku_id, ---- 主SKU
       second_chan_cd                      ,    --商智二级渠道
       appkey                              ,    --小程序key
       appname                             ,    --小程序key名称
       customerinfo                        ,    --渠道信息
       scene                               ,    --场景值
       app_id                              ,    --kepler应用appid       
       shop_typ
    FROM
        (SELECT
           vend_id                             ,    --商家编号
           shop_id                             ,    --店铺编号
           usr_log_acct                        ,    --用户登录账号
           chan_cd                             ,    --渠道代码
           par_sale_ord_id                     ,    --父订单编号
           sale_ord_id                         ,    --订单编号
           itm_sku_id                          ,    --SKU商品编号
           itm_sku_nm                          ,    --SKU商品名称
           itm_id                              ,    --SPU商品编号
           itm_nm                              ,    --SPU商品名称
           itm_frst_catg_cd                    ,    --商品一级分类代码
           itm_frst_catg_nm                    ,    --商品一级分类名称
           itm_scnd_catg_cd                    ,    --商品二级分类代码
           itm_scnd_catg_nm                    ,    --商品二级分类名称
           itm_thrd_catg_cd                    ,    --商品三级分类代码
           itm_thrd_catg_nm                    ,    --商品三级分类名称
           brand_cd                            ,    --品牌代码
           brand_nm                            ,    --品牌名称
           sale_ord_tm                         ,    --销售订单订购时间
           sale_ord_dt                         ,    --销售订单订购日期
           chk_acct_tm                         ,    --对账时间
           pay_tm                              ,    --付账时间
           pay_mde_cd                          ,    --支付方式代码
           pay_mde_dsc                         ,    --支付方式描述
           pay_typ_cd                          ,    --支付类型代码
           pay_typ_dsc                         ,    --支付类型描述
           sale_ord_vld_flg                    ,    --有效标志
           sku_jd_prc                          ,    --sku京东价
           sale_qty                            ,    --销售数量
           free_gds_sale_qty                   ,    --赠品销售数量
           bef_pref_unit_prc                   ,    --优惠前单价
           bef_pref_amt                        ,    --优惠前金额
           aft_pref_amt                        ,    --优惠后金额
           tot_pref_amt                        ,    --总优惠金额
           sku_freit_amt                       ,    --SKU运费金额
           ord_amt                             ,    --订单金额
           ord_tot_pref_amt                    ,    --订单总优惠金额
           ord_aft_pref_amt                    ,    --订单优惠后金额
           is_deal_ord                         ,    --是否成交订单
           deal_tm                             ,    --成交时间
           delv_center_num                     ,    --配送中心编号
           store_id                            ,    --库房编号
           cancel_flag                         ,    --取消标志
           sz_xiadan_flag                      ,    --下单标志
           second_chan_cd                      ,    --商智二级渠道
           appkey                              ,    --小程序key
           appname                             ,    --小程序key名称
           customerinfo                        ,    --渠道信息
           scene                               ,    --场景值
           app_id                              ,    --kepler应用appid
           shop_typ
        FROM retail_benchmark_10t.benchmark_adm_m04_zs_shop_ord_det
        WHERE dt ='""" + zst.data_day_str + """'
        )  ord_det
    LEFT JOIN (SELECT shop_id       AS shop_id  ,
                      usr_log_acct  AS usr_log_acct
                 FROM retail_benchmark_10t.benchmark_gdm_m01_zs_shop_ord_cust_det
                 WHERE dt ='""" + zst.data_day_str + """'
    )  allnew ON ord_det.shop_id = allnew.shop_id AND ord_det.usr_log_acct = allnew.usr_log_acct
    LEFT JOIN (SELECT shop_id       AS shop_id,
                      user_log_acct AS usr_log_acct
                 FROM retail_benchmark_10t.benchmark_adm_m01_zs_shop_ord_cust_di
                 WHERE dt='""" + zst.data_day_str + """'
                 AND dp = 180
    ) 180new ON ord_det.shop_id = 180new.shop_id AND ord_det.usr_log_acct = 180new.usr_log_acct
    LEFT JOIN (SELECT shop_id       AS shop_id,
                      user_log_acct AS usr_log_acct
                 FROM retail_benchmark_10t.benchmark_adm_m01_zs_shop_ord_cust_di
                 WHERE dt='""" + zst.data_day_str + """'
                 AND dp = 730
    ) 730new ON ord_det.shop_id = 730new.shop_id AND ord_det.usr_log_acct = 730new.usr_log_acct
     LEFT JOIN
      (--获取商品的相关信息
        SELECT
          itm_sku_id AS itm_sku_id,
          main_sku_id AS main_sku_id
        FROM
          retail_benchmark_10t.benchmark_adm_th03_merchandise_info
        WHERE
          dt = '""" + zst.data_day_str + """'
      )
      merchandise_info
    on
      ord_det.itm_sku_id = merchandise_info.itm_sku_id    
        
;
"""
zst.exec_sql(schema_name='retail_benchmark_10t', table_name=dest_table_name, sql=sql, exec_engine='spark',
             lzo_compress=True, lzo_index_path=['dt=' + zst.data_day_str], retry_with_hive=False,
             spark_args=["--conf spark.sql.adaptive.enabled=true", "--conf spark.sql.hive.mergeFiles=true",
                         "--conf spark.sql.adaptive.repartition.enabled=true"])
