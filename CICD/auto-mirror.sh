#/bin/bash
set -x
set -e

##仅推送release包镜像
if [[ $BRANCH =~ ^refs\/tags\/.* ]]; then
    curBranch=`echo $BRANCH | sed 's/refs\/tags\///g' | sed 's/.*\///'`
    #获取jwt_token
    JWT_GET=$(curl -X 'POST' 'http://10.198.76.37:9091/api/auth' -H 'accept: application/json' -H 'Content-Type: application/json' -d '{"password": "12345678", "username": "admin"}')
    JWT=${JWT_GET: 8: 152}

    #image包url
    SPARK_IMAGE=$1
    IMAGE_TAGS=is.jd.com/jdosbdp/spark:$curBranch
    IMAGE_TAGS_DADA="is.jd.com/jdosbdp/spark:${curBranch}-dada-spark2"
    DockerFile_PATH=Dockerfile
    Portainer_URL=http://10.198.76.37:9091
    CreateImage_API=/api/endpoints/1/docker/build

    if [[ $curBranch =~ "2.4.7" ]]; then
        #push image to portainer
        curl --location --request POST ''$Portainer_URL''$CreateImage_API'?dockerfile='$DockerFile_PATH'&t='$IMAGE_TAGS'&remote='$SPARK_IMAGE'' \
            --header 'Authorization: Bearer '$JWT''
        #push image to portainer tag rss
        IMAGE_TAGS_RSS="${IMAGE_TAGS}-rss"
        curl --location --request POST ''$Portainer_URL''$CreateImage_API'?dockerfile='$DockerFile_PATH'&t='$IMAGE_TAGS_RSS'&remote='$SPARK_IMAGE'' \
            --header 'Authorization: Bearer '$JWT''
        #push image to portainer tag rss_l3
        IMAGE_TAGS_RSSL3="${IMAGE_TAGS}-rss_l3"
        curl --location --request POST ''$Portainer_URL''$CreateImage_API'?dockerfile='$DockerFile_PATH'&t='$IMAGE_TAGS_RSSL3'&remote='$SPARK_IMAGE'' \
            --header 'Authorization: Bearer '$JWT''
    elif [[ $curBranch =~ "3.0.2" ]]; then
        #push image to portainer
        curl --location --request POST ''$Portainer_URL''$CreateImage_API'?dockerfile='$DockerFile_PATH'&t='$IMAGE_TAGS'&remote='$SPARK_IMAGE'' \
            --header 'Authorization: Bearer '$JWT''
        curl --location --request POST ''$Portainer_URL''$CreateImage_API'?dockerfile='$DockerFile_PATH'&t='${IMAGE_TAGS_DADA}'&remote='$SPARK_IMAGE'' \
            --header 'Authorization: Bearer '$JWT''
    fi
fi



