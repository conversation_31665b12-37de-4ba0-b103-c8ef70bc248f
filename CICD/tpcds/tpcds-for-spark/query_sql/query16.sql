-- start query 1 in stream 0 using template query16.tpl
select  
   count(distinct cs_order_number) as ordercount
  ,sum(cs_ext_ship_cost) as totalshippingcost
  ,sum(cs_net_profit) as totalnetprofit
from
   catalog_sales_xxxxxg cs1
  ,date_dim_xxxxxg as date_dim
  ,customer_address_xxxxxg as customer_address
  ,call_center_xxxxxg as call_center
where
    d_date between '1999-4-01' and 
           (cast('1999-4-01' as date) + interval 60 days)
and cs1.cs_ship_date_sk = d_date_sk
and cs1.cs_ship_addr_sk = ca_address_sk
and ca_state = 'IA'
and cs1.cs_call_center_sk = cc_call_center_sk
and cc_county in ('Mobile County','Maverick County','Huron County','Kittitas County',
                  'Fairfield County'
)
and exists (select *
            from catalog_sales_xxxxxg cs2
            where cs1.cs_order_number = cs2.cs_order_number
              and cs1.cs_warehouse_sk <> cs2.cs_warehouse_sk)
and not exists(select *
               from catalog_returns_xxxxxg cr1
               where cs1.cs_order_number = cr1.cr_order_number)
order by count(distinct cs_order_number)
limit 100;


