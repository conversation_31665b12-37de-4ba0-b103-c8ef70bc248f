-- start query 1 in stream 0 using template query96.tpl
select  count(*) 
from store_sales_xxxxxg as store_sales
    ,household_demographics_xxxxxg as household_demographics
    ,time_dim_xxxxxg as time_dim, store_xxxxxg as store
where ss_sold_time_sk = time_dim.t_time_sk   
    and ss_hdemo_sk = household_demographics.hd_demo_sk 
    and ss_store_sk = s_store_sk
    and time_dim.t_hour = 8
    and time_dim.t_minute >= 30
    and household_demographics.hd_dep_count = 5
    and store.s_store_name = 'ese'
order by count(*)
limit 100;


