-- start query 1 in stream 0 using template query9.tpl
select case when (select count(*) 
                  from store_sales_xxxxxg as store_sales 
                  where ss_quantity between 1 and 20) > *********
            then (select avg(ss_ext_discount_amt) 
                  from store_sales_xxxxxg as store_sales 
                  where ss_quantity between 1 and 20) 
            else (select avg(ss_net_paid_inc_tax)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 1 and 20) end bucket1 ,
       case when (select count(*)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 21 and 40) > *********
            then (select avg(ss_ext_discount_amt)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 21 and 40) 
            else (select avg(ss_net_paid_inc_tax)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 21 and 40) end bucket2,
       case when (select count(*)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 41 and 60) > *********
            then (select avg(ss_ext_discount_amt)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 41 and 60)
            else (select avg(ss_net_paid_inc_tax)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 41 and 60) end bucket3,
       case when (select count(*)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 61 and 80) > *********
            then (select avg(ss_ext_discount_amt)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 61 and 80)
            else (select avg(ss_net_paid_inc_tax)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 61 and 80) end bucket4,
       case when (select count(*)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 81 and 100) > 80139625
            then (select avg(ss_ext_discount_amt)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 81 and 100)
            else (select avg(ss_net_paid_inc_tax)
                  from store_sales_xxxxxg as store_sales
                  where ss_quantity between 81 and 100) end bucket5
from reason_xxxxxg as reason
where r_reason_sk = 1
;


