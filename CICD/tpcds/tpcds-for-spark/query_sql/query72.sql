-- start query 1 in stream 0 using template query72.tpl
select  i_item_desc
      ,w_warehouse_name
      ,d1.d_week_seq
      ,sum(case when p_promo_sk is null then 1 else 0 end) no_promo
      ,sum(case when p_promo_sk is not null then 1 else 0 end) promo
      ,count(*) total_cnt
from catalog_sales_xxxxxg as catalog_sales
join inventory_xxxxxg as inventory on (cs_item_sk = inv_item_sk)
join warehouse_xxxxxg as warehouse on (w_warehouse_sk=inv_warehouse_sk)
join item_xxxxxg as item on (i_item_sk = cs_item_sk)
join customer_demographics_xxxxxg as customer_demographics on (cs_bill_cdemo_sk = cd_demo_sk)
join household_demographics_xxxxxg as household_demographics on (cs_bill_hdemo_sk = hd_demo_sk)
join date_dim_xxxxxg d1 on (cs_sold_date_sk = d1.d_date_sk)
join date_dim_xxxxxg d2 on (inv_date_sk = d2.d_date_sk)
join date_dim_xxxxxg d3 on (cs_ship_date_sk = d3.d_date_sk)
left outer join promotion_xxxxxg as promotion on (cs_promo_sk=p_promo_sk)
left outer join catalog_returns_xxxxxg as catalog_returns on (cr_item_sk = cs_item_sk and cr_order_number = cs_order_number)
where d1.d_week_seq = d2.d_week_seq
  and inv_quantity_on_hand < cs_quantity 
  and d3.d_date > (cast(d1.d_date AS DATE) + interval 5 days)
  and hd_buy_potential = '1001-5000'
  and d1.d_year = 2001
  and cd_marital_status = 'M'
group by i_item_desc,w_warehouse_name,d1.d_week_seq
order by total_cnt desc, i_item_desc, w_warehouse_name, d_week_seq
limit 100;


