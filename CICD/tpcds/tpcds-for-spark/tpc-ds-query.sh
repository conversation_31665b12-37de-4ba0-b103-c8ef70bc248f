#/bin/sh
set -ex

QUERY_IDS=$1
SPARK_PACKAGE_FILE_NAME=$2
SPARK_EXTRA_PARAMS=${3}
TRIGGER_USER_ERP=$4
LABEL=$5
CI_ID=$6
DATA_SCALE=${7:-2}

WORK_DIR=$(dirname $(readlink -f $0))
TMP_DIR=`mktemp -d -p /tmp spark_tpcds_${TRIGGER_USER_ERP}_${CI_CD}_XXXXXXX`
LOG_DIR="${TMP_DIR}/${TRIGGER_USER_ERP}_${CI_ID}_log"
TPCDS_DBNAME=${TPCDS_DBNAME:-tmp}
DST_LOG_DIR="${HOME}/spark-tpcds-log"

function prepare_spark()
{
  pushd ${TMP_DIR}
  spark_package_uri=$1
  spark_file=${spark_package_uri##*/}
  wget -q ${SPARK_PACKAGE_FILE_NAME}
  tar -xf ${spark_file}
  export SPARK_HOME=${TMP_DIR}/${spark_file%*.tgz}
  export PATH=$SPARK_HOME/bin:$PATH
  ${SPARK_HOME}/bin/spark-shell --version
  popd
}

function do_query(){
  query_num=$1
  query_file=${TMP_DIR}/query_sql/query${query_num}.sql
  query_log="${LOG_DIR}/query_${query_num}.log"

  sqlAdd="drop table if exists tpcds_${CI_ID}_query_${query_num}_dev; create table if not exists tpcds_${CI_ID}_query_${query_num}_dev as "
  sed -i  "1i \ $sqlAdd"  $query_file 

  start=$(date +%s)

  echo "start query ${query_num} at $(date) log file: ${query_log}"
  $SPARK_HOME/bin/spark-sql --database $TPCDS_DBNAME --name "spark_tpcds_query${query_num}" ${SPARK_EXTRA_PARAMS} -f "${query_file}" &> $query_log
  end=$(date +%s)
  ELAPSED_TIME=$(( end - start ))
  applicationID=`cat $query_log | grep "Create Applicaiton" |  sed -r "s/.*(application_.*) elapsed.*/\1/g"`
  currentDate=$(date +%Y-%m-%d)

  cat>insert_to_mysql.sql<<EOF
insert into tpcds_result(data_scale_tb, query_id_str, query_id, version, duration, dt, erp, application, params, label, ci_id) values(
  ${DATA_SCALE},
  'query_${query_num}',
  ${query_num},
  '${SPARK_HOME##*/}',
  ${ELAPSED_TIME},
  '${currentDate}',
  '${TRIGGER_USER_ERP}',
  '${applicationID}',
  '${SPARK_EXTRA_PARAMS}',
  '${LABEL}',
  ${CI_ID}
  );
EOF

  mysql -hmy18001m.mysql.jddb.com -P3358 --default-character-set=utf8 -umetastore_mys_rw -pcSVLdCeIRh7qRyMj8tyTURYVIheoYQdf --database=metastore_mysql < insert_to_mysql.sql

}

function main()
{
  source ${WORK_DIR}/env.sh
  mkdir -p ${DST_LOG_DIR}
  prepare_spark ${SPARK_PACKAGE_FILE_NAME}
  cp -r ${WORK_DIR}/query_sql ${TMP_DIR}/
  sed -i "s/xxxxxg/${DATA_SCALE}g/g" ${TMP_DIR}/query_sql/query*
  # mkdir log dir
  mkdir -p ${LOG_DIR}

  if [ "$QUERY_IDS" == "all" ]; then
    QUERY_IDS=`seq 1 99`
  else 
    QUERY_IDS=${QUERY_IDS//,/ }
  fi
  for query in $QUERY_IDS
  do
    echo "==> Query: $query"
    do_query $query
  done

  ## collect log and clean tmp dir
  mv ${TMP_DIR}/query_sql ${LOG_DIR}/
  mv ${LOG_DIR} ${DST_LOG_DIR}/
  \rm -rf ${TMP_DIR}
}

main
