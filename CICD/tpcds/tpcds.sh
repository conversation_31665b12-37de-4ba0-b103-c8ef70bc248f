#/bin/bash
set -ex

TRIGGER_USER_ERP=${1}
CI_ID=${2}

WORK_DIR=$(dirname $(readlink -f $0))


#if [ ! -z "${SPARK_PACKAGE}" ]; then
#    cd $WORKSPACE/CI-CD-Platform/spark/spark-deploy/packages
#    sparkFileName=${SPARK_PACKAGE##*/}
#    rm -f "${sparkFileName}"
#    wget -q ${SPARK_PACKAGE}
#    cd $WORKSPACE/CI-CD-Platform/spark/spark-deploy/
#    sed -i "s#spark-package-name#${sparkFileName}#" roles/spark/defaults/main.yml
#    sed -i "s#spark-package-name#${sparkFileName}#" roles/dev/defaults/main.yml
#    cat roles/spark/defaults/main.yml
#    ansible-playbook setup-dev-client.yml -i host-dev
#fi

if [ ! -z "${QUERY_IDS}" ]; then
    scp -r ${WORK_DIR}/tpcds-for-spark ${DRIVER_NODE}:/tmp/
    ssh ${DRIVER_NODE} /tmp/tpcds-for-spark/tpc-ds-query.sh \
    "\
    \"${QUERY_IDS}\" \
    \"${SPARK_PACKAGE}\" \
    \"${EXTRA_PARAM}\" \
    \"${TRIGGER_USER_ERP}\" \
    \"${LABEL}\" \
    \"${CI_ID}\" \
    \"${DATA_SCALE_GB}\" \
    "
fi
