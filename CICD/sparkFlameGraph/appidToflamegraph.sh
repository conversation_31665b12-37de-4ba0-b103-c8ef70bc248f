#!/bin/bash

set -x
set -e

echo '$1: appid, $2(Optional): stackBase'

if [[ -z "$1" ]]; then
	exit 1
fi

appid=$1
timeStr=`date "+%Y_%m_%d_%H_%M_%S"`

if [[ -n "$2" ]]; then
	stackBase=$2
else
	stackBase='org.apache.spark.executor.Executor$TaskRunner.run'
fi

jsonFile="tmp/$timeStr.json"
foldedFile="tmp/$timeStr.folded"
gramegraphFile="result/flamegraph_$timeStr.svg"

mkdir -p tmp
mkdir -p result

yarn logs -applicationId $appid \
| fgrep "testprofiling" | fgrep  "Stacktrace" | cut -c 37-  | fgrep $stackBase | sed -E 's/Lambda\$[0-9]+\/[0-9]+/Lambda\$/g' \
> $jsonFile

python stackcollapse.py -i $jsonFile > $foldedFile

./flamegraph.pl $foldedFile > $gramegraphFile

echo "gramegraph: $gramegraphFile"
