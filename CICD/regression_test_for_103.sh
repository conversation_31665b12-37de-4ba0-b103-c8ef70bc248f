#/bin/bash
set -x
set -e

source "$(dirname "$0")"/printHostEnv.sh

(
cat <<EOF
#! /bin/bash
set -x
set -e

export JDHXXXXX_CLUSTER_NAME=${PARAM_CLUSTER}
export JDHXXXXX_USER=${PARAM_MARKET}
export JDHXXXXX_QUEUE=${PARAM_QUEUE}
export TEAM_USER=${PARAM_TEAM_USER}
source /software/servers/env/env.sh

if [ "${CHECK_VERSION}" = "True" ]; then
    spark-shell --version
fi

if [ "${CHECK_SPARK_PI}" = "True" ]; then
    run-example SparkPi 100
fi

if [ "${CHECK_SPARK_SQL}" = "True" ]; then
    spark-sql -e "select jobsource,count(1) from fdm.fdm_spark_appinfo_di where dt = '2021-04-15' group by jobsource"
fi

EOF
) > regression_test.sh

scp regression_test.sh bdp_client@**************:/data0/wuguoxiao/
ssh bdp_client@************** sh /data0/wuguoxiao/regression_test.sh
