set -x
set -e

source "$(dirname "$0")"/printHostEnv.sh

cd ${WORKSPACE}

baseDir=/data/home_dir/wuguoxiao/spark_on_jdos

scp SparkConf/client/10k/config root@**************:${baseDir}
scp -r SparkConf/sparkonjdos/* root@**************:${baseDir}
(
cat <<EOF
#! /bin/bash
set -x
set -e
cd ${baseDir}

CONFIG_PARAM=" --kubeconfig config "
KUBECTL_PARAM="\$CONFIG_PARAM -n sparkonjdos "

ls -l
chmod 777 kubectl
./kubectl \$KUBECTL_PARAM version
daemonsetNames=(\$(./kubectl \$KUBECTL_PARAM get ds --no-headers=true | awk '{print \$1}'))

for i in \${daemonsetNames[@]}
do
  [ "\$i" == "local-spark-assistant" ] && ./kubectl \$KUBECTL_PARAM delete ds local-spark-assistant
  [ "\$i" == "local-shuffle-service-new" ] && ./kubectl \$KUBECTL_PARAM delete ds local-shuffle-service-new
  [ "\$i" == "base-lvm-for-local-shuffle-service-new" ] && ./kubectl \$KUBECTL_PARAM delete ds base-lvm-for-local-shuffle-service-new
done

./kubectl \$CONFIG_PARAM apply -f base-lvm-for-local-shuffle-service-new.yaml
./kubectl \$CONFIG_PARAM apply -f local-shuffle-service-new.yaml
./kubectl \$CONFIG_PARAM apply -f local-spark-assistant.yaml

EOF
) > deploy.sh
cat deploy.sh
scp deploy.sh root@**************:${baseDir}
ssh root@************** sh ${baseDir}/deploy.sh