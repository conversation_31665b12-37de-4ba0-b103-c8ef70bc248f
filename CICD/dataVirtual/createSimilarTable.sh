#!/bin/sh

export BEE_SOURCE=BUFFALO4;
export JDHXXXXX_CLUSTER_NAME=cairne;
export JDHXXXXX_USER=dd_edw;
export JDHXXXXX_QUEUE=bdp_jdw_jmart_union.bdp_jdw_jmart_dev;
export TEAM_USER=dd_edw_mart;
source /software/servers/env/env.sh;

set -x

hive -e """
drop table if exists dev.spark_team_temp_similar_model;
CREATE TABLE if not exists dev.spark_team_temp_similar_model (
  cluster_a string COMMENT '集群a',
  db_name_a string COMMENT '库a',
  tbl_name_a string COMMENT '表a',
  cluster_b string COMMENT '集群b',
  db_name_b string COMMENT '库b',
  tbl_name_b string COMMENT '表b'
) stored as orcfile;
"""


