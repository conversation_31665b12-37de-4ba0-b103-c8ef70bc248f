#/bin/bash
set -x
set -e

cd ${WORKSPACE}

DELTA_HOME=${WORKSPACE}/delta
JETCD_HOME=${WORKSPACE}/jetcd
SPARK_HOME=${WORKSPACE}/spark
ICEBERG_HOME=${WORKSPACE}/iceberg
RW_MONITOR_HOME=${WORKSPACE}/RWMonitor
curDateTime=`date '+%Y%m%d-%H%M%S'`
curBranch=`echo $BRANCH | sed 's/refs\/tags\///g' | sed 's/.*\///'`
hadoopVersion=$HADOOP_VERSION
hiveProfile=hive
scalaVersion=scala-2.11.12
mavenIgnoreFailure=''
testFailureIgnore=''
TIMEFORMAT='It takes %R seconds to complete this task...'
sparkSourceVersion=''

echo /dev/null > ${SPARK_HOME}/sparkenv

if [ ! -z "${NOTE}" ]; then
    TEST_SUITE=${NOTE}
    NOTE_HADOOP_VERSION=`echo ${NOTE} | sed -n 's/.*HADOOP_VERSION IS \[\(.*\)\].*/\1/gp'`
    if [ ! -z "${NOTE_HADOOP_VERSION}" ]; then
        HADOOP_VERSION=$NOTE_HADOOP_VERSION
    fi
fi

if [ "${IGNORE_FAILURE}" = "True" ]; then
    mavenIgnoreFailure='-fn'
fi

if [ "${TEST_FAILURE_IGNORE}" = "True" ]; then
    testFailureIgnore='-Dmaven.test.failure.ignore=true'
fi

initialScalaEnv() {
    cd ${WORKSPACE}
    wget -P build/ http://storage.jd.local/moneta/scala-2.12.11.tgz && tar -xvf build/scala-2.12.11.tgz -C build
    wget -P build/ http://storage.jd.local/moneta/scala-2.11.12.tgz && tar -xvf build/scala-2.11.12.tgz -C build
}

initialMavenEnv() {
    export MAVEN_OPTS="-Xss64m -Xmx4g -XX:ReservedCodeCacheSize=1g"
    export MAVEN_HOME=/export/servers/apache-maven-3.6.3
    export PATH=$MAVEN_HOME/bin:$PATH
    cat /export/servers/apache-maven-3.6.3/conf/settings.xml
}

initialPythonEnv() {
    sudo yum install -y python3
}

buildRWMonitor() {
  cd ${RW_MONITOR_HOME}
  mvn clean package -DskipTests
}

checkSparkVersion() {
    cd ${SPARK_HOME}
    mavenProjectVersion=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    sparkSourceVersion=$mavenProjectVersion
    if [[ "$mavenProjectVersion" =~ ^3\.2 ]]; then
        hiveProfile=hive-2.3
        scalaVersion=scala-2.12.11
        scalaVersion2=scala-2.12
        hadoopProfile=hadoop-2.7
        hiveBranchInference='JDhive-release-2.3.9'
    elif [[ "$mavenProjectVersion" =~ ^3 ]]; then
        hiveProfile=hive-1.2
        scalaVersion=scala-2.12.11
        scalaVersion2=scala-2.12
        hadoopProfile=hadoop-2.7
        hiveBranchInference='JDhive-release-1.2.1-spark2'
    else
        hiveProfile=hive
        scalaVersion=scala-2.11.12
        scalaVersion2=scala-2.11
        hadoopProfile=hadoop-2.7
        hiveBranchInference='JDhive-release-1.2.1-spark2'
    fi
}

buildSpark() {
    cd ${SPARK_HOME}

    export SCALA_HOME=${WORKSPACE}/build/${scalaVersion}
    export PATH=$SCALA_HOME/bin:$PATH
    mvn dependency:tree -P${hadoopProfile} -P${hiveProfile} -Phive-thriftserver -Pyarn -Pkubernetes -DskipTests -Dhadoop.version=$hadoopVersion
    \rm -rf spark-*-bin
    export TYPESAFE_MIRROR=http://archive.bdp.jd.com
    export TAG=online-$curBranch-$curDateTime
    SECONDS=0
    ./dev/make-distribution.sh --name $hadoopVersion --tgz -Dhadoop.version=$hadoopVersion -P${hadoopProfile} -P${hiveProfile} ${MAKE_DISTRIBUTION_PROFILE}
    ELAPSED_TIME=$SECONDS
    echo makeSparkPackageStatus="Spark project was successfully built, and it took ${ELAPSED_TIME} seconds." >> ${SPARK_HOME}/sparkenv
}

deploySpark() {
    cd ${SPARK_HOME}
    export SCALA_HOME=${WORKSPACE}/build/${scalaVersion}
    export PATH=$SCALA_HOME/bin:$PATH
    SECONDS=0
    mvn deploy -Dhadoop.version=$hadoopVersion -P${hadoopProfile} -P${hiveProfile} -DskipTests ${MAKE_DISTRIBUTION_PROFILE}
    ELAPSED_TIME=$SECONDS
    echo makeSparkPackageStatus="Spark project was successfully deploy, and it took ${ELAPSED_TIME} seconds." >> ${SPARK_HOME}/sparkenv
}

makeReleasePackage() {
    cd ${SPARK_HOME}

    sparkReleaseName=spark-$mavenProjectVersion-$hadoopVersion-online-$curBranch-$curDateTime
    cp -r dist $sparkReleaseName
    git log --graph --abbrev-commit --format=format:'$h - (%ai) %s - %an%d' --all > $sparkReleaseName/ChangesLog
    cp ${WORKSPACE}/RWMonitor/target/rw-monitor-1.0.jar $sparkReleaseName/jars/
    ls ./external/avro/target/*.jar | grep -v -E 'test|sources|original' | xargs -I {} cp {} $sparkReleaseName/jars/

    ls -l
    ls -l common/network-yarn/target/
    mv common/network-yarn/target/${scalaVersion2}/spark-$mavenProjectVersion-yarn-shuffle.jar \
    common/network-yarn/target/${scalaVersion2}/spark-$mavenProjectVersion-yarn-shuffle-$hadoopVersion-online-$curBranch-$curDateTime.jar
    tar -czvf $sparkReleaseName".tgz" $sparkReleaseName
    zip -r -j $sparkReleaseName".zip" $sparkReleaseName/jars/

    (
cat <<EOF
FROM is.jd.com/jdosbdp/buffalo-plugin-base:v1
ADD ${sparkReleaseName} /bdp/spark
ENV SPARK_HOME=/bdp/spark
EOF
) > Dockerfile

    tar -czvf "image-"$curBranch".tar.gz" Dockerfile $sparkReleaseName
    echo sparkReleaseName=${sparkReleaseName} >> ${SPARK_HOME}/sparkenv

    ls -l
}

publishImageToJDOS() {
    dockerBuildHostWorkspace=/data/home_dir/spark-on-jdos
    revision=`sed -n '3,3p' ${SPARK_HOME}/core/target/extra-resources/spark-version-info.properties`
    imageTag=`echo -n ${revision##*=}`
    scp ${SPARK_HOME}/${sparkReleaseName}".tgz" root@**************:${dockerBuildHostWorkspace}
(
cat <<EOF
#! /bin/bash
set -x
set -e
cd ${dockerBuildHostWorkspace}
tar -xvf $sparkReleaseName".tgz"
cd $sparkReleaseName
docker build -t is.jd.com/sparkonjdos/spark-task:${imageTag} -f kubernetes/dockerfiles/spark/Dockerfile .
docker --config /data/home_dir/wangwenjuan/jdos-docker-login push is.jd.com/sparkonjdos/spark-task:${imageTag}

docker build -t is.jd.com/sparkonjdos/spark-lss:${imageTag} -f kubernetes/dockerfiles/spark/Dockerfile.lss .
docker --config /data/home_dir/wangwenjuan/jdos-docker-login push is.jd.com/sparkonjdos/spark-lss:${imageTag}
EOF
) > publishImage.sh

    cat publishImage.sh
    scp publishImage.sh root@**************:${dockerBuildHostWorkspace}
    ssh root@************** sh ${dockerBuildHostWorkspace}/publishImage.sh
}

buildHiveClient() {
    if [[ -n "$HIVE_BRANCH" ]]; then
        hiveBranchInference=$HIVE_BRANCH
    fi
    cd ${WORKSPACE}/JDhive-release-1.2.1-spark2
    git checkout $hiveBranchInference

    SECONDS=0
    mvn help:effective-settings
    mvn help:evaluate -Dexpression=settings.localRepository
    if [[ "$sparkSourceVersion" =~ ^3\.2 ]]; then
        mvn clean install -DskipTests -Psources -Dgpg.skip=true
    else
        mvn clean install -DskipTests -Phadoop-2 -Psources -Dgpg.skip=true
    fi
    ELAPSED_TIME=$SECONDS
    echo makeHivePackageStatus="HIVE_CLIENT project was successfully built, and it took ${ELAPSED_TIME} seconds." >> ${SPARK_HOME}/sparkenv
}

buildJETCD() {
    cd ${JETCD_HOME}
    mvn help:effective-settings
    mvn help:evaluate -Dexpression=settings.localRepository
    mvn -pl '!jetcd-launcher-maven-plugin-test,!jetcd-osgi/jetcd-karaf' clean install -DskipTests -Pfast
}

buildDelta() {
    cd ${DELTA_HOME}
    sed -i '4 a   jd-maven: http://artifactory.jd.com/libs-releases' build/sbt-config/repositories

    cat  build/sbt-config/repositories
    build/sbt package
    ls -l target/
}

buildIceBerg() {
    cd ${ICEBERG_HOME}
    curl -o gradle/wrapper/gradle-wrapper.jar http://archive.bdp.jd.com/gradle-wrapper.jar
    sed -i '25 a maven { url "http://artifactory.jd.com/libs-releases" }' build.gradle
    cat build.gradle
    ./gradlew build -x test
    ls -l
}

execSparkUT() {
    cd ${SPARK_HOME}

    if [[ "${TEST_SUITE}" =~ "ut_all" ]]; then
        SECONDS=0
        mvn test ${testFailureIgnore} ${mavenIgnoreFailure} -P ${hadoopProfile},yarn,kubernetes,kubernetes-integration-tests,${hiveProfile},hive-thriftserver -Dhadoop.version=$hadoopVersion
        mvn surefire-report:report-only
        ELAPSED_TIME=$SECONDS
        echo utReport="UT ALL successfully, it took ${ELAPSED_TIME} seconds." >> ${SPARK_HOME}/sparkenv
    elif [[ "${TEST_SUITE}" =~ "ut_core" ]]; then
        SECONDS=0
        mvn test ${testFailureIgnore} ${mavenIgnoreFailure} -Dhadoop.version=${hadoopVersion} -Dcurator.version=2.7.1 -Dcheckstyle.skip=true \
        -P ${hadoopProfile},yarn,kubernetes,kubernetes-integration-tests,${hiveProfile},hive-thriftserver -Dcheckstyle.skip=true \
        -pl common/sketch,common/kvstore,common/network-common,common/network-shuffle,common/unsafe,common/tags,launcher,core \
        -DfailIfNoTests=false
        ELAPSED_TIME=$SECONDS
        echo utReport="UT CORE successfully, it took ${ELAPSED_TIME} seconds." >> ${SPARK_HOME}/sparkenv
        mvn surefire-report:report-only
    elif [[ "${TEST_SUITE}" =~ "ut_sql" ]]; then
        SECONDS=0
        mvn test ${testFailureIgnore} ${mavenIgnoreFailure} -Dhadoop.version=${hadoopVersion} -Dcurator.version=2.7.1 -Dcheckstyle.skip=true \
        -P ${hadoopProfile},yarn,kubernetes,kubernetes-integration-tests,${hiveProfile},hive-thriftserver -Dcheckstyle.skip=true \
        -pl common/sketch,common/kvstore,common/network-common,common/network-shuffle,common/unsafe,common/tags,launcher,core,sql/catalyst,sql/core,sql/hive,sql/hive-thriftserver \
        -DfailIfNoTests=false
        ELAPSED_TIME=$SECONDS
        echo utReport="UT SQL successfully, it took ${ELAPSED_TIME} seconds." >> ${SPARK_HOME}/sparkenv
        mvn surefire-report:report-only
    elif [[ "${TEST_SUITE}" =~ "ut_streaming" ]]; then
        SECONDS=0
        mvn test ${testFailureIgnore} ${mavenIgnoreFailure} -Dhadoop.version=${hadoopVersion} -Dcurator.version=2.7.1 -Dcheckstyle.skip=true \
        -P ${hadoopProfile},yarn,kubernetes,kubernetes-integration-tests,${hiveProfile},hive-thriftserver -Dcheckstyle.skip=true \
        -pl sql/core,streaming,resource-managers/kubernetes/core,resource-managers/kubernetes/integration-tests \
        -am \
        '-DwildcardSuites=org.apache.spark.deploy.k8s,org.apache.spark.streaming,org.apache.spark.sql.streaming' \
        '-Dtest=org/apache/spark/deploy/k8s/**,org/apache/spark/streaming/**,org/apache/spark/sql/streaming/**' \
        -DfailIfNoTests=false
        ELAPSED_TIME=$SECONDS
        echo utReport="UT Streaming successfully, it took ${ELAPSED_TIME} seconds." >> ${SPARK_HOME}/sparkenv
        mvn surefire-report:report-only
    elif [[ "${TEST_SUITE}" =~ "ut_common" ]]; then
        SECONDS=0
        mvn test ${testFailureIgnore} ${mavenIgnoreFailure} -Dhadoop.version=${hadoopVersion} -Dcurator.version=2.7.1 -Dcheckstyle.skip=true \
        -P ${hadoopProfile},yarn,kubernetes,kubernetes-integration-tests,${hiveProfile},hive-thriftserver -Dcheckstyle.skip=true \
        -pl common/sketch,common/kvstore,common/network-common,common/network-shuffle,common/unsafe,common/tags \
        -DfailIfNoTests=false
        ELAPSED_TIME=$SECONDS
        echo utReport="UT COMMON successfully, it took ${ELAPSED_TIME} seconds." >> ${SPARK_HOME}/sparkenv
        mvn surefire-report:report-only
    elif [[ "${TEST_SUITE}" =~ "none" ]]; then
        echo "None of the condition met"
    fi
}

initialMavenEnv

initialScalaEnv

initialPythonEnv

source "$(dirname "$0")"/printHostEnv.sh

time {
    if [ "${JETCD}" = "True" ]; then
        buildJETCD
    fi
}

time {
  checkSparkVersion
}

time {
    if [ "${HIVE_CLIENT}" = "True" ]; then
        buildHiveClient
    fi
}

time {
    if [ "${DELTA}" = "True" ]; then
        buildDelta
    fi
}

time {
  buildRWMonitor
}

time {
    if [ "${SPARK}" = "True" ]; then
        buildSpark
    fi
}

time {
    if [ "${SPARK_DEPLOY}" = "True" ]; then
        deploySpark
    fi
}

time {
    if [ "${ICEBERG}" = "True" ]; then
        buildIceBerg
    fi
}

time {
    if [ "${SPARK}" = "True" ]; then
        makeReleasePackage
    fi
}

time {
    if [ "${SPARK}" = "True" ]; then
        execSparkUT
    fi
}

time {
    if [ "${PUBLISH_IMAGE}" = "True" ]; then
        publishImageToJDOS
    fi
}
