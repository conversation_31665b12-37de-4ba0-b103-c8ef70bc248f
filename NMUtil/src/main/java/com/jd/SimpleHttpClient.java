package com.jd;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class SimpleHttpClient {

    private static final Logger logger = Logger.getLogger(SimpleHttpClient.class.getName());

    public static String sendRequest(CloseableHttpClient httpclient, String url, RequestConfig requestConfig) throws IOException {
        HttpGet httpGet = new HttpGet(url);
        if(requestConfig != null) {
            httpGet.setConfig(requestConfig);
        }
        String result = "";
        int code = -1;
        try (CloseableHttpResponse response1 = httpclient.execute(httpGet)) {
            code = response1.getStatusLine().getStatusCode();
            if (code == 200 || code == 500) {
                HttpEntity entity1 = response1.getEntity();
                result = EntityUtils.toString(entity1, "UTF-8");
                EntityUtils.consume(entity1);
            }
        }
        return result;
    }

    public static Properties sendRequestProperties(CloseableHttpClient httpclient, String url,
                                                   RequestConfig requestConfig) throws IOException {
        Properties properties = new Properties();
        HttpGet httpGet = new HttpGet(url);
        if(requestConfig != null) {
            httpGet.setConfig(requestConfig);
        }
        try (CloseableHttpResponse response1 = httpclient.execute(httpGet)) {
            int code = response1.getStatusLine().getStatusCode();
            if (code == 200 || code == 500) {
                HttpEntity entity1 = response1.getEntity();
                InputStream content = entity1.getContent();
                properties = parseXmlToProperties(content);
                EntityUtils.consume(entity1);
            }
        } catch (ParserConfigurationException | SAXException e) {
            e.printStackTrace();
        }
        return properties;
    }

    public static Properties parseXmlToProperties(InputStream content) throws ParserConfigurationException, SAXException, IOException {
        Properties properties = new Properties();
        DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
        Document document = documentBuilder.parse(content);
        NodeList property = document.getElementsByTagName("property");
        for (int i = 0; i < property.getLength(); i++) {
            Node item = property.item(i);
            NodeList childNodes = item.getChildNodes();
            String key = null;
            String value = null;
            for (int j = 0; j < childNodes.getLength(); j++) {
                Node item1 = childNodes.item(j);
                if("name".equals(item1.getNodeName())) {
                    key = item1.getTextContent();
                } else if("value".equals(item1.getNodeName())) {
                    value = item1.getTextContent();
                }
            }
            if(key != null && value != null) {
                properties.setProperty(key, value);
            }
        }
        return properties;
    }

    public static String sendJsonRequest(CloseableHttpClient httpclient,JSONObject object, String url) {
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
        StringEntity stringEntity = new StringEntity(object.toJSONString(), "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        httpPost.setEntity(stringEntity);
        String ret = "";
        try {
            CloseableHttpResponse execute = httpclient.execute(httpPost);
            ret = EntityUtils.toString(execute.getEntity());
            EntityUtils.consume(execute.getEntity());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public static String sendRawRequest(CloseableHttpClient httpclient, String rawStr, String url, RequestConfig requestConfig) {
        HttpPost httpPost = new HttpPost(url);
        if(requestConfig != null) {
            httpPost.setConfig(requestConfig);
        }
        httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        StringEntity stringEntity = new StringEntity(rawStr, "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        httpPost.setEntity(stringEntity);
        String ret = null;
        try {
            CloseableHttpResponse execute = httpclient.execute(httpPost);
            ret = EntityUtils.toString(execute.getEntity());
            EntityUtils.consume(execute.getEntity());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return ret;
    }
}
