package com.jd;

import java.io.EOFException;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

/**
 *
 * <AUTHOR>
 */
public class Main {

    private static CloseableHttpClient httpClient = HttpClients.createDefault();

    private static RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(2000)
            .setConnectionRequestTimeout(2000).setSocketTimeout(5000).build();

    public static void main(String[] args) {
        requestYarn("hope", "*************:50320");
        requestYarn( "10k-yarn2", "*************:50320");
    }

    private static void requestYarn(String cluster, String rmIp) {
        int count = 1 ;
        Integer sEcho = 1;
        int iDisplayStart = 0;
        for (Integer pageNo = 0; pageNo < 1000; pageNo++) {
            String url = "http://" + rmIp + "/ws/v1/cluster/async/nodes?label=*&type=&sortName=%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20Node%20Labels";
            String response = SimpleHttpClient.sendRawRequest(
                    httpClient,
                    "aoData=[{\"name\":\"sEcho\",\"value\":" + sEcho + "},{\"name\":\"iColumns\",\"value\":25},{\"name\":\"sColumns\",\"value\":\"\"},{\"name\":\"iDisplayStart\",\"value\":" + iDisplayStart + "},{\"name\":\"iDisplayLength\",\"value\":20},{\"name\":\"mDataProp_0\",\"value\":0},{\"name\":\"mDataProp_1\",\"value\":1},{\"name\":\"mDataProp_2\",\"value\":2},{\"name\":\"mDataProp_3\",\"value\":3},{\"name\":\"mDataProp_4\",\"value\":4},{\"name\":\"mDataProp_5\",\"value\":5},{\"name\":\"mDataProp_6\",\"value\":6},{\"name\":\"mDataProp_7\",\"value\":7},{\"name\":\"mDataProp_8\",\"value\":8},{\"name\":\"mDataProp_9\",\"value\":9},{\"name\":\"mDataProp_10\",\"value\":10},{\"name\":\"mDataProp_11\",\"value\":11},{\"name\":\"mDataProp_12\",\"value\":12},{\"name\":\"mDataProp_13\",\"value\":13},{\"name\":\"mDataProp_14\",\"value\":14},{\"name\":\"mDataProp_15\",\"value\":15},{\"name\":\"mDataProp_16\",\"value\":16},{\"name\":\"mDataProp_17\",\"value\":17},{\"name\":\"mDataProp_18\",\"value\":18},{\"name\":\"mDataProp_19\",\"value\":19},{\"name\":\"mDataProp_20\",\"value\":20},{\"name\":\"mDataProp_21\",\"value\":21},{\"name\":\"mDataProp_22\",\"value\":22},{\"name\":\"mDataProp_23\",\"value\":23},{\"name\":\"mDataProp_24\",\"value\":24},{\"name\":\"sSearch\",\"value\":\"\"},{\"name\":\"bRegex\",\"value\":false},{\"name\":\"sSearch_0\",\"value\":\"\"},{\"name\":\"bRegex_0\",\"value\":false},{\"name\":\"bSearchable_0\",\"value\":true},{\"name\":\"sSearch_1\",\"value\":\"\"},{\"name\":\"bRegex_1\",\"value\":false},{\"name\":\"bSearchable_1\",\"value\":true},{\"name\":\"sSearch_2\",\"value\":\"\"},{\"name\":\"bRegex_2\",\"value\":false},{\"name\":\"bSearchable_2\",\"value\":true},{\"name\":\"sSearch_3\",\"value\":\"\"},{\"name\":\"bRegex_3\",\"value\":false},{\"name\":\"bSearchable_3\",\"value\":true},{\"name\":\"sSearch_4\",\"value\":\"\"},{\"name\":\"bRegex_4\",\"value\":false},{\"name\":\"bSearchable_4\",\"value\":true},{\"name\":\"sSearch_5\",\"value\":\"\"},{\"name\":\"bRegex_5\",\"value\":false},{\"name\":\"bSearchable_5\",\"value\":true},{\"name\":\"sSearch_6\",\"value\":\"\"},{\"name\":\"bRegex_6\",\"value\":false},{\"name\":\"bSearchable_6\",\"value\":true},{\"name\":\"sSearch_7\",\"value\":\"\"},{\"name\":\"bRegex_7\",\"value\":false},{\"name\":\"bSearchable_7\",\"value\":true},{\"name\":\"sSearch_8\",\"value\":\"\"},{\"name\":\"bRegex_8\",\"value\":false},{\"name\":\"bSearchable_8\",\"value\":false},{\"name\":\"sSearch_9\",\"value\":\"\"},{\"name\":\"bRegex_9\",\"value\":false},{\"name\":\"bSearchable_9\",\"value\":false},{\"name\":\"sSearch_10\",\"value\":\"\"},{\"name\":\"bRegex_10\",\"value\":false},{\"name\":\"bSearchable_10\",\"value\":false},{\"name\":\"sSearch_11\",\"value\":\"\"},{\"name\":\"bRegex_11\",\"value\":false},{\"name\":\"bSearchable_11\",\"value\":true},{\"name\":\"sSearch_12\",\"value\":\"\"},{\"name\":\"bRegex_12\",\"value\":false},{\"name\":\"bSearchable_12\",\"value\":true},{\"name\":\"sSearch_13\",\"value\":\"\"},{\"name\":\"bRegex_13\",\"value\":false},{\"name\":\"bSearchable_13\",\"value\":true},{\"name\":\"sSearch_14\",\"value\":\"\"},{\"name\":\"bRegex_14\",\"value\":false},{\"name\":\"bSearchable_14\",\"value\":true},{\"name\":\"sSearch_15\",\"value\":\"\"},{\"name\":\"bRegex_15\",\"value\":false},{\"name\":\"bSearchable_15\",\"value\":true},{\"name\":\"sSearch_16\",\"value\":\"\"},{\"name\":\"bRegex_16\",\"value\":false},{\"name\":\"bSearchable_16\",\"value\":true},{\"name\":\"sSearch_17\",\"value\":\"\"},{\"name\":\"bRegex_17\",\"value\":false},{\"name\":\"bSearchable_17\",\"value\":true},{\"name\":\"sSearch_18\",\"value\":\"\"},{\"name\":\"bRegex_18\",\"value\":false},{\"name\":\"bSearchable_18\",\"value\":true},{\"name\":\"sSearch_19\",\"value\":\"\"},{\"name\":\"bRegex_19\",\"value\":false},{\"name\":\"bSearchable_19\",\"value\":true},{\"name\":\"sSearch_20\",\"value\":\"\"},{\"name\":\"bRegex_20\",\"value\":false},{\"name\":\"bSearchable_20\",\"value\":true},{\"name\":\"sSearch_21\",\"value\":\"\"},{\"name\":\"bRegex_21\",\"value\":false},{\"name\":\"bSearchable_21\",\"value\":true},{\"name\":\"sSearch_22\",\"value\":\"\"},{\"name\":\"bRegex_22\",\"value\":false},{\"name\":\"bSearchable_22\",\"value\":true},{\"name\":\"sSearch_23\",\"value\":\"\"},{\"name\":\"bRegex_23\",\"value\":false},{\"name\":\"bSearchable_23\",\"value\":true},{\"name\":\"sSearch_24\",\"value\":\"\"},{\"name\":\"bRegex_24\",\"value\":false},{\"name\":\"bSearchable_24\",\"value\":true},{\"name\":\"iSortCol_0\",\"value\":0},{\"name\":\"sSortDir_0\",\"value\":\"asc\"},{\"name\":\"iSortingCols\",\"value\":1},{\"name\":\"bSortable_0\",\"value\":true},{\"name\":\"bSortable_1\",\"value\":true},{\"name\":\"bSortable_2\",\"value\":true},{\"name\":\"bSortable_3\",\"value\":true},{\"name\":\"bSortable_4\",\"value\":true},{\"name\":\"bSortable_5\",\"value\":true},{\"name\":\"bSortable_6\",\"value\":true},{\"name\":\"bSortable_7\",\"value\":true},{\"name\":\"bSortable_8\",\"value\":true},{\"name\":\"bSortable_9\",\"value\":true},{\"name\":\"bSortable_10\",\"value\":true},{\"name\":\"bSortable_11\",\"value\":true},{\"name\":\"bSortable_12\",\"value\":true},{\"name\":\"bSortable_13\",\"value\":true},{\"name\":\"bSortable_14\",\"value\":true},{\"name\":\"bSortable_15\",\"value\":true},{\"name\":\"bSortable_16\",\"value\":true},{\"name\":\"bSortable_17\",\"value\":true},{\"name\":\"bSortable_18\",\"value\":true},{\"name\":\"bSortable_19\",\"value\":true},{\"name\":\"bSortable_20\",\"value\":true},{\"name\":\"bSortable_21\",\"value\":true},{\"name\":\"bSortable_22\",\"value\":true},{\"name\":\"bSortable_23\",\"value\":true},{\"name\":\"bSortable_24\",\"value\":true}]",
                    url,
                    requestConfig);
//            System.out.println("sEcho = " + sEcho);
            if (response == null) {
                System.err.println("Request Error: " + url);
            } else {
                JSONObject jsonObject = JSONObject.parseObject(response);
                for (Object rowObj : jsonObject.getJSONArray("aaData")) {
                    processRow(sEcho, pageNo, (JSONArray) rowObj, cluster, rmIp, count++);
                }
            }
            sEcho += 2;
            iDisplayStart += 20;
        }
    }

    private static void processRow(Integer sEcho, Integer pageNo, JSONArray aaData, String cluster, String rmIp,
                                   int count) {
        String nodeState = aaData.getString(2);
        String ip = getIp(aaData);
        String nodeLabels = aaData.getString(0);
        JmxBean jmxBean = new JmxBean();
        Properties properties = new Properties();
        if("RUNNING".equals(nodeState)) {
            jmxBean = requestJmx(ip);
            properties = requestConf(ip);
        }
        insertToMysql(sEcho, pageNo, ip, nodeLabels, jmxBean,
                nodeState, cluster, rmIp, count,properties);
    }

    private static JmxBean requestJmx(String ip) {
        JmxBean jmxBean = new JmxBean();
        try {
            String url = "http://" + ip + ":8042/jmx";
//                    System.out.println("url = " + url);
            String s1 = SimpleHttpClient.sendRequest(httpClient, url, requestConfig);
//                    System.out.println("s1 = " + s1);
            JSONObject jsonObject1 = JSON.parseObject(s1);
            for (Object beans : jsonObject1.getJSONArray("beans")) {
                JSONObject bean = (JSONObject) beans;
                String name = bean.getString("name");
                if ("java.lang:type=Runtime".equals(name)) {
                    jmxBean.setStartTimeP(bean.getLongValue("StartTime"));
                    jmxBean.setProcessName(bean.getString("Name"));
                }
                if ("java.lang:type=OperatingSystem".equals(name)) {
                    jmxBean.setAvailableProcessorsP(bean.getIntValue("AvailableProcessors"));
                    jmxBean.setTotalPhysicalMemorySizeP(bean.getLongValue("TotalPhysicalMemorySize"));
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return jmxBean;
    }

    private static Properties requestConf(String ip) {
        Properties properties = new Properties();
        String url = "http://" + ip + ":8042/conf";
        try {
            properties = SimpleHttpClient.sendRequestProperties(httpClient, url, requestConfig);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return properties;
    }

    public static Connection connection;

    static {
        resetConnection();
    }

    private static void resetConnection() {
        try {
            connection = KongmingService.getConnection("nature");
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private static void insertToMysql(Integer sEcho, Integer pageNo, String ip, String nodeLabels, JmxBean jmxBean,
                                      String nodeState, String cluster, String rmIp, int count,Properties properties) {
        String startTime2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(jmxBean.getStartTimeP()));
        String duplicated = "false";
        try {
            KongmingService.executeUpdate("delete from t_nm where ip = ?", connection, ip);
            KongmingService.executeUpdate("insert into t_nm " +
                            "(ip,startTime,availableProcessors,sEcho,pageNo,startTime2," +
                            "processName,nodeLabels,node_state,cluster,rmIP," +
                            "totalPhysicalMemorySize,totalPhysicalMemory_mb," +
                            "yarn_nodemanager_resource_memory_mb," +
                            "yarn_nodemanager_resource_cpu_vcores," +
                            "yarn_nodemanager_resource_vmem_pmem_ratio" +
                            ") " +
                            "values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", connection,
                    ip, jmxBean.getStartTimeP(), jmxBean.getAvailableProcessorsP(), sEcho, pageNo,startTime2,
                    jmxBean.getProcessName(), nodeLabels,nodeState, cluster, rmIp,
                    jmxBean.getTotalPhysicalMemorySizeP(),
                    jmxBean.getTotalPhysicalMemorySizeP()/1024/1024,
                    Integer.parseInt(properties.getProperty("yarn.nodemanager.resource.memory-mb", "0")),
                    Integer.parseInt(properties.getProperty("yarn.nodemanager.resource.cpu-vcores", "0")),
                    Double.parseDouble(properties.getProperty("yarn.nodemanager.vmem-pmem-ratio", "0"))
                    );
        } catch (SQLException e) {
            if(e.getCause() instanceof EOFException){
                try {
                    connection.close();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
                resetConnection();
            } else if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
//                System.err.println(ip + "\t" + "has duplicated.");
                duplicated = "true";
            } else {
                e.printStackTrace();
            }
        }
        System.out.println(cluster + "\t" + count+ "\t" + duplicated + "\t" + ip + "\t" + jmxBean.getStartTimeP()
                + "\t" + startTime2 + "\t" + jmxBean.getAvailableProcessorsP()+ "\t");
    }

    private static String getIp(JSONArray aaData) {
        String ipEcode = aaData.getString(23);
        int i = ipEcode.indexOf(">");
        int i1 = ipEcode.indexOf("<", i + 1);
        return ipEcode.substring(i + 1, i1);
    }
}
