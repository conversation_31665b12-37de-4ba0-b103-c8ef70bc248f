package com.jd;

import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;

/**
 *
 * <AUTHOR>
 */
public class YamlUtil {
    public static <T> T loadYaml(String yamlName, Class<T> clazz){
        Yaml yaml = new Yaml();
        InputStream inputStream = YamlUtil.class
                .getClassLoader()
                .getResourceAsStream(yamlName);
        return yaml.loadAs(inputStream, clazz);
    }
}
