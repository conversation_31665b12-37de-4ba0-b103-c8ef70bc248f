package com.jd;

import com.jd.common.util.StringUtils;

import com.alibaba.fastjson.JSON;

import java.sql.*;
import java.util.Map;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class KongmingService {
    private static final Logger logger = Logger.getLogger(KongmingService.class.getName());

    public static int executeUpdate(String sql, Connection connection, Object... params) throws SQLException {
        try {
            PreparedStatement preparedStatement = connection.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                if (params[i] instanceof String) {
                    preparedStatement.setString(i + 1, (String) params[i]);
                } else if (params[i] instanceof Integer) {
                    preparedStatement.setInt(i + 1, (Integer) params[i]);
                } else if (params[i] instanceof Long) {
                    preparedStatement.setLong(i + 1, (Long) params[i]);
                }  else if (params[i] instanceof Double) {
                    preparedStatement.setDouble(i + 1, (Double) params[i]);
                } else if (params[i] instanceof SQLType) {
                    preparedStatement.setNull(i + 1, (int) params[i]);
                } else if (params[i] instanceof Timestamp) {
                    preparedStatement.setTimestamp(i + 1, (Timestamp) params[i]);
                } else {
                    throw new IllegalArgumentException("Unknown data type. Data Type: " + params[i]
                            + " sql: " + sql + " params: " + JSON.toJSONString(params));
                }
            }
            return preparedStatement.executeUpdate();
        } catch (SQLException e) {
//            new RuntimeException("sql = " + sql + " params: " + JSON.toJSONString(params) + " cause = "
//                    + e.getMessage(), e).printStackTrace();
            throw e;
        }
    }

    public static void closeConnection(Connection root) {
        if (root != null) {
            try {
                root.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    public static Connection getConnection(String dbName) throws SQLException {
        Connection root = null;
        MysqlBean mysqlBean = YamlUtil.loadYaml("db.yaml", MysqlBean.class);
        String defaultDb = StringUtils.defaultIfEmpty(dbName, mysqlBean.getDefaultDb());
        Map<String, String> dbAccount = mysqlBean.getDb().get(defaultDb);
        root = DriverManager.getConnection(dbAccount.get("url"), dbAccount.get("u"), dbAccount.get("p"));
        return root;
    }
}
