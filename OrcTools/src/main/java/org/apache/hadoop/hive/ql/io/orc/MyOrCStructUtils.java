package org.apache.hadoop.hive.ql.io.orc;

import org.apache.hadoop.hive.serde2.objectinspector.StructField;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;

import java.util.List;

/**
 * author: xiaowei79
 * description:由于OrcStructInspector无法在orc包以外访问，只能创建一个中间类来进行OrcStruct对象的相关创建
 * date: 2024-01-07 14:52:00
 */
public class MyOrCStructUtils {
    
    
    public static OrcStruct createOrcStruct(StructObjectInspector structObjectInspector, List<Object> columnValues){
        OrcStruct.OrcStructInspector orcStructInspector = (OrcStruct.OrcStructInspector) structObjectInspector;  // ObjectInspector对象类型强制转化
        OrcStruct orcStruct = (OrcStruct) orcStructInspector.create();                                    // 创建一个列数为0的OrcStruct对象
        List<StructField> allStructFieldRefs = orcStructInspector.getAllStructFieldRefs();                // 从StructObjectInspector获取所有子列的结构类型
        for(int i = 0 ; i < allStructFieldRefs.size() ; i++){
            StructField field = allStructFieldRefs.get(i);
            Object fieldValue = columnValues.get(i);
            orcStructInspector.setStructFieldData(orcStruct, field, fieldValue);                          // 为每个子列逐一赋值
        }
        return orcStruct;
    }
}
