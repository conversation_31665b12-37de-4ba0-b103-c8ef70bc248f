package com.jd.bigdata.mapreddemo.origin;


import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hive.ql.io.orc.*;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.typeinfo.TypeInfo;
import org.apache.hadoop.hive.serde2.typeinfo.TypeInfoUtils;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * author: xiaowei79
 * description: 使用原生orc来写入ORC文件
 * date: 2023-10-08 15:08:00
 */
public class OrcFileWriterMain {
    /**
     * 创建对象检测器
     * @return
     */
    private static StructObjectInspector createInspector(){
        TypeInfo typeInfo = TypeInfoUtils.getTypeInfoFromTypeString("struct<id:int,name:string,age:int,address:string>");
        StructObjectInspector structObjectInspector = (StructObjectInspector) OrcStruct.createObjectInspector(typeInfo);  // 创建OrcStructInspector对象实例,但是OrcStructInspector的访问的不是public,只能指向器符类StructObjectInspector
        // ObjectInspector objectInspector = TypeInfoUtils.getStandardWritableObjectInspectorFromTypeInfo(typeInfo);
        System.out.println("oi type:" + structObjectInspector.getTypeName());
        System.out.println("oi category:" + structObjectInspector.getCategory().toString());
        return structObjectInspector;
    }
    
    public static void main(String[] args) throws IOException {
        StructObjectInspector inspector = createInspector();
        Configuration conf = new Configuration();
        OrcFile.WriterOptions writerOptions = OrcFile.writerOptions(conf)
                .rowIndexStride(1000)                     // 设置rowGroup的行数
                .stripeSize(2 * 1024 * 1024)          // stripe 大小2Mb
                .compress(CompressionKind.SNAPPY)       // 设置压缩格式
                .inspector(inspector);                // 设置对象类型检查器
        Path path = new Path("file:///Users/<USER>/studymaterial/orc_study/origin_orc/00000_1");   // 设置输出ORC文件输出路径
        Writer writer = OrcFile.createWriter(path, writerOptions);
        
        
        for(int i = 0 ; i< 2000; i++){                // 1000行 = 736K
            List<Object> elem = new ArrayList<Object>();
            elem.add(new IntWritable(i));
            elem.add(new Text("xiaowei"+ i));
            elem.add(new IntWritable(i));
            elem.add(new Text("北京" + i));
            OrcStruct orcStruct = MyOrCStructUtils.createOrcStruct(inspector, elem);
            writer.addRow(orcStruct);
        }
        writer.close();
    }
   
}
