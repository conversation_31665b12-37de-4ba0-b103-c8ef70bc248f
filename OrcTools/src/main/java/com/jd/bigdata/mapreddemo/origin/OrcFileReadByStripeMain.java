package com.jd.bigdata.mapreddemo.origin;


import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hive.ql.io.orc.*;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * author: xiaowei79
 * description: 使用原生orc来读取ORC文件
 * date: 2023-10-08 15:08:00
 */
public class OrcFileReadByStripeMain {
    private static ObjectMapper objectMapper = new ObjectMapper();
   
    
    public static void main(String[] args) throws IOException {
        Configuration conf = new Configuration();
        conf.set("hive.exec.orc.default.compress", "ZLIB");
        conf.set("hive.exec.orc.default.compress", "SNAPPY");
        OrcFile.ReaderOptions readerOptions = OrcFile.readerOptions(conf);
        Path path = new Path("file:///Users/<USER>/studymaterial/orc_study/failed_orc_2/001165_1");   // 设置输出ORC文件输入路径
        Reader reader = OrcFile.createReader(path, readerOptions);

        
        List<OrcProto.Type> types = reader.getTypes();
        boolean[] include = new boolean[types.size()];
        Arrays.fill(include, true);
        

        List<StripeInformation> stripes = reader.getStripes();
        for(int i = 0 ; i < stripes.size() ;  i++){
            StripeInformation stripe = stripes.get(i);
            System.out.println(String.format("Stripe: %s %s", i, stripe));
            
            OrcStruct previous = null;
            RecordReader recordReader = reader.rows(stripe.getOffset(), stripe.getLength(), include);
            int lines = 0;
            try {
                while (recordReader.hasNext()){
                    previous = (OrcStruct) recordReader.next(previous);
                    lines ++ ;
                }
            } catch (Exception e) {
                System.out.println(String.format("Stripe: %s, 读取时发生了异常", i));
                e.printStackTrace();
                throw new RuntimeException(e);
            }
            System.out.println(String.format("==== stripe: %s,  lines: %s ", i, lines));
            
            if(lines - stripe.getNumberOfRows() != 0){
                System.out.println(String.format("Stripe: %s, 读取时行数对不上", i));
                throw new RuntimeException(String.format("Stripe: %s, 读取时行数对不上", i));
            }

            System.out.println();
            System.out.println();

        }
        

//        Metadata metadata = reader.getMetadata();
//        ObjectInspector objectInspector = reader.getObjectInspector();
//        System.out.println(objectMapper.writeValueAsString(metadata));
//        System.out.println(objectInspector.getTypeName());
        
//        Object previous = null;
//        boolean[] include = new boolean[61];
//        Arrays.fill(include, true);
        

        // Stripe: offset: 3 data: 14020600 rows: 110000 tail: 1553 index: 14759
        // Stripe: offset: 669068065 data: 337346 rows: 2500 tail: 1436 index: 2448
        // 共 5134999 行
        
        // Stripe: offset: 669409295 data: 336848 rows: 2500 tail: 1423 index: 2422  (有问题的stripe)
        // 共 2500 行
        
        // Stripe: offset: 669749988 data: 334197 rows: 2500 tail: 1614 index: 2441
        // Stripe: offset: 1823887163 data: 138377 rows: 1026 tail: 1377 index: 2401 
        // 共 8683579 行
        
        // 总共：5134999 + 2500 + 8683579 = 13821078 行
        
        
        //  3, 669409292
        // 669749988, 1154279330
        
        /*RecordReader recordReader = reader.rows(669749988, 1154279330, include);
        int i = 0;
        try {
            while (recordReader.hasNext()){
                previous = recordReader.next(previous);
                i ++ ;
                if(i % 500000 == 0){
                    System.out.println(previous);
                    System.out.println(i);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("==== i "+ i);*/
    }
   
}
