package com.jd.bigdata.mapreddemo.origin;


import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hive.ql.io.orc.*;
import org.apache.hadoop.hive.serde2.objectinspector.StructObjectInspector;
import org.apache.hadoop.hive.serde2.typeinfo.TypeInfo;
import org.apache.hadoop.hive.serde2.typeinfo.TypeInfoUtils;

import java.io.IOException;
import java.util.Arrays;

/**
 * author: xiaowei79
 * description: 使用原生orc来写入ORC文件
 * date: 2023-10-08 15:08:00
 */
public class OrcFileWriterMain2 {
    /**
     * 创建对象检测器
     * @return
     */
    private static StructObjectInspector createInspector(){
        TypeInfo typeInfo = TypeInfoUtils.getTypeInfoFromTypeString("struct<_col0:string,_col1:string,_col2:string,_col3:string,_col4:string,_col5:bigint,_col6:string,_col7:string,_col8:string,_col9:string,_col10:string,_col11:string,_col12:string,_col13:double,_col14:int,_col15:string,_col16:string,_col17:double,_col18:string,_col19:double,_col20:string,_col21:string,_col22:string,_col23:string,_col24:string,_col25:string,_col26:string,_col27:string,_col28:string,_col29:string,_col30:string,_col31:string,_col32:string,_col33:string,_col34:string,_col35:string,_col36:string,_col37:string,_col38:string,_col39:string,_col40:string,_col41:string,_col42:string,_col43:string,_col44:string,_col45:string,_col46:string,_col47:string,_col48:string,_col49:string,_col50:string,_col51:string,_col52:string,_col53:string,_col54:string,_col55:string,_col56:string,_col57:string,_col58:string,_col59:string>");
        StructObjectInspector structObjectInspector = (StructObjectInspector) OrcStruct.createObjectInspector(typeInfo);  // 创建OrcStructInspector对象实例,但是OrcStructInspector的访问的不是public,只能指向器符类StructObjectInspector
        // ObjectInspector objectInspector = TypeInfoUtils.getStandardWritableObjectInspectorFromTypeInfo(typeInfo);
        System.out.println("oi type:" + structObjectInspector.getTypeName());
        System.out.println("oi category:" + structObjectInspector.getCategory().toString());
        return structObjectInspector;
    }
    
    public static void main(String[] args) throws IOException {
        StructObjectInspector inspector = createInspector();
        Configuration conf = new Configuration();
        OrcFile.WriterOptions writerOptions = OrcFile.writerOptions(conf)
                .compress(CompressionKind.SNAPPY)       // 设置压缩格式
                .inspector(inspector);                // 设置对象类型检查器
        Path outputPath = new Path("file:///Users/<USER>/studymaterial/orc_study/failed_orc_output/00000_1");   // 设置输出ORC文件输出路径
        Writer writer = OrcFile.createWriter(outputPath, writerOptions);


        OrcFile.ReaderOptions readerOptions = OrcFile.readerOptions(conf);
        Path inputPath = new Path("file:///Users/<USER>/studymaterial/orc_study/failed_orc_2/001165_1");   // 设置输出ORC文件输入路径
        Reader reader = OrcFile.createReader(inputPath, readerOptions);

        
        boolean[] include = new boolean[61];
        Arrays.fill(include, true);
        OrcStruct data = null;
        int line = 0;
        
        // 前面stripe
        RecordReader recordReader = reader.rows(3, 669409292, include);
        while (recordReader.hasNext()){
            data = (OrcStruct) recordReader.next(data);
            writer.addRow(data);
            
            line ++ ;
            if(line % 100000 == 0){
                System.out.println(line);
            }
        }
        System.out.println("==== line1: "+ line); // ==== line1: 5134999
        
        
        // 后面stripe
        int line2 = 0;
        Reader reader2 = OrcFile.createReader(inputPath, readerOptions);
        RecordReader recordReader2 = reader2.rows(669749988, 1154279330, include);

        OrcStruct data2 = null;
        while (recordReader2.hasNext()){
            data2 = (OrcStruct) recordReader2.next(data2);
            writer.addRow(data2);
            line2 ++ ;
            if(line2 % 100000 == 0){
                System.out.println(line2);
            }
        }
        
        System.out.println("=== line2: "+line2);
        writer.close();
    }
   
}
