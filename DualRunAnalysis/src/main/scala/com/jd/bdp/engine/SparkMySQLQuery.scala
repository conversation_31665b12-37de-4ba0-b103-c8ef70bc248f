package com.jd.bdp.engine

import org.apache.spark.sql.functions.{col, countDistinct}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{Row, SparkSession}
import java.util
import java.util.Properties

object SparkMySQLQuery {
  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("MySQL Custom Query Example")
      .master("local[*]")
      .getOrCreate()

    Class.forName("com.mysql.jdbc.Driver")

    val errorMessagesMap: Map[String, String] = StrConstant.errorMessagesMap

    val resultDF = spark.read.jdbc(StrConstant.jdbcUrl,
      s"(${StrConstant.sql2}) AS tmp", StrConstant.connectionProperties)

    resultDF.show(10)

    val newSchema = StructType(resultDF.schema.fields :+
      <PERSON>ruct<PERSON>ield("run_2_4_msg", StringType, nullable = true) :+
      <PERSON>ru<PERSON><PERSON><PERSON>("run_3_4_msg", StringType, nullable = true) :+
      Struct<PERSON>ield("check_msg", StringType, nullable = true))

    val nrdd = resultDF.rdd.mapPartitions { partition =>
      val jedis = JimdbClientWithApi.initCluster(null)
      try {
        partition.map { row =>
          val run_2_4 = row.getAs[String]("run_2_4")
          val run_3_4 = row.getAs[String]("run_3_4")
          val data_check = row.getAs[String]("data_check")
          val run24logid = row.getAs[Long]("run_2_4_logid")
          val run34logid = row.getAs[Long]("run_3_4_logid")
          val check_logid = row.getAs[String]("run_check_logid").toLong
          val dualRunId = row.getAs[Integer]("dual_run_id")

          def getErrorMsg(status: String, logid: Long): String = {
            val run24Msgs: util.List[String] = new util.ArrayList[String]
            if (status == "fail") {
              val hash = jedis.hGetAll(s"${dualRunId}_${logid}")
              if (hash != null && !hash.isEmpty) {
                import scala.collection.JavaConversions._
                for (entry <- errorMessagesMap.entrySet) {
                  if (hash.get(entry.getKey) != null) run24Msgs.add(entry.getValue)
                }
              }
            }
            var msg: String = String.join(",", run24Msgs)
            if (run24Msgs.isEmpty) msg = "unknow"
            msg
          }
          Row.fromSeq(row.toSeq :+ getErrorMsg(run_2_4, run24logid)
            :+ getErrorMsg(run_3_4, run34logid) :+ getErrorMsg(data_check, check_logid))
        }
      } finally {
        // TODO shutdown jedis client
      }
    }

    val msgDf = spark.createDataFrame(nrdd, newSchema)

    msgDf.show(10)

    msgDf.filter("run_2_4 = 'success'")
      .groupBy("run_2_4","run_3_4")
      .agg(countDistinct("run_3_4_logid"))
      .show()
    val result1DF = msgDf.filter("run_2_4 = 'success'")
      .filter("run_3_4 = 'fail'")
      .groupBy("run_2_4","run_3_4","run_3_4_msg")
      .agg(countDistinct("run_3_4_logid").alias("count_distinct_logid"))
    result1DF.show()



    spark.stop()
  }
}