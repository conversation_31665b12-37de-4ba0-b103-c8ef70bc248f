package com.jd.bdp.engine

import org.apache.spark.sql.SparkSession

import java.net.HttpURLConnection
import org.apache.spark.sql.functions._
import java.net.URL
import scala.io.Source
import org.apache.log4j.{Logger, Level}

object SubmitDualRunApp {
  val logger: Logger = Logger.getLogger(SubmitDualRunApp.getClass)

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("Submit DualRun App")
      .master("local[*]")
      .getOrCreate()
    import spark.implicits._

    val limitRecord: Int = args.headOption.map(arg => try arg.toInt catch { case _: NumberFormatException => 1 }).getOrElse(1)
    logger.info(s"limit record: $limitRecord")

    val resultDF = spark.read.jdbc(StrConstant.jdbcUrl,
      s"(${StrConstant.unexecutedTasks}) AS tmp", StrConstant.connectionProperties)
    resultDF.show(limitRecord)

    val originTaskids = resultDF.groupBy("origin_buffalo_id").agg(count("task_def_id") alias ("count_dual_run_id")).count()
    logger.info(s"${resultDF.count} dual-run tasks not started, related to ${originTaskids} original tasks.")

    val addHttpResponse = udf((dualRunTaskId: String) => fetchFromHttp(dualRunTaskId))
    val resultDF1 = resultDF.limit(limitRecord).withColumn("invoke_run", addHttpResponse($"task_def_id"))

    resultDF1.show(limitRecord, false)
    spark.stop()
  }

  def fetchFromHttp(dualRunTaskId: String): String = {
    val str = "http://troubleshooting.jd.com/buffaloDoubleRun?responseType=json&taskid=" + dualRunTaskId
    val connection = new URL(str).openConnection().asInstanceOf[HttpURLConnection]
    logger.info(str)
    connection.setRequestMethod("GET")
    try {
      val response = Source.fromInputStream(connection.getInputStream).mkString
      logger.info(response)
      response
    } finally {
      connection.disconnect();
    }
  }

}
