package com.jd.bdp.engine

import java.io.{File}

import com.jd.jss.JingdongStorageService
import com.jd.jss.domain.Bucket

class OssUtils(jss: JingdongStorageService) {
  def getBuckets(): Seq[Bucket] = {
    import scala.collection.JavaConverters._
    jss.listBucket().asScala.toList
  }

  def uploadFile(bucketName: String, ossDir: String, localFilePath: String): Unit = {
    val file = new File(localFilePath)

    jss.bucket(bucketName).`object`(getValieDir(ossDir) + file.getName).entity(file).put()
  }

  def deleteFile(bucketName: String, ossDir: String, ossFileNames: Seq[String]): Unit = {
    val dir = getValieDir(ossDir)
    ossFileNames.foreach(fileName => jss.bucket(bucketName).`object`(dir + fileName).delete())
  }


  private def getValieDir(dir: String): String = {
    if (dir.endsWith("/")) dir else dir + "/"
  }
}

object OssUtils {
  val accessKey = "9IwSbgaxVScf6KDk"
  val secretKey = "AoCgtd9nrDw7TXp22gg5ilukxwBOlFV0rv7kUIbv"

  def apply(jss: JingdongStorageService): OssUtils = new OssUtils(jss)

  def main(args: Array[String]): Unit = {
    val jss = new JingdongStorageService(accessKey, secretKey)
    val bucket = "moneta"

    apply(jss).uploadFile(bucket, "wuguoxiao", "/Users/<USER>/Desktop/double_run_scripts/2863_2003683800_1_3_4.sql")
    val destPath = "/tmp/lvfulong_test2.txt"
//    apply(jss).downloadFile(bucket, "lvfulong/","lvfulong_test.txt", destPath)
//    apply(jss).deleteFile(bucket,"lvfulong/",Seq("lvfulong_test.txt"))
    jss.destroy()
  }
}
