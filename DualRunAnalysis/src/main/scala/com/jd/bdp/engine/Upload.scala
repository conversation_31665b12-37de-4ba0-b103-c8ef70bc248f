package com.jd.bdp.engine

import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession

object Upload {
  val logger: Logger = Logger.getLogger(this.getClass)
  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName(" App")
      .enableHiveSupport()
      .getOrCreate()

    import java.io.File
    val dir = new File("/home/<USER>/dual-run/dual-run/double_run_scripts/")
    val files = dir.listFiles.filter(_.isFile).filter(!_.getName.startsWith(".")).map(_.getAbsolutePath)
    val rdd = spark.sparkContext.parallelize(files)
    rdd.count()

    rdd.mapPartitions(partition => {
      val accessKey = "9IwSbgaxVScf6KDk"
      val secretKey = "AoCgtd9nrDw7TXp22gg5ilukxwBOlFV0rv7kUIbv"
      val jss = new com.jd.jss.JingdongStorageService(accessKey, secretKey)
      val ossUtils = new com.jd.bdp.engine.OssUtils(jss)
      val bucket = "moneta"
      var i = 0
      partition.map(row => {
        ossUtils.uploadFile(bucket, "wgx2", row)
        i+=1
        if(i % 1000 == 0){
          println(s"${Thread.currentThread().getName} -> ${i}")
        }
      })
    }).count()

    spark.stop()
  }
}
