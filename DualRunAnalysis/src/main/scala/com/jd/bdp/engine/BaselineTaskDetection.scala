package com.jd.bdp.engine

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

import com.jd.bdp.engine.StrConstant._
import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

/**
 * 1 运行环境：
export JDHXXXXX_CLUSTER_NAME=cairne;
export JDHXXXXX_USER=dd_edw;
export JDHXXXXX_QUEUE=bdp_jdw_dd_edw_bdp_spark;
export TEAM_USER=dd_edw_system_optimization;
source /software/servers/env/env.sh
 *
 * 2 提交方式：
 * spark-submit --conf spark.sql.externalCatalog.requireDbExists.enabled=false --class com.jd.bdp.engine.BaselineTaskDetection DualRunAnalysis-1.0-SNAPSHOT.jar
 *
 * 3 任务清单会导出成csv并保存至 hdfs://ns17/tmp/ 目录下（默认）
 */
object BaselineTaskDetection {
  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("BaselineTaskDetection App")
      .enableHiveSupport()
      .getOrCreate()

    // 格式化日期时间
    val defaultPath = "hdfs://ns17/tmp/baseline_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
    val outputDir = if(args.length==0) defaultPath else args(0)

//    val outputDir="hdfs://ns17/tmp/baseline_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
    logger.info("Result in csv file at " + outputDir)
    spark.conf.set("spark.sql.shuffle.partitions", "400")

    val job = spark.sql(shuffleHeavyTasksSQL)

    // Task Level
    val taskLevelSQL =
      """
        |SELECT
        |	task_id,
        |	priority
        |FROM
        |	adm.adm_m99_task_detail_info_da
        |WHERE
        |	dt = '2024-09-30'
        |	AND datatype IN('BUFFALO4')
        |	AND is_delete = '0'
        |	AND status NOT IN('冻结', '已升级', '禁用', '过期')
        |	AND task_cate IN('工作流任务', '标准任务')
        |""".stripMargin
    val taskLevel = spark.sql(taskLevelSQL)
    val aggTask_allHt_level = job.join(taskLevel, job("task_id") === taskLevel("task_id"), "left").drop(taskLevel("task_id"))

    // 所属基线id
    val baselineSQL = "select task_id,concat_ws('|',collect_set(baseline_id)) as baseline from tmp.tmp_base_line_tasks group by task_id"
    val baseline = spark.sql(baselineSQL)
    val aggTask_allHt_level_online_level_p95_baseline = aggTask_allHt_level.join(baseline,
      aggTask_allHt_level("task_id") === baseline("task_id"), "inner").drop(baseline("task_id"))

    aggTask_allHt_level_online_level_p95_baseline.repartition(1).write.format("csv").option("header", "true").save(outputDir)

    spark.close()
  }
}
