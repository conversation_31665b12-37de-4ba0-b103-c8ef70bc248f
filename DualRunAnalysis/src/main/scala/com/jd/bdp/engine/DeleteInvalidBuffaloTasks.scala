package com.jd.bdp.engine

import com.jd.bdp.engine.StrConstant.{connectionProperties, dualRunTasksSQL, jdbcUrl}
import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession

object DeleteInvalidBuffaloTasks {
  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.shuffle.targetPostShuffleInputSize", "41943040")
      .config("spark.dynamicAllocation.initialExecutors", "400")
      .config("spark.dynamicAllocation.minExecutors", "200")
      .config("spark.dynamicAllocation.maxExecutors", "2000")
      .appName("V34UpgradeAssessment App")
      .enableHiveSupport()
      .getOrCreate()

//    val dualRunTasks = spark.read.jdbc(jdbcUrl, s"(${dualRunTasksSQL}) AS tmp", connectionProperties)


    spark.close()
  }
}
