package com.jd.bdp.engine

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 *
 */
object DateTimeConverter {
  def convert(dateTimeString :String): String = {
    val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val localDateTime = LocalDateTime.parse(dateTimeString, dateTimeFormatter)
    val localDate = localDateTime.toLocalDate
    val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    localDate.format(dateFormatter)
  }
}
