package com.jd.bdp.engine

//import org.apache.hudi.DataSourceReadOptions.{BEGIN_INSTANTTIME, END_INSTANTTIME, QUERY_TYPE, QUERY_TYPE_INCREMENTAL_OPT_VAL}
import org.apache.spark.broadcast.Broadcast
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.{broadcast, col, lit, when, coalesce}
import org.apache.spark.storage.StorageLevel

import java.time.format.DateTimeFormatter
import java.time.{LocalDateTime, ZoneId}
import java.util.concurrent.{Executors, TimeUnit}

/*
export DATALAKE_HOME=/software/servers/10k/dd_edw/datalake
export SPARK_HOME=${SPARK_HOME}_3.0
spark-shell --master yarn --jars 'hdfs://ns22027/user/dd_edw/udf/hudi-spark3.0-bundle_2.12-JD.0.13.1-0925_v1.jar' --conf 'spark.serializer=org.apache.spark.serializer.KryoSerializer' --conf 'spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension' --conf 'spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar' --conf 'spark.dynamicAllocation.maxExecutors=3000' --conf 'spark.executor.memory=40g' --conf 'spark.sql.shuffle.partitions=3000' --conf 'spark.executor.cores=2' --conf 'spark.task.cpus=2' --conf 'spark.driver.memory=30g' --conf 'spark.driver.cores=10' --conf 'spark.driver.maxResultSize = 10G'

export DATALAKE_HOME=/software/servers/10k/dd_edw/datalake
export SPARK_HOME=${SPARK_HOME}_3.0
spark-shell --master yarn --jars 'hdfs://ns22027/user/dd_edw/udf/hudi-spark3.0-bundle_2.12-JD.0.13.1-0925_v1.jar','hdfs://ns1/user/dd_edw/product/PartialUpdateAvroEarliestTimeMultiTsPayload.jar' --conf 'spark.serializer=org.apache.spark.serializer.KryoSerializer' --conf 'spark.sql.extensions=org.apache.spark.sql.hudi.HoodieSparkSessionExtension' --conf 'spark.kryo.registrator=org.apache.spark.HoodieSparkKryoRegistrar' --conf 'spark.dynamicAllocation.maxExecutors=3000' --conf 'spark.executor.memory=40g' --conf 'spark.sql.shuffle.partitions=3000' --conf 'spark.executor.cores=2' --conf 'spark.task.cpus=2' --conf 'spark.driver.memory=30g' --conf 'spark.driver.cores=10' --conf 'spark.driver.maxResultSize = 10G'

import scala.collection.JavaConversions._
import org.apache.spark.sql.SaveMode._
import org.apache.hudi.DataSourceReadOptions._
import org.apache.hudi.DataSourceWriteOptions._
import org.apache.hudi.common.table.HoodieTableConfig._
import org.apache.hudi.config.HoodieWriteConfig._
import org.apache.hudi.keygen.constant.KeyGeneratorOptions._
import org.apache.hudi.common.model.HoodieRecord
import spark.implicits._
import org.apache.spark.storage.StorageLevel
import org.apache.spark.sql.functions.{broadcast, col, count, explode}
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.ZoneId
import java.time.Instant
import org.apache.spark.sql.SaveMode.Append
import java.util.concurrent.{ArrayBlockingQueue, Callable, ExecutorService, Executors, Future, RejectedExecutionException, ThreadPoolExecutor, TimeUnit}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}
import ExecutionContext.Implicits.global
import org.apache.spark.broadcast.Broadcast
*/
object pop_odm {

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder().appName(s"skuAct").
      enableHiveSupport().
      getOrCreate()
    import spark.implicits._

    println("入参个数："+args.length)

    var stream_hour_start_timestamp = "20241009080000000"
    var stream_hour_end_timestamp =   "20241009100000000"
    var batch_hour_start_timestamp = "20241009080000000"
    var batch_hour_end_timestamp =   "20241009100000000"
    // spark.sql("create table if not exists tmp.spark_team_temp_odm_jdr_sch_d03_sku_pop_act_hudi_da_ronghe_v2 like odm_dev.odm_jdr_sch_d03_sku_pop_act_hudi_da_ronghe_v2")
    var tal_db = "tmp"
    var tal_table = "spark_team_temp_odm_jdr_sch_d03_sku_pop_act_hudi_da_ronghe_v2"
    // if (args.length>=6) {
    //   stream_hour_start_timestamp = args(0)
    //   stream_hour_end_timestamp = args(1)
    //   batch_hour_start_timestamp = args(2)
    //   batch_hour_end_timestamp = args(3)
    //   tal_db = args(4)
    //   tal_table = args(5)
    // }
    println("stream_hour_start_timestamp："+stream_hour_start_timestamp)
    println("stream_hour_end_timestamp:"+stream_hour_end_timestamp)
    println("batch_hour_start_timestamp:"+batch_hour_start_timestamp)
    println("batch_hour_end_timestamp:"+batch_hour_end_timestamp)
    println("tal_db:"+tal_db)
    println("tal_table:"+tal_table)

    spark.conf.set("hoodie.combine.before.upsert", "false")
    spark.conf.set("hoodie.memory.dfs.buffer.max.size","104857600")

    //Product全部字段的sql片段
    var product_column = Seq(
      "_hoodie_commit_time",
      "product_id",
      "product_name",
      "brand_id",
      "place_of_production",
      "shelf_life",
      "yn",
      "product_status",
      "item_third_cate_cd",
      "item_ext_third_cate_cd",
      "is_sn",
      "created",
      "pay_first",
      "day_limited_sales",
      "length",
      "width",
      "height",
      "calc_volume",
      "weight",
      "unit",
      "package_type",
      "col_type",
      "vender_id",
      "shop_id",
      "platform",
      "spu_id",
      "product_tag",
      "unlimit_cid",
      "unlimit_cid as item_last_cate_cd",
      "coalesce(unlimit_cid,item_third_cate_cd) as unlimit_cid_new",
      "purchaser_erp_acct",
      "saler_erp_acct",
      "remark",
      "message",
      "model",
      "item_num",
      "shop_category",
      "delivery",
      "timeliness_id",
      "free_goods_flag",
      "refill_freight",
      "features",
      "buyer_post",
      "saler",
      "GREATEST(ts1,ts2) as ts_product"
    )

    var product_snap_view = spark.read.format("hudi").
      option("as.of.instant", stream_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/odm.db/odm_jdr_sch_d03_sku_pop_product_pk_hudi_act").
      filter("ts1 is not null").
      selectExpr(product_column: _*)

    var pop_vender_vender_snap_view = spark.read.format("hudi").
      option("as.of.instant", stream_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/fdm.db/fdm_pop_vender_vender_chain_hudi").
      selectExpr("_hoodie_commit_time","company_id", "company_name", "shop_id", "shop_name", "status", "concat('0600', lpad(status, 2, '0')) as join_key", "ts as ts_ven").
      filter("dp = 'ACTIVE'")

    var odm_cate_oper_rule_column = Seq(
      "_hoodie_commit_time",
      "vender_id",
      "category_id",
      "brand_id",
      "case when lower(trim(operator_erp))='' then null else lower(trim(operator_erp)) end as operator_erp",
      "cx_dept_id as cx_dept_id",
      "dept_id as dept_id",
      "dept_name as dept_name",
      "ts as ts_vender_cate",
      "rule_type",
      "template_id"
    )

    var odm_cate_oper_rule_snap_view = spark.read.format("hudi").
      option("as.of.instant", batch_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/odm.db/odm_jdr_sch_d03_sku_pop_cate_oper_rule_dept_fk_hudi_da").
      selectExpr(odm_cate_oper_rule_column: _*)

    var brand_column = Seq(
      "_hoodie_commit_time",
      "brand_id",
      "name as barndname_full",
      "en_name as barndname_en",
      "cn_name as barndname_cn",
      "ts as ts_forest_brands",
      "main_brand_id as main_brand_code",
      "brand_group_name as main_barndname_full"
    )

    var forest_brands_snap_view = spark.read.format("hudi").
      option("as.of.instant", stream_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/fdm.db/fdm_forest_brands_chain_hudi").
      selectExpr(brand_column: _*).filter("dp='ACTIVE'")

    var cate_column = Seq(
      "_hoodie_commit_time",
      "lvl_1_cate_id   as  item_first_cate_cd",
      "lvl_1_cate_name as  item_first_cate_name",
      "lvl_2_cate_id   as  item_second_cate_cd",
      "lvl_2_cate_name as  item_second_cate_name",
      "lvl_3_cate_id   as  item_third_cate_cd",
      "lvl_3_cate_name as  item_third_cate_name",
      "cate_id",
      "cate_name"
    )

    var cate_snap_view = spark.read.format("hudi").
      option("as.of.instant", batch_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/dim.db/dim_jdr_sch_item_category_hudi").
      selectExpr(cate_column: _*)

    var vender_type = Seq(
      "_hoodie_commit_time",
      "vender_id",
      "if(SUBSTR(reverse(conv(is_cod, 10, 2)), 14, 1) = 1,'1','0') as global_buy_vender_flag",
      "no_cate_oper_flag",
      "lower(trim(cx_dept_id)) as no_cate_cx_dept_id",
      "lower(trim(vender_cx_erp)) as no_cate_cx_erp",
      "dept_id as no_cate_dept_id",
      "dept_name as no_cate_dept_name",
      "major_item_first_cate_id",
      "pop_vender_own_afs_flag"
    )

    var pop_vender_type_snap_view = spark.read.format("hudi").
      option("as.of.instant", batch_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/odm.db/odm_jdr_sch_d03_sku_pop_vender_type_dept_hudi_da").
      selectExpr(vender_type: _*)

    //TODO 判断是否可以去重后广播
    var peking_admin_snap_view = spark.read.format("hudi").
      option("as.of.instant", stream_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/fdm.db/fdm_peking_admin_chain_hudi").
      selectExpr("_hoodie_commit_time","trim(lower(op_code)) AS erp_acct", "op_name AS purchaser_name").
      filter("dp='ACTIVE' and yn='1'")

    //TODO 改成广播获取
    var dept_column = Seq(
      "_hoodie_commit_time",
      "dept_id",
      "dept_name",
      "concat_ws('_',COALESCE(bu_id,''),COALESCE(dept_id_1,''),COALESCE(dept_id_2,''),COALESCE(dept_id_3,''),COALESCE(dept_id_4,''),COALESCE(dept_id_5,'')) as dept_id_all",
      "concat_ws('_', COALESCE(bu_name,''),COALESCE(dept_name_1,''),COALESCE(dept_name_2,''),COALESCE(dept_name_3,''),COALESCE(dept_name_4,''),COALESCE(dept_name_5,'')) as dept_name_all"
    )

    var dim_cmo_dept_snap_view = spark.read.format("hudi").
      option("as.of.instant", batch_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/dim.db/dim_jdr_sch_cmo_dept_hudi").
      selectExpr(dept_column: _*)

    var dim_df = spark.sql("SELECT agmt_status_cd,agmt_status_desc FROM dim.dim_d99_agmt_status_cd WHERE agmt_status_type_cd = '06'")

    var ven_ext_snap = pop_vender_vender_snap_view.as("pop_vender_vender_snap_view").join(broadcast(dim_df.as("dim_df")), $"pop_vender_vender_snap_view.join_key" === $"dim_df.agmt_status_cd")

    // 刨除大表，验证性能
    var vender_cate_3_snap = odm_cate_oper_rule_snap_view.filter("rule_type = '3' and template_id = '1'").as("vender_cate_3").
      withColumnRenamed("operator_erp","vender_cate_operator_erp_3").
      withColumnRenamed("cx_dept_id","vender_cate_cx_dept_id_3").
      withColumnRenamed("dept_id","vender_cate_dept_id_3").
      withColumnRenamed("dept_name","vender_cate_dept_name_3").
      drop(col("rule_type")).drop(col("template_id"))
    var vender_cate_2_snap = odm_cate_oper_rule_snap_view.filter("rule_type = '2' and template_id = '1'").as("vender_cate_2").
      withColumnRenamed("operator_erp","vender_cate_operator_erp_2").
      withColumnRenamed("cx_dept_id","vender_cate_cx_dept_id_2").
      withColumnRenamed("dept_id","vender_cate_dept_id_2").
      withColumnRenamed("dept_name","vender_cate_dept_name_2")
    var vender_cate_1_snap = odm_cate_oper_rule_snap_view.filter("rule_type = '1' and template_id = '1'").as("vender_cate_1").
      withColumnRenamed("operator_erp","vender_cate_operator_erp_1").
      withColumnRenamed("cx_dept_id","vender_cate_cx_dept_id_1").
      withColumnRenamed("dept_id","vender_cate_dept_id_1").
      withColumnRenamed("dept_name","vender_cate_dept_name_1")
    var global_buy_cate_oper_3_snap = odm_cate_oper_rule_snap_view.filter("rule_type = '3' and template_id = '2'").as("global_buy_cate_oper_3").
      withColumnRenamed("operator_erp","global_buy_operator_erp_3").
      withColumnRenamed("cx_dept_id","global_buy_cx_dept_id_3").
      withColumnRenamed("dept_id","global_buy_dept_id_3").
      withColumnRenamed("dept_name","global_buy_dept_name_3")
    var global_buy_cate_oper_2_snap = odm_cate_oper_rule_snap_view.filter("rule_type = '2' and template_id = '2'").as("global_buy_cate_oper_2").
      withColumnRenamed("operator_erp","global_buy_operator_erp_2").
      withColumnRenamed("cx_dept_id","global_buy_cx_dept_id_2").
      withColumnRenamed("dept_id","global_buy_dept_id_2").
      withColumnRenamed("dept_name","global_buy_dept_name_2")
    var global_buy_cate_oper_1_snap = odm_cate_oper_rule_snap_view.filter("rule_type = '1' and template_id = '2'").as("global_buy_cate_oper_1").
      withColumnRenamed("operator_erp","global_buy_operator_erp_1").
      withColumnRenamed("cx_dept_id","global_buy_cx_dept_id_1").
      withColumnRenamed("dept_id","global_buy_dept_id_1").
      withColumnRenamed("dept_name","global_buy_dept_name_1")

    var fin_dim = spark.sql("""SELECT dim_item_fin_third_cate_id, dim_item_fin_zero_cate_name dim_item_fin_first_cate_name FROM dim.dim_item_fin_cate_getmap""")

    //全量Product与全量维表Join  vender_cate_3无法broadcast
    var productFkJoinDF = product_snap_view.as("product").
      join(vender_cate_3_snap.as("vender_cate_3"), $"product.vender_id" === $"vender_cate_3.vender_id" && $"product.unlimit_cid_new" === $"vender_cate_3.category_id", "left_outer").
      join(broadcast(pop_vender_type_snap_view.as("pop_vender_type")),$"product.vender_id" === $"pop_vender_type.vender_id", "left_outer").
      join(broadcast(vender_cate_2_snap.as("vender_cate_2")), $"product.brand_id" === $"vender_cate_2.brand_id" && $"product.unlimit_cid_new" === $"vender_cate_2.category_id", "left_outer").
      join(broadcast(vender_cate_1_snap.as("vender_cate_1")), $"product.unlimit_cid_new" === $"vender_cate_1.category_id", "left_outer").
      join(broadcast(global_buy_cate_oper_3_snap.as("buy_cate_3")), $"product.vender_id" === $"buy_cate_3.vender_id" && $"product.unlimit_cid_new" === $"buy_cate_3.category_id", "left_outer").
      join(broadcast(global_buy_cate_oper_2_snap.as("buy_cate_2")), $"product.brand_id" === $"buy_cate_2.brand_id" && $"product.unlimit_cid_new" === $"buy_cate_2.category_id", "left_outer").
      join(broadcast(global_buy_cate_oper_1_snap.as("buy_cate_1")), $"product.unlimit_cid_new" === $"buy_cate_1.category_id", "left_outer").
      join(broadcast(forest_brands_snap_view.as("forest_brands")), $"product.brand_id" === $"forest_brands.brand_id", "left_outer").
      join(broadcast(cate_snap_view.as("cate")), $"product.item_last_cate_cd" === $"cate.cate_id", "left_outer").
      join(broadcast(cate_snap_view.as("ext_cate")), $"product.item_ext_third_cate_cd" === $"ext_cate.cate_id", "left_outer").
      join(broadcast(peking_admin_snap_view.as("purchaser")), $"product.purchaser_erp_acct" === $"purchaser.erp_acct", "left_outer").
      join(broadcast(peking_admin_snap_view.as("saler")), $"product.saler_erp_acct" === $"saler.erp_acct", "left_outer").
      join(broadcast(dim_cmo_dept_snap_view.as("dim_cmo_dept")), $"product.buyer_post" === $"dim_cmo_dept.dept_id", "left_outer").
      join(broadcast(ven_ext_snap.as("ven_ext")), $"product.shop_id" === $"ven_ext.shop_id", "left_outer").
      join(broadcast(fin_dim.as("fin_dim")), $"cate.item_third_cate_cd" === $"fin_dim.dim_item_fin_third_cate_id", "left_outer").
      withColumn("jingxizhiying_flag", when(col("product.features").like("%gxlysplx:3%"), "1").otherwise("0")).
      withColumn("jingxizhiying_cx_dept_id", col("product.buyer_post")).
      withColumn("no_cate_oper_flag", when(col("pop_vender_type.vender_id").isNull, lit(null)).otherwise(coalesce(col("pop_vender_type.no_cate_oper_flag"), lit("0")))).
      withColumn("global_buy_vender_flag", when(col("pop_vender_type.vender_id").isNull, lit(null)).otherwise(coalesce(col("pop_vender_type.global_buy_vender_flag"), lit("0")))).
      withColumn("jingxizhiying_cx_erp", col("product.saler")).
      withColumn("jingxizhiying_dept_id", col("dim_cmo_dept.dept_id_all")).
      withColumn("jingxizhiying_dept_name", col("dim_cmo_dept.dept_name_all")).
      persist(StorageLevel.MEMORY_AND_DISK_SER)
    //    productFkJoinDF.count()

    var incr_filter =
      s"""
         |(product._hoodie_commit_time >= '$batch_hour_start_timestamp' and product._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(pop_vender_type._hoodie_commit_time >= '$batch_hour_start_timestamp' and pop_vender_type._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(vender_cate_3._hoodie_commit_time >= '$batch_hour_start_timestamp' and vender_cate_3._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(vender_cate_2._hoodie_commit_time >= '$batch_hour_start_timestamp' and vender_cate_2._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(vender_cate_1._hoodie_commit_time >= '$batch_hour_start_timestamp' and vender_cate_1._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(buy_cate_3._hoodie_commit_time >= '$batch_hour_start_timestamp' and buy_cate_3._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(buy_cate_2._hoodie_commit_time >= '$batch_hour_start_timestamp' and buy_cate_2._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(buy_cate_1._hoodie_commit_time >= '$batch_hour_start_timestamp' and buy_cate_1._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(forest_brands._hoodie_commit_time >= '$batch_hour_start_timestamp' and forest_brands._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(cate._hoodie_commit_time >= '$batch_hour_start_timestamp' and cate._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(ext_cate._hoodie_commit_time >= '$batch_hour_start_timestamp' and ext_cate._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(purchaser._hoodie_commit_time >= '$batch_hour_start_timestamp' and purchaser._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(saler._hoodie_commit_time >= '$batch_hour_start_timestamp' and saler._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(dim_cmo_dept._hoodie_commit_time >= '$batch_hour_start_timestamp' and dim_cmo_dept._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |(ven_ext._hoodie_commit_time >= '$batch_hour_start_timestamp' and ven_ext._hoodie_commit_time <='$batch_hour_end_timestamp') OR
         |""".stripMargin

    var productJoinColumn = Seq(
      "product.product_id as item_id",
      "product.product_name as item_name",
      "product.brand_id as brand_code",
      "forest_brands.barndname_en as barndname_en",
      "forest_brands.barndname_cn as barndname_cn",
      "forest_brands.barndname_full as barndname_full",
      "forest_brands.main_brand_code as main_brand_code",
      "forest_brands.main_barndname_full as main_barndname_full",
      "product.place_of_production as item_origin",
      "product.shelf_life as qgp",
      "product.yn as item_valid_flag",
      "product.product_status as product_status",
      "cate.item_first_cate_cd as item_first_cate_cd",
      "cate.item_first_cate_name as item_first_cate_name",
      "cate.item_second_cate_cd as item_second_cate_cd",
      "cate.item_second_cate_name as item_second_cate_name",
      "cate.item_third_cate_cd as item_third_cate_cd",
      "cate.item_third_cate_name as item_third_cate_name",
      "product.item_last_cate_cd",
      "cate.cate_name as item_last_cate_name",
      "ext_cate.item_first_cate_cd as item_ext_first_cate_cd",
      "ext_cate.item_first_cate_name as item_ext_first_cate_name",
      "ext_cate.item_second_cate_cd as item_ext_second_cate_cd",
      "ext_cate.item_second_cate_name as item_ext_second_cate_name",
      "product.item_ext_third_cate_cd",
      "ext_cate.item_third_cate_name as item_ext_third_cate_name",
      "coalesce(fin_dim.dim_item_fin_first_cate_name,'未知') as dim_item_fin_cate_name",
      "product.is_sn as seq_num_mgmt_flag",
      "coalesce(product.created,'') as shelves_tm",
      "coalesce(substring(product.created,1,10),'') as shelves_dt",
      "product.pay_first as support_cash_on_deliver_flag",
      "product.day_limited_sales as sale_qtty_lim",
      "product.length as len",
      "product.width",
      "product.height",
      "cast(product.length as bigint)*cast(product.width as bigint)*cast(product.height as bigint) as calc_volume",
      "product.weight as wt",
      "product.unit as pac_propt",
      "product.package_type as pac_spec",
      "product.col_type as pop_coop_mode_cd",
      "ven_ext.company_id as pop_vender_corp_id",
      "product.vender_id as pop_vender_id",
      "ven_ext.company_name as pop_vender_name",
      "product.shop_id",
      "ven_ext.shop_name as shop_name",
      "ven_ext.status as pop_vender_status_cd",
      "ven_ext.agmt_status_desc as pop_vender_status_name",
      "LOWER(TRIM(pop_vender_type.no_cate_cx_erp)) as pop_operator_erp_acct",
      "pop_vender_type.no_cate_cx_dept_id as work_post_cd",
      "pop_vender_type.major_item_first_cate_id as major_item_first_cate_id",
      "pop_vender_type.pop_vender_own_afs_flag as pop_vender_own_afs_flag",
      "product.platform as sale_plat_cd",
      "product.spu_id",
      "product.product_tag as item_tag",
      "product.message as slogan",
      "product.model as item_type",
      "product.item_num as item_num",
      "product.remark as item_desc",
      "product.shop_category as shop_item_cate_cd",
      "product.delivery",
      "product.purchaser_erp_acct",
      "purchaser.purchaser_name as purchaser_name",
      "product.saler_erp_acct",
      "saler.purchaser_name as sale_staf_name",
      "product.timeliness_id as item_timeliness_id",
      "product.refill_freight as refill_freight",
      "case when jingxizhiying_flag='1' and COALESCE(LOWER(TRIM(jingxizhiying_cx_dept_id)),'') <> '' then jingxizhiying_cx_dept_id when no_cate_oper_flag = '1' then no_cate_cx_dept_id when global_buy_vender_flag = '1' then COALESCE(if(global_buy_cx_dept_id_3='0',null,global_buy_cx_dept_id_3), if(global_buy_cx_dept_id_2='0',null,global_buy_cx_dept_id_2), if(global_buy_cx_dept_id_1='0',null,global_buy_cx_dept_id_1)) else COALESCE(if(vender_cate_cx_dept_id_3='0',null,vender_cate_cx_dept_id_3), if(vender_cate_cx_dept_id_2='0',null,vender_cate_cx_dept_id_2), if(vender_cate_cx_dept_id_1='0',null,vender_cate_cx_dept_id_1)) end as oper_post_id",
      "case when jingxizhiying_flag='1' and COALESCE(LOWER(TRIM(jingxizhiying_cx_erp)),'') <> '' then jingxizhiying_cx_erp when no_cate_oper_flag = '1' then no_cate_cx_erp when global_buy_vender_flag = '1' then COALESCE(if(global_buy_operator_erp_3='0',null,global_buy_operator_erp_3), if(global_buy_operator_erp_2='0',null,global_buy_operator_erp_2), if(global_buy_operator_erp_1='0',null,global_buy_operator_erp_1))else COALESCE(if(vender_cate_operator_erp_3='0',null,vender_cate_operator_erp_3), if(vender_cate_operator_erp_2='0',null,vender_cate_operator_erp_2), if(vender_cate_operator_erp_1='0',null,vender_cate_operator_erp_1)) end as oper_erp_acct",
      "case when jingxizhiying_flag='1' and COALESCE(LOWER(TRIM(jingxizhiying_dept_id)),'') <> '' then jingxizhiying_dept_id when no_cate_oper_flag = '1' then no_cate_dept_id when global_buy_vender_flag = '1' then COALESCE(if(global_buy_operator_erp_3='0',null,global_buy_dept_id_3), if(global_buy_operator_erp_2='0',null,global_buy_dept_id_2), if(global_buy_operator_erp_1='0',null,global_buy_dept_id_1)) else COALESCE(if(vender_cate_operator_erp_3='0',null,vender_cate_dept_id_3), if(vender_cate_operator_erp_2='0',null,vender_cate_dept_id_2), if(vender_cate_operator_erp_1='0',null,vender_cate_dept_id_1)) end as oper_dept_id",
      "case when jingxizhiying_flag='1' and COALESCE(LOWER(TRIM(jingxizhiying_dept_name)),'') <> '' then jingxizhiying_dept_name when no_cate_oper_flag = '1' then no_cate_dept_name when global_buy_vender_flag = '1' then COALESCE(if(global_buy_operator_erp_3='0',null,global_buy_dept_name_3), if(global_buy_operator_erp_2='0',null,global_buy_dept_name_2), if(global_buy_operator_erp_1='0',null,global_buy_dept_name_1)) else COALESCE(if(vender_cate_operator_erp_3='0',null,vender_cate_dept_name_3), if(vender_cate_operator_erp_2='0',null,vender_cate_dept_name_2), if(vender_cate_operator_erp_1='0',null,vender_cate_dept_name_1)) end as oper_dept_name",
      "no_cate_dept_id as dept_id",
      "no_cate_dept_name as dept_name"
    )

    var productFkJoinIncrDF = productFkJoinDF.filter(incr_filter).selectExpr(productJoinColumn: _*)

    var sku_column = Seq(
      "_hoodie_commit_time",
      "sku_id",
      "main_sku_id",
      "product_id",
      "sku_name",
      "yn",
      "sku_status_cd",
      "product_code",
      "upc_code",
      "on_shelves_time",
      "off_shelves_time",
      "size",
      "size_note",
      "size_sequence",
      "color",
      "consumption_vat",
      "output_vat",
      "input_vat",
      "sku_mark",
      "sku_tag",
      "box_spec",
      "created_tm",
      "features2",
      "sku_features",
      "sku_dim_spu_id",
      "sku_timeliness_id",
      "spec",
      "sale_atts",
      "first_on_shelves_time",
      "first_into_wh_tm",
      "jd_prc",
      "mkt_prc"
    )


    // 读取sku全量，并持久化到内存中
    var skuSnapDF = spark.read.format("hudi").
      option("as.of.instant", batch_hour_end_timestamp).
      load("hdfs://ns22034/user/dd_edw/odm.db/odm_jdr_sch_d03_sku_pop_sku_pk_merge_act").
      selectExpr(sku_column: _*).
      persist(StorageLevel.MEMORY_AND_DISK_SER)
    //    skuSnapDF.count()

    var skuActColumn = Seq(
      "sku.sku_id as item_sku_id",
      "sku.main_sku_id",
      "product.item_id",
      "sku.sku_name",
      "product.item_name",
      "product.item_desc",
      "product.brand_code",
      "product.barndname_en",
      "product.barndname_cn",
      "product.barndname_full",
      "product.item_origin",
      "product.qgp",
      "sku.yn as sku_valid_flag",
      "product.item_valid_flag",
      "concat('30',lpad(sku.sku_status_cd,2,'0')) as sku_status_cd",
      "concat('30',lpad(product.product_status,2,'0')) as item_status_cd",
      "product.item_first_cate_cd",
      "product.item_first_cate_name",
      "product.item_second_cate_cd",
      "product.item_second_cate_name",
      "product.item_third_cate_cd",
      "product.item_third_cate_name",
      "product.item_ext_first_cate_cd",
      "product.item_ext_first_cate_name",
      "product.item_ext_second_cate_cd",
      "product.item_ext_second_cate_name",
      "product.item_ext_third_cate_cd",
      "product.item_ext_third_cate_name",
      "product.dim_item_fin_cate_name",
      "product.seq_num_mgmt_flag",
      "sku.product_code as item_seq_num",
      "sku.upc_code as upc",
      "product.shelves_tm",
      "product.shelves_dt",
      "sku.on_shelves_time as otc_tm",
      "sku.off_shelves_time as utc_tm",
      "product.support_cash_on_deliver_flag",
      "product.slogan",
      "product.sale_qtty_lim",
      "sku.first_into_wh_tm",
      "product.item_type",
      "sku.size",
      "sku.size_note as size_rem",
      "sku.size_sequence as size_seq",
      "product.len",
      "product.width",
      "product.height",
      "product.calc_volume",
      "product.wt",
      "sku.color as colour",
      "product.pac_propt",
      "product.pac_spec",
      "'0' as free_goods_flag",
      "product.pop_coop_mode_cd",
      "product.pop_vender_corp_id",
      "product.pop_vender_id",
      "product.pop_vender_name",
      "product.shop_id",
      "product.shop_name",
      "product.pop_vender_status_cd",
      "product.pop_vender_status_name",
      "product.pop_operator_erp_acct",
      "product.work_post_cd",
      "product.major_item_first_cate_id",
      "product.pop_vender_own_afs_flag",
      "'0' as discount_rate",
      "product.sale_plat_cd",
      "sku.consumption_vat",
      "sku.output_vat",
      "sku.input_vat",
      "product.item_num",
      "product.spu_id",
      "sku.sku_mark",
      "sku.sku_tag",
      "product.item_tag",
      "sku.first_on_shelves_time as first_into_otc_tm",
      "sku.box_spec",
      "product.item_last_cate_cd",
      "product.item_last_cate_name",
      "null as cate_oper_erp_acct",
      "null as cate_oper_name",
      "null as cate_oper_dept_id",
      "sku.created_tm",
      "product.shop_item_cate_cd",
      "product.delivery",
      "product.oper_post_id",
      "product.oper_erp_acct",
      "'0' as oper_erp_name",
      "sku.features2",
      "product.main_brand_code",
      "product.main_barndname_full",
      //      "CASE WHEN getDataTypeBySkuId(sku.sku_id)>=1 and getDataTypeBySkuId(sku.sku_id)<=9 and product.pop_coop_mode_cd = 1 THEN coalesce(substring(lpad(bin(cast(product.refill_freight as bigint)),2,0),-2,1),0) ELSE 99 END as refill_freight",
      "'' as refill_freight",
      "product.purchaser_erp_acct",
      "product.purchaser_name",
      "product.saler_erp_acct",
      "product.sale_staf_name",
      "sku.sku_features",
      "sku.sku_dim_spu_id",
      "sku.spec",
      "sku.sale_atts",
      "sku.sku_timeliness_id",
      "product.item_timeliness_id",
      "sku.jd_prc",
      "sku.mkt_prc",
      "product.oper_dept_id",
      "product.oper_dept_name",
      "product.dept_id",
      "product.dept_name"
    )

    var skuActIncrDF = skuSnapDF.as("sku").join(productFkJoinIncrDF.as("product"),$"sku.product_id"===$"product.item_id","inner").selectExpr(skuActColumn: _*)
    var skuIncrDF=skuSnapDF.filter(s"_hoodie_commit_time >= '$batch_hour_start_timestamp' and _hoodie_commit_time <='$batch_hour_end_timestamp'")
    var skuActSnapDF = skuIncrDF.as("sku").join(productFkJoinDF.selectExpr(productJoinColumn: _*).as("product"),$"sku.product_id"===$"product.item_id","inner").selectExpr(skuActColumn: _*)

    // 给ts字段赋值
    var formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
    var ts = LocalDateTime.parse(batch_hour_start_timestamp.substring(0, 14), formatter).atZone(ZoneId.systemDefault()).toInstant.toEpochMilli

    var finalDf=skuActIncrDF.union(skuActSnapDF).withColumn("ts", lit(ts)).withColumn("tp", lit("ACTIVE"))

    //    finalDf.count()


    finalDf.createOrReplaceTempView("insert_view")

    finalDf.show()
    var insert_sql=
      s"""
         |insert into $tal_db.$tal_table
         |select * from insert_view
         |""".stripMargin
    spark.sql(insert_sql)

  }
}