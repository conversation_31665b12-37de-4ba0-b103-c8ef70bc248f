package com.jd.bdp.engine

import org.apache.spark.sql.SparkSession

import java.net.HttpURLConnection
import org.apache.spark.sql.functions._
import java.net.URL
import scala.io.Source
import org.apache.log4j.{Logger, Level}


object KillLongTailTasks {
  val logger: Logger = Logger.getLogger(KillLongTailTasks.getClass)

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("KillLongTailTasks App")
      .master("local[*]")
      .getOrCreate()

    val limitRecord: Int = args.headOption.map(arg => try arg.toInt catch { case _: NumberFormatException => 1 }).getOrElse(1)
    logger.info(s"limit record: $limitRecord")

    val resultDF = spark.read.jdbc(StrConstant.jdbcUrl,
      s"(${StrConstant.last}) AS tmp", StrConstant.connectionProperties)
      .filter("run_status not in (\"success\", \"fail\", \"queue\")")
      .filter("name like 'BUFFALO_%'")
      .filter("instance_type is not null")
      .orderBy(col("elapsedSecond").desc)
      .select("name", "dual_run_id", "inst_type", "log_id", "instance_id",
        "run_status", "elapsedSecond", "task_ins_id")

    resultDF.show(limitRecord)

    import spark.implicits._

    val addHttpResponse = udf((instanceId: String) => fetchFromHttp(instanceId))
    val resultDF1 = resultDF.limit(limitRecord).withColumn("kill_instance", addHttpResponse($"task_ins_id"))

    resultDF1.show(limitRecord, false)
    spark.stop()
  }

  def fetchFromHttp(instanceId: String): String = {
    val str = "http://troubleshooting.jd.com/buffaloDoubleRun?responseType=json&instanceId=" + instanceId
    val connection = new URL(str).openConnection().asInstanceOf[HttpURLConnection]
    logger.info(str)
    connection.setRequestMethod("GET")
    try {
      val response = Source.fromInputStream(connection.getInputStream).mkString
      logger.info(response)
      response
    } finally {
      connection.disconnect();
    }
  }
}
