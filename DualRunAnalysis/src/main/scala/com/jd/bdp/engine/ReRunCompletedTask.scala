package com.jd.bdp.engine

import java.net.{HttpURLConnection, URL}

import scala.io.Source

import org.apache.commons.lang3.StringUtils
import org.apache.log4j.Logger

import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.sql.functions._
import com.jd.bdp.engine.StrConstant.{buffaloInstanceSQL, connectionProperties, dualRunResultCheckSQL, dualRunTasksSQL, jdbcUrl, tempConnectionProperties, tempJdbcUrl, upgradedAndRollbackTasksSQL}
import com.fasterxml.jackson.databind.{JsonNode, ObjectMapper}
import com.fasterxml.jackson.module.scala.DefaultScalaModule

object ReRunCompletedTask {
  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("Submit DualRun App")
      .getOrCreate()

    val limitRecord: Int = args.headOption.map(arg => try arg.toInt catch {
      case _: NumberFormatException => 5
    }).getOrElse(5)
    logger.info(s"sleep time: $limitRecord")

    val upgradedAndRollbackTasks = spark.read.jdbc(tempJdbcUrl,
      s"(${upgradedAndRollbackTasksSQL}) AS tmp", tempConnectionProperties)
    val dualRunTasks = spark.read.jdbc(jdbcUrl,
      s"(${dualRunTasksSQL}) AS tmp", connectionProperties)
    val dualRunResultCheckTasks = spark.read.jdbc(tempJdbcUrl,
      s"(${dualRunResultCheckSQL}) AS tmp", tempConnectionProperties)
    val buffaloInstance = spark.read.jdbc(jdbcUrl,
      s"(${buffaloInstanceSQL}) AS tmp", connectionProperties)

    println(s"已上线的正式任务ID数(包含回滚): ${upgradedAndRollbackTasks.groupBy("origin_task_id").count().count()}")
    println(s"已双跑过的正式任务ID数(包含失败):${dualRunTasks.groupBy("origin_buffalo_id").count().count()}")
    println("已上线的正式任务状态（包含已升级和已回溯）")
    upgradedAndRollbackTasks.groupBy("status").agg(countDistinct("task_id").alias("cnt_origin_task_ids")).show

    val env = spark.sql("select * from fdm.fdm_spark_environmentinfo_di where dt=sysdate(-1) group by spark_sql_source, spark_app_type")
    env.filter("spark_sql_source != 'HiveTask'").groupBy("spark_app_type").count().show()
    env.filter(col("spark_sql_source") === "HiveTask").show

    val aa = spark.sql("select * from adm.adm_m99_task_detail_info_da where dt = '2024-07-30' and is_delete = '0' and status not in ('冻结', '已升级', '禁用', '过期')")
    val bb = aa.filter("datatype = 'BUFFALO4'")
    val cc = bb.filter("task_cate IN('工作流任务', '标准任务')")
    cc.groupBy("engine_type").agg(countDistinct("task_id")).show()

    bb.groupBy("task_cate").agg(countDistinct("task_id")).show()

    aa.groupBy("datatype").agg(countDistinct("task_id")).show()

    val thr_sql =
      """
        |SELECT
        |	m1.task_id,
        |	m1.task_name,
        |	m1.task_cate,
        |	m1.job_cycle,
        |	m2.dept_2_name,
        |	m1.priority,
        |	m1.engine_type,
        |	m3.job_num,
        |	m1.exec_long,
        |	m1.req_vcore
        |FROM
        |	(
        |		SELECT
        |			task_id,
        |			task_name,
        |			task_cate,
        |			job_cycle,
        |			bgname,
        |			dept_1_name,
        |			dept_2_name,
        |			task_owner,
        |			task_owner_name,
        |			priority,
        |			engine_type,
        |			product_account,
        |			ROUND(AVG(IF(exec_long['1days'] > 0, exec_long['1days'], exec_long['latest'])), 2) AS exec_long,
        |			ROUND(AVG(IF(req_vcore['1days'] > 0, req_vcore['1days'], req_vcore['latest'])), 2) AS req_vcore
        |		FROM
        |			adm.adm_m99_task_detail_info_da
        |		WHERE
        |			dt >= '2024-07-16'
        |			AND dt <= '2024-07-30'
        |			AND datatype IN('BUFFALO4')
        |			AND is_delete = '0'
        |			AND status NOT IN('冻结', '已升级', '禁用', '过期')
        |			AND task_cate IN('工作流任务', '标准任务')
        |		GROUP BY
        |			task_id,
        |			task_name,
        |			task_cate,
        |			job_cycle,
        |			bgname,
        |			dept_1_name,
        |			dept_2_name,
        |			task_owner,
        |			task_owner_name,
        |			priority,
        |			engine_type,
        |			product_account
        |	)
        |	m1
        |LEFT JOIN
        |	(
        |		SELECT
        |			account_code,
        |			account_principal_one_dept_2_name AS dept_2_name
        |		FROM
        |			dim.dim_jdr_plat_platdata_jsm_account_info_da
        |		WHERE
        |			dt = '2024-07-30'
        |		GROUP BY
        |			account_code,
        |			account_principal_one_dept_2_name
        |	)
        |	m2
        |ON
        |	m1.product_account = m2.account_code
        |LEFT JOIN
        |	(
        |		SELECT
        |			task_id,
        |			job_type,
        |			ROUND(COUNT(1) / 15, 2) AS job_num
        |		FROM
        |			gdm.gdm_m99_job_run_log_da
        |		WHERE
        |			dt >= '2024-07-16'
        |			AND dt < '2024-07-31'
        |		GROUP BY
        |			task_id,
        |			job_type
        |	)
        |	m3
        |ON
        |	m1.task_id = m3.task_id
        |""".stripMargin
        // m2.dept_2_name IN('数据资产与应用部', '智能平台部', '集团数据计算平台部')
    val all = spark.sql(thr_sql)
    all.show()

    all.filter("dept_2_name IN ('数据资产与应用部', '智能平台部', '集团数据计算平台部')").select("task_id").distinct().count()
    all.filter("dept_2_name NOT IN ('数据资产与应用部', '智能平台部', '集团数据计算平台部')").select("task_id").distinct().count()
    all.groupBy("dept_2_name").agg(round(sum("job_num"),2), round(sum("req_vcore"),2)).show
    all.groupBy("dept_2_name", "priority").agg(round(sum("job_num"),2)).show

    // Job 表中 Spark 任务
    val new_sql =
      """
        |SELECT
        |	bee_source,
        |	task_id,
        |	account_principal_one_bgname ,
        |	account_principal_one_dept_1_name ,
        |	account_principal_one_dept_2_name ,
        |	AVG(total_long_s) AS avg_exe_long,
        |	ROUND(SUM(req_vcore_c_s) / 15, 2) AS req_vcore_c_s,
        |	ROUND(SUM(total_long_s) / 15, 2) AS sum_exe_long,
        |	ROUND(COUNT(1) / 15, 2) AS job_num
        |FROM
        |	(
        |		SELECT
        |			r1.task_id,
        |			r1.bee_source,
        |			r1.job_id,
        |			r1.req_vcore_c_s,
        |			r1.exe_long_s,
        |			r1.total_long_s,
        |			r1.job_user,
        |			r2.account_principal,
        |			r2.account_principal_one,
        |			r2.account_principal_one_bgcode,
        |			r2.account_principal_one_bgname,
        |			r2.account_principal_one_dept_1_code,
        |			r2.account_principal_one_dept_1_name,
        |			r2.account_principal_one_dept_2_code,
        |			r2.account_principal_one_dept_2_name,
        |			r2.account_principal_one_dept_3_code,
        |			r2.account_principal_one_dept_3_name,
        |			r1.dt
        |		FROM
        |			(
        |				SELECT
        |					bee_source,
        |					job_id,
        |					job_user,
        |					job_name,
        |					task_id,
        |					bee_businessid,
        |					req_mem_mbs_s,
        |					used_mem_mbs_s,
        |					req_vcore_c_s,
        |					exe_long_s,
        |					total_long_s,
        |					dt
        |				FROM
        |					gdm.gdm_m99_job_run_log_da
        |				WHERE
        |					dt >= '2024-07-16'
        |					AND dt <= '2024-07-30'
        |					AND job_type = 'spark'
        |					AND finishedstatus IN('FINISHED', 'SUCCEEDED')
        |			)
        |			r1
        |		LEFT OUTER JOIN
        |			(
        |				SELECT
        |					account_code,
        |					account_principal,
        |					account_principal_one,
        |					account_principal_one_bgcode,
        |					account_principal_one_bgname,
        |					account_principal_one_dept_1_code,
        |					account_principal_one_dept_1_name,
        |					account_principal_one_dept_2_code,
        |					account_principal_one_dept_2_name,
        |					account_principal_one_dept_3_code,
        |					account_principal_one_dept_3_name
        |				FROM
        |					dim.dim_jdr_plat_platdata_jsm_account_info_da
        |				WHERE
        |					dt = '2024-09-30'
        |				GROUP BY
        |					account_code,
        |					account_principal,
        |					account_principal_one,
        |					account_principal_one_bgcode,
        |					account_principal_one_bgname,
        |					account_principal_one_dept_1_code,
        |					account_principal_one_dept_1_name,
        |					account_principal_one_dept_2_code,
        |					account_principal_one_dept_2_name,
        |					account_principal_one_dept_3_code,
        |					account_principal_one_dept_3_name
        |			)
        |			r2
        |		ON
        |			r1.job_user = r2.account_code
        |	)
        |WHERE
        |	dt >= '2024-07-16'
        |	AND dt <= '2024-07-30'
        |GROUP BY
        |	bee_source,
        |	task_id,
        |	account_principal_one_bgname,
        |	account_principal_one_dept_1_name,
        |	account_principal_one_dept_2_name
        |""".stripMargin
    val job = spark.sql(new_sql)
    val aggTask = job.filter("account_principal_one_dept_2_name in ('数据资产与应用部', '智能平台部', '集团数据计算平台部')")

    val isAllHiveTaskSQL =
      """
        |SELECT
        |	m.task_id,
        |	CASE
        |		WHEN m.source_count = 1
        |			AND m.task_count = m.total_count
        |		THEN 'Y'
        |		ELSE 'N'
        |	END AS all_hivetask
        |FROM
        |	(
        |		SELECT
        |			buffaloEnvTaskDefId AS task_id,
        |			COUNT(DISTINCT spark_sql_source) AS source_count,
        |			COUNT(
        |				CASE
        |					WHEN spark_sql_source = 'HiveTask'
        |					THEN 1
        |				END) AS task_count,
        |			COUNT( *) AS total_count
        |		FROM
        |			fdm.fdm_spark_environmentinfo_di
        |		WHERE
        |			dt >= '2024-07-16'
        |			AND dt <= '2024-07-30'
        |		GROUP BY
        |			buffaloEnvTaskDefId
        |	)
        |	m
        |""".stripMargin
    val isAllHiveTask = spark.sql(isAllHiveTaskSQL)
    val aggTask_allHT = aggTask.join(isAllHiveTask, aggTask("task_id") === isAllHiveTask("task_id"), "left").drop(isAllHiveTask("task_id"))

    val taskLevelSQL =
      """
        |SELECT
        |	task_id,
        |	priority
        |FROM
        |	adm.adm_m99_task_detail_info_da
        |WHERE
        |	dt = '2024-09-30'
        |	AND datatype IN('BUFFALO4')
        |	AND is_delete = '0'
        |	AND status NOT IN('冻结', '已升级', '禁用', '过期')
        |	AND task_cate IN('工作流任务', '标准任务')
        |""".stripMargin
    val taskLevel = spark.sql(taskLevelSQL)
    val aggTask_allHt_level = aggTask_allHT.join(taskLevel, aggTask_allHT("task_id") === taskLevel("task_id"), "left").drop(taskLevel("task_id"))
    val upgradeDF = spark.sql("select * from tmp.lvfulong_task_with_status")
    val aggTask_allHt_level_online = aggTask_allHt_level.join(upgradeDF, aggTask_allHt_level("task_id") === upgradeDF("task_id"), "left").drop(upgradeDF("task_id"))
    val levelDF = spark.sql("select * from tmp.lvfulong_all_levels_task_table")

    val aggTask_allHt_level_online_level = aggTask_allHt_level_online.join(levelDF,
      aggTask_allHt_level("task_id") === levelDF("task_id"), "left").drop(levelDF("task_id"))
      .na.fill("1 执行计划待校验", Seq("status"))
    aggTask_allHt_level_online_level.repartition(1).write.format("csv").option("header", "true").save("hdfs://ns17/tmp/3_depart_5")



    // 回归池的任务清单
    // 共双跑了 25436 个任务，
    val dualRunedTask = dualRunTasks.select("origin_buffalo_id").distinct()
    // 已升级了 13964 个任务
    val upgradeTasks = upgradedAndRollbackTasks.groupBy("status", "origin_task_id").count().filter(col("status") === "UPGRADED")
    // 未升级 11477 个任务
    val unupgradedTasks = dualRunedTask.join(upgradeTasks, dualRunedTask("origin_buffalo_id") === upgradeTasks("origin_task_id"), "left").filter("origin_task_id is null")
    val unupgradeDualRunId = dualRunTasks.join(unupgradedTasks, dualRunTasks("origin_buffalo_id") === unupgradedTasks("origin_buffalo_id"), "inner").drop(unupgradedTasks("origin_buffalo_id"))
    unupgradeDualRunId.select("origin_buffalo_id").distinct().count()
    // 未升级 11477 个任务
    val unUpgradeOriginTasks = unupgradeDualRunId.groupBy("origin_buffalo_id").agg(concat_ws(",", collect_list("dual_run_id")).alias("dual_run_ids"))
    unUpgradeOriginTasks.repartition(1).write.format("csv").option("header", "true").save("hdfs://ns17/tmp/wgx/unUpgrade")

    val df3 = dualRunTasks.join(upgradedAndRollbackTasks, dualRunTasks("origin_buffalo_id") === upgradedAndRollbackTasks("origin_task_id"), "inner")
    df3.show(false)

    import org.apache.spark.sql.types._
    val newSchema = StructType(Array(
      StructField("dual_run_id", StringType, nullable = true),
      StructField("name", StringType, nullable = true),
      StructField("origin_buffalo_id", StringType, nullable = true),
      StructField("isDualRun", StringType, nullable = true),
      StructField("dualRunInstanceId", StringType, nullable = true),
      StructField("instanceTaskId", StringType, nullable = true)
    ))

    val nrdd = df3.rdd.mapPartitions { partition =>
      val jedis = com.jd.bdp.engine.JimdbClientWithApi.initCluster(null)
      val redisKey = "isDualRun1004"
      val redisInstanceKey = "dualRunInstance1004"
      try {
        partition.map { row =>
          val name = row.getAs[String]("name")
          val dualRunTaskId = row.getAs[Integer]("dual_run_id") + ""
          val origin_buffalo_id = row.getAs[String]("origin_buffalo_id")
          val isDualRun = StringUtils.defaultIfEmpty(jedis.hGet(redisKey, dualRunTaskId), "false")
          val dualRunInstance = StringUtils.defaultIfEmpty(jedis.hGet(redisInstanceKey, dualRunTaskId), "{}")
          val mapper = new ObjectMapper()
          mapper.registerModule(DefaultScalaModule)
          val rootNode: JsonNode = mapper.readTree(dualRunInstance)
          val instanceTaskId = rootNode.path("obj").path("instanceTaskId").asText()
          Row.fromSeq(Array(dualRunTaskId, name, origin_buffalo_id, isDualRun, dualRunInstance, instanceTaskId))
        }
      } finally {
        // TODO shutdown jedis client
      }
    }

    val nDf = spark.createDataFrame(nrdd, newSchema).drop(col("dualRunInstanceId"))

    println("未双跑的任务")
    nDf.filter("instanceTaskId = '' ").show(10,false)

    println("提交/未提交双跑的任务统计")
    nDf.groupBy("isDualRun").agg(countDistinct("dual_run_id").alias("cnt_dual_run_id")).show

    val successDF = dualRunResultCheckTasks.withColumn("isSuccess", when(col("compare_code") === 0 || col("compare_code") === 7, 0).otherwise(1))
      .groupBy("instance_id")
      .agg(sum("isSuccess").alias("result_check_all_success"),
        sum("before_file_count").alias("sum_before_file"),
        sum("after_file_count").alias("sum_after_file"),
        sum("before_file_storage").alias("sum_before_storage"),
        sum("after_file_storage").alias("sum_after_storage"))

    val check = nDf.join(successDF, nDf("instanceTaskId") === successDF("instance_id"), "left").drop(successDF("instance_id"))

    val buffalo2 = check.join(buffaloInstance, check("instanceTaskId") === buffaloInstance("task_ins_id"), "left").drop(buffaloInstance("task_ins_id"))
      .withColumn("run_status_int", when(col("run_status") === "success", 0).otherwise(1))

    buffalo2.show(false)

    // 实例运行情况（已创建实例的任务）
    buffalo2.groupBy("run_status").agg(countDistinct("dual_run_id")).show

    // 3.4 版本比 2.4 版本文件数多的任务
    buffalo2.withColumn("diff_file_cnt", col("sum_after_file") - col("sum_before_file")).orderBy(desc("diff_file_cnt")).show(false)

    // 所有对数成功的双跑任务，2.4/3.4 总文件
    buffalo2.filter(col("result_check_all_success") === 0)
      .agg(countDistinct("dual_run_id"),sum("sum_before_file"),
        sum("sum_after_file"), sum("sum_before_storage"), sum("sum_after_storage")).show

    // 对数成功失败的双跑任务汇总
    check.groupBy("result_check_all_success").agg(countDistinct("dual_run_id")).show

    // 正式任务汇总（双跑通过的任务）
    buffalo2.groupBy("origin_buffalo_id").agg(sum("run_status_int").alias("all_inst_success"))
      .groupBy("all_inst_success").agg(countDistinct("origin_buffalo_id")).show

    // 双跑任务运行情况汇总
    buffalo2.groupBy("run_status").agg(countDistinct("dual_run_id")).show()

    val toDualRun = nDf.filter("isDualRun = 'false'")
    toDualRun.show()

    def fetchFromHttp(dualRunTaskId: String): String = {
      val str = "http://troubleshooting.jd.com/buffaloDoubleRun?responseType=json&taskid=" + dualRunTaskId
      val connection = new URL(str).openConnection().asInstanceOf[HttpURLConnection]
      logger.info(str)
      connection.setRequestMethod("GET")
      try {
        val response = Source.fromInputStream(connection.getInputStream).mkString
        logger.info(s"response = ${response}")
        response
      } finally {
        connection.disconnect();
      }
    }

    def modifyNode(dualRunTaskId: String, taskNodeId: Integer): String = {
      val str = "http://troubleshooting.jd.com/buffaloDoubleRun?modifyRunNode=true&taskid=" + dualRunTaskId + "&taskNodeId=" + taskNodeId
      val connection = new URL(str).openConnection().asInstanceOf[HttpURLConnection]
      logger.info(str)
      connection.setRequestMethod("GET")
      try {
        val response = Source.fromInputStream(connection.getInputStream).mkString
        logger.info(s"modify node response = ${response}")
        response
      } finally {
        connection.disconnect();
      }
    }

    val jedis = com.jd.bdp.engine.JimdbClientWithApi.initCluster(null)
    val redisKey = "isDualRun1004"
    val redisInstanceKey = "dualRunInstance1004"

    var counter = 0
    toDualRun.collect().foreach(row => {
      val dualRunId = row.getAs[String]("dual_run_id")
      counter += 1
      if (counter % 2 == 0) {
        modifyNode(dualRunId, 1402)
      } else if (counter % 2 == 1) {
        modifyNode(dualRunId, 1302)
      }
      val response = fetchFromHttp(dualRunId)
      val setStatus = jedis.hSet(redisKey, dualRunId, "true")
      jedis.hSet(redisInstanceKey, dualRunId, response)
      println(s"dualRunId[$dualRunId] setStatus = ${setStatus}")
      Thread.sleep(limitRecord * 1000)
    })

    // 失败的任务重置为待双跑
//    buffalo2.filter("run_status = 'fail'").collect().foreach(row => {
//      val dualRunId = row.getAs[String]("dual_run_id")
//      val setStatus = jedis.hSet(redisKey, dualRunId, "false")
//      jedis.hDel(redisInstanceKey, dualRunId)
//      logger.info(s"Reset to wait for dual-run [$dualRunId] [${setStatus}]")
//    })


    spark.stop()
  }
}
