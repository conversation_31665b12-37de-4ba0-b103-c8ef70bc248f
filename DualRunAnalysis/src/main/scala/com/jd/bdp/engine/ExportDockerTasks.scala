package com.jd.bdp.engine

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession

object ExportDockerTasks {

  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("Submit DualRun App")
      .enableHiveSupport()
      .getOrCreate()
    val now = LocalDateTime.now()
    val formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
    // 格式化日期时间
    val defaultPath = "hdfs://ns17/tmp/last_7_docker_tasks_" + now.format(formatter)
    //    val outputDir="hdfs://ns17/tmp/" + formattedDateTime

    val outputDir = if (args.length == 0) defaultPath else args(0)
    logger.info("Result in csv file at " + outputDir)
    val new_sql =
      """
        |SELECT DISTINCT
        |	beeSource,
        |	buffaloEnvTaskDefId
        |FROM
        |	fdm.fdm_spark_environmentinfo_di
        |WHERE
        |	dt >= sysdate( - 30)
        |	AND appMasterEnv_docker_image != 'None'
        |""".stripMargin
    val job = spark.sql(new_sql)
    job.repartition(1).write.format("csv").option("header", "true").save(outputDir)
  }
}
