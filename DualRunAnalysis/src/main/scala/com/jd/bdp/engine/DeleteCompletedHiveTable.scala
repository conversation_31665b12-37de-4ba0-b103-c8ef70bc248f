package com.jd.bdp.engine

import org.apache.spark.sql.{SparkSession}
object DeleteCompletedHiveTable {

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("DeleteCompletedHiveTable App")
      .master("local[*]")
      .enableHiveSupport()
      .getOrCreate()

    import org.apache.spark.sql.{DataFrame, Row}
    import java.net.HttpURLConnection
    import org.apache.spark.sql.functions._
    import java.net.URL
    import scala.io.Source
    import org.apache.log4j.Logger
    val logger: Logger = Logger.getLogger(this.getClass)
    import java.time.LocalDate
    val startDate = LocalDate.parse("2024-09-17")
    val dateArray = (1 until 16).map(days => startDate.minusDays(days)).toArray
    import org.apache.spark.sql.types._
    val schema = StructType(List(
      StructField("dt", IntegerType, nullable = true),
      StructField("cnt_def_id", StringType, nullable = true)
    ))

    val sss = StructType(List(
      StructField("dt",StringType,true),
      StructField("beeSource", StringType, true),
      StructField("buffaloEnvTaskDefId", StringType, true),
      StructField("jdhxxxxxClusterName", StringType, true),
      StructField("jdhxxxxxUser", StringType, true),
      StructField("jdhxxxxxQueue", StringType, true),
      StructField("sparkUser", StringType, true),
      StructField("beeUser", StringType, true),
      StructField("spark_sql_outputTables", StringType, true),
      StructField("outputLen", IntegerType, true),
      StructField("outputTable", StringType, true)
    ))
    var explodeTable: DataFrame = spark.createDataFrame(
      spark.sparkContext.emptyRDD[Row], sss
    )

    var emptyDF: DataFrame = spark.createDataFrame(
      spark.sparkContext.emptyRDD[Row], schema
    )
    dateArray.foreach( curDate => {
      var df = spark.sql(
        s"""
          |SELECT
          |    a.dt,
          |    beeSource,
          |    buffaloEnvTaskDefId,
          |    jdhxxxxxClusterName,
          |    jdhxxxxxUser,
          |    jdhxxxxxQueue,
          |    b.sparkUser,
          |    beeUser,
          |    spark_sql_outputTables
          |FROM
          |    fdm.fdm_spark_environmentinfo_di AS a
          |INNER JOIN fdm.fdm_spark_appinfo_di AS b
          |ON
          |    a.appid = b.appid
          |    AND a.appattemptid = b.appattemptid
          |    AND a.dt = b.dt
          |WHERE
          |    a.dt = '$curDate'
          |    AND (spark_sql_outputTables LIKE 'wangriyu_test%' or spark_sql_outputTables LIKE 'zmy_test%')
          |    AND beeSource = 'SPARK_3_UPGRADE'
          |""".stripMargin)
      df = df.withColumn("outputLen", length(col("spark_sql_outputTables")))

      val cnt_tables = df.withColumn("outputTable",
          explode(split(trim(col("spark_sql_outputTables")), ",")))
      explodeTable = explodeTable.union(cnt_tables)

      cnt_tables.show(5)

      val cnt = df.groupBy("dt").agg(countDistinct("buffaloEnvTaskDefId").alias("cnt_def_id"))
      cnt.show
      emptyDF = emptyDF.union(cnt)
      logger.error(s"appid: ${df.count()}")

    })
    emptyDF.show
    explodeTable.write.format("orc").mode("overwrite").saveAsTable("tmp.spark_team_temp_explode_output_table")

    val ndf = spark.sql("select * from tmp.spark_team_temp_explode_output_table")
    ndf.groupBy("dt").agg(countDistinct(trim(col("outputTable")))).orderBy(col("dt").desc).show()

    ndf.groupBy().agg(countDistinct(trim(col("outputTable")))).show

    val resultDF = spark.read.jdbc(StrConstant.jdbcUrl,
      s"(${StrConstant.buffaloDualTask}) AS tmp", StrConstant.connectionProperties)
    resultDF.count()
    import spark.implicits._
    val join = resultDF.select("id","name","creator", "created").join(ndf, resultDF("id") === ndf("buffaloEnvTaskDefId"), "left")
//    join.filter($"spark_sql_outputTables".isNotNull).show(100)

    join.filter($"spark_sql_outputTables".isNotNull)

//
//    val resultDF1 = resultDF.limit(limitRecord).withColumn("invoke_run", addHttpResponse($"task_def_id"))
//
//    val originTaskids = resultDF.groupBy("origin_buffalo_id").agg(count("task_def_id") alias ("count_dual_run_id")).count()
//    logger.info(s"${resultDF.count} dual-run tasks not started, related to ${originTaskids} original tasks.")
//    resultDF1.show(limitRecord, false)
    spark.stop()
  }

}
