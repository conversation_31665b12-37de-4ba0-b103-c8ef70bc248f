package com.jd.bdp.engine

import java.net.URI
import java.sql.Timestamp
import java.time.LocalDateTime

import scala.util.Try

import org.apache.hadoop.conf.Configuration
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.log4j.Logger

import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.sql.types.{IntegerType, LongType, StringType, StructField, StructType, TimestampType}

object DualRunResultChecklist {
  val logger: Logger = Logger.getLogger(this.getClass)
  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("Submit DualRun Checklist App")
      .enableHiveSupport()
      .getOrCreate()

    if(args.length < 3) {
      println("Please provide two argument: <beforeTable> <afterTable> <exitcode>")
      System.exit(1)
    }

    val taskId = sys.env.get("BUFFALO_ENV_TASK_DEF_ID").flatMap(t => Try(t.toLong).toOption).getOrElse(0L)
    val instanceId = sys.env.get("BEE_BUSINESSID").flatMap(i => Try(i.toLong).toOption).getOrElse(0L)
    val actionId = sys.env.get("BUFFALO_ENV_ACTION_DEF_ID").flatMap(i => Try(i.toLong).toOption).getOrElse(0L)

    val beforeTable = args(0)
    val afterTable = args(1)
    val exitCode = args(2).toInt
    val compareMessage = args(3)

    import spark.implicits._

    def getTotalSizeAndFiles(hdfs: FileSystem, path: Path): (Long, Long) = {
      val summary = hdfs.getContentSummary(path)
      (summary.getLength, summary.getFileCount)
    }

    def getTotalSizeAndFilesByTable(hiveTableName: String) = {
      val tableLocation = spark.sql(s"describe formatted $hiveTableName").filter($"col_name" === "Location").select("data_type").collect()(0)(0).toString
      val hdfsUriPattern = """(hdfs://[^/]+)""".r
      val hdfsUri = hdfsUriPattern.findFirstIn(tableLocation).getOrElse("")
      val hdfs = FileSystem.get(new URI(hdfsUri), new Configuration())
      val (totalSize, totalFiles) = getTotalSizeAndFiles(hdfs, new Path(tableLocation))
      (totalSize, totalFiles)
    }

    val (beforeTotalSize: Long, beforeTotalFiles: Long) = getTotalSizeAndFilesByTable(beforeTable)
    val (afterTotalSize: Long, afterTotalFiles: Long) = getTotalSizeAndFilesByTable(afterTable)

    val schema = StructType(List(
      StructField("dual_run_id", LongType, false),
      StructField("instance_id", LongType, false),
      StructField("action_id", LongType, false),
      StructField("before_file_count", LongType, false),
      StructField("before_file_storage", LongType,false),
      StructField("after_file_count", LongType, false),
      StructField("after_file_storage", LongType, false),
      StructField("record_time", TimestampType, false),
      StructField("before_table", StringType, false),
      StructField("after_table", StringType, false),
      StructField("compare_code", IntegerType, false),
      StructField("compare_message", StringType, false)
    ))

    val currentTime = Timestamp.valueOf(LocalDateTime.now())
    val data = Seq(Row(taskId, instanceId, actionId, beforeTotalFiles, beforeTotalSize, afterTotalFiles,
      afterTotalSize, currentTime, beforeTable, afterTable, exitCode, compareMessage))
    val rdd = spark.sparkContext.parallelize(data)
    val metricDF = spark.createDataFrame(rdd, schema)
    metricDF.show(false)
    val tableName = "dual_run_result_checklist_3"
//    val tableName = "dual_run_result_checklist_3_test"
    metricDF.write.format("jdbc")
      .option("url", "***************************************************************************")
      .option("dbtable", tableName)
      .option("driver", "com.mysql.jdbc.Driver")
      .option("user", "salt")
      .option("password", "salt")
      .mode("append")
      .save()

//    import java.util.Properties
//    val query = spark.read.jdbc("***************************************************************************",
//      tableName, {
//      val props = new Properties()
//      props.put("driver", "com.mysql.jdbc.Driver")
//      props.put("user", "salt")
//      props.put("password", "salt")
//      props
//    })

    spark.stop()
  }
}
