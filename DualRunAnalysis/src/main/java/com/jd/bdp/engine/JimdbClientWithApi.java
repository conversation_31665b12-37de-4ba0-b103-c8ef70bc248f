package com.jd.bdp.engine;

import com.google.common.base.Strings;
import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.ReloadableJimClientFactory;
import com.jd.jim.cli.config.ConfigClient;
import com.jd.jim.cli.config.ConfigLongPollingClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JimdbClientWithApi {
    private static final Logger log = LoggerFactory.getLogger(JimdbClientWithApi.class);

    private static final String jimUrl = "jim://2941132917553898407/10466";

    public static Cluster initCluster(String jimUrlParam){
        //测试环境拉取客户端配置和集群拓扑信息的元数据服务域名
        ConfigLongPollingClientFactory cfsConfigClientFactory = new ConfigLongPollingClientFactory();
        ConfigClient configClient = cfsConfigClientFactory.create();

        ReloadableJimClientFactory jimClientFactory = new ReloadableJimClientFactory();
        //JIMDB根据元数据服务地址、JimUrl两个参数来确定唯一的集群，所以这两个参数是静态参数、不可以修改。
        jimClientFactory.setConfigClient(configClient);
        jimClientFactory.setJimUrl(Strings.isNullOrEmpty(jimUrlParam)?jimUrl:jimUrlParam);

        //使用ID为0的这个客户端配置
        jimClientFactory.setConfigId("0");
        // netty IO线程池数量，一般情况下设置为4效果最佳，针对吞吐要求高的情况，可以根据不同的客户端CPU配置和集群规模建议测试后进行调整
        jimClientFactory.setIoThreadPoolSize(4);
        //流量控制，该队列由请求和响应两部组成，当队列瞬时达到10000，此时会提示超出队列长度，可以根据业务的流量进行调整，特别针对异步和pipeline调用
        jimClientFactory.setRequestQueueSize(10000);

        return jimClientFactory.getClient();
    }
    public static void main(String[] args) {
        Cluster jimcli = initCluster(null);
        String key ="name";
        String value ="helloworld";
        jimcli.set(key,value);
        log.info("key:{} value:{}",key,jimcli.get(key));
        String s = jimcli.get(key);
        System.out.println("s = " + s);

    }
}