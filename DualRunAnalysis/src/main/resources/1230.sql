SELECT
	t1.task_id,
	t1.task_name,
	t1.job_cycle,
	t1.dept_2_name,
	t1.task_owner,
	t1.priority,
	t1.engine_type,
	t1.exec_long,
	t1.req_vcore,
	IF(COALESCE(t2.dr_ornot, '') = '', '否', t2.dr_ornot) AS dr_ornot,
	IF(COALESCE(t3.job_num, '') = '', 0, t3.job_num) AS job_num
FROM
	(
		SELECT
			task_id,
			task_name,
			task_cate,
			job_cycle,
			bgname,
			dept_1_name,
			dept_2_name,
			task_owner,
			task_owner_name,
			priority,
			engine_type,
			exec_long['1days'] AS exec_long,
			req_vcore['1days'] AS req_vcore
		FROM
			adm.adm_m99_task_detail_info_da
		WHERE
			dt = sysdate( - 1)
			AND datatype IN('BUFFALO4')
			AND is_delete = '0'
			AND status NOT IN('冻结', '已升级', '禁用', '过期')
			AND task_cate IN('工作流任务', '标准任务')
			AND dept_1_name = '平台产品与研发中心'
			AND dept_2_name IN('数据资产与应用部', '智能平台部', '集团数据计算平台部')
			AND engine_type != 'hive引擎'
	)
	t1
LEFT JOIN
	(
		SELECT
			m.task_id,
			CASE
				WHEN m.source_count = 1
					AND m.task_count = m.total_count
				THEN '是'
				ELSE '否'
			END AS dr_ornot
		FROM
			(
				SELECT
					buffaloEnvTaskDefId AS task_id,
					COUNT(DISTINCT spark_sql_source) AS source_count,
					COUNT(
						CASE
							WHEN spark_sql_source = 'HiveTask'
							THEN 1
						END) AS task_count,
					COUNT( *) AS total_count
				FROM
					fdm.fdm_spark_environmentinfo_di
				WHERE
					dt = sysdate( - 1)
				GROUP BY
					buffaloEnvTaskDefId
			)
			m
	)
	t2
ON
	t1.task_id = t2.task_id
LEFT JOIN
	(
		SELECT
			task_id,
			COUNT(1) AS job_num
		FROM
			gdm.gdm_m99_job_run_log_da
		WHERE
			dt = sysdate( - 1)
			AND job_type = 'spark'
		GROUP BY
			task_id
	)
	t3
ON
	t1.task_id = t3.task_id