package com.jd.bigdata;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.orc.OrcFile;
import org.apache.orc.TypeDescription;
import org.apache.orc.Writer;
import org.apache.hadoop.hive.ql.exec.vector.BytesColumnVector;
import org.apache.hadoop.hive.ql.exec.vector.VectorizedRowBatch;

public class SimpleOrcStringWriter {
    public static void main(String[] args) throws Exception {
        // ORC 文件输出路径
        String orcFilePath = "string_demo.orc";
        if (args.length > 0) {
            orcFilePath = args[0];
        }

        TypeDescription schema = TypeDescription.createStruct()
                .addField("name", TypeDescription.createString());

        Configuration conf = new Configuration();
        Writer writer = OrcFile.createWriter(new Path(orcFilePath),
                OrcFile.writerOptions(conf).setSchema(schema));

        VectorizedRowBatch batch = schema.createRowBatch();
        BytesColumnVector nameCol = (BytesColumnVector) batch.cols[0];

        for (int i = 0; i < 10; i++) {
            int row = batch.size++;
            String value = "hello_orc_" + i;
            byte[] bytes = value.getBytes("UTF-8");
            nameCol.setVal(row, bytes, 0, bytes.length);

            if (batch.size == batch.getMaxSize()) {
                writer.addRowBatch(batch);
                batch.reset();
            }
        }

        if (batch.size > 0) {
            writer.addRowBatch(batch);
        }
        writer.close();
        System.out.println("ORC 文件写入完成: " + orcFilePath);
    }
} 