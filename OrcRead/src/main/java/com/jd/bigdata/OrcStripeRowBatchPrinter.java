package com.jd.bigdata;
import org.apache.hadoop.hive.ql.exec.vector.BytesColumnVector;
import org.apache.hadoop.hive.ql.exec.vector.ColumnVector;
import org.apache.hadoop.hive.ql.exec.vector.DoubleColumnVector;
import org.apache.orc.*;
import org.apache.hadoop.hive.ql.exec.vector.VectorizedRowBatch;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class OrcStripeRowBatchPrinter {
    public static void main(String[] args) throws Exception {
        if (args.length < 2) {
            System.err.println("Usage: OrcStripeRowBatchPrinter <orc-file-path> <col1,col2,...>");
            System.exit(1);
        }
        String orcFilePath = args[0];
        String[] colNames = args[1].split(",");
        Set<String> colNameSet = new HashSet<String>();
        for (String col : colNames) {
            colNameSet.add(col.trim());
        }
        Configuration conf = new Configuration();

        Reader reader = OrcFile.createReader(new Path(orcFilePath), OrcFile.readerOptions(conf));

        TypeDescription schema = reader.getSchema();
        // 获取所有列名和索引
        List<String> allFieldNames = schema.getFieldNames();
        List<Integer> selectedColIndices = new ArrayList<Integer>();
        List<String> selectedColNames = new ArrayList<String>();
        for (int i = 0; i < allFieldNames.size(); i++) {
            if (colNameSet.contains(allFieldNames.get(i))) {
                selectedColIndices.add(i);
                selectedColNames.add(allFieldNames.get(i));
            }
        }
        if (selectedColIndices.isEmpty()) {
            System.err.println("No matching columns found!");
            System.exit(2);
        }

        TypeDescription selectedSchema = TypeDescription.createStruct();
        for (int idx : selectedColIndices) {
            selectedSchema.addField(allFieldNames.get(idx), schema.getChildren().get(idx));
        }

        List<StripeInformation> stripes = reader.getStripes();
        for (int stripeIdx = 4; stripeIdx < stripes.size(); stripeIdx++) {
            if(stripeIdx >= 5) {
                return;
            }
            StripeInformation stripe = stripes.get(stripeIdx);
            Reader.Options range = reader.options().range(stripe.getOffset(), stripe.getLength())
                    .include(getInclude(schema, selectedColIndices));
            RecordReader rows = reader.rows(range);
            VectorizedRowBatch batch = reader.getSchema().createRowBatch();
            int rowBatchIdx = 0;
            while (rows.nextBatch(batch)) {
                for (int r = 0; r < batch.size; r++) {
                    System.out.printf("stripe=%d, rowBatch=%d, row=%d: ", stripeIdx, rowBatchIdx, r);
                    for (int c = 0; c < selectedColIndices.size(); c++) {
                        int colIdx = selectedColIndices.get(c);
                        ColumnVector colVector = batch.cols[colIdx];

                        String colName = selectedColNames.get(c);
                        String valueStr;
                        if (colVector instanceof BytesColumnVector) {
                            BytesColumnVector bcv = (BytesColumnVector) colVector;
                            if (bcv.isNull[r]) {
                                valueStr = "NULL";
                            } else {
                                valueStr = new String(bcv.vector[r], bcv.start[r], bcv.length[r]);
                            }
                        } else if (colVector instanceof DoubleColumnVector) {
                            DoubleColumnVector dcv = (DoubleColumnVector) colVector;
                            if (dcv.isNull[r]) {
                                valueStr = "NULL";
                            } else {
                                valueStr = String.valueOf(dcv.vector[r]);
                            }
                        } else {
                            // 其他类型直接 toString
                            valueStr = colVector.toString();
                        }
                        System.out.print(colName + "=" + valueStr + " ");
                    }
                    System.out.println();
                }
                rowBatchIdx++;
            }
            rows.close();
        }
    }

    // 构造 include 数组，true 表示读取该列
    private static boolean[] getInclude(TypeDescription schema, List<Integer> selectedColIndices) {
        int numCols = schema.getMaximumId() + 1;
        boolean[] include = new boolean[numCols];
        // ORC 的 include 数组是按 field id 标记的
        for (int idx : selectedColIndices) {
            int fieldId = schema.getChildren().get(idx).getId();
            include[fieldId] = true;
        }
        // 还要把 root struct 标记为 true
        include[0] = true;
        return include;
    }
}