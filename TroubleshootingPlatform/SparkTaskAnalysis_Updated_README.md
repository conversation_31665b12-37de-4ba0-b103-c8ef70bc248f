# Spark Application Task 分析工具 (更新版)

## 功能概述

这个工具用于分析 Spark Application 的 task 执行情况，支持直接输入具体的 Spark Application API 地址。

## 主要修改

### 输入方式变更

**之前**：需要输入 Spark History Server 基础地址
```
http://spark-history-server:18080/history/application_1234567890_0001
```

**现在**：直接输入具体的 Spark Application API 地址
```
http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855
```

### 支持格式

工具现在支持以下 URL 格式：

1. **API 格式**：
   ```
   http://host:port/api/v1/applications/application_xxx_xxx
   ```

2. **History 格式**：
   ```
   http://host:port/history/application_xxx_xxx
   ```

## 使用方法

### 1. 访问分析页面

```
http://your-server:port/TroubleshootingPlatform/sparkTaskAnalysis.jsp
```

### 2. 输入 Spark Application API URL

直接输入完整的 Application API 地址，例如：
```
http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855
```

### 3. 点击"开始分析"

系统会自动：
- 从 URL 中提取 Application ID
- 获取 Application 基本信息
- 获取所有 Stages 信息
- 分析每个 Stage 的 Task 详情
- 统计推测执行和重试的 Task 数量

### 4. 查看分析结果

结果包括：
- **Application 基本信息**：ID、名称、API URL、处理的 Stages 数量
- **Task 统计信息**：总数量、推测执行数量、重试数量、相关比例

## API 接口

### POST /sparkTaskAnalysis

**请求参数：**
- `historyUrl`: Spark Application API URL

**响应格式：**
```json
{
  "success": true,
  "message": "分析完成",
  "data": {
    "appId": "application_1745827167801_22878855",
    "appName": "My Spark App",
    "historyUrl": "http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855",
    "totalTasks": 1000,
    "speculativeTasks": 50,
    "retryTasks": 10,
    "processedStages": 5,
    "totalStages": 5
  }
}
```

## 技术实现

### 核心改进

1. **URL 解析**：新增 `extractAppIdFromUrl()` 方法，自动从 URL 中提取 Application ID
2. **直接 API 调用**：直接调用提供的 API 地址，无需构建额外的 URL
3. **兼容性**：支持多种 URL 格式，保持向后兼容

### URL 提取逻辑

```java
private String extractAppIdFromUrl(String url) {
    // 查找 application_ 开头的部分
    int appIndex = url.indexOf("application_");
    if (appIndex == -1) {
        return null;
    }
    
    // 提取从 application_ 开始到 URL 结束或下一个 / 的部分
    String appIdPart = url.substring(appIndex);
    int slashIndex = appIdPart.indexOf("/");
    if (slashIndex != -1) {
        appIdPart = appIdPart.substring(0, slashIndex);
    }
    
    return appIdPart;
}
```

## 测试

### 测试页面

访问测试页面验证功能：
```
http://your-server:port/TroubleshootingPlatform/sparkTaskAnalysisTest.jsp
```

### 测试用例

1. **有效 URL 测试**：
   ```
   http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855
   ```

2. **空 URL 测试**：
   - 预期结果：返回"Application URL 不能为空"错误

3. **无效 URL 测试**：
   - 预期结果：返回相应的错误信息

## 部署说明

1. 确保项目依赖正确配置
2. 部署到 Web 容器（如 Tomcat）
3. 确保网络可以访问 Spark History Server
4. 配置适当的防火墙规则

## 注意事项

1. **网络访问**：确保应用服务器可以访问 Spark History Server
2. **URL 格式**：确保输入的 URL 包含完整的 Application ID
3. **权限要求**：History Server 需要允许外部访问
4. **性能考虑**：大量 Task 的应用可能需要较长分析时间

## 错误处理

### 常见错误

1. **URL 格式错误**
   - 错误信息：请输入有效的 HTTP/HTTPS URL
   - 解决方案：检查 URL 格式是否正确

2. **无法提取 Application ID**
   - 错误信息：无法从 URL 中提取 Application ID
   - 解决方案：确保 URL 包含 application_xxx_xxx 格式的 ID

3. **无法访问 API**
   - 错误信息：无法获取 Application 信息
   - 解决方案：检查网络连接和 API 地址

4. **API 响应异常**
   - 错误信息：无法获取 Stages 信息
   - 解决方案：检查 Spark 版本兼容性 