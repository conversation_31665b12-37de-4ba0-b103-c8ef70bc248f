<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.jd</groupId>
  <artifactId>TroubleshootingPlatform</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>war</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <spring.version>5.2.9.RELEASE</spring.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.jd.bdp.hbase</groupId>
      <artifactId>jdnosql-client</artifactId>
      <version>3.2.8</version>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.netty</groupId>
          <artifactId>netty-all</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.6.7.3</version>
    </dependency>
    <dependency>
      <groupId>org.asynchttpclient</groupId>
      <artifactId>async-http-client</artifactId>
      <version>2.10.5</version>
    </dependency>
    <dependency>
      <groupId>com.jd.jim.cli</groupId>
      <artifactId>jim-cli-spring</artifactId>
      <version>2.1.9-HOTFIX-T1</version>
    </dependency>
    <dependency>
      <groupId>com.jd.ump</groupId>
      <artifactId>profiler</artifactId>
      <version>20210630</version>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>2.15.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.15.0</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>
    <dependency>
      <groupId>javax</groupId>
      <artifactId>javaee-api</artifactId>
      <version>7.0</version>
      <scope>provided</scope>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>org.influxdb</groupId>-->
<!--      <artifactId>influxdb-java</artifactId>-->
<!--      <version>2.1</version>-->
<!--    </dependency>-->
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>1.21</version>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <version>3.0.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>5.1.6</version>
    </dependency>
    <dependency>
      <groupId>commons-dbutils</groupId>
      <artifactId>commons-dbutils</artifactId>
      <version>1.7</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.5</version>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jaxrs</artifactId>
      <version>2.3.1.GA</version>
    </dependency>
    <dependency>
      <groupId>net.sf.scannotation</groupId>
      <artifactId>scannotation</artifactId>
      <version>1.0.2</version>
    </dependency>
    <dependency>
      <groupId>com.jd</groupId>
      <artifactId>Utils</artifactId>
      <version>2.2</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpmime</artifactId>
      <version>4.5.13</version>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.11.3</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <version>${spring.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
      <version>${spring.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
      <version>${spring.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
      <version>${spring.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
      <version>${spring.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
      <version>${spring.version}</version>
    </dependency>
    <dependency>
      <groupId>com.jd</groupId>
      <artifactId>jsf</artifactId>
      <version>1.7.4</version>
    </dependency>
    <dependency>
      <groupId>com.jd.common</groupId>
      <artifactId>sso-uim-spring</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.16.18</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.jd.xbp</groupId>
      <artifactId>client</artifactId>
      <version>3.2.6</version>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-simple</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.squareup.okhttp3</groupId>
          <artifactId>okhttp</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.jd.xbp</groupId>
      <artifactId>jsf-api</artifactId>
      <version>1.1.3</version>
    </dependency>
    <dependency>
      <groupId>com.jcraft</groupId>
      <artifactId>jsch</artifactId>
      <version>0.1.55</version>
    </dependency>
    <dependency>
      <groupId>presto-jdbc-jd</groupId>
      <artifactId>presto-jdbc-jd</artifactId>
      <version>350.1</version>
    </dependency>
    <dependency>
      <groupId>jd-omdm-client</groupId>
      <artifactId>jd-omdm-client</artifactId>
      <version>0.2.7-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.jd.jbdp</groupId>
      <artifactId>jbdp-edc-api</artifactId>
      <version>1.0.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.jd.bdp</groupId>
      <artifactId>gw-sdk</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.jd</groupId>
      <artifactId>DataVirtual</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.opencsv</groupId>
      <artifactId>opencsv</artifactId>
      <version>5.5</version> <!-- 版本号可能需要调整 -->
    </dependency>
    <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>3.14.4</version>
    </dependency>
    <dependency>
      <artifactId>mybatis</artifactId>
      <groupId>org.mybatis</groupId>
      <version>3.5.6</version>
    </dependency>

    <dependency>
      <groupId>com.jd.unified.metadata.blood</groupId>
      <artifactId>metadata-rpc</artifactId>
      <version>2.12-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.jd.jdq</groupId>
      <artifactId>jdq4-clients</artifactId>
      <version>1.3.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.jd.bdp.jdq</groupId>
      <artifactId>jdwdata</artifactId>
      <version>1.0.2-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid</artifactId>
      <version>1.2.8</version>
    </dependency>

    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis-spring</artifactId>
      <version>2.0.1</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jdbc</artifactId>
      <version>5.3.22</version>
    </dependency>
  </dependencies>

  <build>
    <finalName>SparkTroubleshooting</finalName>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.7</version>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.3</version>
      </plugin>
      <plugin>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok-maven-plugin</artifactId>
        <version>1.16.6.1</version>
      </plugin>
    </plugins>
  </build>

  <repositories>
    <repository>
      <id>JD maven2 snapshots</id>
      <name>JD maven2 repository-snapshots</name>
      <url>http://artifactory.jd.com/libs-snapshots</url>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>JD maven2 repository</id>
      <name>JD maven2 repository-releases</name>
      <url>http://artifactory.jd.com/libs-releases</url>
    </repository>
  </repositories>
  <distributionManagement>
    <repository>
      <id>snapshots_1</id>
      <url>http://artifactory.jd.com/libs-snapshots</url>
    </repository>
  </distributionManagement>
</project>
