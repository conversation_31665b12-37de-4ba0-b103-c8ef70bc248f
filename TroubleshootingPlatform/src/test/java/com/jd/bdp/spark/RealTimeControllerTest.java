package com.jd.bdp.spark;

import com.jd.bdp.common.SDFThreadLocal;
import org.junit.Test;

import java.util.*;

public class RealTimeControllerTest {
    @Test
    public void test(){
        Map<String, Map<Long, Double>> realtimeMemory = new HashMap<>();
        Map<Long, Double> aaa = new HashMap<>();
        aaa.put(20l,30d);
        aaa.put(10l,20d);
        Map<Long, Double> bbb = new HashMap<>();
        bbb.put(15l,26d);
        bbb.put(8l,15d);
        realtimeMemory.put("application_1571127790599_9888000", aaa);
        realtimeMemory.put("application_1571127790599_9888001", bbb);

        System.out.println(SDFThreadLocal.get().format(new Date()) + " realtimeMemory = " + realtimeMemory);

        Collection<Map<Long, Double>> values = realtimeMemory.values();
        long maxKey = 0L;
        for(Map<Long,Double> map : values) {
            ArrayList<Long> sortedKeys = new ArrayList<Long>(map.keySet());
            Collections.sort(sortedKeys);
            for (Long x : sortedKeys) {
                System.out.println("Key = " + x +
                        ", Value = " + map.get(x));
                maxKey = x > maxKey ? x:maxKey;
            }
        }
        System.out.println("maxKey = " + maxKey);

        for(Map<Long,Double> map : values) {
            ArrayList<Long> sortedKeys = new ArrayList<Long>(map.keySet());
            Collections.sort(sortedKeys);

            List<Long> x = new ArrayList<>();
            for(int i = 0;i<maxKey; i++) {

            }
        }
    }
}
