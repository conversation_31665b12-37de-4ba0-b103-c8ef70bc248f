package com.jd.bdp.spark;

import com.jd.bdp.bean.AllowList;
import com.jd.bdp.bean.HDFSNameNode;
import com.jd.bdp.bean.Keywords;
import com.jd.bdp.bean.MysqlBean;
import com.jd.bdp.common.YamlUtil;
import org.junit.Test;

import java.util.Map;

public class YamlUtilTest {

    @Test
    public void testAllowList(){
        AllowList allowList = YamlUtil.loadYaml("allowList.yaml", AllowList.class);
        assert allowList.getAllowList().size() > 0;
    }

    @Test
    public void testYaml(){
        Keywords keywords = YamlUtil.loadYaml("keywords.yaml",Keywords.class);
        assert keywords.getKeywordList().size() > 0;
    }

    @Test
    public void testYaml2(){
        HDFSNameNode hdfsNameNode = YamlUtil.loadYaml("HDFSNameNode.yaml", HDFSNameNode.class);
        assert hdfsNameNode.getEventLogPathPrefix().size() > 0;
    }

    @Test
    public void testYaml3(){
        Map map = YamlUtil.loadYaml("resourceManagers.yaml", Map.class);
        assert map.size() > 0;
    }

    @Test
    public void testMysqlBean(){
        MysqlBean mysqlBean = YamlUtil.loadYaml("db.yaml", MysqlBean.class);
        assert mysqlBean.getDb() != null;
    }

}
