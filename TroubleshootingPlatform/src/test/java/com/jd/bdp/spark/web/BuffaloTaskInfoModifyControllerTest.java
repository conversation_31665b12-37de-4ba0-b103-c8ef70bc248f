package com.jd.bdp.spark.web;

import com.jd.bdp.filter.SSOFilterImpl;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class BuffaloTaskInfoModifyControllerTest {

    @Test
    public void buffaloTaskInfoModifyTest() throws IOException {
        String query = BuffaloTaskInfoModifyController.buffaloTaskInfoModify("query", "677894",
                null, null, "wuguoxiao", null, null);
        assert StringUtils.isNotEmpty(query);
        query = BuffaloTaskInfoModifyController.buffaloTaskInfoModify("modify", "677894",
                "spark", "3.0", "wuguoxiao", null, null);
        assert StringUtils.isNotEmpty(query);
        query = BuffaloTaskInfoModifyController.buffaloTaskInfoModify("modify", "677894",
                "spark", "default", "wuguoxiao", null, null);
        assert StringUtils.isNotEmpty(query);
    }

    @Test
    public void getActions() throws IOException {
        List<Integer> actions = BuffaloTaskInfoModifyController.getActions("1466664");
        System.out.println("integers = " + actions);
        assert actions.size() == 1;
    }

}
