package com.jd.bdp.spark.web;

import org.junit.Test;

import java.io.IOException;
import java.util.Map;

public class EasyBIControllerTest {
    @Test
    public void buffaloTaskInfoModifyTest() {
        Map<String, String> sparkMigrateSQL = EasyBIController.sparkMigrateSQL;
        String dualRun = sparkMigrateSQL.get("dual_run");

        dualRun = String.format(dualRun, " AND f.run_2_4_logid IS NOT NULL " +
                "AND (run_2_4 = 'fail' or run_3_4 = 'fail' or data_check = 'fail')");
        System.out.println("dualRun = " + dualRun);

    }
}
