package com.jd.bdp.spark.web;

import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;

public class TaskCriticalPathTest {
    @Test
    public void parseXml() throws IOException {
        String tasks = "708547";

        ArrayList<Long> criticalPath = TaskCriticalPath.getCriticalPath(Long.parseLong(tasks), "2024-10-18", 100);
        System.out.println("criticalPath = " + criticalPath);

    }
}
