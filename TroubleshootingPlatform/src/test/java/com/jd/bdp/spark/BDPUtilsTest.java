package com.jd.bdp.spark;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.CreateTableBean;
import com.jd.bdp.bean.Keyword;
import com.jd.bdp.common.YamlUtil;
import com.jd.bdp.scheduler.Scheduler;
import com.jd.bdp.spark.web.BDPUtils;
import com.jd.bdp.spark.web.OSUtils;
import com.jd.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.junit.BeforeClass;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.regex.Pattern;

import static com.jd.bdp.bean.Constants.*;
import static com.jd.bdp.spark.web.BDPUtils.amPattern;

public class BDPUtilsTest {
    private static final Logger logger = Logger.getLogger(BDPUtilsTest.class.getName());

    private static String gitProjectId;
    private static String projectSpaceId;
    private static String applicationId;

    private final String newDBName = "test";
    private final String newTablePrefix = "AAA";

    @BeforeClass
    public static void beforeClass(){
        Scheduler.initThreadPool();
        Map<String, String> sqlTemplate = YamlUtil.sqlTemplate;
        gitProjectId = sqlTemplate.get("gitProjectId");
        projectSpaceId = sqlTemplate.get("projectSpaceId");
        applicationId = sqlTemplate.get("applicationId");
    }

    @Test
    public void testMatch(){
        String command = "spark-sql --master yarn --conf spark.submit.deployMode=client " +
                "--conf spark.executor.instances=450 --conf spark.executor.cores=4 --conf spark.executor.memory=16g " +
                "--conf spark.driver.memory=10g --conf spark.driver.cores=4 --conf spark.sql.shuffle.partitions=1800 " +
                "--conf spark.dynamicAllocation.enabled=True --conf spark.shuffle.service.enabled=True " +
                "--conf spark.speculation=true --conf spark.isLoadHivercFile=true --conf spark.sql.hive.convertMetastoreOrc=true --conf spark.sql.adaptive.enabled=true --conf spark.sql.tempudf.ignoreIfExists=true --conf spark.sql.parser.quotedRegexColumnNames=true --conf spark.sql.crossJoin.enabled=true --conf spark.resource.level=medium --conf spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version=2 --conf spark.sql.retry.with.hive=True --hiveconf hive.exec.orc.split.strategy=BI --conf spark.sql.source=HiveTask --conf spark.hive.ht.taskid=977727_BUFFALO4_683770_1010_1 --conf spark.hive.ht.instanceid=977727_BUFFALO4_2209495_3505165823920570371_172.21.130.107_683770_1010_1 --conf spark.sql.hive.mergeFiles=true --conf spark.shuffle.io.connectionTimeout=180s --conf spark.shuffle.io.maxRetries=4 --conf spark.sql.adaptive.enabled=false --conf spark.files.fetchFailure.unRegisterOutputOnHost=false --conf spark.scheduler.notifyExecutorFetchFailedStageAttempt=false --conf spark.sql.hive.mergeFiles=true --conf spark.sql.viewPermission.enabled=true --conf spark.executor.cores=8 --conf spark.executor.memory=20g --conf spark.dynamicAllocation.maxExecutors=3600 --conf spark.sql.hive.merge.smallfile.parallelism.v2=8 --conf spark.driver.maxResultSize=20g --conf spark.shuffle.manager=\"org.apache.spark.shuffle.RssShuffleManager\" --conf spark.sql.viewPermission.enabled=\"true\"" +
                " --conf spark.shuffle.rss.v3.enabled=\"true\" --conf spark.sql.externalCatalog.requireDbExists.enabled=\"false\" --conf spark.sql.parser.quotedRegexColumnNames.isRegex.enabled=\"true\" --conf spark.sql.parser.quotedRegexColumnNames=\"true\" --conf spark.rss.coordinator.quorum=\"**************:19999,**************:19999,**************:19999\" -e \" set hive.exec.dynamic.partition=true; set hive.exec.dynamic.partition.mode=nonstrict; use app; INSERT OVERWRITE TABLE app.app_d03_base_info_da_orc_3c PARTITION (dt='2022-12-18',tp='3') SELECT item_sku_id, -- SKU_ID sku_name, -- 商品名称 real_item_sku_id, -- SKU_ID的主SKU(影分身机制) item_model, -- 商品型号 spu_id, -- SPU_ID item_name, -- ITEM_NAME sku_valid_flag, -- SKU是否有效 sku_status_cd, -- SKU状态编码 sku_status_name, -- SKU状态名称 brand_code, -- 品牌编码 brandname_en, -- 品牌名称-英文 brandname_cn, -- 品牌名称-中文 brandname_full, -- 品牌名";
        JSONObject rssV3 = BDPUtils.matchLastKeyword(Pattern.compile("spark\\.shuffle\\.rss\\.v3\\.enabled=\"?(\\w+)\"?"), "rssV3", command);
        System.out.println("rssV3 = " + rssV3);

    }
    @Test
    public void testParseEnv() {
        String env = "JDHXXXXX_CLUSTER_NAME=hope,JDHXXXXX_USER=mart_sch,JDHXXXXX_QUEUE=bdp_jmart_sch_union.bdp_jmart_osm_union.bdp_jmart_osm_formal,TEAM_USER=mart_sch_osm,BEE_HIVETASK_EXEC_ENGINE=spark";
        Map<String, String> stringStringMap = BDPUtils.parseEnv(env);
        System.out.println("stringStringMap = " + stringStringMap);
    }

    @Test
    public void testRewriteTableName() {
        JSONObject xxx = BDPUtils.rewriteSql(null, "insert overwrite table dev.aaa select * from dev.aaa_cc",
                null, newTablePrefix, "dev");

        String assertRewrote = StringUtils.join(Scheduler.commonBean.getForkBuffaloTaskAppendParamsAtStart(), "\n") +
                "\n" +
                "\n" +
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_aaa;\n" +
                "CREATE TABLE IF NOT EXISTS dev.spark_team_temp_erp_xn_spark_AAA_aaa LIKE dev.aaa;\n" +
                "insert overwrite table dev.spark_team_temp_erp_xn_spark_AAA_aaa select * from dev.aaa_cc".trim();
        String rewrote = xxx.getString("rewrote").trim();
        assert rewrote.contentEquals(assertRewrote);

        xxx = BDPUtils.rewriteSql(null,
                "--conf spark.executor.cores=5 -e \"USE adm;\n" +
                        "insert overwrite table dev.bbb select * from aaa\n", "--conf spark.executor.memory=10g", newTablePrefix, "dev");
        rewrote = xxx.getString("rewrote").trim();
        assertRewrote = StringUtils.join(Scheduler.commonBean.getForkBuffaloTaskAppendParamsAtStart(), "\n") + "\n" +
                "--conf spark.executor.cores=5 --conf spark.executor.memory=10g -e \"USE adm;\n" +
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_bbb;\n" +
                "CREATE TABLE IF NOT EXISTS dev.spark_team_temp_erp_xn_spark_AAA_bbb LIKE dev.bbb;\n" +
                "\n" +
                "insert overwrite table dev.spark_team_temp_erp_xn_spark_AAA_bbb select * from aaa\n".trim();
        assert rewrote.contentEquals(assertRewrote);

        xxx = BDPUtils.rewriteSql(null,
                "--conf spark.executor.cores=5 -e \"use adm;\n" +
                        "insert overwrite table dev.bbb select * from aaa\n", "--conf spark.executor.memory=10g", newTablePrefix, "dev");
        rewrote = xxx.getString("rewrote").trim();
        assertRewrote = StringUtils.join(Scheduler.commonBean.getForkBuffaloTaskAppendParamsAtStart(), "\n") + "\n" +
                        "--conf spark.executor.cores=5 --conf spark.executor.memory=10g -e \"use adm;\n" +
                        "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_bbb;\n" +
                        "CREATE TABLE IF NOT EXISTS dev.spark_team_temp_erp_xn_spark_AAA_bbb LIKE dev.bbb;\n" +
                        "\n" +
                        "insert overwrite table dev.spark_team_temp_erp_xn_spark_AAA_bbb select * from aaa\n".trim();
        assert rewrote.contentEquals(assertRewrote);
    }

    @Test
    public void testRewriteTableName1(){
        JSONObject jsonObject = BDPUtils.rewriteSql(null,
                            "use app; CREATE  table app.table_A (aaa string) ;" +
                                "insert overwrite table app.table_A ", "", newTablePrefix, "dev");
        String rewrote = jsonObject.getString("rewrote");
        String assertRewrote = StringUtils.join(Scheduler.commonBean.getForkBuffaloTaskAppendParamsAtStart(), "\n") + "\n"+
                "use app;\n" +
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_A;\n" +
                " CREATE  table dev.spark_team_temp_erp_xn_spark_AAA_table_A (aaa string) ;insert overwrite table dev.spark_team_temp_erp_xn_spark_AAA_table_A ";
        assert rewrote.equals(assertRewrote);

        String rewriteStr = BDPUtils.rewriteSql(null,
                        "use app; CREATE  table app.table_A\n" +
                                " (aaa string) ; insert overwrite table app.table_B ", "", newTablePrefix, "dev").getString("rewrote");
        assertRewrote = StringUtils.join(Scheduler.commonBean.getForkBuffaloTaskAppendParamsAtStart(), "\n") + "\n"+
                "use app;\n"+
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_A;\n" +
                "\n" +
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_B;\n" +
                "CREATE TABLE IF NOT EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_B LIKE app.table_B;\n" +
                " CREATE  table dev.spark_team_temp_erp_xn_spark_AAA_table_A\n" +
                " (aaa string) ; insert overwrite table dev.spark_team_temp_erp_xn_spark_AAA_table_B ";
        assert rewriteStr.equals(assertRewrote);
    }

    @Test
    public void testRewriteTableName2(){
        JSONObject jsonObject = BDPUtils.rewriteSql(null,
                "use app;\n" +
                    "CREATE table app.table_A (aaa string) ;\n" +
                    "CREATE table app.table_B (aaa string) ;\n" +
                    "CREATE table app.table_D (aaa string) ;\n" +
                    "insert overwrite table app.table_A \n" +
                    "insert overwrite table app.table_B \n" +
                    "insert overwrite table app.table_C \n" +
                    "",
                "", newTablePrefix, "dev");
        System.out.println("jsonObject = " + jsonObject);
        String anObject = StringUtils.join(Scheduler.commonBean.getForkBuffaloTaskAppendParamsAtStart(), "\n") +
                "use app;\n" +
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_D;\n" +
                "\n" +
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_B;\n" +
                "\n" +
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_A;\n" +
                "\n" +
                "DROP TABLE IF EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_C;\n" +
                "CREATE TABLE IF NOT EXISTS dev.spark_team_temp_erp_xn_spark_AAA_table_C LIKE app.table_C;\n" +
                "\n" +
                "CREATE table dev.spark_team_temp_erp_xn_spark_AAA_table_A (aaa string) ;\n" +
                "CREATE table dev.spark_team_temp_erp_xn_spark_AAA_table_B (aaa string) ;\n" +
                "CREATE table dev.spark_team_temp_erp_xn_spark_AAA_table_D (aaa string) ;\n" +
                "insert overwrite table dev.spark_team_temp_erp_xn_spark_AAA_table_A insert overwrite table dev.spark_team_temp_erp_xn_spark_AAA_table_B insert overwrite table dev.spark_team_temp_erp_xn_spark_AAA_table_C ";
        jsonObject.getString("rewrote").equals(anObject);
    }

    @Test
    public void testAlterTable(){
        String s = BDPUtils.removeAlterTableCmd(null,
                "alter table gdm.gdm_xxxx\ndrop if exists partition(dt = '2021-07-20') ;\n" +
                "insert\n");
        assert s.trim().equals("insert");

        s = BDPUtils.removeAlterTableCmd(null,
                "drop table gdm.gdm_xxxx;\n" +
                        "insert\n");
        assert s.trim().equals("insert");

        s = BDPUtils.removeAlterTableCmd(null,
                "truncate table gdm.gdm_xxxx;\n" +
                        "insert\n");
        assert s.trim().equals("insert");
    }

    @Test
    public void removeLocationKeyword(){
        String s = BDPUtils.removeLocationKeyword(null, "create table tmp.summary" +
                " location 'hdfs://ns1007/user/recsys/recup/tmp.db/tmp_userprofile_jx_staytime_summary_2'" +
                " as");
        assert "create table tmp.summary  as".equals(s);
    }

    @Test
    public void rewriteSqlCluster(){
        String s = BDPUtils.rewriteSqlCluster(
                "spark-sql-cluster --master yarn --conf spark.submit.deployMode=cluster --conf spark.executor.instances=2");
        System.out.println("s = " + s);
        assert s.equals("spark-sql --master yarn  --conf spark.executor.instances=2");
    }

    @Test
    public void testFindOutputTable() {
        List<CreateTableBean> insertTable = BDPUtils.findInsertTable("insert overwrite table dev.aaa ", newDBName, newTablePrefix);
        assert insertTable.size() == 1;
        assert insertTable.get(0).getOriginalDBTableName().equals("dev.aaa");
        assert insertTable.get(0).getReplacementDBTableName().equals(newDBName + "."+newTablePrefix+"aaa");
    }

    @Test
    public void testFindOutputTableNotFound(){
        List<CreateTableBean> insertTable = BDPUtils.findInsertTable("insert dev.aaa table dev.bbb ", newDBName, newTablePrefix);
        assert insertTable.size() == 0;
    }

    @Test
    public void findInsertIntoTable() {
        List<CreateTableBean> insertTable = BDPUtils.findInsertTable("insert into table app.app_jwms_performance_statistics partition ( ", newDBName, newTablePrefix);
        assert insertTable.size() == 1;
        assert insertTable.get(0).getOriginalDBTableName().equals("app.app_jwms_performance_statistics");

        insertTable = BDPUtils.findInsertTable("INSERT INTO app.app_jm_monitor_miniprogram_log_index_page partition", newDBName, newTablePrefix);
        assert insertTable.size() == 1;
        assert insertTable.get(0).getOriginalDBTableName().equals("app.app_jm_monitor_miniprogram_log_index_page");
    }

    @Test
    public void testFindMultiOutputTable(){
        List<CreateTableBean> insertTable = BDPUtils.findInsertTable("insert overwrite table dev.aaa insert overwrite table dev.bbb ", newDBName, newTablePrefix);
        assert insertTable.size() == 2;
        assert insertTable.get(0).getOriginalDBTableName().trim().equals("dev.aaa");
        assert insertTable.get(1).getOriginalDBTableName().trim().equals("dev.bbb");
    }

    @Test
    public void testGetBuffalo4LogsOneMonthAgo(){
        JSONArray buffalo4Logs = BDPUtils.getBuffalo4Logs("dp.jd.com", "550426", null,
                "single", null, "before");
        System.out.println("buffalo4Logs = " + buffalo4Logs);
        assert !buffalo4Logs.isEmpty();
    }

    @Test
    public void rewriteCreateTableStatement() {
        String db="app";
        String table="table_A";
        String input = "use app; CREATE  table "+db+"."+table +"\n" +
                "(aaa string) ; insert overwrite into "+db+"."+table +" ";
        List<CreateTableBean> createTableBeans = BDPUtils.findCreateTableStatement(input, newDBName, newTablePrefix);
        assert createTableBeans.size() == 1;
        assert createTableBeans.get(0).getOriginalDBTableName().equals(db + "." + table);
        assert createTableBeans.get(0).getOriginalTableName().equals(table);
        assert createTableBeans.get(0).getReplacementDBTableName().equals(newDBName+"."+newTablePrefix+"table_A");
        String s = BDPUtils.replaceCreateTableName(createTableBeans, input);
        System.out.println("s = " + s);
        assert s.equals("use app; CREATE  table test.AAAtable_A\n" +
                "(aaa string) ; insert overwrite into test.AAAtable_A ");
    }

    @Test
    public void rewriteCreateTableStatement1() {
        String input = "use app; \n" +
                "CREATE table if not exists app.table_A (aaa string) ;\n" +
                "CREATE table if not exists app.table_B (aaa string) ;\n";
        List<CreateTableBean> createTableBeans = BDPUtils.findCreateTableStatement(input, newDBName, newTablePrefix);
        assert createTableBeans.size() == 2;
        assert createTableBeans.get(0).getReplacementDBTableName().equals(newDBName+"."+newTablePrefix+"table_A");

        String s = BDPUtils.replaceCreateTableName(createTableBeans, input);
        assert s.equals("use app; \n" +
                "CREATE table if not exists test.AAAtable_A (aaa string) ;\n" +
                "CREATE table if not exists test.AAAtable_B (aaa string) ;\n");
    }

    @Test
    public void rewriteCreateTableStatement2() {
        String input = "CREATE TABLE IF NOT EXISTS app.app_7fresh_labels_base_da(";
        List<CreateTableBean> createTableBeans = BDPUtils.findCreateTableStatement(input, newDBName, newTablePrefix);
        assert createTableBeans.size() == 1;
        assert createTableBeans.get(0).getReplacementDBTableName().equals(newDBName+"."+newTablePrefix+"app_7fresh_labels_base_da");

        String s = BDPUtils.replaceCreateTableName(createTableBeans, input);
        assert s.equals("CREATE TABLE IF NOT EXISTS test.AAAapp_7fresh_labels_base_da(");
    }

    @Test
    public void logAnalysisByUrl() {
        JSONObject jsonObject = BDPUtils.logAnalysisByUrl("http://dp.jd.com/buffalo4/instance/log/downloadLogs.ajax?runLogId=" + 396251796, true, null, null);
        assert jsonObject.containsKey("originalCommand");
        jsonObject = BDPUtils.logAnalysisByUrl("http://dp.jd.com/buffalo4/instance/log/downloadLogs.ajax?runLogId=" + 396253619, true, null, null);
        assert jsonObject.containsKey("originalCommand");
        jsonObject = BDPUtils.logAnalysisByUrl("http://dp.jd.com/buffalo4/instance/log/downloadLogs.ajax?runLogId=" + 396251919, true, null, null);
        System.out.println("jsonObject = " + jsonObject.getString("originalCommand"));
    }

    @Test
    public void logAnalysisByUrl1() {
        JSONObject jsonObject = BDPUtils.logAnalysisByUrl("http://dp.jd.com/buffalo4/instance/log/downloadLogs.ajax?runLogId=" + 498484638, true, null, null);
        System.out.println("jsonObject = " + jsonObject.getString("originalCommand"));
    }

    @Test
    public void getLogIdByUrl(){
        String buffalo3Url = "http://dp.jd.com/buffalo/taskLog/downloadLogHbase.ajax?logId=629802524&businessType=001&downloadsource=hbase";
        assert "629802524".equals(BDPUtils.getLogIdByUrl(buffalo3Url));
    }

    @Test
    public void downloadBuffaloLog(){
        String fileName = OSUtils.downloadBuffaloLog(
                "http://dp.jd.com/buffalo4/instance/log/downloadLogs.ajax?runLogId=492170849", new JSONObject(), null);
        assert fileName != null;
    }

    private static final String correctResult = "spark-sql --master yarn                         --conf spark.submit.deployMode=client                         --conf spark.executor.instances=100                         --conf spark.executor.cores=12                         --conf spark.executor.memory=24g                         --conf spark.driver.memory=8g                         --conf spark.driver.cores=4                         --conf spark.sql.shuffle.partitions=1500                         --conf spark.dynamicAllocation.enabled=True                         --conf spark.shuffle.service.enabled=True                         --conf spark.speculation=true                         --conf spark.isLoadHivercFile=true                         --conf spark.sql.adaptive.enabled=true                         --conf spark.sql.tempudf.ignoreIfExists=true                         --conf spark.sql.parser.quotedRegexColumnNames=true                         --conf spark.sql.crossJoin.enabled=true                         --conf spark.resource.level=medium                         --conf spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version=2                         --conf spark.sql.retry.with.hive=False                         --hiveconf hive.exec.orc.split.strategy=BI                         --conf spark.sql.source=HiveTask --conf spark.executor.cores=4 --conf spark.executor.memory=12g --conf spark.dynamicAllocation.maxExecutors=500 --conf spark.sql.adaptive.repartition.enabled=true --conf spark.sql.hive.convertMetastoreOrc=true                         -e \"\n" +
            "set hive.exec.dynamic.partition=true;\n" +
            "set hive.exec.dynamic.partition.mode=nonstrict;\n" +
            "USE adm;\n" +
            "\n" +
            "use adm;\n" +
            "set spark.sql.hive.mergeFiles=true;\n" +
            "set spark.sql.parser.quotedRegexColumnNames=false; --关闭正则匹配列的校验\n" +
            "SET hive.exec.parallel=true;    --设置多个奇job并行执行\n" +
            "\n" +
            "\n" +
            "\n" +
            "insert overwrite table adm.adm_s05_content_guide_ord_sum partition (dt='2020-08-03',tp='day',plat_type)\n" +
            "select\n" +
            "     content_id\n" +
            "    ,content_type_cd\n" +
            "    ,content_cate_cd\n" +
            "    ,sub_cha_id\n" +
            "    ,sub_cha_name\n" +
            "    ,author_id\n" +
            "    ,author_type_cd\n" +
            "    ,author_source_cd\n" +
            "    ,sku_id\n" +
            "    ,content_1day_pnt_cw_ord_num\n" +
            "    ,content_1day_cw_ord_num\n" +
            "    ,content_1day_cw_ord_amount\n" +
            "    ,content_1day_cw_ord_users\n" +
            "    ,content_1day_cw_ord_goods\n" +
            "\n" +
            "    ,content_7days_pnt_cw_ord_num\n" +
            "    ,content_7days_cw_ord_num\n" +
            "    ,content_7days_cw_ord_amount\n" +
            "    ,content_7days_cw_ord_users\n" +
            "    ,content_7days_cw_ord_goods\n" +
            "    ,lvl\n" +
            "    ,plat_type\n" +
            "from(\n" +
            "      select\n" +
            "            channel as plat_type\n" +
            "           ,content_id\n" +
            "           ,content_type_cd\n" +
            "           ,content_cate_cd\n" +
            "           ,item_sku_id as sku_id\n" +
            "           ,author_id\n" +
            "           ,content_sub_cha_id as sub_cha_id\n" +
            "           ,case when substr(lpad(bin(cast(grouping__id as bigint)),7,'0'),7,1) = '0' then max(content_sub_cha_name) else null end as sub_cha_name\n" +
            "           ,case when substr(lpad(bin(cast(grouping__id as bigint)),7,'0'),6,1) = '0' then max(author_type_cd) else null end as author_type_cd\n" +
            "           ,case when substr(lpad(bin(cast(grouping__id as bigint)),7,'0'),6,1) = '0' then max(author_source_cd) else null end as author_source_cd\n" +
            "           --当天成交父单量\n" +
            "           ,coalesce(count(distinct case when intraday_ord_deal_flag = '1' then parent_sale_ord_id else null end),0) as content_1day_pnt_cw_ord_num\n" +
            "           --当天成交子单量\n" +
            "           ,coalesce(count(distinct case when intraday_ord_deal_flag = '1' then sale_ord_id else null end),0) as content_1day_cw_ord_num\n" +
            "           --当天成交金额\n" +
            "           ,coalesce(sum(case when intraday_ord_deal_flag = '1' then after_prefr_amount else 0 end),0) as content_1day_cw_ord_amount\n" +
            "           --当天成交人数\n" +
            "           ,coalesce(count(distinct case when intraday_ord_deal_flag = '1' then user_log_acct else null end),0) as content_1day_cw_ord_users\n" +
            "           --当天成交商品数\n" +
            "           ,coalesce(sum(case when intraday_ord_deal_flag = '1' then sale_qtty else 0 end),0) as content_1day_cw_ord_goods\n" +
            "\n" +
            "           --7天成交父单量\n" +
            "           ,coalesce(count(distinct case when intraweek_ord_deal_flag = '1' then parent_sale_ord_id else null end),0) as content_7days_pnt_cw_ord_num\n" +
            "           --7天成交子单量\n" +
            "           ,coalesce(count(distinct case when intraweek_ord_deal_flag = '1' then sale_ord_id else null end),0) as content_7days_cw_ord_num\n" +
            "           --7天成交金额\n" +
            "           ,coalesce(sum(case when intraweek_ord_deal_flag = '1' then after_prefr_amount else 0 end),0) as content_7days_cw_ord_amount\n" +
            "           --7天成交人数\n" +
            "           ,coalesce(count(distinct case when intraweek_ord_deal_flag = '1' then user_log_acct else null end),0) as content_7days_cw_ord_users\n" +
            "           --7天成交商品数\n" +
            "           ,coalesce(sum(case when intraweek_ord_deal_flag = '1' then sale_qtty else 0 end),0) as content_7days_cw_ord_goods\n" +
            "           ,case when lpad(bin(cast(grouping__id as bigint)),7,'0') = '0001111' then '11'   --0001111\n" +
            "                 when lpad(bin(cast(grouping__id as bigint)),7,'0') = '0111101' then '62'   --0111101\n" +
            "                 when lpad(bin(cast(grouping__id as bigint)),7,'0') = '0111001' then '63'\n" +
            "                 when lpad(bin(cast(grouping__id as bigint)),7,'0') = '0001001' then '64'\n" +
            "                 when lpad(bin(cast(grouping__id as bigint)),7,'0') = '0110111' then '65'\n" +
            "                 when lpad(bin(cast(grouping__id as bigint)),7,'0') = '0110101' then '66'\n" +
            "                 when lpad(bin(cast(grouping__id as bigint)),7,'0') = '0111110' then '67'\n" +
            "                 when lpad(bin(cast(grouping__id as bigint)),7,'0') = '0111100' then '68'\n" +
            "           end as lvl\n" +
            "      from adm.adm_d05_content_guide_ord_di\n" +
            "      where  dt = '2020-08-03'\n" +
            "          and content_id > 0\n" +
            "          and content_type_cd >= 0\n" +
            "          and content_type_cd not in ('99')  ---只过滤99\n" +
            "          and content_cate_cd > 0\n" +
            "          and (intraday_ord_intr_flag + intraday_ord_deal_flag + intraweek_ord_intr_flag + intraweek_ord_deal_flag)>0  ----去除无引入打标订单\n" +
            "          and ( after_prefr_amount < 100000 or\n" +
            "               (after_prefr_amount >= 100000 and (case when coalesce(check_account_tm, '') = '' then 0 else 1 end)= 1)\n" +
            "              )\n" +
            "      group by\n" +
            "           channel\n" +
            "          ,content_id\n" +
            "          ,content_type_cd\n" +
            "          ,content_cate_cd\n" +
            "          ,item_sku_id\n" +
            "          ,author_id\n" +
            "          ,content_sub_cha_id\n" +
            "        grouping sets(\n" +
            "           (channel,content_id,content_type_cd),  --11\n" +
            "           (channel,author_id),    --62\n" +
            "           (channel,item_sku_id,author_id),  --63\n" +
            "           (channel,content_id,content_type_cd,item_sku_id,author_id),--64\n" +
            "           (channel,content_cate_cd),  --65\n" +
            "           (channel,content_cate_cd,author_id),  --66\n" +
            "           (channel,content_sub_cha_id), --67\n" +
            "           (channel,author_id,content_sub_cha_id)  --68\n" +
            "        )\n" +
            ")ta\n" +
            ";\n" +
            "\"";

    @Test
    public void parseSparksql() {
        JSONObject jsonObject = BDPUtils.parseScript(correctResult);
        System.out.println(jsonObject);
    }

    @Test
    public void testFindTracking() {
        JSONObject trackingUrl = BDPUtils.findTrackingUrl("tracking URL: http://BJLFRZ-10k-149-67.hadoop.jd.local:50320/proxy/application_1571126973033_6989839/");
        System.out.println("trackingUrl = " + trackingUrl);
        JSONObject trackingUrl1 = BDPUtils.findTrackingUrl("tracking URL: http://BJXGSJZX-Thailand-50-204.hadoop.jd.local:50320/proxy/application_1576306108465_9435/");
        System.out.println("trackingUrl1 = " + trackingUrl1);
    }

    @Test
    public void getAMUrl() {
        ArrayList<String> matchedContent = new ArrayList<>();
        BDPUtils.matchKeywordByRow("substr(current_timestamp, 1, 19), date_add('2022-04-25', -1) ",
                new Keyword(1, "CURRENT_timestamp", 1, null, matchedContent, false, 0));
        assert matchedContent.size() == 1;

        assert BDPUtils.matchKeyword(amPattern, "applicationMasterIp", "ApplicationMaster host: BJLFRZ-10k-94-72.hadoop.jd.local")
                .getString("applicationMasterIp").equals("BJLFRZ-10k-94-72.hadoop.jd.local");
        assert BDPUtils.matchKeyword(amPattern, "applicationMasterIp", "ApplicationMaster host: N/A\nApplicationMaster host: *************")
                .getString("applicationMasterIp").equals("*************");
        assert BDPUtils.matchKeyword(amPattern, "applicationMasterIp", "ApplicationMaster host: N/A")
                .getString("applicationMasterIp") == null;

        JSONObject obj = BDPUtils.matchEventLog("21/03/23 13:58:46 [main] INFO EventLoggingListener: Logging events to hdfs://ns1002/user/spark/log_hope/2021-03-23/spark-application-1616479126581.lz4\n");
        JSONObject obj1 = BDPUtils.matchEventLog("21/04/13 09:37:19 [main] INFO EventLoggingListener: Logging events to hdfs://ns1/user/spark/log/2021-04-13/application_3958782110806_66380.lz4");
        JSONObject obj2 = BDPUtils.matchEventLog("21/04/13 09:41:13 [main] INFO SingleEventLogFileWriter: Logging events to hdfs://ns1/user/spark/log/2021-04-13/application_1073165109806_66585.lz4.inprogress");
        assert obj.getString(LOG_ANALYSIS_JDOS_APPID).equals("spark-application-1616479126581");
        assert obj1.getString(LOG_ANALYSIS_JDOS_APPID).equals("application_3958782110806_66380");
        assert obj2.getString(LOG_ANALYSIS_JDOS_APPID).equals("application_1073165109806_66585");
        assert obj.getString(LOG_ANALYSIS_JDOS_EVENT_PATH).equals("hdfs://ns1002/user/spark/log_hope/2021-03-23/spark-application-1616479126581.lz4");
        assert obj.getString(LOG_ANALYSIS_JDOS_EVENT_NS).equals("ns1002");
    }

    @Test
    public void uploadScriptSuit(){
        BDPUtils.uploadScript(gitProjectId, projectSpaceId,"cc dd", applicationId, "aa.sh");
    }

    @Test
    public void checkFileExist(){
        JSONObject jsonObject = BDPUtils.checkFileExist(gitProjectId, projectSpaceId, "ggg.sql");
        logger.info(jsonObject.toJSONString());
        assert  jsonObject.getJSONObject("obj").getBoolean("result");
        jsonObject = BDPUtils.checkFileExist(gitProjectId, projectSpaceId, "ggg-1.sql");
        logger.info(jsonObject.toJSONString());
        assert !jsonObject.getJSONObject("obj").getBoolean("result");
    }

    @Test
    public void getInfo(){
        JSONObject info = BDPUtils.getInfo(gitProjectId, "aa.sh", projectSpaceId);
        logger.info("info = " + info);
    }

    @Test
    public void saveContent(){
        JSONObject info = BDPUtils.getInfo(gitProjectId, "aa.sh", projectSpaceId);
        logger.info("info = " + info);
        JSONObject content = BDPUtils.saveContent(gitProjectId, "aa - " + System.currentTimeMillis(),
                "aa.sh", info.getJSONObject("obj").getString("version"));
        logger.info("content = " + content);
    }

    @Test
    public void logAnalysisByFile(){
        JSONObject jsonObject = new JSONObject();
        BDPUtils.logAnalysisByFile(true, jsonObject, "");
    }

    @Test
    public void testJSONObject(){
        String strObj = "{\"msg\":\"刘辉勃(liuhuibo6)于2024-01-19 15:44:49执行强制成功.\\n\",\"run_type\":\"normal\",\"runStatusCN\":\"成功\",\"cycle\":\"2024-01-19-14-00-00\",\"operator\":\"\",\"mem_limit_size\":20480,\"duration\":0,\"engine\":\"<a target=\\\"_blank\\\" style=\\\"color: red\\\" href=\\\"http://dp.jd.com/buffalo4/task/add.html?editType=edit&taskId=1284976\\\">请设置引擎</a>\",\"modified\":1705650289000,\"instance_task_type\":\"normal\",\"cpu_limit_size\":10.0,\"id\":1619882325,\"sche_time\":1705644000000,\"run_status\":\"success\",\"run_time\":1705650289000,\"created\":1705650289000,\"action_def_id\":2933152,\"end_time\":1705650289000,\"task_def_id\":1284976,\"buffaloType\":\"buffalo4\",\"queue_time\":1705650289000,\"run_batch_number\":\"buffalo7-190-103_1705030607532\",\"instance_id\":3577007890577227782,\"deleted\":false,\"mem_request_size\":13048,\"task_ins_id\":3577007890568839174,\"cpu_request_size\":0.94,\"instance_type\":2}";
        JSONObject jsonObject = JSONObject.parseObject(strObj);
        Timestamp runTime = (Timestamp)jsonObject.getTimestamp("run_time");
        System.out.println("runTime = " + runTime);
    }
}
