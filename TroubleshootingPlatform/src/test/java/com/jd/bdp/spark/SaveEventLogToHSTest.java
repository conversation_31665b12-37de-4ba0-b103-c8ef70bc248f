package com.jd.bdp.spark;

import com.jd.bdp.spark.web.BDPUtils;
import org.junit.Test;

import java.util.List;

public class SaveEventLogToHSTest {
    @Test
    public void testFindTracking() {
        String content = "drwxrwxrwx   - dd_edw           supergroup          0 2019-06-26 13:07 hdfs://ns100/user/spark/log/2019-05-09\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-07-03 18:10 hdfs://ns100/user/spark/log/2019-05-16\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-07-03 18:10 hdfs://ns100/user/spark/log/2019-05-17\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-06-30 16:08 hdfs://ns100/user/spark/log/2019-05-20\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-06-30 16:08 hdfs://ns100/user/spark/log/2019-05-22\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-06-30 19:16 hdfs://ns100/user/spark/log/2019-05-24\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-07-19 18:44 hdfs://ns100/user/spark/log/2019-05-28\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-06-30 19:16 hdfs://ns100/user/spark/log/2019-06-03\n" +
                "drwxrwxrwx   - dd_edw           supergroup          0 2019-06-26 13:07 hdfs://ns100/user/spark/log/2019-06-06\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-06-30 19:16 hdfs://ns100/user/spark/log/2019-06-08\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-06-30 16:08 hdfs://ns100/user/spark/log/2019-06-10\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-06-30 16:08 hdfs://ns100/user/spark/log/2019-06-12\n" +
                "drwxrwxrwx   - dd_edw           supergroup          0 2019-06-26 13:03 hdfs://ns100/user/spark/log/2019-06-14\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-07-03 21:14 hdfs://ns100/user/spark/log/2019-06-16\n" +
                "drwxrwxrwx   - mapred           supergroup          0 2019-06-30 19:16 hdfs://ns100/user/spark/log/2019-06-17";
        List<String> date = BDPUtils.convertToList(content);
        System.out.println("date = " + date);
    }
}
