package com.jd.bdp.common;

import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class JimDBUtilsTest {

    @Test
    public void showSparkVersion() {
//        String running_spark_version = JimDBUtils.getSparkInfoByKey(Arrays.asList("21/01/20 15:38:28 [main] INFO SparkContext: Running Spark version 2.4.5.online-JD2.4.5.17-202012291533"), "Running Spark version");
//        System.out.println("running_spark_version = " + running_spark_version);
        List<String> list = new ArrayList<>();
        list.add("hello");
        list.add("hello1");
        list.add("hello2");
        List<String> strings = list.subList(0, 0);
        System.out.println(strings.size());
        
        
        String skipDtsStr = "2025-01-01,2025-01-02,2025-01-03,2025-01-26";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> skipDts = Arrays.asList(skipDtsStr.split(","));
        for(String s : skipDts){
            System.out.println(s);
        }
        String now =  sdf.format(new Date());
        if(skipDts.contains(now)){
            System.out.println(String.format("=== just skip now: %s", now));
        }
        
        List<Integer> nums = new ArrayList<>();
        nums.add(10);
        nums.add(1);
        nums.add(5);
        nums.add(101);
        nums.add(6);
        nums.add(9);
        nums.sort((a, b) -> b - a);
        for(Integer num: nums){
            System.out.println(num);
        }
        
    }
}
