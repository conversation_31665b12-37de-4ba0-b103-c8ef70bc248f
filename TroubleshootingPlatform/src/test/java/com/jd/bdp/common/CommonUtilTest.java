package com.jd.bdp.common;

import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import org.junit.Test;
import static org.junit.Assert.assertEquals;

public class CommonUtilTest {

  @Test
  public void getTaskNodeId() {
    // check for valid double run task id.
    {
      assertEquals(1402, CommonUtil.getTaskNodeId("100"));
      assertEquals(1302, CommonUtil.getTaskNodeId("101"));
      Random random = new Random(System.currentTimeMillis());
      int doubleRunTaskId = random.nextInt(1000000)+1;
      int mod = doubleRunTaskId % 2;
      int expectedTaskNodeId = mod == 0? 1402:1302;
      assertEquals(expectedTaskNodeId, CommonUtil.getTaskNodeId(String.valueOf(doubleRunTaskId)));
    }
    // check for invalid double run task id.
    {
      Set<Integer> taskNodeIds = new HashSet<>();
      String doubleRunTaskId = "100_TEST";
      for (int i = 0; i < 100; i++) {
        taskNodeIds.add(CommonUtil.getTaskNodeId(doubleRunTaskId));
      }
      Set<Integer> expectedTaskNodeIds = new HashSet<>();
      expectedTaskNodeIds.add(1402);
      expectedTaskNodeIds.add(1302);
      assertEquals(expectedTaskNodeIds,taskNodeIds);
    }
  }
}
