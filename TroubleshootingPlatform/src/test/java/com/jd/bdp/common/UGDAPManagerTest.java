package com.jd.bdp.common;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

import java.io.IOException;

public class UGDAPManagerTest {

    @Test
    public void getMarketByErpV2() throws IOException {
        JSONObject result = UGDAPManager.getMarketByErpV2("wuguoxiao", 3);
        assert result.getIntValue("code") == 0;
    }

    @Test
    public void getBusinessLineByErp() throws IOException {
        JSONObject result = UGDAPManager.getBusinessLineByErp("wuguoxiao", "mart_sc");
        assert result.getIntValue("code") == 0;
    }

    @Test
    public void queryProductionAccountPowerV2() throws IOException {
        JSONObject result = UGDAPManager.queryProductionAccountPowerV2("wuguoxiao", "mart_sc");
        assert result.getIntValue("code") == 0;
    }

    @Test
    public void queryQueueByErpV2() throws IOException {
        JSONObject result = UGDAPManager.queryQueueByErpV2("wuguoxiao", "mart_sc", null);
        assert result.getIntValue("code") == 0;
    }
}
