package com.jd.bdp.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.Buffalo4TaskManager.CgroupConfigEnum;
import com.jd.bdp.scheduler.Scheduler;
import com.jd.utils.Curl;
import com.jd.utils.beans.HttpResponse;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

import static com.jd.bdp.common.Buffalo4TaskManager.buffalo4RerunInstanceByInstId;
import static com.jd.bdp.common.Buffalo4TaskManager.sortNumberFromStr;
import static org.asynchttpclient.util.Assertions.assertNotNull;
import static org.hibernate.validator.internal.util.Contracts.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

public class Buffalo4TaskManagerTest {

    @Test
    public void buffalo4GetTaskInfoTest() throws IOException {
        JSONObject jsonObject = Buffalo4TaskManager.buffalo4GetTaskInfo(645138L);
        assert jsonObject.getIntValue("code") == 0;
        JSONObject obj = jsonObject.getJSONObject("obj");
        assert obj.getIntValue("market") == 280;
        assert obj.getIntValue("queue") == 983;
        assert obj.getIntValue("account") == 2152;
        assert obj.getIntValue("taskNodeId") == 180;
        assert obj.getIntValue("taskNodeType") == 3;
        assert obj.getString("taskName").equals("exe_app_eco_pk_lme_item_rule_attr_union_da_spark_team_181077_494151941_1630643856467");

        jsonObject = Buffalo4TaskManager.buffalo4GetTaskInfo(520117L);
        obj = jsonObject.getJSONObject("obj");
        assert obj.getString("marketCode").equals("mart_ai");
        assert obj.getString("queueCode").equals("root.bdp_jmart_ai_union.bdp_jmart_ai_aicsd");
        assert obj.getString("accountCode").equals("mart_ai_aicsd_xz");
        assert obj.getString("clusterCode").equals("cairne");
        assert obj.getString("taskNodeType").equals("2");
        assert obj.getString("taskNodeId").equals("238");
        assert obj.getString("taskName").equals("aimart_exe_app_xzfz_non_adopt_info");
    }

    @Test
    public void getRunLogByTaskId() throws IOException {
        Long taskid = 1063853L;
        JSONObject runLogByTaskId = Buffalo4TaskManager.getRunLogByTaskId(taskid, "2024-10-15 00:00:00", "2024-10-16 00:00:00");
        System.out.println("getRunLogByTaskId = " + runLogByTaskId);

    }


    @Test
    public void saveWFTask() throws IOException {
        JSONObject jsonObject = Buffalo4TaskManager.buffalo5TaskCreate("BUFFALO_TEST_AAA5", "AAAAAA",
                106020, "wf", "manual", "wuguoxiao", 0, 3);
        System.out.println("saveWFTask = " + jsonObject);
        Long buffaloTaskId = jsonObject.getJSONObject("obj").getLong("taskId");

        assert jsonObject.getIntValue("code") == 0;
    }


    @Test
    public void stopInstanceByInstId() throws IOException {
        JSONObject jsonObject = Buffalo4TaskManager.stopInstanceByInstId("3620643873845412167,3620643928981635399,3620643824786735430,3620643824786735430,3620658158095040838,3620658168001986886,3620658169180586311,3620658169180586311,3620658172202582342,3620658206476337479,3620658252125045063,3620658264332566854,3620658299262730567,3620658328610275654,3620658328610275654,3620643945842737479,3620643945842737479,3620658217360556359,3620658158447362374,3620658243820323142,3620658257732829510,3620658276496048455,3620658303717081415,3620643960512315718,3620658312564965702,3620658317866565958,3620632807134462278,3620676102990594375,3620676132092772678,3620676161358528838,3620676214661841222,3620676214661841222,3620676220181545286,3620676221712466247,3620676221712466247,3620676228167500102,3620676232844149063,3620676232844149063,3620676261138923847,3620676261566742854,3620676261566742854,3620676261981978951,3620676261981978951,3620676304224911687,3620676304224911687,3620676310124200262,3620676310596059463,3620676321866154310,3620658224711074119,3620676107807752518,3620658331441430854,3620676175696757063,3620676254635655494,3620676242067423559,3620676256984465735,3620676260209885510,3620658295615783239,3620658202875527494,3620692015932506118,3620692015932506118");
        System.out.println("stopInstanceByInstId = " + jsonObject);
        assert jsonObject.getIntValue("code") == 0;
    }

    @Test
    public void buffalo5TaskActionCreate() throws IOException {
        JSONObject jsonObject = Buffalo4TaskManager.buffalo5TaskActionCreate(1502116, "2_4",
                "pyscript", "-1", 3, "1302", 983221,
                "data_compare1.sh", null, "", "", "", "");
        System.out.println("actionCreate = " + jsonObject);
        String actionId = jsonObject.getJSONObject("obj").getString("actionId");
    }

    @Test
    public void getActionListByTaskId() throws IOException {
        JSONObject jsonObject = Buffalo4TaskManager.getActionListByTaskId(1624835+"");
        System.out.println("actionCreate = " + jsonObject);
    }

    @Test
    public void buffalo5ScriptAdd() throws IOException {
        String filename = "data_compare.sh";
//        String filename = "run_spark.sh";
        File file = new File("/Users/<USER>/JD/IdeaProjects-git/bag_SparkAPM/TroubleshootingPlatform/src/main/resources/" + filename);
        JSONObject jsonObject = Buffalo4TaskManager.buffalo5ScriptAdd(106020, "wuguoxiao",
                filename, new FileInputStream(file), "data_compare3.sh");
        System.out.println("actionCreate = " + jsonObject);
        assert jsonObject.getIntValue("code") == 0;

        filename = "run_spark.sh";
//        String filename = "run_spark.sh";
        file = new File("/Users/<USER>/JD/IdeaProjects-git/bag_SparkAPM/TroubleshootingPlatform/src/main/resources/" + filename);
        jsonObject = Buffalo4TaskManager.buffalo5ScriptAdd(106020, "wuguoxiao",
                filename, new FileInputStream(file), "run_spark3.sh");
        System.out.println("actionCreate = " + jsonObject);
        assert jsonObject.getIntValue("code") == 0;
    }

    @Test
    public void buffalo5ScriptUpdate() throws IOException {
        String fileName = "run_spark3.sh";
        File file = new File("/tmp/double_run/"+fileName);
        JSONObject jsonObject = Buffalo4TaskManager.buffalo5ScriptUpdate(200081,"",
            "wuguoxiao", fileName, "spark,hive",fileName,new FileInputStream(file), fileName);
        System.out.println("actionCreate = " + jsonObject);
        assert jsonObject.getIntValue("code") == 0;

        fileName="data_compare3.sh";
        file = new File("/tmp/double_run/"+fileName);
        jsonObject = Buffalo4TaskManager.buffalo5ScriptUpdate(200030,"update script","wuguoxiao", fileName,
          "spark,hive",fileName,new FileInputStream(file),fileName);
        System.out.println("actionCreate = " + jsonObject);
        assert jsonObject.getIntValue("code") == 0;
    }


    @Test
    public void integrationTest() throws IOException {
        String testFlag = "";
        String source = "BUFFALO";
//        String taskid = "1462308_1966995114";
//        String index = "36";
//        String cluster = "10k";

        FileUtil.CommonResult commonResult = FileUtil.readContent("/Users/<USER>/JD/IdeaProjects-git/bag_SparkAPM/TroubleshootingPlatform/src/test/resources/buffalo.txt");
        for (String s : commonResult.getContent().split("\n")) {
            System.out.println("s = " + s);
            String[] split = s.split(",");
            String cluster = split[0];
            String taskid = split[1] + "_" + split[2];
            String index = split[3];
            String originVersion = "2_4;";
            String compareVersion = "3_4;";
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(testFlag, source, taskid, index, cluster, originVersion, compareVersion);
            System.out.println("result = " + result);
        }
    }

    @Test
    public void integrationTest1() throws IOException {
        String testFlag = "";
        String source = "BUFFALO";
        String taskid = "1323466_2078031161";
        String index = "1";
        String originVersion = "2_4;";
        String compareVersion = "3_4;";
        JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(testFlag, source, taskid, index, null, originVersion, compareVersion);
        System.out.println("result = " + result);
        assert !result.isEmpty();
    }

    @Test
    public void integrationTest2() throws IOException {
        String testFlag = "";
        String source = "BUFFALO";
        String taskidAndIns = "1323466_2078031111";
        String index = "1";
        String doubleRunId = "1496738";
        JSONObject result = Buffalo4TaskManager.updateTaskNameAndActions(testFlag,source,taskidAndIns, index, null, doubleRunId,
            CgroupConfigEnum._2C4G);
        System.out.println("result = " + result);
        assert !result.isEmpty();
    }

    @Test
    public void integrationTest3() throws IOException {
        String testFlag = "";
        String source = "BUFFALO";
        String taskidAndIns = "1323466_2078031124";
        String index = "1,2,3";
        String doubleRunId = "1496629";
        JSONObject result = Buffalo4TaskManager.updateTaskNameAndActionsNew(testFlag,source,taskidAndIns, index, null, doubleRunId,
            CgroupConfigEnum._2C4G,"2.4;","3.4;");
        System.out.println("result = " + result);
        assert !result.isEmpty();
    }

    @Test
    public void batchModifyMarketInfo() throws IOException {
        ArrayList<Integer> actionIds = new ArrayList<Integer>();
        actionIds.add(1637936);
        JSONObject spark = Buffalo4TaskManager.batchModifyMarketInfo(new ArrayList<Integer>(), actionIds,
                "spark", "3.0", Scheduler.commonBean.getBuffaloApiUserToken(), "buffalo4.bdp.jd.local");
        System.out.println("spark = " + spark);
        assert spark.getBooleanValue("success");
    }

    @Test
    public void getRunLogInfo() throws IOException {
        JSONObject runLogInfo = Buffalo4TaskManager.getRunLogInfo(563448669L, null, null);
        System.out.println("runLogInfo = " + runLogInfo);
    }

    @Test
    public void testSortNumberFromStr_NormalCase() {
        String input = "3,1,4,1,5,9,2,6";
        List<Integer> expected = Arrays.asList(1, 1, 2, 3, 4, 5, 6, 9);
        List<Integer> result = sortNumberFromStr(input);
        assertEquals(expected, result);
    }

    @Test
    public void testSortNumberFromStr_WithNonIntegers() {
        String input = "3,a,4,1,5,hello,9,2,6";
        List<Integer> expected = Arrays.asList(1, 2, 3, 4, 5, 6, 9);
        List<Integer> result = sortNumberFromStr(input);
        assertEquals(expected, result);
    }

    @Test
    public void testSortNumberFromStr_EmptyString() {
        String input = "";
        List<Integer> expected = Arrays.asList();
        List<Integer> result = sortNumberFromStr(input);
        assertEquals(expected, result);
    }

    @Test
    public void testSortNumberFromStr_OnlySpaces() {
        String input = "   ";
        List<Integer> expected = Arrays.asList();
        List<Integer> result = sortNumberFromStr(input);
        assertEquals(expected, result);
    }

    @Test
    public void testSortNumberFromStr_WithNegatives() {
        String input = "-3,1,-4,1,-5,9,-2,6";
        List<Integer> expected = Arrays.asList(-5, -4, -3, -2, 1, 1, 6, 9);
        List<Integer> result = sortNumberFromStr(input);
        assertEquals(expected, result);
    }

    @Test
    public void testSortNumberFromStr_WithDuplicates() {
        String input = "1,2,2,3,3,3";
        List<Integer> expected = Arrays.asList(1, 2, 2, 3, 3, 3);
        List<Integer> result = sortNumberFromStr(input);
        List<Integer> sortedExpected = Arrays.asList(1, 2, 3);
        Collections.sort(result);
        assertEquals(sortedExpected, result);
    }

    @Test
    public void getInstanceParentDependList() throws IOException {
        JSONObject instanceParentDependList = Buffalo4TaskManager.getInstanceParentDependList(3626543091181158406L);
        System.out.println("instanceParentDependList = " + instanceParentDependList);
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithValidVersions() {
        // 测试使用有效的 originVersion 和 compareVersion 参数

        String testFlag = "testFlagValue";
        String source = "BUFFALO";
        String taskid = "12345q";
        String indexes = "1,2,3";
        String cluster = "clusterA";
        String originVersion = "2_5";
        String compareVersion = "3_5";

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    null, // cluster 参数为 null
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");

            assertTrue(result.containsKey("dual-run ID"), "结果应包含 'dual-run ID' 键");

            int dualRunId = result.getIntValue("dual-run ID");
            assertTrue(dualRunId > 0, "'dual-run ID' 应为正整数");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithDefaultVersions() {

        String testFlag = "";
        String source = "IDEONLINE";
        String taskid = "67890";
        String indexes = "4,5,6";
        String cluster = "clusterB";
        String originVersion = null; // 应默认设置为 "2_4;"
        String compareVersion = "";   // 应默认设置为 "3_4;"

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    null,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");

            assertTrue(result.containsKey("dual-run ID"), "结果应包含 'dual-run ID' 键");

            int dualRunId = result.getIntValue("dual-run ID");
            assertTrue(dualRunId > 0, "'dual-run ID' 应为正整数");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateTaskNameAndActionsNew_WithValidInputs() {

        String testFlag = "updateTestFlag";
        String source = "BUFFALO";
        String originTaskIdAndIns = "12345_67890";
        String indexes = "7,8,9";
        String cluster = "clusterC";
        String doubleRunTaskId = "98765";
        CgroupConfigEnum cgroupConfig = CgroupConfigEnum._1C1G;
        String originVersion = "2_6";
        String compareVersion = "3_6";

        try {
            JSONObject result = Buffalo4TaskManager.updateTaskNameAndActionsNew(
                    testFlag,
                    source,
                    originTaskIdAndIns,
                    indexes,
                    null, // cluster 参数为 null
                    doubleRunTaskId,
                    cgroupConfig,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertEquals("success", result.getString("status"), "任务更新应成功");

        } catch (Exception e) {
            fail("调用 updateTaskNameAndActionsNew 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateTaskNameAndActionsNew_WithDefaultVersions() {

        String testFlag = "updateTestFlagDefault";
        String source = "BUFFALO";
        String originTaskIdAndIns = "54321_09876";
        String indexes = "10,11,12";
        String cluster = "clusterD";
        String doubleRunTaskId = "87654";
        CgroupConfigEnum cgroupConfig = CgroupConfigEnum._1C1G;
        String originVersion = null; // 应默认设置为 "2_4;"
        String compareVersion = "";   // 应默认设置为 "3_4;"

        try {
            JSONObject result = Buffalo4TaskManager.updateTaskNameAndActionsNew(
                    testFlag,
                    source,
                    originTaskIdAndIns,
                    indexes,
                    null,
                    doubleRunTaskId,
                    cgroupConfig,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertEquals("success", result.getString("status"), "任务更新应成功");

        } catch (Exception e) {
            fail("调用 updateTaskNameAndActionsNew 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithInvalidIndexes() {

        String testFlag = "invalidIndexesTest";
        String source = "BUFFALO";
        String taskid = "invalid_taskid";
        String indexes = "a,b,c"; // 无效的索引值
        String cluster = "clusterE";
        String originVersion = "2_7";
        String compareVersion = "3_7";

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    null,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("Incorrect `indexes`"), "错误消息应提示 `indexes` 参数不正确");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateTaskNameAndActionsNew_WithInvalidDoubleRunTaskId() {

        String testFlag = "invalidDoubleRunIdTest";
        String source = "BUFFALO";
        String originTaskIdAndIns = "invalid_origin_task";
        String indexes = "13,14,15";
        String cluster = "clusterF";
        String doubleRunTaskId = "invalid_id";
        CgroupConfigEnum cgroupConfig = CgroupConfigEnum._1C1G;
        String originVersion = "2_8";
        String compareVersion = "3_8";

        try {
            JSONObject result = Buffalo4TaskManager.updateTaskNameAndActionsNew(
                    testFlag,
                    source,
                    originTaskIdAndIns,
                    indexes,
                    null,
                    doubleRunTaskId,
                    cgroupConfig,
                    originVersion,
                    compareVersion
            );
            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("Buffalo task not found"), "错误消息应提示任务未找到");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("Buffalo task not found"), "异常消息应提示任务未找到");
        }
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithNullTestFlag() {
        // 测试 testFlag 为 null 的情况

        String testFlag = null;
        String source = "BUFFALO";
        String taskid = "123456";
        String indexes = "1,2,3";
        String cluster = "clusterG";
        String originVersion = "2_9";
        String compareVersion = "3_9";

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    cluster,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("dual-run ID"), "结果应包含 'dual-run ID' 键");
            int dualRunId = result.getIntValue("dual-run ID");
            assertTrue(dualRunId > 0, "'dual-run ID' 应为正整数");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithInvalidSource() {
        // 测试无效的 source 参数

        String testFlag = "invalidSourceTest";
        String source = "INVALID_SOURCE";
        String taskid = "123457";
        String indexes = "4,5,6";
        String cluster = "clusterH";
        String originVersion = "2_10";
        String compareVersion = "3_10";

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    cluster,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("Invalid `source`"), "错误消息应提示 `source` 参数不正确");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithMissingTaskId() {
        // 测试缺少 taskid 参数

        String testFlag = "missingTaskIdTest";
        String source = "BUFFALO";
        String taskid = ""; // 为空字符串
        String indexes = "7,8,9";
        String cluster = "clusterI";
        String originVersion = "2_11";
        String compareVersion = "3_11";

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    cluster,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("Missing `taskid`"), "错误消息应提示缺少 `taskid` 参数");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithInvalidOriginVersion() {
        // 测试无效的 originVersion 格式

        String testFlag = "invalidOriginVersionTest";
        String source = "BUFFALO";
        String taskid = "123458";
        String indexes = "10,11,12";
        String cluster = "clusterJ";
        String originVersion = "invalid_version";
        String compareVersion = "3_12";

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    cluster,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("Invalid `originVersion`"), "错误消息应提示 `originVersion` 参数不正确");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithNullCompareVersion() {
        // 测试 compareVersion 为 null，应默认设置为 "3_4;"

        String testFlag = "nullCompareVersionTest";
        String source = "BUFFALO";
        String taskid = "123459";
        String indexes = "13,14,15";
        String cluster = "clusterK";
        String originVersion = "2_12";
        String compareVersion = null; // 应默认设置为 "3_4;"

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    cluster,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("dual-run ID"), "结果应包含 'dual-run ID' 键");
            int dualRunId = result.getIntValue("dual-run ID");
            assertTrue(dualRunId > 0, "'dual-run ID' 应为正整数");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testCreateDoubleRunBuffaloTask_WithEmptyIndexes() {
        // 测试 indexes 为空字符串

        String testFlag = "emptyIndexesTest";
        String source = "BUFFALO";
        String taskid = "123460";
        String indexes = "";
        String cluster = "clusterL";
        String originVersion = "2_13";
        String compareVersion = "3_13";

        try {
            JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(
                    testFlag,
                    source,
                    taskid,
                    indexes,
                    cluster,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("Missing or invalid `indexes`"), "错误消息应提示缺少或无效的 `indexes` 参数");

        } catch (Exception e) {
            fail("调用 createDoubleRunBuffaloTask 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateTaskNameAndActionsNew_WithNullCgroupConfig() {
        // 测试 cgroupConfig 为 null

        String testFlag = "nullCgroupConfigTest";
        String source = "BUFFALO";
        String originTaskIdAndIns = "12345_67891";
        String indexes = "16,17,18";
        String cluster = "clusterM";
        String doubleRunTaskId = "98766";
        CgroupConfigEnum cgroupConfig = null;
        String originVersion = "2_14";
        String compareVersion = "3_14";

        try {
            JSONObject result = Buffalo4TaskManager.updateTaskNameAndActionsNew(
                    testFlag,
                    source,
                    originTaskIdAndIns,
                    indexes,
                    cluster,
                    doubleRunTaskId,
                    cgroupConfig,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertEquals("failure", result.getString("status"), "任务更新应失败");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("`cgroupConfig` cannot be null"), "错误消息应提示 `cgroupConfig` 不能为空");

        } catch (Exception e) {
            fail("调用 updateTaskNameAndActionsNew 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateTaskNameAndActionsNew_WithInvalidCgroupConfig() {
        // 测试无效的 cgroupConfig 枚举值

        String testFlag = "invalidCgroupConfigTest";
        String source = "BUFFALO";
        String originTaskIdAndIns = "12345_67892";
        String indexes = "19,20,21";
        String cluster = "clusterN";
        String doubleRunTaskId = "98767";
        CgroupConfigEnum cgroupConfig = CgroupConfigEnum._2C2G; // 假设此枚举值无效
        String originVersion = "2_15";
        String compareVersion = "3_15";

        try {
            JSONObject result = Buffalo4TaskManager.updateTaskNameAndActionsNew(
                    testFlag,
                    source,
                    originTaskIdAndIns,
                    indexes,
                    cluster,
                    doubleRunTaskId,
                    cgroupConfig,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertEquals("failure", result.getString("status"), "任务更新应失败");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("Invalid `cgroupConfig`"), "错误消息应提示 `cgroupConfig` 参数不正确");

        } catch (Exception e) {
            fail("调用 updateTaskNameAndActionsNew 时抛出异常: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateTaskNameAndActionsNew_WithMissingDoubleRunTaskId() {
        // 测试缺少 doubleRunTaskId 参数

        String testFlag = "missingDoubleRunTaskIdTest";
        String source = "BUFFALO";
        String originTaskIdAndIns = "12345_67893";
        String indexes = "22,23,24";
        String cluster = "clusterO";
        String doubleRunTaskId = ""; // 为空字符串
        CgroupConfigEnum cgroupConfig = CgroupConfigEnum._1C1G;
        String originVersion = "2_16";
        String compareVersion = "3_16";

        try {
            JSONObject result = Buffalo4TaskManager.updateTaskNameAndActionsNew(
                    testFlag,
                    source,
                    originTaskIdAndIns,
                    indexes,
                    cluster,
                    doubleRunTaskId,
                    cgroupConfig,
                    originVersion,
                    compareVersion
            );

            assertNotNull(result, "返回结果应不为 null");
            assertTrue(result.containsKey("msg"), "结果应包含 'msg' 键");
            String msg = result.getString("msg");
            assertTrue(msg.contains("Missing `doubleRunTaskId`"), "错误消息应提示缺少 `doubleRunTaskId` 参数");

        } catch (Exception e) {
            fail("调用 updateTaskNameAndActionsNew 时抛出异常: " + e.getMessage());
        }
    }


}
