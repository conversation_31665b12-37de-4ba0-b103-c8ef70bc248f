SELECT
	qt.cluster_a,
	qt.db_name_a,
	qt.tbl_name_a,
	qt.cluster_b,
	qt.db_name_b,
	qt.tbl_name_b,
	CAST(qt.tb_name_similarity AS        DECIMAL(12, 3)) * 100 AS tb_name_similarity,
	CAST(qt.column_similarity AS         DECIMAL(12, 3)) * 100 AS column_similarity,
	CAST(qt.table_lineage_similirity AS  DECIMAL(12, 3)) * 100 AS table_lineage_similirity,
	CAST(qt.column_lineage_similirity AS DECIMAL(12, 3)) * 100 AS column_lineage_similirity,
	CAST(qt.storage_similirity AS        DECIMAL(12, 3)) * 100 AS storage_similirity,
	CAST(qt.storage_trend_similirity AS  DECIMAL(12, 3)) * 100 AS storage_trend_similirity,
	CAST(qt.similarity AS                DECIMAL(12, 3)) AS similarity,
	qt.part_name_a,
	qt.part_name_b,
	qt.part_name_similarity,
	qt.s_size_mb,
	qt.s_invalidfile_size_mb,
	qt.s_coldfile_size_mb,
	qt.s_tbl_master_dept,
	qt.s_tbl_master,
	qt.s_tbl_buss_type,
	qt.s_tbl_topic,
	qt.t_size_mb,
	qt.t_invalidfile_size_mb,
	qt.t_coldfile_size_mb,
	qt.t_tbl_master_dept,
	qt.t_tbl_master,
	qt.call_times_31,
	qt.call_times_93,
	qt.expires_days,
	qt.tbl_buss_type,
	qt.tbl_topic,
	qt.tbl_storage_cost,
	qt.tbl_calc_cost,
	qt.tbl_cost,
	qt.buffalo_down_tk_count,
	qt.ide_down_tk_count,
	qt.down_tk_count,
	qt.priority,
	qt.db_type,
	qt.tbl_type,
	qt.tbl_offline_income,
	qt.tbl_offline_cost,
	qt.gonven_advise,
	tb_topic_a,
	tb_topic_b
FROM
	(
		SELECT
			ss.cluster_a,
			ss.db_name_a,
			ss.tbl_name_a,
			ss.cluster_b,
			ss.db_name_b,
			ss.tbl_name_b,
			ss.tb_name_similarity,
			ss.column_similarity,
			ss.table_lineage_similirity,
			ss.column_lineage_similirity,
			ss.storage_similirity,
			ss.storage_trend_similirity,
			ss.similarity,
			ss.part_name_a,
			ss.part_name_b,
			ss.part_name_similarity,
			CAST(mt_s.multi_num_size_mb AS            DECIMAL(16, 4)) AS s_size_mb,
			CAST(mt_s.muti_invalidfile_num_size_mb AS DECIMAL(16, 4)) AS s_invalidfile_size_mb,
			CAST(mt_s.muti_coldfile_num_size_mb AS    DECIMAL(16, 4)) AS s_coldfile_size_mb,
			mt_s.tbl_master_dept AS s_tbl_master_dept,
			mt_s.tbl_master AS s_tbl_master,
			mt_s.tbl_comment AS s_tbl_comment,
			mt_s.tbl_buss_type AS s_tbl_buss_type,
			mt_s.tbl_topic AS s_tbl_topic,
			CAST(mt_t.multi_num_size_mb AS            DECIMAL(16, 4)) AS t_size_mb,
			CAST(mt_t.muti_invalidfile_num_size_mb AS DECIMAL(16, 4)) AS t_invalidfile_size_mb,
			CAST(mt_t.muti_coldfile_num_size_mb AS    DECIMAL(16, 4)) AS t_coldfile_size_mb,
			mt_t.tbl_master_dept AS t_tbl_master_dept,
			mt_t.tbl_master AS t_tbl_master,
			mt_t.call_times_31,
			mt_t.call_times_93,
			mt_t.expires_days,
			mt_t.tbl_buss_type,
			mt_t.tbl_topic,
			mt_t.tbl_storage_cost,
			mt_t.tbl_calc_cost,
			mt_t.tbl_cost,
			mt_t.buffalo_down_tk_count,
			mt_t.ide_down_tk_count,
			mt_t.down_tk_count,
			mt_t.priority,
			CASE
				WHEN mt_t.xt_db_type = 'DEV'
				THEN '开发库'
				WHEN mt_t.xt_db_type = 'PROD'
					AND mt_t.db_classify = '1'
				THEN '临时库'
				ELSE '正式库'
			END AS db_type,
			CASE
				WHEN mt_t.is_test = '1'
				THEN '测试表'
				WHEN mt_t.is_tmp = '1'
				THEN '临时表'
				WHEN mt_t.is_dev = '1'
				THEN '开发表'
				WHEN mt_t.is_backup = '1'
				THEN '备份表'
				WHEN mt_t.is_refresh = '1'
				THEN '刷数表'
				WHEN mt_t.is_press = '1'
				THEN '压测表'
				WHEN mt_t.is_mid = '1'
				THEN '中间表'
				WHEN mt_t.is_dts = '1'
				THEN '同步表'
				ELSE '生产表'
			END AS tbl_type,
			(365 * mt_t.tbl_cost) AS tbl_offline_income,
			(4500 * mt_t.buffalo_down_tk_count) AS tbl_offline_cost,
			CASE
				WHEN
					(
						mt_t.is_test = '1'
						OR mt_t.is_tmp = '1'
						OR mt_t.is_dev = '1'
						OR mt_t.is_press = '1'
						OR mt_t.is_refresh = '1'
						OR mt_t.xt_db_type = 'DEV'
					)
					AND mt_t.expires_days > 31
				THEN '建议生命周期设置小于31天'
				WHEN mt_t.is_backup = '1'
					AND mt_t.expires_days > 93
				THEN '建议生命周期设置小于93天'
					-- WHEN mt_t.is_mid = '1'
					--  AND mt_t.expires_days > 31
					-- THEN '建议生命周期设置小于31天'
				WHEN mt_s.is_dts = '1'
				THEN '暂不处理'
				WHEN COALESCE(mt_t.tbl_cost, 0) <> 0
					AND 4500 * mt_t.buffalo_down_tk_count /(365 * mt_t.tbl_cost) > 5
				THEN '暂不处理'
				WHEN mt_t.buffalo_down_tk_count > 6
					AND 365 * mt_t.tbl_cost - 4500 * mt_t.buffalo_down_tk_count < 0
				THEN '暂不处理'
				ELSE '建议下线'
			END AS gonven_advise,
			tb_topic_a,
			tb_topic_b
		FROM
			(
				SELECT
					cluster_a,
					db_name_a,
					tbl_name_a,
					cluster_b,
					db_name_b,
					tbl_name_b,
					tb_name_similarity,
					column_similarity,
					table_lineage_similirity,
					column_lineage_similirity,
					storage_similirity,
					storage_trend_similirity,
					similarity,
					part_name_a,
					part_name_b,
					part_name_similarity,
					tb_topic_a,
					tb_topic_b
				FROM
					gdm.gdm_jdr_plat_d99_platdata_similarmodel_info_da
				WHERE
					dt = sysdate( - 2)
					AND db_name_a NOT IN('bdm', 'stg')
					AND db_name_b NOT IN('bdm', 'stg')
					AND db_name_a NOT LIKE '%\_dev' ESCAPE '\'
					AND db_name_b NOT LIKE '%\_dev' ESCAPE '\'
					AND similarity >= --SIMILARITY--
					AND sub_type = 'fa-son'
			)
			ss
		JOIN
			(
				SELECT
					cluster_name,
					meta_cluster_code,
					db_name,
					tbl_id,
					tbl_name,
					tbl_comment,
					num_size_mb,
					multi_num_size_mb,
					muti_invalidfile_num_size_mb,
					muti_coldfile_num_size_mb,
					tbl_master,
					tbl_master_dept,
					call_times_31,
					call_times_93,
					CASE
						WHEN COALESCE(expires_days, '') = ''
						THEN '9999999'
						ELSE expires_days
					END AS expires_days,
					advice_expires_days,
					tbl_buss_type,
					tbl_topic,
					is_test,
					is_tmp,
					is_dev,
					is_backup,
					is_refresh,
					is_press,
					is_mid,
					is_dts,
					xt_db_type,
					db_classify,
					identified_flag,
					total_quality_score,
					cost_final_scores,
					value_final_scores,
					quality_label_name,
					cost_label_name,
					value_label_name,
					tbl_storage_cost,
					tbl_calc_cost,
					tbl_cost,
					priority,
					buffalo_down_tk_count,
					ide_down_tk_count,
					down_tk_count
				FROM
					tmp.tmp_similarity_model_govern_tbl_info
			)
			mt_s
		ON
			ss.cluster_a = mt_s.cluster_name
			AND ss.db_name_a = mt_s.db_name
			AND ss.tbl_name_a = mt_s.tbl_name
		JOIN
			(
				SELECT
					cluster_name,
					meta_cluster_code,
					db_name,
					tbl_id,
					tbl_name,
					tbl_comment,
					num_size_mb,
					multi_num_size_mb,
					muti_invalidfile_num_size_mb,
					muti_coldfile_num_size_mb,
					tbl_master,
					tbl_master_dept,
					call_times_31,
					call_times_93,
					CASE
						WHEN COALESCE(expires_days, '') = ''
						THEN '9999999'
						ELSE expires_days
					END AS expires_days,
					advice_expires_days,
					tbl_buss_type,
					tbl_topic,
					is_test,
					is_tmp,
					is_dev,
					is_backup,
					is_refresh,
					is_press,
					is_mid,
					is_dts,
					xt_db_type,
					db_classify,
					identified_flag,
					total_quality_score,
					cost_final_scores,
					value_final_scores,
					quality_label_name,
					cost_label_name,
					value_label_name,
					tbl_storage_cost,
					tbl_calc_cost,
					tbl_cost,
					priority,
					buffalo_down_tk_count,
					ide_down_tk_count,
					down_tk_count
				FROM
					tmp.tmp_similarity_model_govern_tbl_info
			)
			mt_t
		ON
			ss.cluster_b = mt_t.cluster_name
			AND ss.db_name_b = mt_t.db_name
			AND ss.tbl_name_b = mt_t.tbl_name
		WHERE
			mt_s.tbl_master_dept LIKE '--DEPT--'
			AND mt_t.tbl_master_dept LIKE '--DEPT--'
	)
	qt
WHERE
	qt.db_name_a NOT LIKE '%\_tmp' ESCAPE '\'
	AND CONCAT(qt.db_name_a, '.', qt.tbl_name_a) <> CONCAT(qt.db_name_b, '.', qt.tbl_name_b)
ORDER BY
	qt.s_size_mb DESC LIMIT 5000