drop table if exists dev.spark_team_temp_downstream;
create table if not exists dev.spark_team_temp_downstream stored as orcfile as
select distinct task_type, task_id, action_id, priority, bee_source, job_cycle, c.from_similar_type, cluster_code,
a.db_name, a.tbl_name, engine, b.param_source_type,b.is_hivetask, test_db_name, test_tbl_name
from gdm.gdm_jdr_plat_platdata_lineage_task_tbl_da as a
left join (select distinct source, bufflo_id, engine, param_source_type,'Y' as is_hivetask from app.app_hivetask_log where dt = sysdate(-2) and source='buffalo4') as b on a.task_id = b.bufflo_id
left join (
select db_name_a as db_name, tbl_name_a as tbl_name, 'Y' as from_similar_type, test_db_name_a as test_db_name, test_tbl_name_a as test_tbl_name
from dev.spark_team_temp_similar_model where status_type = 'Y' and is_similar_type = 'Y'
union
select db_name_b as db_name, tbl_name_b as tbl_name, 'Y' as from_similar_type, test_db_name_b as test_db_name, test_tbl_name_b as test_tbl_name
from dev.spark_team_temp_similar_model where status_type = 'Y' and is_similar_type = 'Y'
) as c on a.db_name = c.db_name and a.tbl_name = c.tbl_name
where a.dt = sysdate(-2) and bee_source_original != 'IDE_JOB' and oper_mode != 'write' and db_flag != 'DEV'
 and task_type != 'hive2hive' and task_status != '禁用';