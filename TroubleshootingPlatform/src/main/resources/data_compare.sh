#!/bin/bash
dataCheckOptimize=0
if [ "$#" -ne 6 ] && [ "$#" -ne 5 ]; then
  echo "错误：必须传入 5或者6 个参数。"
  echo "参数说明："
  echo "1. 参数1 - 表1"
  echo "2. 参数2 - 表2"
  echo "3. 参数3 - 任务id或日志id"
  echo "4. 参数4 - 对数包的http地址"
  echo "5. 参数5 - 集群（10k或hope）"
  echo "6. 参数6 - 是否开启优化【可选参数】"
  exit 1
elif [ "$#" -eq 6 ]; then
  dataCheckOptimize=$6
fi

tbl1=$1
tbl2=$2
TASK_ID=$3
doubleRun=$4
cluster=$5
accu=0.001

git clone https://coding.jd.com/benchmark/DoubleRunScript.git -b bdp_ide_branch
source DoubleRunScript/${TASK_ID}.sh

set -x

export BEE_SOURCE=SPARK_3_UPGRADE
export BEE_USER=org.nature.data1
if [[ $SPARK_HOME != *spark_3.0 ]]; then
  export SPARK_HOME=${SPARK_HOME}_3.0;
fi

if [[ $cluster == '10k' ]];then
  queue=root.bdp_jdw_dd_edw_bdp_spark
  hmsUrl="thrift://**************:10113"
  dbName="wangriyu_test"
else
  queue=bdp_jdw_dd_edw_spark
  hmsUrl="thrift://**************:10114"
  dbName="zmy_test"
fi

fileName2_4="${TASK_ID}_2_4.sql"
fileName3_4="${TASK_ID}_3_4.sql"
table_2_4=`grep -o "${dbName}\.[^ ]*" $fileName2_4 | awk -F. '{print $1"."$2}' | sort | uniq`
table_3_4=`grep -o "${dbName}\.[^ ]*" $fileName3_4 | awk -F. '{print $1"."$2}' | sort | uniq`

if [ -z "$(echo "$table_2_4" | xargs)" ]; then
  echo "没找到表名"
  exit 100
else
  echo "字符串不为空"
fi

if [ -z "$(echo "$table_3_4" | xargs)" ]; then
  echo "没找到表名"
  exit 100
else
  echo "字符串不为空"
fi

# 版本一：远程下载对数代码
# tgzname=`basename $doubleRun`
# dirname=`basename $doubleRun .tar`
# if [ -e "$tgzname" ]; then
#     \rm $tgzname
#     echo "$tgzname has been deleted."
# fi
# if [ -e "$dirname" ]; then
#     \rm -r $dirname
#     echo "$dirname has been deleted."
# fi
# wget $doubleRun
# tar -xf $tgzname

# 版本二：使用coding上的对数代码
cd DoubleRunScript

spark-submit --class com.jd.compute.ResultCompare --queue $queue \
  --conf spark.jd.kongming.optimize.enable=false \
  --conf spark.hadoop.hive.metastore.uris=$hmsUrl \
  --conf spark.hadoop.hive.exec.dynamic.partition.mode=nonstrict \
  --conf spark.sql.source=HiveTask \
  --conf spark.isLoadHivercFile=true \
  --conf spark.sql.tempudf.ignoreIfExists=true \
  --conf spark.sql.crossJoin.enabled=true \
  --conf spark.hadoop.dfs.pipeline.support.qos=true  \
  --conf spark.hadoop.dfs.pipeline.qos.value=128  \
  --conf spark.hadoop.dfs.client.read.support.caller.context.qos=true  \
  --conf spark.hadoop.dfs.client.read.qos.value=128  \
  --conf spark.hadoop.dfs.client.transfer.qos.value=128  \
  --conf spark.network.support.qos=true  \
  --conf spark.network.qos.value=128  \
  --conf spark.submit.deployMode=client \
  --conf spark.dynamicAllocation.enabled=true \
  --conf spark.shuffle.service.enabled=true \
  --conf spark.speculation=true \
  --conf spark.sql.hive.convertMetastoreOrc=true \
  --conf spark.sql.adaptive.enabled=true \
  --conf spark.sql.parser.quotedRegexColumnNames=true \
  --conf spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version=2 \
  --conf spark.sql.retry.with.hive=false \
  --hiveconf hive.exec.orc.split.strategy=BI \
  --conf spark.hadoop.dfs.client.block.pread.support.switch.enabled=false \
  --conf spark.sql.viewPermission.enabled=true \
  --conf spark.sql.parser.quotedRegexColumnNames.isRegex.enabled=true \
  --conf spark.sql.externalCatalog.requireDbExists.enabled=false \
  double_run/lib/double_run-1.0-SNAPSHOT.jar "${table_2_4}" "${table_3_4}" ${accu} ${dataCheckOptimize}

exit_status=$?
if [[ $exit_status -eq 7 ]]; then
  echo "返回状态为7，代表两张表的数据均为空"
  exit 0
else
  echo "返回状态为 $exit_status"
  exit $exit_status
fi
