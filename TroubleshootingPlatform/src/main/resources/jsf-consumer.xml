<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf  http://jsf.jd.com/schema/jsf/jsf.xsd">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="i.jsf.jd.com"/>

    <jsf:consumer id="bloodJsfInterface" interface="com.jd.unified.metadata.blood.service.BloodJsfInterface" protocol="jsf" alias="bloodJsfInterface-prod" timeout="5000">
        <jsf:parameter key="authToken" value="355b19e8-5fb8-4a61-b487-09aefda3702a" hide="true" />
        <jsf:parameter key="clientName" value="J-dos-sparkmonitor" hide="true" />
        <jsf:parameter key="alias" value="bloodJsfInterface-prod" hide="true" />
    </jsf:consumer>
</beans>
