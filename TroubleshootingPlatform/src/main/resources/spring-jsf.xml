<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">

    <bean id="gwCustomerSignFilter" class="com.jd.bdp.gateway.sdk.filter.GWCustomerSignFilter" scope="prototype">
        <property name="ak" value="af345ad56e4749f2a2d5fffc8fb0d7c3"/>
        <property name="sk" value="c57a3f6ee7f8e87a121b029defb9857978110ae8"/>
    </bean>

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="i.jsf.jd.com"/>

    <jsf:consumer id="ssoService" interface="com.jd.ssa.service.SsoService" protocol="jsf"
                  alias="v1" timeout="10000"/>
    <jsf:consumer
            id="ticketService"
            interface="com.jd.xbp.jsf.api.TicketService"
            protocol="jsf"
            alias="PROD"
            timeout="10000"
            retries="0">
    </jsf:consumer>

    <jsf:consumer id="hrUserServiceJsf" interface="com.jd.official.omdm.is.hr.HrUserService"
                  protocol="jsf" alias="HR_USER_SERVICE_JSF" timeout="1000" retries="3">
    </jsf:consumer>

    <jsf:consumer id="ideJobServiceJsf" interface="com.jd.jbdp.edc.api.extract.service.JobInfoInterface"
                  protocol="jsf" alias="EDC_PRODUCT" timeout="1000" retries="3"
                  filter="gwCustomerSignFilter">
    </jsf:consumer>

    <context:component-scan base-package="com.jd.bdp" />
    <context:annotation-config />

    <bean id="propertyPlaceholderConfigurer"
          class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE" />
        <property name="searchSystemEnvironment" value="true" />
    </bean>

    <bean id="springSSOInterceptor" class="com.jd.common.springmvc.interceptor.SpringSSOInterceptor">
        <property name="ssoDomainName" value=".jd.com"/>
        <property name="appDomainName" value="${APP_DOMAIN}"/>
        <property name="appHomeUrl" value="http://${APP_DOMAIN}/"/>
        <property name="loginUrl" value="https://ssa.jd.com/sso/login"/>
        <property name="excludePath" value="/js,/css,/xbpController,/dataVirtual,/upload,/exploring,/compressing,/buffaloDoubleRun,/easyBI,/buffaloLogs,/spark/task/notify,/spark/tasks,/dualRun,/sparkUpgradeCreateInstance,/moveMountains,/TaskFailCallback,/sparkMetric"/>
        <property name="ssoService" ref="ssoService"/>
    </bean>

    <bean id="jimClient" class="com.jd.jim.cli.ReloadableJimClientFactoryBean">
        <property name="jimUrl" value="jim://2941132917553898407/10466" />
        <!-- configId的作用是配置不同访问策略和超时等参数 -->
        <property name="configId" value="0" />
        <!-- netty IO线程池数量，一般情况下设置为2效果最佳，针对吞吐要求高的情况，可以根据不同的客户端CPU配置和集群规模建议测试后进行调整 -->
        <property name="ioThreadPoolSize" value="2"/>
        <!-- 流量控制,该队列由未发送的命令请求个数和已发送给服务端但还未得到响应的命令个数相加而来，当队列达到设置的阈值后，此时会提示超出队列长度抛出异 常，可以根据业务的流量进行调整，特别针对异步和pipeline调用-->
        <property name="requestQueueSize" value="50000"/>
    </bean>


    <!--spark升级默认数据库-->
    <bean class="com.alibaba.druid.pool.DruidDataSource" id="sparkDataSource">
        <property name="driverClassName" value="com.mysql.jdbc.Driver"/>
        <property name="url" value="***********************************************************************************************************************************************************************"/>
        <property name="username" value="salt"/>
        <property name="password" value="salt"/>
    </bean>
    <bean class="org.mybatis.spring.SqlSessionFactoryBean" id="sparkSqlSessionFactory">
        <property name="dataSource" ref="sparkDataSource"/>
        <property name="mapperLocations">
            <list>
                <value>classpath:mapper/spark/*.xml</value>
            </list>
        </property>
        <property name="configuration">
            <bean class="org.apache.ibatis.session.Configuration">
                <property name="mapUnderscoreToCamelCase" value="true"/>
            </bean>
        </property>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer" id="sparkScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="sparkSqlSessionFactory"/>
        <property name="basePackage" value="com.jd.bdp.mapper.spark"/>
    </bean>
    
    <!--buffalo调度从库-->
    <bean class="com.alibaba.druid.pool.DruidDataSource" id="buffaloDataSource">
        <property name="driverClassName" value="com.mysql.jdbc.Driver"/>
        <property name="url" value="*************************************************************************************************************************************************************************"/>
        <property name="username" value="dispatch_1_0_ro"/>
        <property name="password" value="pXRirVcfdZ7SbXQdiFnV4diFdF9OVUCj"/>
    </bean>
    <bean class="org.mybatis.spring.SqlSessionFactoryBean" id="buffaloSqlSessionFactory">
        <property name="dataSource" ref="buffaloDataSource"/>
        <property name="mapperLocations">
            <list>
                <value>classpath:mapper/buffalo/*.xml</value>
            </list>
        </property>
        <property name="configuration">
            <bean class="org.apache.ibatis.session.Configuration">
                <property name="mapUnderscoreToCamelCase" value="true"/>
            </bean>
        </property>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer" id="buffaloScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="buffaloSqlSessionFactory"/>
        <property name="basePackage" value="com.jd.bdp.mapper.buffalo"/>
    </bean>
    
</beans>
