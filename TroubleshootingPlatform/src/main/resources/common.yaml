!!com.jd.bdp.bean.CommonBean
monitorAndFillSchedulerPeriodSecond: 240
forkBuffaloTaskSchedulerPeriodSecond: 60
fillSparkVersionSchedulerPeriodSecond: 120
runSpark3SchedulerPeriodSecond: 120
syncJrcToJimDBSchedulerPeriodSecond: 120
compareToolSchedulerPeriodSecond: 120
createXbpFlowSchedulerPeriodSecond: 30

buffaloApiAppId: spark.bdp.jd.com
buffaloApiUserToken: URM23ad31a5b73456ee55c2cc8c81d20fc6
buffaloApiAppToken: 1034463c78fe4619cad70640daf4a627

buffaloNewApiAppId: J-dos-jd-spark-rss
buffaloNewApiAppToken: 917161bc9c136fe2

#buffaloApiAppId: diagnosis
#buffaloApiUserToken: 7f9411e0392d256007ba2e358b2aa671
#buffaloApiAppToken: c70a904daa782dd468095c92363dc079

shiftHillsBuffaloApiAppId: 7.jd.com
shiftHillsBuffaloApiUserToken: URMcc35f3c4216fe6f8514db095bfb0a404
shiftHillsBuffaloApiAppToken: 128729938a521813

ugdapApiAppid: jcpt.jd.com
ugdapApiToken: d831eb0ecae6a41328279d456f6e62ba

forkBuffaloTaskAppendParams: " --conf spark.sql.partitionFilePruning.ratio=0.1 "

forkBuffaloTaskAppendParamsAtStart:
  - "set -x"
  - "whoami"
  - "env|grep JDH"
  - "env|grep TEAM_USER"

resultLimitPerBatchOfForkTask: 10

kongMingAddBuffaloTemplateConfUrl: http://baize.kongming.jd.com/optimizeConf/addBufflaoTemplateConf
kongMingGetBuffaloTemplateConfUrl: http://baize.kongming.jd.com/optimizeConf/getTaskTemplate
kongMingDelBuffaloTemplateConfUrl: http://baize.kongming.jd.com/optimizeConf/delBuffaloTemplateConf
xbpUsername: xnspark
xbpNatureUsername: org.nature.data1
xbpWuGuoXiao: wuguoxiao

xbpMailCopyAddressesForInspection: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>


downloadBuffaloLogDir: /data0/wuguoxiao

buffaloJdqTopic: "buffalo-instance-status-message"
buffaloJdqUserName: "C50cd2a7d"
buffaloJdqPassword: "uSQQbLEbFuE536hB"
buffaloJdqDomainName: ""

initUpgradeSparkTaskInterval: 300000

baizePgUrl: http://baizepg-hadoop.jd.local/api/put?service_id=b4e95386-9f4a-41ba-97e6-8d91f6f889b6&data_fmt=opentsdb
baizeRequestTimeout: 10000
baizeReadTimeout: 10000

