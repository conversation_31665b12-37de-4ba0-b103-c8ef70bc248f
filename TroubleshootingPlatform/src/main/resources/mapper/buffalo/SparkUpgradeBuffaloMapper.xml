<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.bdp.mapper.buffalo.SparkUpgradeBuffaloMapper">

    <sql id="globalViewForDoubleRunSqlId">
        select aa.task_id, aa.latest_modified_date, bb.double_run_task_id, bb.name, cc.latest_double_run_task_ins_id, cc.run_status, cc.run_time, dd.total_duration_2_4, dd.total_duration_3_4
        from
            (
                select substring_index(substring_index(name, '_', 2), '_', - 1) as task_id, max(date(modified)) as latest_modified_date
                from b_def_task
                where app_group_id = 106020
                  and status = 3
                  and disabled = 0
                  and deleted = 0
                  and name regexp 'BUFFALO_[0-9]+_[0-9]+'
                  and length(substring_index(name, '_', -1)) > 5
                group by task_id
            ) aa
            inner join
            (
                select id as double_run_task_id, name, substring_index(substring_index(name, '_', 2), '_', - 1) as task_id, date(modified) as modified_date
                from b_def_task
                where app_group_id = 106020
                  and status = 3
                  and disabled = 0
                  and deleted = 0
                  and name regexp 'BUFFALO_[0-9]+_[0-9]+'
                  and length(substring_index(name, '_', -1)) > 5
            ) bb
            on aa.task_id = bb.task_id and aa.latest_modified_date = bb.modified_date
            left join
            (
                select t3.double_run_task_id, t4.run_status, t4.run_time, t3.latest_double_run_task_ins_id
                from
                    (
                        select t1.id as double_run_task_id, max(t2.task_ins_id) as latest_double_run_task_ins_id
                        from (
                                 select id
                                 from b_def_task
                                 where app_group_id = 106020
                                   and status = 3
                                   and disabled = 0
                                   and deleted = 0
                                   and name regexp 'BUFFALO_[0-9]+_[0-9]+'
                                   and length(substring_index(name, '_', -1)) > 5
                             ) t1
                        inner join b_run_log t2 on t1.id = t2.task_def_id and t2.instance_type = 1
                        group by t1.id
                    ) t3
                    inner join  b_run_log t4
                    on t3.latest_double_run_task_ins_id = t4.task_ins_id and t4.instance_type = 1
            ) cc
            on bb.double_run_task_id  = cc.double_run_task_id
            left join 
            (
                select double_run_task_id,  sum(if(inst_type  = '2.4', duration, 0)) as total_duration_2_4, sum(if(inst_type  = '3.4', duration, 0)) as total_duration_3_4
                from
                    (
                        select case
                                   when t8.args like '2_4%' then '2.4'
                                   when t8.args like '3_4%' then '3.4'
                                   else 'check'
                                   end as inst_type, t8.duration, t7.double_run_task_id
                        from
                            (
                                select t5.id as double_run_task_id, max(t6.task_ins_id) as latest_double_run_task_ins_id
                                from (
                                         select id
                                         from b_def_task
                                         where app_group_id = 106020
                                           and status = 3
                                           and disabled = 0
                                           and deleted = 0
                                           and name regexp 'BUFFALO_[0-9]+_[0-9]+'
                                           and length(substring_index(name, '_', -1)) > 5
                                     ) t5
                                    inner join b_run_log t6
                                    on t5.id = t6.task_def_id and t6.instance_type = 1
                                group by t5.id
                            ) t7
                            inner join b_run_log t8
                            on t7.latest_double_run_task_ins_id = t8.task_ins_id and t8.instance_type = 2
                    ) t9
                where (inst_type  = '2.4' or inst_type = '3.4')
                group by double_run_task_id
            ) dd
            on bb.double_run_task_id = dd.double_run_task_id
    </sql>
    
    
    <select id="selectDoubleTaskIdByOriginTaskId" parameterType="java.lang.Integer" resultType="java.lang.String">
        select concat_id
        from (
                 select date(modified) as modify_date, group_concat(id) as concat_id
                 from b_def_task
                 where app_group_id = 106020
                   and status = 3
                   and disabled = 0
                   and deleted = 0
                   and name regexp 'BUFFALO_[0-9]+_[0-9]+'
                   and substring_index(substring_index(name, '_', 2), '_', - 1) = #{originTaskId}
                 group by modify_date
                 order by modify_date desc
                 limit 1
             ) t
    </select>

    <select id="selectLatestInstRunStatusByDoubleTaskIdFromToday" parameterType="java.util.List" resultType="java.lang.String">
        select t2.run_status
        from
        (
        select max(task_ins_id) as latest_task_ins_id
        from b_run_log
        <where>
            <if test="doubleRunTaskIds != null and doubleRunTaskIds.size > 0">
                and task_def_id in
                <foreach collection="doubleRunTaskIds" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            and instance_type = 1
        </where>
        group by task_def_id
        ) t1
        inner join b_run_log t2 on t1.latest_task_ins_id = t2.task_ins_id
        where t2.instance_type = 1 and date(t2.created) >= date(now())
    </select>

    <select id="selectLatestInstRunStatusByDoubleTaskIdFromYesterday" parameterType="java.util.List" resultType="java.lang.String">
        select t2.run_status
        from
        (
        select max(task_ins_id) as latest_task_ins_id
        from b_run_log
        <where>
            <if test="doubleRunTaskIds != null and doubleRunTaskIds.size > 0">
                and task_def_id in
                <foreach collection="doubleRunTaskIds" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            and instance_type = 1
        </where>
        group by task_def_id
        ) t1
        inner join b_run_log t2 on t1.latest_task_ins_id = t2.task_ins_id
        where t2.instance_type = 1 and date(t2.created) >= date(now() - interval 1 day)
    </select>
    
    <select id="selectFailedDoubleRunTaskLogByDoubleRunTaskIds" parameterType="java.util.List" resultType="com.jd.bdp.bean.SparkUpgradeBuffaloLogBean">
        select aa.task_def_id as double_run_task_id, aa.task_ins_id as double_run_instance_id, aa.id as double_run_log_id
        from 
         (
             select task_def_id, max(task_ins_id) as latest_task_ins_id
             from b_run_log
             <where>
                 <if test="doubleRunTaskIds != null and doubleRunTaskIds.size() > 0">
                    task_def_id in 
                     <foreach collection="doubleRunTaskIds" item="item" open="(" close=")" separator=",">
                         #{item}
                     </foreach>
                 </if>
                 and instance_type = 1
             </where> 
             group by task_def_id
         ) bb
         inner join b_run_log aa    
         on aa.task_ins_id = bb.latest_task_ins_id
         where aa.instance_type = 2 and aa.run_status = 'fail'
    </select>
    
    <select id="selectDoubleRunSummary" resultType="com.jd.bdp.bean.bo.DoubleRunTaskSummaryBo">
        select task_id, double_run_task_id, latest_double_run_task_ins_id, run_time, run_status, total_duration_2_4, total_duration_3_4
        from (
          <include refid="globalViewForDoubleRunSqlId" />     
        ) global_view
        <where>
            <if test="startTime != null and startTime != ''">
                and run_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and run_time &lt;= #{endTime}
            </if>
        </where>
    </select>
    
    <select id="selectOriginTaskInInterval" resultType="java.lang.Integer">
        select distinct a.origin_task_id
        from
        (
            select id as double_run_task_id, substring_index(substring_index(name, '_', 2), '_', - 1) as origin_task_id
            from b_def_task
            where
            app_group_id = 106020
            and status = 3
            and disabled = 0
            and deleted = 0
            and name regexp 'BUFFALO_[0-9]+_[0-9]+'
            and length(substring_index(name, '_', -1)) > 5
        ) a
        inner join b_run_log b on a.double_run_task_id = b.task_def_id
        where b.run_time &gt;= #{startTime} and b.run_time &lt;= #{endTime}
    </select>
    
    
    <select id="selectRunningTaskInstancesCount" resultType="java.lang.Integer">
        select count(distinct t2.id)
        from ( select id from b_def_task where app_group_id = 106020 and deleted = 0 ) t1
                 inner join b_instance_task t2 on t1.id = t2.task_id
        where t2.run_status in ('wait', 'queue', 'run', 'rerun')
          and t2.instance_type in ('normal', 'hisRun', 'test')
          and date(t2.created) = date(now())
    </select>
    
    <select id="selectBuffaloInstanceStatusAndSize" resultType="com.jd.bdp.bean.bo.DoubleRunTaskStatusSizeBo">
        select t2.run_status as status, count(distinct t2.id) as size
        from ( select id from b_def_task where app_group_id = 106020 and deleted = 0 ) t1
                 inner join b_instance_task t2 on t1.id = t2.task_id
        where t2.instance_type in ('normal', 'hisRun', 'test')
          and date(t2.created) = date(now())
        group by t2.run_status
    </select>
</mapper>

