<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.bdp.mapper.spark.SparkUpgradeBigGraphMapper">

    <select id="selectListByPage" resultType="com.jd.bdp.bean.SparkUpgradeAssessmentBean">
        select t1.task_id, t1.isSpark, t1.isHive
        from wuguoxiao_spark_double_run_graph t1 left join spark_upgrade_engine_task_tbl t2 on t1.task_id = t2.task_id
        <where>
            <if test="condition != null and condition != ''">
                ${condition}
            </if>
            <if test="taskIds != null and taskIds.size() > 0">
                and t1.task_id in
                <foreach collection="taskIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="offset != null and length != null">
            limit #{offset}, #{length}
        </if>
    </select>
    
    <update id="updateImportStatusByTaskId">
        update wuguoxiao_spark_double_run_graph set is_imported = #{isImport} where task_id = #{taskId}
    </update>
    
    <select id="selectErrorCodeForBigGraph" resultType="com.jd.bdp.bean.bo.DoubleRunErrorCodeAndSizeBo">
        select t2.error_code, exception_keyword, error_description, count(distinct t1.task_id) as size
        from (select a.task_id from wuguoxiao_spark_double_run_graph a
               inner join spark_upgrade_engine_task_tbl b  on a.task_id = b.task_id         
               where a.status = '正常' and b.status = 'DOUBLE_RUN_FAILED' and date(b.update_time) = date(now())
            ) t1
         left join spark_upgrade_task_error_rlt t2 on t1.task_id = t2.origin_task_id
         left join spark_upgrade_error_code_dict t3 on t2.error_code = t3.error_code
        group by exception_keyword, t2.error_code
        order by size desc
    </select>

    <select id="selectTaskStatusAndSizeIncr" resultType="com.jd.bdp.bean.bo.DoubleRunTaskStatusSizeBo">
        select status, count(distinct task_id) as size
        from spark_upgrade_engine_task_tbl
        where date(update_time) = date(now())
        group by status;
    </select>

    <select id="selectAllErrorCodeFromCurrent" resultType="com.jd.bdp.bean.bo.DoubleRunErrorCodeAndSizeBo">
        select t2.error_code, exception_keyword, error_description, count(distinct t1.task_id) as size
        from (select task_id from spark_upgrade_engine_task_tbl b
              where status = 'DOUBLE_RUN_FAILED' and date(b.update_time) = date(now())
             ) t1
                 left join spark_upgrade_task_error_rlt t2 on t1.task_id = t2.origin_task_id
                 left join spark_upgrade_error_code_dict t3 on t2.error_code = t3.error_code
        group by exception_keyword, t2.error_code
        order by size desc
    </select>

    <select id="selectSumErrorCodeCountFromCurrent" parameterType="java.util.List" resultType="java.lang.Integer">
        select count(distinct a.origin_task_id)
        from spark_upgrade_task_error_rlt a
        left join spark_upgrade_error_code_dict b on a.error_code = b.error_code
        <where>
            date(a.create_time) = date(now())
            <if test="list != null and list.size() > 0">
                and a.error_code in
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
          
    </select>
</mapper>

