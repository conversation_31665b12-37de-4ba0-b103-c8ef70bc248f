<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.bdp.mapper.spark.SparkUpgradeAssessmentMapper">

    <select id="selectListByPage" resultType="com.jd.bdp.bean.SparkUpgradeAssessmentBean">
        select t1.task_id, t1.isSpark, t1.isHive, t1.explain_is_failed, t1.is_double_run_task_created, t1.inst_all_success
        from v34_upgrade_assessment_2 t1 left join spark_upgrade_engine_task_tbl t2 on t1.task_id = t2.task_id
        <where>
            <if test="condition != null and condition != ''">
                ${condition}
            </if>
            <if test="taskIds != null and taskIds.size() > 0">
                and t1.task_id in
                <foreach collection="taskIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="offset != null and length != null">
            limit #{offset}, #{length}
        </if>
    </select>
    
    <update id="updateImportStatusByTaskId">
        update v34_upgrade_assessment_2 set is_imported = #{isImport} where task_id = #{taskId}
    </update>


    <select id="selectTaskStaticInfoByTaskId" parameterType="java.lang.Integer" resultType="com.jd.bdp.bean.bo.DoubleRunTaskStaticInfoBo">
        select job_num jobNum, if(cast(p95 as unsigned) = 1, true, false) as isP95, if(baseline is not null, true, false) as isBaseLine, req_vcore_day as reqVcoreDay, t2.status as upgradeStatus
        from v34_upgrade_assessment_2 t1
        left join spark_upgrade_engine_task_tbl t2 on t1.task_id = t2.task_id
        where t1.task_id = #{taskId}
        limit 1
    </select>

    
</mapper>

