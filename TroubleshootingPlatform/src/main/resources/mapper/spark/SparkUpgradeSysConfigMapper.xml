<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.bdp.mapper.spark.SparkUpgradeSysConfigMapper">

    <select id="getConfigValue" parameterType="java.lang.String" resultType="java.lang.String">
        select config_value
        from t_sys_config
        where config_key = #{configKey}
    </select>

    <select id="getConfigValueLike" parameterType="java.lang.String" resultType="java.lang.String">
        select config_value
        from t_sys_config
        <where>
            <if test="configKey != null and configKey != ''">
                <bind name="configKeyPattern" value="'%' + configKey + '%'"/>
                and config_key like #{configKeyPattern}
            </if>
        </where>
    </select>

    <update id="setConfigValue" parameterType="java.lang.String">
        update t_sys_config set config_value = #{configValue} where config_key = #{configKey}
    </update>

    <select id="selectAllConfigs"  resultType="com.jd.bdp.bean.domain.SparkUpgradeSysConfigBean">
        select *
        from t_sys_config
    </select>
    
    <insert id="addConfig">
        insert into t_sys_config(config_key, config_value, description, create_time, erp) values(#{configKey}, #{configValue}, #{description}, now(), #{erp}) 
    </insert>
</mapper>

