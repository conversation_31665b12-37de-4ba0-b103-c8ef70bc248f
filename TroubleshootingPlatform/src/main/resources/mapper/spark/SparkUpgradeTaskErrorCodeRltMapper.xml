<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.bdp.mapper.spark.SparkUpgradeTaskErrorCodeRltMapper">

    <delete id="deleteRltsByTaskId" parameterType="java.lang.Integer">
        delete from spark_upgrade_task_error_rlt where origin_task_id = #{originTaskId}
    </delete>
    
    <insert id="batchInsertTaskErrorRlts" parameterType="java.util.List">
        <if test="list != null and list.size > 0">
            insert into spark_upgrade_task_error_rlt (origin_task_id, error_code, explain_id,  double_run_task_id, double_run_instance_id, double_run_log_id, details, create_time)
            values 
            <foreach collection="list" separator="," item="item">
                (#{item.originTaskId}, #{item.errorCode}, #{item.explainId}, #{item.doubleRunTaskId}, #{item.doubleRunInstanceId}, #{item.doubleRunLogId}, #{item.details}, #{item.createTime})
            </foreach>
        </if>
    </insert>
    
    <select id="selectErrorRltListByTaskId" parameterType="java.lang.Integer" resultType="com.jd.bdp.bean.SparkUpgradeTasKErrorRltBean">
        select * from spark_upgrade_task_error_rlt where origin_task_id = #{originTaskId}
    </select>

    <insert id="batchInsertTaskErrorRltsHistory" parameterType="java.util.List">
        <if test="list != null and list.size > 0">
            insert into spark_upgrade_task_error_rlt_his (origin_task_id, error_code, explain_id,  double_run_task_id, double_run_instance_id, double_run_log_id, details, create_time)
            values
            <foreach collection="list" separator="," item="item">
                (#{item.originTaskId}, #{item.errorCode}, #{item.explainId}, #{item.doubleRunTaskId}, #{item.doubleRunInstanceId}, #{item.doubleRunLogId}, #{item.details}, #{item.createTime})
            </foreach>
        </if>
    </insert>
    
</mapper>

