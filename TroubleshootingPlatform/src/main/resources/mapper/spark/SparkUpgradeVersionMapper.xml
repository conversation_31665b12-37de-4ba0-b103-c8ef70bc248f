<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.bdp.mapper.spark.SparkUpgradeVersionMapper">
    <select id="selectSparkUpgradeVersionList" resultType="com.jd.bdp.bean.SparkUpgradeEngineVersionBean">
        select * from spark_upgrade_engine_version_tbl
    </select>
    
    
    <insert id="batchInsertSparkUpgradeVersions" parameterType="java.util.List">
        insert into spark_upgrade_engine_version_tbl(task_id, action_id, task_type, engine, from_version, to_version, op_type, status, create_time, creator)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.taskId}, #{item.actionId}, #{item.taskType},  #{item.engine}, #{item.fromVersion}, #{item.toVersion}, #{item.opType}, #{item.status}, #{item.createTime}, #{item.creator})
        </foreach>
    </insert>
</mapper>

