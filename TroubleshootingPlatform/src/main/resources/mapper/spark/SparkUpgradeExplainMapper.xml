<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.bdp.mapper.spark.SparkUpgradeExplainMapper">

    <select id="selectListByTaskId" resultType="com.jd.bdp.bean.SparkUpgradeExplainBean">
        select * from spark_upgrade_sql_rewrite_tbl_3_4
        <where>
            <if test="taskId != null and taskId != ''">
                buffalo_id = #{taskId}
            </if>
        </where>
    </select>


    <select id="selectListByTaskIdAndStatus" resultType="com.jd.bdp.bean.SparkUpgradeExplainBean">
        select * from spark_upgrade_sql_rewrite_tbl_3_4
        <where>
            <if test="taskId != null and taskId != ''">
                buffalo_id = #{taskId}
            </if>
            <if test="status != null and status.size() > 0">
                and status in
                <foreach collection="status" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectLatestListByTaskId" resultType="com.jd.bdp.bean.SparkUpgradeExplainBean">
        select * from spark_upgrade_sql_rewrite_tbl_3_4 t
        inner join (
            select max(create_time) as latest_create_time
            from spark_upgrade_sql_rewrite_tbl_3_4
            where buffalo_id = #{taskId}
            group by create_time
        ) t2
        on t.create_time = t2.latest_create_time
        where buffalo_id = #{taskId}
    </select>
    
</mapper>

