<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper">

    <select id="selectList" resultType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        select * from spark_upgrade_engine_task_tbl
    </select>

    <select id="selectTotalCount" resultType="java.lang.Long">
        select count(*) from spark_upgrade_engine_task_tbl
        <where>
            <if test="list != null and list.size() > 0">
                task_id in
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status != '' ">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectListByPage" resultType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        select * from spark_upgrade_engine_task_tbl
        <where>
            <if test="list != null and list.size() > 0">
                task_id in 
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status != null and status != '' ">
                and status = #{status}
            </if>
        </where>
        limit #{offset}, #{length}
    </select>

    <select id="selectListByStatus" resultType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        select * from spark_upgrade_engine_task_tbl
        <where>
            <if test="status != null and status != '' ">
                and status = #{status}
            </if>
        </where>
        order by task_id
        <if test="length != null">
            limit #{length}
        </if>
        
    </select>

    <select id="selectOneByTaskId" parameterType="java.lang.String" resultType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        select * from spark_upgrade_engine_task_tbl where task_id = #{taskId} limit 1
    </select>

    <select id="selectUpgradedOneByTaskId" parameterType="java.lang.String" resultType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        select * from spark_upgrade_engine_task_tbl where task_id = #{taskId} and status = 'UPGRADED'
    </select>
    
    <insert id="insertSparkUpgradeTask" parameterType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        insert into spark_upgrade_engine_task_tbl (task_id, origin_version, status, create_time, creator) 
        values(#{taskId}, #{originVersion}, #{status}, #{createTime}, #{creator}) ON DUPLICATE KEY UPDATE status=#{status}, update_time=now()
    </insert>
    
    <insert id="insertSparkUpgradeTasks" parameterType="java.util.List">
        insert into spark_upgrade_engine_task_tbl(task_id, origin_version, status, create_time, creator)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.taskId}, #{item.originVersion}, #{item.status},  #{item.createTime}, #{item.creator})
        </foreach>
        ON DUPLICATE KEY UPDATE status=#{status}, update_time=now()
    </insert>

    <update id="updateTask" parameterType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        update spark_upgrade_engine_task_tbl
        <set>
            <if test="originVersion != null and originVersion != ''">
                origin_version = #{originVersion},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="createTime != null and createTime != ''">
                create_time = #{createTime},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time = #{updateTime},
            </if>
        </set>
        where task_id = #{taskId}
    </update>


    <update id="updateTasks" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";" >
            update spark_upgrade_engine_task_tbl
            <set>
                <if test="originVersion != null and originVersion != ''">
                    origin_version = #{item.originVersion},
                </if>
                <if test="status != null and status != ''">
                    status = #{item.status},
                </if>
                <if test="createTime != null and createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="creator != null and creator != ''">
                    creator = #{item.creator},
                </if>
                <if test="updateTime != null and updateTime != ''">
                    update_time = #{updateTime},
                </if>
            </set>
            <where>
                task_id = #{item.taskId}
            </where>
        </foreach>
    </update>
    
    
    <update id="updateStatusByCondition">
        update spark_upgrade_engine_task_tbl set status = #{targetStatus}
        <where>
            task_id = #{taskId}
            <if test="preStatus != null and preStatus.size > 0" >
                and status in 
                <foreach collection="preStatus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>
    </update>

    <update id="updateStatusByList">
        update spark_upgrade_engine_task_tbl set status = #{targetStatus}
        <where>
            <if test="taskIds != null and taskIds.size > 0" >
                and task_id in
                <foreach collection="taskIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="preStatus != null and preStatus.size > 0" >
                and status in
                <foreach collection="preStatus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>


    <update id="updateDoubleRunTaskIdByOriginTaskId">
        update spark_upgrade_engine_task_tbl set double_run_task_id = #{doubleRunTaskId} where task_id = #{originTaskId}
    </update>


    <insert id="insertSparkUpgradeTaskV2" parameterType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        insert into spark_upgrade_engine_task_tbl (task_id, origin_version, status, create_time, creator, double_run_task_id)
        values(#{taskId}, #{originVersion}, #{status}, #{createTime}, #{creator}, #{doubleRunTaskId}) ON DUPLICATE KEY UPDATE status=#{status}, double_run_task_id = #{doubleRunTaskId}, update_time=now()
    </insert>
    
    <select id="selectTargetTaskList" resultType="com.jd.bdp.bean.SparkUpgradeTaskBean">
        <if test="selectTargetTaskSql != null and selectTargetTaskSql != ''">
            ${selectTargetTaskSql}
        </if>
    </select>
</mapper>

