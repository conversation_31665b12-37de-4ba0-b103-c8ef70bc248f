compare2and3: >-
  SELECT info.checkData,
         info.message,
         info.managers,
         compare.*
  FROM   (SELECT a.cluster,
                 a.market,
                 a.queue,
                 a.teamuser,
                 a.buffalo_version,
                 a.origin_task_id,
                 a.fork_task_id,
                 a.runlogid              AS log_id_2,
                 a.duration_actual       AS duration_2,
                 a.spark_version         AS version_2,
                 a.targettablename       AS table_2,
                 b.runlogid              AS log_id_3,
                 b.duration_actual       AS duration_3,
                 b.spark_version         AS version_3,
                 b.targettablename       AS table_3,
                 b.duration_actual / a.duration_actual AS duration_ratio
          FROM   (SELECT b.*
                  FROM   (SELECT buffalo_version,
                                 fork_task_id,
                                 Max(runlogid) AS latest_log_id
                          FROM   %s
                          WHERE  spark_version LIKE '2%%'
                                 AND runstatus = 'success'
                          GROUP  BY buffalo_version,
                                    fork_task_id) AS a
                          INNER JOIN %s AS b
                                 ON a.buffalo_version = b.buffalo_version
                                    AND a.fork_task_id = b.fork_task_id
                                    AND a.latest_log_id = b.runlogid) AS a
          INNER JOIN
                  (SELECT b.*
                  FROM   (SELECT    buffalo_version,
                                    fork_task_id,
                                    <PERSON>(runlogid) AS latest_log_id
                             FROM   %s
                             WHERE  spark_version LIKE '3%%'
                                    AND runstatus = 'success'
                             GROUP  BY buffalo_version,
                                       fork_task_id) AS a
                            INNER JOIN %s AS b
                                    ON a.buffalo_version =
                                       b.buffalo_version
                                       AND a.fork_task_id = b.fork_task_id
                                       AND a.latest_log_id = b.runlogid)
                          AS b
                  ON a.buffalo_version = b.buffalo_version
                          AND a.fork_task_id = b.fork_task_id) AS compare
          INNER JOIN %s AS info
                  ON info.task_id = compare.origin_task_id
                          AND info.fork_task_id = compare.fork_task_id
  %s
  ORDER  BY compare.duration_ratio DESC
compare2And3Summary: >-
  SELECT SUM(duration_2) AS sum_duration_2,
  SUM(duration_3) AS sum_duration_3,
  SUM(duration_3) * 100 /sum(duration_2) AS duration_rate
  FROM ( %s ) AS c
compare2And3Regression: >-
  SELECT count(1)                                    AS total,
  SUM(IF(duration_ratio > 1, 1, 0))                  AS slow,
  SUM(IF(duration_ratio > 1, 1, 0)) * 100 / count(1) AS ratio
  FROM ( %s ) AS c
checkDataSummary: >-
  SELECT checkData  AS checkData,
  COUNT(1)          AS checkCnt
  FROM ( %s ) AS c
  GROUP BY checkData
  ORDER BY checkData ASC
failureRegerssion: >-
  SELECT * FROM (
  SELECT b.*
  FROM
      (
          SELECT  buffalo_version,
                  fork_task_id,
                  max(runlogid) AS latest_log_id
          FROM   buffalo_log_info
          WHERE  spark_version LIKE '2%'
          GROUP  BY buffalo_version, fork_task_id
      ) AS a
  INNER JOIN buffalo_log_info AS b
  ON a.buffalo_version = b.buffalo_version
      AND a.fork_task_id = b.fork_task_id
      AND a.latest_log_id = b.runlogid
  WHERE b.runStatus = 'success'
  and b.fork_task_id not in
  (
      SELECT distinct b.fork_task_id
      FROM
          (
              SELECT    buffalo_version,
                      fork_task_id,
                      max(runlogid) AS latest_log_id
              FROM   buffalo_log_info
              WHERE  spark_version LIKE '3%'
              GROUP  BY buffalo_version, fork_task_id
          ) AS a
      INNER JOIN buffalo_log_info AS b
      ON a.buffalo_version = b.buffalo_version
          AND a.fork_task_id = b.fork_task_id
          AND a.latest_log_id = b.runlogid
      WHERE b.runStatus = 'success'
  )
  ) AS c
dual_run: >-
  SELECT
    f.name,
    f.origin_buffalo_id,
    f.origin_log_id,
    dual_run_id,
    run_2_4,
    run_3_4,
    CASE
    WHEN data_check IS NULL
    THEN 'skipped_or_waiting'
    ELSE data_check
    END AS data_check,
    duration_2_4,
    duration_3_4,
    duration_2_4 - duration_3_4 AS time_saved_3_vs_2,
    1 - duration_3_4 / duration_2_4 AS ratio,
    duration_check,
    f.run_2_4_logid,
    f.run_3_4_logid,
    CASE WHEN f.run_check_logid IS NULL
      THEN '-1'
      ELSE f.run_check_logid
    END AS run_check_logid
  FROM
  (
  SELECT
  e.name,
  e.dual_run_id,
  e.origin_buffalo_id,
  e.origin_log_id,
  MAX(
  CASE
  WHEN e.inst_type = '2.4'
  THEN log_id
  END) AS run_2_4_logid,
  MAX(
  CASE
  WHEN e.inst_type = '3.4'
  THEN log_id
  END) AS run_3_4_logid,
  MAX(
  CASE
  WHEN e.inst_type = 'check'
  THEN log_id
  END) AS run_check_logid,
  MAX(
  CASE
  WHEN e.inst_type = '2.4'
  THEN run_status
  END) AS run_2_4,
  MAX(
  CASE
  WHEN e.inst_type = '3.4'
  THEN run_status
  END) AS run_3_4,
  MAX(
  CASE
  WHEN e.inst_type = 'check'
  THEN run_status
  END) AS data_check,
  MAX(
  CASE
  WHEN e.inst_type = '2.4'
  THEN e.duration
  END) AS duration_2_4,
  MAX(
  CASE
  WHEN e.inst_type = '3.4'
  THEN e.duration
  END) AS duration_3_4,
  MAX(
  CASE
  WHEN e.inst_type = 'check'
  THEN e.duration
  END) AS duration_check
  FROM
  (
  SELECT
  a.name,
  a.origin_buffalo_id,
  a.origin_log_id,
  a.task_def_id AS dual_run_id,
  CASE
  WHEN args LIKE '2\_4%%'
  THEN '2.4'
  WHEN args LIKE '3\_4%%'
  THEN '3.4'
  WHEN script_path = 'data_compare3.sh'
  THEN 'check'
  ELSE 'UNKNOW'
  END AS inst_type,
  f.id AS log_id,
  f.*
  FROM
  (
  SELECT
  a.id AS task_def_id,
  a.name,
  substring_index(substring_index(a.name, '_', 2), '_', - 1) AS origin_buffalo_id,
  substring_index(substring_index(a.name, '_', 3), '_', - 1) AS origin_log_id,
  a.created,
  MAX(e.instance_id) AS latest_ins_id
  FROM
  b_def_task AS a
  LEFT JOIN b_run_log AS e
  ON
  a.id = e.task_def_id
  AND e.instance_type = 1
  WHERE
  a.app_group_id = 106020
  AND a.status = 3
  AND a.disabled = 0
  AND a.deleted = 0
  AND a.name LIKE 'BUFFALO_%%'
  /* 测试任务均以BUFFALO前缀 */
  AND LENGTH(SUBSTRING_INDEX(a.name, '_', - 1)) > 5
  /*AND a.id = 1519456   For Test */
  /*AND e.instance_id IS NULL
  For Test */
  GROUP BY
  a.id,
  a.name,
  a.created
  ) AS a
  LEFT JOIN b_run_log AS f
  ON
  a.task_def_id = f.task_def_id
  AND a.latest_ins_id = f.task_ins_id
  AND f.instance_type = 2
  ) AS e
  GROUP BY
  task_def_id,
  e.origin_buffalo_id,
  e.origin_log_id
  ) AS f
  WHERE 1 = 1
  %s
dual_run_detail: >-
  SELECT
  	origin_buffalo_id,
  	COUNT(1) as cnt_action,
  	CASE
  		WHEN SUM(run_2_4_success + run_3_4_success + data_check_success) = 0
  		THEN 'all_success'
  		ELSE 'has_failed'
  	END AS status,
  	SUM(duration_2_4) AS duration_2_4,
  	SUM(duration_3_4) AS duration_3_4,
  	SUM(duration_check) AS duration_check
  FROM
  	(
  		SELECT
  			CASE
  				WHEN run_2_4 = 'success'
  				THEN 0
  				ELSE 1
  			END AS run_2_4_success,
  			CASE
  				WHEN run_3_4 = 'success'
  				THEN 0
  				ELSE 1
  			END AS run_3_4_success,
  			CASE
  				WHEN data_check = 'success'
  				THEN 0
  				ELSE 1
  			END AS data_check_success,
  			h.*
  		FROM
  			(
  				SELECT
  					f.name,
  					f.origin_buffalo_id,
  					f.origin_log_id,
  					task_def_id,
  					run_2_4,
  					run_3_4,
  					CASE
  						WHEN data_check IS NULL
  						THEN 'skipped_or_waiting'
  						ELSE data_check
  					END AS data_check,
  					duration_2_4,
  					duration_3_4,
  					duration_2_4 - duration_3_4 AS time_saved_3_vs_2,
  					1 - duration_3_4 / duration_2_4 AS ratio,
  					duration_check
  				FROM
  					(
  						SELECT
  							e.name,
  							e.task_def_id,
  							e.origin_buffalo_id,
  							e.origin_log_id,
  							MAX(
  								CASE
  									WHEN e.inst_type = '2.4'
  									THEN run_status
  								END) AS run_2_4,
  							MAX(
  								CASE
  									WHEN e.inst_type = '3.4'
  									THEN run_status
  								END) AS run_3_4,
  							MAX(
  								CASE
  									WHEN e.inst_type = 'check'
  									THEN run_status
  								END) AS data_check,
  							MAX(
  								CASE
  									WHEN e.inst_type = '2.4'
  									THEN e.duration
  								END) AS duration_2_4,
  							MAX(
  								CASE
  									WHEN e.inst_type = '3.4'
  									THEN e.duration
  								END) AS duration_3_4,
  							MAX(
  								CASE
  									WHEN e.inst_type = 'check'
  									THEN e.duration
  								END) AS duration_check
  						FROM
  							(
  								SELECT
  									a.name,
  									a.origin_buffalo_id,
  									a.origin_log_id,
  									CASE
  										WHEN args LIKE '2\_4%'
  										THEN '2.4'
  										WHEN args LIKE '3\_4%'
  										THEN '3.4'
  										WHEN script_path = 'data_compare3.sh'
  										THEN 'check'
  										ELSE 'UNKNOW'
  									END AS inst_type,
  									f.*,
  									f.id AS log_id
  								FROM
  									(
  										-- select app_group_id,status,disabled,deleted  from b_def_task WHERE  name LIKE '%1006775%'
  										SELECT
  											a.id AS task_def_id,
  											a.name,
  											substring_index(substring_index(name, '_', 2), '_', - 1) AS origin_buffalo_id,
  											substring_index(substring_index(name, '_', 3), '_', - 1) AS origin_log_id,
  											MAX(e.instance_id) AS latest_ins_id
  										FROM
  											b_def_task AS a
  										LEFT JOIN b_run_log AS e
  										ON
  											a.id = e.task_def_id and e.instance_type = 1
  										WHERE
  											a.app_group_id = 106020
  											AND status = 3
  											AND disabled = 0
  											AND a.deleted = 0
  											AND a.name LIKE 'BUFFALO_%'
  											AND LENGTH(SUBSTRING_INDEX(name, '_', - 1)) > 5
  											/* AND a.id = 1502384  For Test
  											AND a.name LIKE '%1006775%' */
  										GROUP BY
  											a.id,
  											a.name
  									) AS a
  								LEFT JOIN b_run_log AS f
  								ON
  									a.task_def_id = f.task_def_id
  									AND f.instance_type = 2
  									AND a.latest_ins_id = f.task_ins_id
  							) AS e
  						GROUP BY
  							task_def_id,
  							e.origin_buffalo_id,
  							e.origin_log_id
  					) AS f
  			) AS h
  	) AS i
  GROUP BY
  	origin_buffalo_id
