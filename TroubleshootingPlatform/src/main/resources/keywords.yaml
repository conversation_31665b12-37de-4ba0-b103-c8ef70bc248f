!!com.jd.bdp.bean.Keywords
keywordList:
  - id: 2
    keyword: "Job aborted due to stage failure"
    samples: 10
  - id: 3
    keyword: "org.apache.hadoop.yarn.exceptions.YarnException"
    samples: 10
  - id: 4
    keyword: "进行了终止操作"
    samples: 1
  - id: 5
    keyword: "内存已耗尽"
    samples: 1
  - id: 6
    keyword: "Diagnostics message"
    samples: 1
  - id: 7
    keyword: "任务执行超时"
    samples: 1
  - id: 8
    keyword: "WARNING 将会使用hive重试"
    samples: 1
    solutionLink: https://joyspace.jd.com/pages/qHaWQ0HlitPlOZqfo0gl
  - id: 9
    keyword: "Exit status: 143"
    samples: 5
  - id: 10
    keyword: "平台日志：异常退出"
    samples: 1
  - id: 11
    keyword: "(INFO|ERROR).* Error in query"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/fgZqIW5ose81xdUI1jQo
  - id: 12
    keyword: "ExecutorAllocationManager: Uncaught exception in thread spark-dynamic-executor-allocation"
    samples: 10
  - id: 13
    keyword: "Unable to create executor due to Unable to register with external"
    samples: 10
  - id: 14
    keyword: "is not considered to be threadsafe"
    samples: 5
  - id: 15
    keyword: "FetchFailedException"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/KaKoaGTm4ev3QZggPS6v
  - id: 16
    keyword: "ParseException"
    samples: 5
  - id: 17
    keyword: "Enable to run task with UDF"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/4Au9Fk8jCxpLcUrlM56t
  - id: 18
    keyword: "Container killed by YARN for exceeding memory limits"
    samples: 5
  - id: 19
    keyword: "java.lang.OutOfMemoryError"
    samples: 5
  - id: 20
    keyword: "单个任务日志输出"
    samples: 1
  - id: 21
    keyword: "Running Spark version"
    samples: 1
  - id: 22
    keyword: "退出码："
    samples: 1
  - id: 23
    keyword: "失败原因："
    samples: 1
  - id: 24
    keyword: "BDP系统账号(bdp_sys)"
    samples: 1
  - id: 25
    keyword: "uncaught error in thread"
    samples: 5
  - id: 26
    keyword: "Caused by: org.apache.spark.SparkException"
    samples: 5
  - id: 26
    keyword: "org.apache.spark.SparkException"
    samples: 5
  - id: 27
    keyword: "line.*(Killed|Terminated)"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/rqIwx1JpjpWPbb7STa2w
  - id: 28
    keyword: "User reset log level to:"
    samples: 1
    solutionLink: https://joyspace.jd.com/pages/jQbOEdxX5yDXV646RYAL
  - id: 29
    keyword: "tracking URL: "
    samples: 50
  - id: 30
    keyword: "cannot submit applications to queue"
    samples: 1
    solutionLink: https://joyspace.jd.com/pages/6im4kIkB3mz1hxwAmDHu
  - id: 31
    keyword: "AccessControlException: Permission denied:"
    samples: 5
  - id: 32
    keyword: "HiveTask Version"
    samples: 5
  - id: 33
    keyword: "Caused by: org.apache.spark.sql.catalyst.errors"
    samples: 5
  - id: 34
    keyword: "java.lang.IllegalArgumentException:"
    samples: 5
  - id: 35
    keyword: "java.util.NoSuchElementException:"
    samples: 5
  - id: 36
    keyword: "java.util.NoSuchElementException: None.get"
    samples: 5
  - id: 37
    keyword: "java.util.NoSuchElementException: spark.shuffle.rss.hosts"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/bwOhfKHo3hv0Yu7JZ7tj
  - id: 38
    keyword: "java.lang.ClassCastException"
    samples: 5
  - id: 39
    keyword: "Caused by: java.io.FileNotFoundException"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/STk4j12zKN79Lay3PMYU
  - id: 40
    keyword: "Error in query: Invalid usage of '*'"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/4rD35e4zDAqV6TzhFcp4
  - id: 41
    keyword: "用户输入需要合并小文件目录为:"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/0toVLOmNlXHsf5SZtSkZ
  - id: 42
    keyword: "java.lang.IllegalArgumentException: cannot generate compare code"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/bwOhfKHo3hv0Yu7JZ7tj
  - id: 43
    keyword: "Failed to obtain data from etcd"
    samples: 5
  - id: 44
    keyword: "TimeZoneAwareExpression.zoneId\\(datetimeExpressions.scala"
    samples: 5
    solutionLink: "https://joyspace.jd.com/pages/bwOhfKHo3hv0Yu7JZ7tj"
  - id: 45
    keyword: "BUFFALO4-JOB-INFO"
    samples: 5
  - id: 46
    keyword: "Bound SparkUI"
    samples: 5
  - id: 47
    keyword: "scala/collection/mutable/ArrayOps"
    samples: 1
    solutionLink: https://joyspace.jd.com/pages/aY86yJ5iz7HbguBohNTQ
  - id: 48
    keyword: org.apache.spark.util.SparkFatalException
    samples: 1
    solutionLink: https://joyspace.jd.com/pages/aJ8ecqe5t9nPTw2gDpxW
  - id: 49
    keyword: com.jd.bdp.udf.UDFGetRandPara
    samples: 1
    solutionLink: https://joyspace.jd.com/pages/QpVXCApZDDE82pI9bbyT
  - id: 50
    keyword: spark-sql-cluster
    samples: 1
  - id: 51
    keyword: Unable to fetch table
    samples: 1
    solutionLink: https://joyspace.jd.com/pages/QECbfdYesG6VM8s5CwpX
  - id: 52
    keyword: "HiveException: MetaException"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/AaW8vGxtqCMbJIHmpjMi
  - id: 53
    keyword: "HiveException：Unable to alter partition"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/5uKtggoe9oS8iYKCpNgx
  - id: 54
    keyword: "EventLoggingListener: Logging events to"
    samples: 5
  - id: 55
    keyword: "newConfXml ="
    samples: 10
    htmlEncode: true
  - id: 56
    keyword: "bigger than spark.driver.maxResultSize"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/n34yFAfJcwt7H1UWUF1m
  - id: 57
    keyword: "CREATE TEMPORARY TABLE is not supported yet"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/Z1HMgAwzC1Xq99R2y7B0
  - id: 58
    keyword: "Error in query: No such struct field"
    samples: 5
  - id: 59
    keyword: "Error in query: Undefined function: 'regexp'"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/zkwo6jgTRb1oCfDvPR6S
  - id: 60
    keyword: "Error in query: grouping_id()"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/YZchOZDLE3IGM2I2AeiZ
  - id: 61
    keyword: "Error in query: No handler for UDF/UDAF/UDTF"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/SxqFporbSLlRMqa1Tt8h
  - id: 62
    keyword: "Error in query: Window function row_number()"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/H1IrwsdJhiK4yTSWEjCG
  - id: 63
    keyword: "The current account does not have the permission of database"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/1lHNYMjfdvMcD5Ds7bd6
  - id: 64
    keyword: "INFO Partition "
    samples: 10
  - id: 65
    keyword: ": add partition command"
    samples: 10
  - id: 66
    keyword: "Spark UI:"
    samples: 10
  - id: 67
    keyword: "com.jd.bi.hive.udf.RowNumberUDF"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/qc4h2hbwqAowWzdhkcLt
  - id: 68
    keyword: "current_timestamp"
    samples: 5
  - id: 69
    keyword: "SingleEventLogFileWriter: Logging events to"
    samples: 5
  - id: 70
    keyword: "key:ht"
    samples: 10
  - id: 72
    keyword: "Unsupported shuffle manager of executor"
    samples: 10
    solutionLink: https://joyspace.jd.com/pages/oy9ocxwX49aBmBTvVAzK
  - id: 73
    keyword: "is ambiguous"
    samples: 10
  - id: 74
    keyword: "ERROR YarnScheduler: Lost executor"
    samples: 20
  - id: 75
    keyword: "Region data is not ready"
    samples: 10
    solutionLink: https://joyspace.jd.com/pages/N0v2mlmLBCRJBPnqjWEE
  - id: 76
    keyword: "The DiskSpace quota"
    samples: 10
    solutionLink: https://joyspace.jd.com/pages/GoFB2ZmGUsoGhLv7EDz4
  - id: 77
    keyword: "平台日志：.*已接受执行命令"
    samples: 2
  - id: 78
    keyword: "Could not execute broadcast in"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/cNvmxxsxEEorLtWKzr5k
  - id: 79
    keyword: "Invocation returned exception"
    samples: 20
    solutionLink: https://joyspace.jd.com/pages/oMVsyjN7ITqOzkx7vfoe
  - id: 80
    keyword: "Detected implicit cartesian product"
    samples: 2
  - id: 81
    keyword: "Starting Job =.*Tracking URL = .*"
    samples: 50
  - id: 82
    keyword: "A fatal error has been detected"
    samples: 5
  - id: 83
    keyword: "Query exceeded maximum split"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/hncEyuX5nF7m5hdiDGvc
  - id: 84
    keyword: "Caused by: org.apache.hadoop.ipc.RemoteException"
    samples: 20
    solutionLink: https://joyspace.jd.com/pages/4T2oz5bTrB3RD82PcurS
  - id: 85
    keyword: "datalake.*jar"
    samples: 2
  - id: 86
    keyword: "io.prestosql.sql.parser.ParsingException"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/uzf3kh3QrURNEZIIWZko
  - id: 87
    keyword: "io.prestosql.spi.PrestoException: Query exceeded"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/WBkuhyXqLvNGNgtc0O7o
  - id: 88
    keyword: "io.prestosql.orc.OrcCorruptionException:"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/HMUUsKT6sfgyJrlbqPOY
  - id: 89
    keyword: "io.prestosql.spi.PrestoException: Division by zero"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/1O3smtvV4GrApv6RGuf4
  - id: 90
    keyword: "Python in worker has different version"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/G0isXf3UF58sid66ArNm
  - id: 91
    keyword: "查杀原因"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/9bbGoQPidgz8wtYoiWJP
  - id: 92
    keyword: "Can't zip RDDs"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/GoDmwK4tNLJI0u7twQmz
  - id: 93
    keyword: "org.apache.iceberg.exceptions.ValidationException"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/TA6ghCVqBL8ug7HzgWvE
  - id: 94
    keyword: "HiveMetaStoreClient.*connect to metastore"
    samples: 5
  - id: 95
    keyword: "java.lang.ClassNotFoundException"
    samples: 5
  - id: 96
    keyword: "平台日志：任务ID：(\\d+)_action开始执行"
    samples: 1
    group: 1
  - id: 97
    keyword: "Caused by: java.lang.RuntimeException"
    samples: 10
  - id: 98
    keyword: "BaseExpireSnapshotsSparkAction: Deleted"
    samples: 10
  - id: 99
    keyword: "ShadowUtils:"
    samples: 20
  - id: 100
    keyword: "TaskMemoryManager: Failed to allocate a page"
    samples: 10
    solutionLink: https://joyspace.jd.com/pages/5M3d1FKpGSAHLhZxtBF1
  - id: 101
    keyword: "Iceberg table create is not allowed"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/z21cR0GBAoJQuKMY0Ygg
  - id: 102
    keyword: "AmIpFilter.param.PROXY_URI_BASES"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/zJEuysC1TA59MtQgktB9
  - id: 103
    keyword: "GetSplits finish"
    samples: 20
  - id: 104
    keyword: "Caused by: java.lang.NullPointerException"
    samples: 5
  - id: 104
    keyword: "type:ETL_RUN_FAIL; msg:Broker list"
    samples: 5
  - id: 105
    keyword: "at org.apache.spark.sql.execution.datasources.orc.OrcColumnVector"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/QX2sfZZBnuhu10dpvLcZ
  - id: 106
    keyword: "org.apache.spark.SparkException.*java.lang.StackOverflowError"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/8EC0e2SfSVRVaHviQIB7
  - id: 107
    keyword: "Caused by: java.lang.ArrayIndexOutOfBoundsException"
    samples: 5
  - id: 108
    keyword: "ExchangeCoordinator.scala:314"
    samples: 5
  - id: 109
    keyword: "The url to track the job: "
    samples: 50
  - id: 110
    keyword: "Stage.*Map.*Reduce.*Elapsed"
    samples: 50
  - id: 111
    keyword: "will retry command"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/tKl1h5AK4FkXE37ly3Rs
  - id: 112
    keyword: "CheckAnalysis.failAnalysis"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/gnRMpHl4qpaf1l9lVSGx
  - id: 113
    keyword: "CheckAnalysis\\$class.failAnalysis"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/gnRMpHl4qpaf1l9lVSGx
  - id: 114
    keyword: "ImportError: No module named"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/YrQ7VZNKY2pFOFELKx2H
  - id: 115
    keyword: "SemanticException"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/rO8V2kCrLEOwM18TaJoA
  - id: 116
    keyword: "SCHEMA_NOT_FOUND"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/xCu1XPY4fWWSoyrZiXTq
  - id: 117
    keyword: "TABLE_OR_VIEW_NOT_FOUND"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/gNj4wOJOvpM0rQfqTkeM
  - id: 118
    keyword: "nondeterministic expressions are only allowed"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/fJOn05WG38XUcDkK9Qfa
  - id: 119
    keyword: "com.jd.compute.compare.Result"
    samples: 50
  - id: 120
    keyword: "free data blocks which is less than minimum required"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/Q0WcrBAzz7FFGpFZOJhV
  - id: 121
    keyword: "Exception message: Unable to find image"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/fJOn05WG38XUcDkK9Qfa
  - id: 122
    keyword: "Cannot broadcast the table that is larger than 8GB"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/0MXF7hphLDuZyMVogPgy
  - id: 123
    keyword: "WriteNsWhiteListProxyWrapper"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/1gaI5QgqaB8xOOQRJwYu
  - id: 124
    keyword: "UNRESOLVED_ROUTINE"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/OIKYUjpbNQqNJ4uXWKp6
  - id: 125
    keyword: "org.apache.spark.SparkFileNotFoundException: File does not exist"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/hK8528ZqaX3QsCifKYm4
  - id: 126
    keyword: "requires that the data to be inserted have the same number of columns as the target table"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/gSSV8901pNM8ig5JHDGo
  - id: 127
    keyword: "Operation not allowed: ROW FORMAT DELIMITED is only compatible with"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/Ff36jg0e1riXEXvtb68z
  - id: 128
    keyword: "PARSE_SYNTAX_ERROR"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/DC7E1Zpp7LtY6KktSLth
  - id: 129
    keyword: "mismatched input 'wangriyu_test' expecting"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/DC7E1Zpp7LtY6KktSLth
  - id: 130
    keyword: "com.jd.compute.compare.Result: \\(2,"
    samples: 50
  - id: 131
    keyword: "错误码：buffalo_error_00000002"
    samples: 50
  - id: 132
    keyword: "fields than the actual ORC physical schema"
    samples: 50
  - id: 133
    keyword: "java.lang.ExceptionInInitializerError"
    samples: 50
  - id: 134
    keyword: "超时失败"
    samples: 50
  - id: 135
    keyword: "MetaException"
    samples: 50
  - id: 200
    keyword: "com.jd.compute.compare.Result"
    samples: 50
  - id: 201
    keyword: "mismatched input 'wangriyu_test'"
    samples: 50
  - id: 202
    keyword: "mismatched input 'zmy_test'"
    samples: 50
  - id: 203
    keyword: "Unable to get the specify cluster by params"
    samples: 50
  - id: 204
    keyword: "Executor heartbeat timed out"
    samples: 50
  - id: 205
    keyword: "Job aborted due to stage failure"
    samples: 50
  - id: 206
    keyword: "没找到表名"
    samples: 50
  - id: 207
    keyword: "退出码：100"
    samples: 50
  - id: 208
    keyword: "退出码：137"
    samples: 50
  - id: 209
    keyword: "Table or view not found:"
    samples: 50
  - id: 210
    keyword: "has been deleted"
    samples: 50
  - id: 211
    keyword: "java.net.UnknownHostException"
    samples: 50
  - id: 212
    keyword: "接受到终止指令"
    samples: 50
  - id: 213
    keyword: "java.io.IOException: Not a file:"
    samples: 50
  - id: 214
    keyword: "Cannot overwrite a path that is also being read from"
    samples: 50
    solutionLink: https://joyspace.jd.com/pages/sA9K3PPw7MiYDS3a0eUC
  - id: 215
    keyword: "平台日志：接受到终止指令"
    samples: 2
  - id: 216
    keyword: "retry sql engine hive to"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/T0RkfdKYbkIkpqIB1ZwQ
  - id: 217
    keyword: "CalcitePlanner.fixUpAfterCbo"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/iEi0UHKBb2iMuQjHUuPt
  - id: 218
    keyword: "MISSING_AGGREGATION"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/r4CC0K5HQUdkSA9O9gln
  - id: 219
    keyword: "UNSUPPORTED_EXPR_FOR_WINDOW"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/USNAhB1L7oFOeMGVqLIz
  - id: 220
    keyword: "Operation not allowed: CREATE TEMPORARY TABLE"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/Z1HMgAwzC1Xq99R2y7B0
  - id: 221
    keyword: "java.lang.OutOfMemoryError: GC overhead limit exceeded"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/L2pelpR3Nv73g3M7lGcV
  - id: 222
    keyword: "java.lang.IllegalArgumentException: spark.sql.files.maxPartitionBytes should be long"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/G3k4PLv9NE5zApkUddi5
  - id: 223
    keyword: "no viable alternative at input"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/720VFyW4zmeZxZBsd9CD
  - id: 224
    keyword: "Caused by: java.lang.ClassNotFoundException: org.apache.spark.sql.hudi.catalog.HoodieCatalog"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/2RS1cDnON6ABZHcPzWF6
  - id: 225
    keyword: "Exception in thread \"main\" java.lang.LinkageError"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/0yjgcpgqM8NTOXbyWeQY
  - id: 226
    keyword: "WARNING 将会使用spark_2.4重试"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/720VFyW4zmeZxZBsd9CD
  - id: 227
    keyword: "when resolving method \"org.slf4j.impl.StaticLoggerBinder.getLoggerFactory()"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/0yjgcpgqM8NTOXbyWeQY
  - id: 228
    keyword: "Caused by: java.lang.ClassNotFoundException: org.apache.spark.sql.hudi.analysis.HoodieSpark32PlusAnalysis\\$HoodieV1OrV2Table\\$"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/wQtngsT5Rxu9tDvEZQDR
  - id: 229
    keyword: "Get the GetMapOutputStatuses Response"
    samples: 10
  - id: 230
    keyword: "finalSplitBytes:"
    samples: 10
  - id: 231
    keyword: "broadcastSide:"
    samples: 10
  - id: 232
    keyword: "update spark version"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/BiWXHdJ0na9bZ8SLlYjn
  - id: 233
    keyword: "Master must either be yarn or start with spark, mesos, k8s, or local"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/rqEGYBWAxdfwSrK0Rx7l
  - id: 234
    keyword: "Number of dynamic partitions created is"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/q81LJnT6R6E4ErHTDPsz
  - id: 235
    keyword: "skip_mr_merge_auto"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/0Nn39Ws0RoHS6OyBwmUu
  - id: 236
    keyword: "DAGSchedulerEventProcessLoop failed"
    samples: 2
    solutionLink: https://joyspace.jd.com/pages/1yQrHHr4XA7xOVbPflCt
  - id: 237
    keyword: "ApplicationMaster host"
    samples: 10
  - id: 238
    keyword: "Failed to send LogicalPlan to JDQ"
    samples: 10
    solutionLink: https://joyspace.jd.com/pages/6X16uNzDcXikrfMgnita
  - id: 239
    keyword: "java.lang.IllegalArgumentException: Comparison method violates its general contract"
    samples: 10
    solutionLink: https://joyspace.jd.com/pages/meaxVmTVkw0iaxOtKEZM
  - id: 240
    keyword: "AbstractTableFileSystemView: Could not read commit details from"
    samples: 20
  - id: 241
    keyword: "MalformedInputException"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/xDKAdxNExLRxRH8dJSb5
  - id: 242
    keyword: "DynamicByteArray.java:115"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/mM1tM8SVGf0JXn9ZUUsE
  - id: 243
    keyword: "Caused by: java.lang.IllegalArgumentException: spark.blacklist.task.maxTaskAttemptsPerNode"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/k9aZyrV0s5V8FMUX2wcY
  - id: 244
    keyword: "COLUMN_ALREADY_EXISTS"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/48RrIcbIbYEoR0B173fw
  - id: 245
    keyword: "federation.router.NoNamenodesAvailableException"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/bisMiHd3RFXflSTK5qxT
  - id: 246
    keyword: "ConnClickHouseUtil.getClickHouseConnection"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/GiKZe9nCHOAmiiVNQhFo
  - id: 247
    keyword: "Buffer size too small"
    samples: 5
    solutionLink: https://joyspace.jd.com/pages/T9yjv6uVSvyTUVfM64Fy