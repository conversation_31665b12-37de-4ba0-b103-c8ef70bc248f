<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <!--加载数据库配置文件-->
    <settings>
        <!-- 打开延迟加载的开关 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 将积极加载改为消极加载（即按需加载） -->
        <setting name="aggressiveLazyLoading" value="false"/>
        <!-- 打开全局缓存开关（二级缓存）默认值就是 true -->
        <setting name="cacheEnabled" value="true"/>
        <!--开启驼峰命名法-->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 打印sql查询语句 -->
        <!--<setting name="logImpl" value="STDOUT_LOGGING"/>-->
    </settings>



    <typeAliases>
        <!--它的原理就是扫描指定包下的类，这些类的全路径名都被自动赋予了与类同名的别名，不区分大小写-->
<!--        <package name="com.jmr"/>-->
    </typeAliases>

    <environments default="default">
        <environment id="default">
            <transactionManager type="JDBC"></transactionManager>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.jdbc.Driver"/>
                <property name="url" value="***********************************************************************************************************************************************************************"/>
                <property name="username" value="salt"/>
                <property name="password" value="salt"/>
            </dataSource>
        </environment>
        <environment id="jmr_dev">
            <transactionManager type="JDBC"></transactionManager>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.jdbc.Driver"/>
                <property name="url" value="*****************************************************************************************************************************************************"/>
                <property name="username" value="root"/>
                <property name="password" value="dabao@Xw12"/>
            </dataSource>
        </environment>
        <environment id="buffalo">
            <transactionManager type="JDBC"></transactionManager>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.jdbc.Driver"/>
                <property name="url" value="*************************************************************************************************************************************************************************"/>
                <property name="username" value="dispatch_1_0_ro"/>
                <property name="password" value="pXRirVcfdZ7SbXQdiFnV4diFdF9OVUCj"/>
            </dataSource>
        </environment>
        <environment id="suqian">
            <transactionManager type="JDBC"></transactionManager>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.jdbc.Driver"/>
                <property name="url" value="**************************************************************************************************************************************************************************"/>
                <property name="username" value="datavirtual_rw"/>
                <property name="password" value="t23dnM15R_djPh9H"/>
            </dataSource>
        </environment>
    </environments>

    <mappers>
        <mapper resource="mapper/buffalo/SparkUpgradeBuffaloMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeAssessmentMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeErrorCodeMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeExplainMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeSysConfigMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeTaskErrorCodeRltMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeTaskMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeVersionMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeTestMapper.xml"></mapper>
        <mapper resource="mapper/spark/SparkUpgradeBigGraphMapper.xml"></mapper>
        <!-- 使用package标签的作用是一次性引入com.jd.myMapper包下的所有的sql映射文件，但是这种引入方式有一个限制： sql映射文件对应的接口类必须放在同一个，且包名中不能有正则表达式，否则将不能执行sql查询,不推荐使用这种导入方式 -->
        <!--<package name="com.jd.myMapper"/>-->
    </mappers>

</configuration>



