#!/bin/bash
set -x

sparkVersion=$1
TASK_ID=$2
cluster=$3
sparkUrl=$4
if [[ $cluster == '10k' ]];then
  queue=root.bdp_jdw_dd_edw_bdp_spark
  hmsUrl="thrift://**************:10113"
else
  queue=bdp_jdw_dd_edw_spark
  hmsUrl="thrift://**************:10114"
fi

git clone https://coding.jd.com/benchmark/DoubleRunScript.git -b bdp_ide_branch
source DoubleRunScript/${TASK_ID}.sh

export BEE_SOURCE=SPARK_3_UPGRADE
export BEE_USER=org.nature.data1
export BEE_BDP_TASK_LEVEL=10

echo "\n----查看sql文本--Start--------\n"

cat DoubleRunScript/${TASK_ID}_${sparkVersion}.sql

echo "\n----查看sql文本--End--------\n"

if [[ $sparkVersion == "2_4" ]]; then
  export SPARK_HOME=/software/servers/spark
  export PATH=$SPARK_HOME/bin:$PATH
elif [[ $sparkVersion == "3_4" ]]; then
  # 版本一：下载3.4的包
  # tgzname=`basename $sparkUrl`
  # dirname=`basename $sparkUrl .tgz`
  # if [ -e "$tgzname" ]; then
  #   \rm $tgzname
  #   echo "$tgzname has been deleted."
  # fi
  # if [ -e "$dirname" ]; then
  #   \rm -r $dirname
  #   echo "$dirname has been deleted."
  # fi
  # wget $sparkUrl
  # tar -xf $tgzname
  # export SPARK_HOME=`pwd`/$dirname
  # export PATH=$SPARK_HOME/bin:$PATH

  # 版本二：使用正式3.4的包
  export SPARK_HOME=/software/servers/spark_3.4
  export PATH=$SPARK_HOME/bin:$PATH
fi

spark-sql --version

echo "\n--------------\n"

# file_content=$(cat DoubleRunScript/${TASK_ID}.sql)
# eval "parsed_content=\"$file_content\""

spark-sql --queue $queue \
--conf spark.jd.kongming.optimize.enable=false \
--conf spark.hadoop.hive.metastore.uris=$hmsUrl \
--conf spark.hadoop.hive.exec.dynamic.partition.mode=nonstrict \
--conf spark.sql.source=HiveTask \
--conf spark.isLoadHivercFile=true \
--conf spark.sql.tempudf.ignoreIfExists=true \
--conf spark.sql.crossJoin.enabled=true \
--conf spark.submit.deployMode=client \
--conf spark.dynamicAllocation.enabled=true \
--conf spark.shuffle.service.enabled=true \
--conf spark.speculation=true \
--conf spark.sql.hive.convertMetastoreOrc=true \
--conf spark.sql.parser.quotedRegexColumnNames=true \
--conf spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version=2 \
--hiveconf hive.exec.orc.split.strategy=BI \
--conf spark.hadoop.dfs.client.block.pread.support.switch.enabled=false \
--conf spark.sql.viewPermission.enabled=true \
--conf spark.sql.parser.quotedRegexColumnNames.isRegex.enabled=true \
--conf spark.sql.externalCatalog.requireDbExists.enabled=false \
--conf spark.hadoop.hive.exec.max.dynamic.partitions=10000 \
--conf spark.sql.shuffle.partitions=5000 \
--conf spark.speculation.quantile=0.7 \
--conf spark.sql.adaptive.forceOptimizeSkewedJoin=true  \
--conf spark.dynamicAllocation.maxExecutors=5000 \
-f DoubleRunScript/${TASK_ID}_${sparkVersion}.sql
