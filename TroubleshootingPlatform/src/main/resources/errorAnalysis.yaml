"nondeterministic expressions are only allowed": "nondeterministic"
"SCHEMA_NOT_FOUND": "schema not found"
"WriteNsWhiteListProxyWrapper": "white ns"
"UNRESOLVED_ROUTINE": "unresolved routine"
"org.apache.spark.SparkFileNotFoundException: File does not exist": "file does not exist"
"requires that the data to be inserted have the same number of columns as the target table": "columns num mismatch"
"TABLE_OR_VIEW_NOT_FOUND": "table not found"
"Operation not allowed: ROW FORMAT DELIMITED is only compatible with": "row format"
"PARSE_SYNTAX_ERROR": "syntax error"
"mismatched input 'wangriyu_test' expecting": "multi table"
"com.jd.compute.compare.Result: \\(2,": "code 2"
"错误码：buffalo_error_00000002": "cgroup exceed"
"fields than the actual ORC physical schema": "schema check error"
"java.lang.ExceptionInInitializerError": "create temp function error"
"超时失败": "running timeout"
"Could not execute broadcast in 600 secs": "broadcast timeout"
"com.jd.compute.compare.Result": "different row number"
"mismatched input 'wangriyu_test'": "table parse error"
"mismatched input 'zmy_test'": "table parse error"
"Unable to get the specify cluster by params": "queue error"
"Executor heartbeat timed out": "Executor heartbeat timed out"
"Job aborted due to stage failure": "stage failure"
"没找到表名": "table not found"
"退出码：100": "table not found"
"退出码：137": "instance killed"
"接受到终止指令": "instance killed"
"Table or view not found:": "Table or view not found"
"has been deleted": "Table has been deleted"
"java.net.UnknownHostException": "UnknownHostException"
"java.io.IOException: Not a file:": "Not file"