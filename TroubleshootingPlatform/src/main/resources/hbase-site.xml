<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
    <property>
        <name>hbase.client.scanner.caching</name>
        <value>200</value>
    </property>
    <property>
        <name>hbase.client.write.buffer</name>
        <value>5242880</value>
    </property>
    <property>
        <name>bdp.hbase.instance.name</name>
        <value>SL1000000003269</value>
    </property>
    <property>
        <name>bdp.hbase.accesskey</name>
        <value>bdp_buffalo_master_read_C1A520</value>
    </property>
    <property>
        <name>bdp.api.url</name>
        <value>http://10.187.138.120:1601/ops/api/hbase/accesskey/v2/getTbNamesByAccessKey.ajax</value>
    </property>
    <property>
        <name>bdp.hbase.erp</name>
        <value>test_user</value>
    </property>
</configuration>