package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * author: xiaowei79
 * description: spark3.4只能逻辑计划校验的结果
 * date: 2024-10-29 18:07:00
 */
@ToString
@Getter
@Setter
public class SparkUpgradeExplainBean {
    private Long id;
    private String cluster;
    private String mart;
    private String queue;
    private String account;
    private String originSqlTxt;  // 用户原始sql
    private String newSqlTxt;     // 改写后的sql，字段废弃
    private String status;
    private String details;
    private String buffaloId;
    private String logId;
    private String userErp;
    private String createTime;
}
