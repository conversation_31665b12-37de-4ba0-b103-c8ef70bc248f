package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class BloodRequestBean {
    private String vertexLabel;  // 当前顶点类型
    private String key; // 顶点业务主键（返回节点中key）任务：taskId:beeSource（如 1366432    :BUFFALO4）
    private String direct; // 扩展方向（up或down），上下游都查使用both
    private Integer level; // 扩展层级，空则使用默认值1【鉴于集群稳定性和接口性能，建议level只传1】
    private String resultType; // 返回格式，枚举值1、2、3分别表示树形、列表、树列表。
    private Map<String, Object> filter; // 过滤条件,根据名称、顶点类型过滤

    public BloodRequestBean() {}

    @Override
    public String toString() {
        return "BloodRequestBean{" +
                "vertexLabel='" + vertexLabel + '\'' +
                ", key='" + key + '\'' +
                ", direct='" + direct + '\'' +
                ", level=" + level +
                ", resultType='" + resultType + '\'' +
                ", filter=" + filter +
                '}';
    }
}
