package com.jd.bdp.bean;

public enum EnumBuffaloTaskStatus {
    INITIAL(0, "Initialization Successful"),
    FORK_TASK_SUCCESS(1, "Fork task successful"),
    FORK_TASK_ERROR(2, "Fork task failed")
    ;
    public int statusCode;
    public String description;

    EnumBuffaloTaskStatus(int statusCode, String description) {
        this.statusCode = statusCode;
        this.description = description;
    }
}
