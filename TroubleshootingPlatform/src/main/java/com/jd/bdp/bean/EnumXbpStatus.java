package com.jd.bdp.bean;

public enum EnumXbpStatus {
    INITIAL(0, "初始化状态"),
    CREATING_XBP(1, "待创建审批流"),
    CREATED_XBP(2, "待用户审批"),
    APPROVED_XBP(3, "用户已审批通过"),
    REJECT_XBP(8, "用户拒绝审批"),
    REVOKE_XBP(10, "单子已撤回"),
    PLATFORM_RECHECK_APPROVED(11, "平台复核通过"),
    PLATFORM_RECHECK_REJECT(12, "平台复核驳回"),
    FAILED(9, "失败"),
    COMPLETED(100, "流程结束");

    public Integer statusCode;
    public String description;
    EnumXbpStatus(Integer statusCode, String description) {
        this.statusCode = statusCode;
        this.description = description;
    }
}
