package com.jd.bdp.bean;

import java.util.Arrays;
import java.util.List;

public class XbpForm {
    public static final String[] engineUpdated = new String[]{"引擎类型修改为", "引擎类型升级至"};
    public static final String[] engineVersionUpdated = new String[]{"引擎版本修改为", "引擎版本升级至"};
    public static final List<String> sparkVersions = Arrays.asList("default", "3.0", "3.2", "3.4", "rss", "rss_l3");
    public static final List<String> hiveVersions = Arrays.asList("default");
    public static final List<String> FLOW_NAME = Arrays.asList("执行引擎升级", "同意", "任务负责人处理", "任务负责人治理");
    public static final List<String> FLOW_NAME_PLATFORM_RECHECK = Arrays.asList("平台复核");

    public static final List<String> FLOW_NAME_INSPECTION = Arrays.asList("任务负责人");
    public static final List<String> FLOW_NAME_PRESS = Arrays.asList("是否压测");
    public static final List<String> FLOW_NAME_PRESTO = Arrays.asList("业务负责人");
    public static final List<String> FLOW_NAME_INSPECTION_2 = Arrays.asList("任务负责人确认", "任务负责人治理", "任务负责人处理");

    public static final String SOURCE_ZI_ZHU = "自助提数";

    public static final String PROCESS_NAME_TABLE_OWNER_APPROVAL = "表负责人审批";
}
