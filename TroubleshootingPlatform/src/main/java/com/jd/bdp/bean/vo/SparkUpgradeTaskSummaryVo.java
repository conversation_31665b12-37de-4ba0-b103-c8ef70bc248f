package com.jd.bdp.bean.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * author: xiaowei79
 * description:
 * date: 2024-11-13 01:37:00
 */
@Getter
@Setter
@ToString
public class SparkUpgradeTaskSummaryVo {
    private Integer taskId;
    private String runStatus;     // 最新双跑状态： success、fail
    private Long duration24;      // 需要累加
    private Long duration34;      // 需要累加
    private Double jobNum;        // 静态值
    private Boolean isBaseLine;   // 静态值
    private Boolean isP95;        // 静态值
    private Double vCores;        // 静态值
    private String upgradeStatus;  // 静态值升级状态： UPGRADE、ROLLBACK
    private List<String> instanceStatus = new ArrayList<>();  // 各个双跑环节的状态
    
    
    public void addAccumulateVal(String newRunStatus, Long newDuration24, Long newDuration34){
        instanceStatus.add(newRunStatus);
        long sucCount = instanceStatus.stream().filter(item -> "success".equals(item)).count();
        long failCount = instanceStatus.stream().filter(item -> "fail".equals(item)).count();
        if(failCount > 0){
            this.runStatus = "fail";
        }else if(sucCount == instanceStatus.size()){
            this.runStatus = "success";
        }else{
            this.runStatus = "run";
        }
        
        this.duration24 += (newDuration24 == null ? 0 :  newDuration24);
        this.duration34 += (newDuration34 == null ? 0 : newDuration34);
    }
    
    public void setRunStatus(String runStatus){
        this.runStatus = runStatus;
        this.instanceStatus.add(runStatus);
    }
}
