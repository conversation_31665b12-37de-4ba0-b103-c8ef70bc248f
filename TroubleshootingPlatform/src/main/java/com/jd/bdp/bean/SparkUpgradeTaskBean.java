package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
public class SparkUpgradeTaskBean {
    private Long id;
    private Integer taskId;
    private String originVersion;
    private String status;
    private String createTime;
    private String creator;
    private String updateTime;
    private String doubleRunTaskId;
    
    public SparkUpgradeTaskBean() {
    }

    public SparkUpgradeTaskBean(Integer taskId, String originVersion, String status, String createTime, String creator) {
        this.taskId = taskId;
        this.originVersion = originVersion;
        this.status = status;
        this.createTime = createTime;
        this.creator = creator;
    }

    public SparkUpgradeTaskBean(Long id, Integer taskId, String originVersion, String status, String createTime, String creator, String updateTime) {
        this.id = id;
        this.taskId = taskId;
        this.originVersion = originVersion;
        this.status = status;
        this.createTime = createTime;
        this.creator = creator;
        this.updateTime = updateTime;
    }

    public SparkUpgradeTaskBean(Long id, Integer taskId, String originVersion, String status, String createTime, String creator, String updateTime, String doubleRunTaskId) {
        this.id = id;
        this.taskId = taskId;
        this.originVersion = originVersion;
        this.status = status;
        this.createTime = createTime;
        this.creator = creator;
        this.updateTime = updateTime;
        this.doubleRunTaskId = doubleRunTaskId;
    }
}
