package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BuffaloTaskRunLogBean {
    private Long takeId;
    private String instanceId;
    private String runStatus;

    public BuffaloTaskRunLogBean() {
    }

    @Override
    public String toString() {
        return "BuffaloTaskRunLogBean{" +
                "takeId=" + takeId +
                ", instanceId='" + instanceId + '\'' +
                ", runStatus='" + runStatus + '\'' +
                '}';
    }
}
