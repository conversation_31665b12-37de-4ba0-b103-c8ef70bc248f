package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * author: xiaowei79
 * description: spark升级双跑任务日志bean
 * date: 2024-10-30 22:38:00
 */
@ToString
@Getter
@Setter
public class SparkUpgradeBuffaloLogBean {
    private Integer doubleRunTaskId;    // 双跑任务id
    private Long doubleRunInstanceId;   // 双跑任务实例id
    private Long doubleRunLogId;        // 双跑日志id

    public String getDoubleRunLogIdentity(){
        return String.format("%s##%s##%s", this.doubleRunTaskId, this.doubleRunInstanceId, this.doubleRunLogId);
    }
}
