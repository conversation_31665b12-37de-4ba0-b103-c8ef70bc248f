package com.jd.bdp.bean.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * author: xiaowei79
 * description:
 * date: 2024-11-12 23:25:00
 */
@Setter
@Getter
@ToString
public class DoubleRunTaskSummaryBo {
    private Integer taskId;
    private Integer doubleRunTaskId;
    private Long latestDoubleRunTaskInsId;
    private String runTime;
    private String runStatus;
    private Long totalDuration24;
    private Long totalDuration34;
}
