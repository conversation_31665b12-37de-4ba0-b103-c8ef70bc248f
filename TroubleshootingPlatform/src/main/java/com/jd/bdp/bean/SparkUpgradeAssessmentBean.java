package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * author: xiaowei79
 * description: Spark升级任务摸底全景Bean
 * date: 2024-10-28 19:00:00
 */
@ToString
@Getter
@Setter
public class SparkUpgradeAssessmentBean {
    private String beeSource;            // 任务来源
    private String taskId;               // 任务id
    private String accountPrincipalOneBgname;  //  子集团
    private String accountPrincipalOneDept1Name; // 一级部门
    private String accountPrincipalOneDept2Name; // 二级部门
    private Double taskDurationMinutes;  // 任务执行平均耗时，单位：分钟
    private Double reqVcoreDay;          // 任务一天所有核数
    private Double sumExeMin;            // 任务一天总耗时
    private Double jobNum;              // 任务一天总application数
    private Integer upgradeTaskId;
    private Double reqVcoreM09;
    private Double reqVcoreM10;
    private String allHivetask;         // 任务所有环节是否都是hiveTask任务
    private String priority;             // 任务等级
    private String taskStatus;
    private String task_first_category;
    private String task_2nd_category;
    private Double execLongFirstMinutes;
    private Double execLong1dayMinutes;
    private String taskInfoDt;
    private Double execLong20240531;
    private Double execLong20231031;
    private Double execLong20241031;
    private Integer isHive;
    private Integer isSpark;
    private Double maxShuffleTB;
    private Double sumShuffleTB;
    private String status;
    private Integer level;
    private Integer p95;
    private String baseline;
    private String isCriticalPath;
    private String isDoubleRunTaskCreated;
    private Long duation24;  // 废弃
    private Long duation34;  // 废弃
    private BigDecimal explainIsFailed;
    private Long instAllSuccess;
    private Long instRun;
    private Long instFail;
    private Long duration24;
    private Long duration34;
    private Long maxLatestInstId;
    private String maxLastInstRuntime;
    private String upgradeStatus;
    private String upgradeDowngradeDate;
}
