package com.jd.bdp.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class SparkMetricBean {
    private long inputBytes;
    private long outputBytes;
    private long shuffleReadBytes;
    private long shuffleWriteBytes;
    private long executorRunTime;

    @Override
    public String toString() {
        return "inputBytes\t" + inputBytes +
                "\toutputBytes\t" + outputBytes +
                "\tshuffleReadBytes\t" + shuffleReadBytes +
                "\tshuffleWriteBytes\t" + shuffleWriteBytes +
                "\texecutorRunTime\t" + executorRunTime;
    }
}
