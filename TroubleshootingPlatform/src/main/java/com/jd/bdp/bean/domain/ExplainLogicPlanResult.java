package com.jd.bdp.bean.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * author: xiaowei79
 * description: explain结果
 * date: 2024-11-18 10:59:00
 */

@NoArgsConstructor
@AllArgsConstructor
@Data
public class ExplainLogicPlanResult {
    private String taskId;
    private boolean isExplainSuc;                   // explain是否成功
    private boolean isNeedAnalysisErrorCode;        // 是否需要分析错误码
    private String details;                         // 详情 
}
