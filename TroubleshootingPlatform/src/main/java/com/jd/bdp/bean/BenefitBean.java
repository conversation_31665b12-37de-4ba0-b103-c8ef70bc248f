package com.jd.bdp.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class BenefitBean {
    private String dbName;
    private String tblName;
    private String ownerERP;
    private String department;
    private Long previousStorage;
    private Long savedStorage;
    private String xbpStatus;
    private String xbpLink;
    private String mergeStatus;
    private String compressStatus;
    private String checkStatus;
    private String checkataStatus; // 在前端展示为备注
    private Double replication; // 副本数
}
