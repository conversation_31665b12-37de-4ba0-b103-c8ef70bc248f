package com.jd.bdp.bean.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * author: xiaowei79
 * description: 双跑任务耗时对比
 * date: 2024-11-05 20:54:00
 */
@ToString
@Getter
@Setter
public class DoubleRunTaskBaselineBo {
    private Integer totalBaseLineTaskNum;     // 双跑成功的任务中基线任务个数
    private Integer totalP95TaskNum;          // 双跑成功的任务中p95任务个数
    private Long totalJobNum;              // 总query数
}
