package com.jd.bdp.bean.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * author: xiaowei79
 * description:
 * date: 2024-09-25 03:20:00
 */
public enum SparkUpgradeTaskStatusEnum {
    UN_EXE("UN_EXE", "执行计划待校验"),
    EXPLAINING("EXPLAINING", "执行计划校验中"),
    EXPLAIN_FAILED("EXPLAIN_FAILED", "执行计划校验失败"),
    EXPLAIN_SUCCESS("EXPLAIN_SUCCESS", "执行计划校验成功"),
    DOUBLE_RUN_TASK_CREATING("DOUBLE_RUN_TASK_CREATING", "双跑任务创建中"),
    DOUBLE_RUN_TASK_CREATE_FAILED("DOUBLE_RUN_TASK_CREATE_FAILED", "双跑任务失败"),
    DOUBLE_RUN_TASK_CREATED("DOUBLE_RUN_TASK_CREATED", "双跑中任务已创建"),
    DOUBLE_RUNNING("DOUBLE_RUNNING", "双跑中"),
    DOUBLE_RUN_FAILED("DOUBLE_RUN_FAILED", "双跑失败"),
    DOUBLE_RUN_SUCCESS("DOUBLE_RUN_SUCCESS", "双跑成功"),
    NOTICED("NOTICED", "已通知用户升级"),
    UPGRADE_FAILED("UPGRADE_FAILED", "升级失败"),
    UPGRADED("UPGRADED", "已升级"),
    ROLLBACKED("ROLLBACKED", "已回滚"),
    USERCHECK("USERCHECK", "用户自校验"),
    UNKNOWN("UNKNOWN", "未知状态");
    
    private String code;
    private String name;

    SparkUpgradeTaskStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<String> needRerunStatus(){
        List<String> list = new ArrayList<>();
        list.add(UN_EXE.getCode());
//        list.add(EXPLAIN_FAILED.getCode());
//        list.add(DOUBLE_RUN_FAILED.getCode());
//        list.add(UNKNOWN.getCode());
        return list;
    }
    @Override
    public String toString() {
        return "SparkUpgradeTaskStatusEnum{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
