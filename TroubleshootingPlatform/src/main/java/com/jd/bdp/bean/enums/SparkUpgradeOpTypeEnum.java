package com.jd.bdp.bean.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * author: xiaowei79
 * description:
 * date: 2024-09-25 03:20:00
 */
public enum SparkUpgradeOpTypeEnum  {
    UPGRADE("UPGRADE", "升级任务"),
    ROLLBACK("ROLLBACK", "回滚任务"),
    RESET("ROLLBACK", "重置任务"),
    OTHER("OTHER", "其他操作");
    
    private String code;
    private String name;
    private static Set<String> highVersion = new HashSet<>(Arrays.asList("3.4", "3.4_preview"));
    private static Set<String> lowVersion = new HashSet<>(Arrays.asList("default", "3.0", "rss", "rss_l3", "3.2"));
    
    SparkUpgradeOpTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    
    public static SparkUpgradeOpTypeEnum calc(String fromVersion, String toVersion){
        if(StringUtils.isEmpty(fromVersion)){
            fromVersion = "default";
        }
        if(StringUtils.isEmpty(toVersion)){
            toVersion = "default";
        }
        
        if(lowVersion.contains(fromVersion) && highVersion.contains(toVersion)){
            return UPGRADE;
        }else if(highVersion.contains(fromVersion) && lowVersion.contains(toVersion)){
            return ROLLBACK;
        }else {
            return OTHER;
        }
    }

    @Override
    public String toString() {
        return "SparkUpgradeOpTypeEnum{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
