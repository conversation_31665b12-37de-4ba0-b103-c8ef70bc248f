package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * author: xiaowei79
 * description: spark任务跟错误码的关联关系
 * date: 2024-10-30 18:07:00
 */
@ToString
@Getter
@Setter
public class SparkUpgradeTasKErrorRltBean {
    private Long id;
    private Integer originTaskId;  // 原始任务id
    private String errorCode;
    private Integer doubleRunTaskId;     // 双跑任务id
    private Long doubleRunInstanceId;  // 双跑实例id
    private Long doubleRunLogId;       // 双跑实例id
    private Long explainId;             // 执行计划id
    private String details;              // 记录无法命中错误码的日志详情
    private String createTime;
}
