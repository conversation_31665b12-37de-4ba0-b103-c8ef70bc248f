package com.jd.bdp.bean.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * author: xiaowei79
 * description: 通用操作结果
 * date: 2024-11-27 22:18:00
 */
@Setter
@Getter
@ToString
public class CommonOpResult {
    private boolean isSuc;
    private Integer code;
    private String message;

    public CommonOpResult(boolean isSuc, Integer code, String message) {
        this.isSuc = isSuc;
        this.code = code;
        this.message = message;
    }

    public CommonOpResult(boolean isSuc, String message) {
        this.isSuc = isSuc;
        this.message = message;
    }

    public CommonOpResult(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
