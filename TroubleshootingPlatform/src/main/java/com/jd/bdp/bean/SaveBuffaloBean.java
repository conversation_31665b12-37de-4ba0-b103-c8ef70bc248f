package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SaveBuffaloBean {
    // legacy fields
    private String market;
    private String queue;
    private String account;

    private String accountCode;
    private String actionName = "test_03_exe_adm_s14_traffic_plat_item_shop_di_day_spark_spark_team_temp_55147";
    private String actionType = "pyscript";
    private String alarmId;
    private String alarmType = "email,sms";
    private String args;
    private String cgroupSetType = "12";
    private String clusterCode;
    private String cpuLimitSize = "12";
    private String description = "TTTT";
    private String endAlarm = "0";
    private String endFa = "00:00";
    private String failAlarm = "1";
    private String hadoopEngineType = "spark";
    private String marketCode;
    private String maxRunTime = "0";
    private String memLimitSizeDouble = "12";
    private String queueCode;
    private String scriptId; //script
    private String scriptPath; //script
    private String scriptStartFile; //script
    private String startAlarm = "0";
    private String startFa = "00:00";
    private String taskId;
    private String taskNodeId; // node
    private String taskNodeName;
    private String taskNodeType; // node
    private String taskType = "single";
}
