package com.jd.bdp.bean;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * author: xiaowei79
 * description: spark引擎升级bean
 * date: 2024-09-25 02:25:00
 */
@ToString
@Getter
@Setter
public class SparkUpgradeEngineVersionBean {
    private Long id;
    private Integer taskId;
    private Integer actionId;
    private String taskType;
    private String engine;
    private String fromVersion;
    private String toVersion;
    private String opType;
    private String status;
    private String createTime;
    private String creator;
}
