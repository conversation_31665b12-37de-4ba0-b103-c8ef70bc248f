package com.jd.bdp.bean.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * author: xiaowei79
 * description:
 * date: 2024-10-29 18:21:00
 */
public enum  SparkUpgradeExplainEnum {
    EXE_SQL_FAILED("EXE_SQL_FAILED", "spark-sql启动失败"),
    FAILED("FAILED", "explain执行失败"),
    INSERT_FAILED("INSERT_FAILED", "explain结果写入数据库失败"),
    SUCCESSFUL("SUCCESSFUL", "执行计划校验成功"),
    UN_EXE("UN_EXE", "未执行explain");

    private String code;
    private String name;

    SparkUpgradeExplainEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    public static List<String> getFailedStatus(){
        ArrayList<String> list = new ArrayList<>();
        list.add(EXE_SQL_FAILED.getCode());
        list.add(FAILED.getCode());
        list.add(INSERT_FAILED.getCode());
        return list;
    }

    @Override
    public String toString() {
        return "SparkUpgradeExplainEnum{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
