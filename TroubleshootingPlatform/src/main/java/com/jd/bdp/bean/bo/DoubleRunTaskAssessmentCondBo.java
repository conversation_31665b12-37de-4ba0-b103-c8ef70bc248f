package com.jd.bdp.bean.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * author: xiaowei79
 * description:
 * date: 2024-11-07 12:05:00
 */
@Setter
@Getter
@ToString
public class DoubleRunTaskAssessmentCondBo {
    private List<String> taskIds;
    private String allHiveTask;  // 是否是hiveTask提交的任务, Y or N
    private Boolean isSpark;    // 是否是spark任务
    private Boolean isHive;     // 是否是hive任务
    private String isDoubleRunTaskCreated; // 是否已经创建双跑任务
    private Long instAllSuccess;  // 是否双跑成功， 0成功，非0失败
    private String startRunTime;   // 开始时间
    private String endRunTime;     // 结束时间
    private Integer offset;
    private Integer length;
}
