package com.jd.bdp.bean;

/**
 * status_code
 */
public enum EnumBuffaloLogStatus {
    INITIAL(0, "Next step will fill spark version"),
    FILL_SPARK_VERSION(1, "Next step will run with spark3"),
    SUCCESS(2, ""),
    FAILED(3, "失败");
    public int statusCode;
    public String description;
    EnumBuffaloLogStatus(int statusCode, String description) {
        this.statusCode = statusCode;
        this.description = description;
    }
}
