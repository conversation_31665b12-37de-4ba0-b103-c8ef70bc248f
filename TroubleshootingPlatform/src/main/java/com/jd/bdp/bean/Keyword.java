package com.jd.bdp.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Keyword {

    private Integer id;
    private String keyword;
    private int samples;
    private String solutionLink;
    private List<String> matchedContent = new ArrayList<>();
    private boolean htmlEncode;
    private Integer group;
}