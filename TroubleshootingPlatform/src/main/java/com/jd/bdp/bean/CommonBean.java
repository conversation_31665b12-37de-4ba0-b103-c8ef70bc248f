package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CommonBean {
    private Integer monitorAndFillSchedulerPeriodSecond;
    private Integer forkBuffaloTaskSchedulerPeriodSecond;
    private Integer fillSparkVersionSchedulerPeriodSecond;
    private Integer runSpark3SchedulerPeriodSecond;
    private Integer compareToolSchedulerPeriodSecond;
    private Integer syncJrcToJimDBSchedulerPeriodSecond;
    private Integer createXbpFlowSchedulerPeriodSecond;

    private String buffaloApiAppId;
    private String buffaloApiUserToken;
    private String buffaloApiAppToken;

    private String buffaloNewApiAppId;
    private String buffaloNewApiAppToken;

    private String shiftHillsBuffaloApiAppId;
    private String shiftHillsBuffaloApiUserToken;
    private String shiftHillsBuffaloApiAppToken;

    private String ugdapApiAppid;
    private String ugdapApiToken;

    private String forkBuffaloTaskAppendParams;
    private List<String> forkBuffaloTaskAppendParamsAtStart;

    private Integer resultLimitPerBatchOfForkTask;

    private String kongMingAddBuffaloTemplateConfUrl;
    private String kongMingGetBuffaloTemplateConfUrl;
    private String kongMingDelBuffaloTemplateConfUrl;
    private String xbpMailCopyAddressesForInspection;
    private String xbpUsername;
    private String xbpNatureUsername;
    private String xbpWuGuoXiao;

    private String downloadBuffaloLogDir;

    private String buffaloJdqTopic;
    private String buffaloJdqUserName;
    private String buffaloJdqPassword;
    private String buffaloJdqDomainName;

    private Long initUpgradeSparkTaskInterval;

    private String baizePgUrl;
    private int baizeRequestTimeout;
    private int baizeReadTimeout;
}
