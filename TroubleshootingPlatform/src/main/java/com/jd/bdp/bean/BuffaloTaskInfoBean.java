package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BuffaloTaskInfoBean {
    private Long id;
    private String name;
    private String originTaskId;

    public BuffaloTaskInfoBean() {
    }

    @Override
    public String toString() {
        return "BuffaloTaskInfoBean{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", originTaskId='" + originTaskId + '\'' +
                '}';
    }
}
