package com.jd.bdp.bean.enums;

/**
 * author: xiaowei79
 * description:
 * date: 2024-09-25 03:20:00
 */
public enum SparkUpgradeStatusEnum {
    SUCCESSFUL("SUCCESSFUL", "成功"),
    FAILED("FAILED", "失败");
    
    private String code;
    private String name;

    SparkUpgradeStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "SparkUpgradeOpTypeEnum{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
