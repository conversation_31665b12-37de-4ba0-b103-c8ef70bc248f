package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BuffaloTaskBean {
    private Long taskId;
    private String taskName;
    private String taskInstId; // 实例ID
    //private String cycleType; // 周期类型，month-月 week-周 day-天 hour-小时 minute-分钟
    //private String cycle; // 业务周期
    private String txDate; // 业务周期

    public BuffaloTaskBean() {
    }

    @Override
    public String toString() {
        return "BuffaloTaskBean{" +
                "taskId=" + taskId +
                ", taskName='" + taskName + '\'' +
                ", taskInstId='" + taskInstId + '\'' +
                ", txDate='" + txDate + '\'' +
                '}';
    }
}
