package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * author: xiaowei79
 * description: spark升级自动报表（本周）
 * date: 2024-11-05 18:22:00
 */
@Getter
@Setter
public class SparkUpgradeReporterBean {
    private Integer totalTaskNum;      // 本周双跑的任务
    private Double totalVcoresNum;     // 本周所有双跑任务涉及的总核数
    private Integer successTaskNum;    // 成功的任务
    private Long failTaskNum;       // 失败任务
    private Long runningTaskNum;    // 运行中任务
    private Double successQueryNum;   // 成功的任务涉及的job数量
    private Double performanceRatio;   // 双跑性能提升
    private Long successP95Num;     // 双跑成功的任务中p95的任务数
    private Long successBaselineNum; // 双跑成功的任务中核心基线的任务数
    private Map<String, Double> failedResult;  // 双跑失败的任务分类和占比
    private Double totalSucVcores;    // 成功的任务涉及总核数
    private Double saveVcores;        // 预计节省的核数
    
    @Override
    public String toString() {
        return String.format("【总任务数】：当前共双跑%s个任务，涉及总%.0f核/天;\n【任务分类】：成功%s个任务，失败任务%s个，运行中任务%s个;\n【成功任务】：双跑成功率%.2f%%, 涉及%.0f个query，双跑性能提升%.2f%%;\n【业务详情】: 成功任务中包含P95任务%s个，核心基线任务%s个, 涉及总%.2f核/天，升级后预计节省%.0f核/天。", 
                totalTaskNum, totalVcoresNum, successTaskNum, failTaskNum, runningTaskNum, successTaskNum * 1.0 / totalTaskNum * 100, successQueryNum, performanceRatio * 100, successP95Num, successBaselineNum, totalSucVcores, saveVcores);
    }
}
