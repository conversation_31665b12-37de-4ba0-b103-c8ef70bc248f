package com.jd.bdp.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author: xiaowei79
 * description:
 * date: 2024-11-06 22:24:00
 */
@ToString
@Getter
@Setter
public class SparkUpgradeImportResultBean {
    private Map<String, String> failedMap = new HashMap<>(); // 失败的任务和失败原因
    private Integer totalImportTaskNum;
    private Integer importFailedTaskNum;
    private Integer importSuccessTaskNum;
    private List<String> importSucTaskList = new ArrayList<>(); 
}
