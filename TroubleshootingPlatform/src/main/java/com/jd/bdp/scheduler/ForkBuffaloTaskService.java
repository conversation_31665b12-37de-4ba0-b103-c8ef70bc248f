package com.jd.bdp.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.*;
import com.jd.bdp.common.Buffalo4TaskManager;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.common.YamlUtil;
import com.jd.bdp.spark.web.BDPController;
import com.jd.bdp.spark.web.BDPUtils;
import com.jd.bdp.spark.web.KongmingService;
import com.jd.bdp.spark.web.OSUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.jd.bdp.bean.Constants.*;
import static com.jd.bdp.bean.EnumBuffaloLogStatus.*;
import static com.jd.bdp.bean.EnumBuffaloTaskStatus.FORK_TASK_ERROR;
import static com.jd.bdp.scheduler.Scheduler.commonBean;

public class ForkBuffaloTaskService {

    private static final Logger logger = Logger.getLogger(ForkBuffaloTaskService.class.getName());

    private final static BDPController bdpController = new BDPController();

    public static JSONObject forkBuffaloTask(String buffaloVersion, Long taskId, String outputTableFlag) {
        CallerInfo forkBuffaloTask = Profiler.registerInfo("bdp.spark3.scheduler1.forkBuffaloTask", "SparkMonitorApp", false, true);
        try {
            JSONObject improve = forkBuffaloTask(taskId, outputTableFlag);
            if (improve.containsKey("errMsg")) {
                updateTaskStatus(buffaloVersion, taskId,
                        improve.getString("forkedTask"),
                        FORK_TASK_ERROR.statusCode,
                        improve.getString("errMsg"),
                        improve.getString("originalSQLCommand"),
                        improve.getString("managers"),
                        improve.getString("cluster"),
                        improve.getString("market"),
                        improve.getString("teamuser"),
                        improve.getString("queue"),
                        improve.getLongValue("originalLogId"));
                Profiler.registerInfoEnd(Profiler.registerInfo("bdp.spark3.scheduler1.forkBuffaloTask.failed", "SparkMonitorApp", false, true));
                return improve;
            } else if (improve.containsKey("forkedTask")) {
                updateTaskStatus(buffaloVersion, taskId,
                        improve.getString("forkedTask"),
                        EnumBuffaloTaskStatus.FORK_TASK_SUCCESS.statusCode, "复制任务成功",
                        improve.getString("originalSQLCommand"),
                        improve.getString("managers"),
                        improve.getString("cluster"),
                        improve.getString("market"),
                        improve.getString("teamuser"),
                        improve.getString("queue"),
                        improve.getLongValue("originalLogId"));
                Profiler.registerInfoEnd(Profiler.registerInfo("bdp.spark3.scheduler1.forkBuffaloTask.success", "SparkMonitorApp", false, true));
                return improve;
            }
        } catch (Throwable e) {
            logger.warning("Fork task failed. buffaloVersion: " + buffaloVersion + " taskId: " + taskId +
                    " outputTableFlag: " + outputTableFlag + " " + CommonUtil.exceptionToString(e));
            updateTaskStatus(buffaloVersion, taskId, null, FAILED.statusCode,
                    "复制任务失败"+e.getMessage(), null, null, null, null, null, null, null);
            Profiler.functionError(forkBuffaloTask);
        } finally {
            Profiler.registerInfoEnd(forkBuffaloTask);
        }
        return null;
    }

    public static JSONObject forkBuffaloTask(Long taskId, String outputTableFlag) throws IOException {
        JSONObject improve = bdpController.improve(
                YamlUtil.sqlTemplate.get("gitProjectId"),
                YamlUtil.sqlTemplate.get("applicationId"),
                YamlUtil.sqlTemplate.get("site"),
                YamlUtil.sqlTemplate.get("projectSpaceId"),
                taskId,
                null,
                commonBean.getForkBuffaloTaskAppendParams(),
                null,
                null,
                true, outputTableFlag, null);
        logger.info("Forked task: " + improve.toJSONString());
        return improve;
    }

    public static void monitorAndFillBuffaloLogBySql(String site, String sql) {
        List<Map<String, Object>> kongming = KongmingService.executeSql(sql, null);
        logger.info("kongming.size = " + kongming.size());
        for (Map<String, Object> row : kongming) {
            monitorAndFillBuffaloLog(site, row);
        }
    }

    public static void monitorAndFillBuffaloLog(String site, Map<String, Object> row) {
        Integer fork_task_id = (Integer)row.get("fork_task_id");
        if(fork_task_id != null) {
            CallerInfo callerInfo = Profiler.registerInfo("bdp.spark3.scheduler2.monitorAndFillBuffaloLog.task", "SparkMonitorApp", false, true);
            JSONArray buffalo4Logs = BDPUtils.getBuffalo4Logs(site, fork_task_id + "",
                    null, "single", null, "current");
            if (buffalo4Logs != null && !buffalo4Logs.isEmpty()) {
                for(int i = 0 ;i< buffalo4Logs.size(); i++) {
                    JSONObject jsonObject = buffalo4Logs.getJSONObject(i);
                    insertOrUpdateBuffaloLog(jsonObject, row);
                }
            }
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    public static void updateTaskStatus(String originalTaskVersion, Long originalTaskId, String forkedTaskId,
                                        Integer status_code, String status, String originalSQLCommand,
                                        String managers, String cluster, String market, String teamuser,
                                        String queue, Long logId) {
        KongmingService.executeUpdate("update "+SPARK_BUFFALO_TASK_INFO+" set"
                        + " status_code = ?"
                        + " ,status = ?"
                        + " ,fork_task_id = ? "
                        + " ,originalSQLCommand = ? "
                        + " ,managers = ? "
                        + " ,originalCluster = ? "
                        + " ,originalMarket = ? "
                        + " ,originalUser = ? "
                        + " ,originalQueue = ? "
                        + " ,originalLogId = ? "
                        + " where task_id = ? and buffalo_version = ?",
                null, status_code,
                        StringUtils.defaultString(status, ""),
                        forkedTaskId == null ? 0 : forkedTaskId,
                        StringUtils.defaultString(originalSQLCommand, ""),
                        StringUtils.defaultString(managers, ""),
                        StringUtils.defaultString(cluster, ""),
                        StringUtils.defaultString(market, ""),
                        StringUtils.defaultString(teamuser, ""),
                        StringUtils.defaultString(queue, ""),
                        logId == null ? 0 : logId,
                        originalTaskId, originalTaskVersion);
    }

    public static void insertOrUpdateBuffaloLog(JSONObject jsonObject, Map<String, Object> row) {
        CallerInfo callerInfo = Profiler.registerInfo("bdp.spark3.scheduler2.monitorAndFillBuffaloLog.log", "SparkMonitorApp", false, true);
        String sql = "select * from " + SPARK_BUFFALO_LOG_INFO +
                " where runLogId = " + jsonObject.getString("runLogId");
        List<Map<String, Object>> maps = KongmingService.executeSql(sql, null);
//        logger.info("sql: " + sql + " result: " + JSON.toJSONString(maps));
        if (maps.isEmpty()) {
            Map<String, String> env = BDPUtils.parseEnv(jsonObject.getString("env"));
            Long runLogId = jsonObject.getLong("runLogId");
            Long instanceId = jsonObject.getLong("instanceId");
            String runStatus = jsonObject.getString("runStatus");
            Long duration = jsonObject.getLong("duration");

            String buffalo_version = row.get("buffalo_version").toString();
            String task_id = row.get("task_id").toString();
            String fork_task_id = row.get("fork_task_id").toString();

            String cluster = env.get("cluster");
            String market = env.get("market");
            String queue = env.get("queue");
            String teamuser = env.get("teamuser");
            String engine = env.get("engine");
            KongmingService.insertLogInfo(runLogId, instanceId, runStatus, duration, buffalo_version,
                    task_id, fork_task_id, cluster, market, queue, teamuser, engine,  INITIAL.statusCode, null);
            Profiler.registerInfoEnd(Profiler.registerInfo("bdp.spark3.scheduler2.insertBuffaloLog", "SparkMonitorApp", false, true));
        } else if (!"success".equals(maps.get(0).get("runStatus")) &&
                !"fail".equals(maps.get(0).get("runStatus"))) {
            String updateSQL = "update " + SPARK_BUFFALO_LOG_INFO +
                    " set runStatus = '" + jsonObject.getString("runStatus") + "'" +
                    "   , duration = " + jsonObject.getLong("duration") +
                    " where buffalo_version = '" + row.get("buffalo_version").toString() +"'" +
                    " and fork_task_id = " + row.get("fork_task_id").toString() +
                    " and runLogId = " + jsonObject.getLong("runLogId");
            int result = KongmingService.executeUpdate(updateSQL, null);
            logger.info("UpdateSql: "+updateSQL + " result: " + result);
            Profiler.registerInfoEnd(Profiler.registerInfo("bdp.spark3.scheduler2.updateBuffaloLog", "SparkMonitorApp", false, true));
        }
        Profiler.registerInfoEnd(callerInfo);
    }

    public static LogAnalysisBean logAnalysis(String buffaloDomain, String runLogId, String buffalo_version) {
        LogAnalysisBean logAnalysisBean = new LogAnalysisBean();
        CallerInfo fillSparkVersion = Profiler.registerInfo("bdp.spark3.scheduler3.fillSparkVersion", "SparkMonitorApp", false, true);
        try {
            JSONObject logAnalysisJSON = BDPUtils.logAnalysis(BDPUtils.getUrlBuffaloVersionMap(buffaloDomain,
                    runLogId, null, buffalo_version), true, null, null);
            logger.info("RunLogId: " + runLogId + " result: " + logAnalysisJSON.toJSONString());
            logAnalysisBean.setOutputTableName(logAnalysisJSON.getString("outputTableName"));
            logAnalysisBean.setSparkApplicationId(logAnalysisJSON.getString(LOG_ANALYSIS_JDOS_APPID));
            logAnalysisBean.setWaitAMDuration(logAnalysisJSON.getInteger(WAIT_AM_DURATION));
            JSONArray keywords = logAnalysisJSON.getJSONArray("keywords");
            logAnalysisBean.setVersion("Not found");
            for (int i = 0; i < keywords.size(); i++) {
                JSONObject jsonObject = keywords.getJSONObject(i);
                if("spark-sql-cluster".equals(jsonObject.getString("keyword"))
                        && !jsonObject.getJSONArray("matchedContent").isEmpty()) {
                    String isClusterMode = "Is cluster mode";
                    logAnalysisBean.setVersion(isClusterMode);
                    Profiler.registerInfoEnd(Profiler.registerInfo("bdp.spark3.scheduler3.fillSparkVersion.isClusterMode", "SparkMonitorApp", false, true));
                    break;
                } else if ("Running Spark version".equals(jsonObject.getString("keyword"))
                        && !jsonObject.getJSONArray("matchedContent").isEmpty()) {
                    String running_spark_version = jsonObject.getJSONArray("matchedContent").getString(0);
                    Matcher matcher = Pattern.compile("Spark version (\\d+\\.\\d+\\.\\d+)").matcher(running_spark_version);
                    if (matcher.find()) {
                        logAnalysisBean.setVersion(matcher.group(1));
                        Profiler.registerInfoEnd(Profiler.registerInfo("bdp.spark3.scheduler3.fillSparkVersion.hasVersion", "SparkMonitorApp", false, true));
                        break;
                    }
                }
            }
        } catch(RuntimeException e) {
            Profiler.functionError(fillSparkVersion);
            throw e;
        } finally {
            Profiler.registerInfoEnd(fillSparkVersion);
        }

        return logAnalysisBean;
    }

    public static EnumBuffaloLogStatus runSpark(Integer originTaskId, Integer forkTaskId, String originalSQLCommand,
                                                String hadoopEngineVersion, String random, String originalCluster) {
        EnumBuffaloLogStatus logStatus = FAILED;
        try {
            ArrayList<Integer> taskIdArray = new ArrayList<>();
            taskIdArray.add(forkTaskId);
            JSONObject modifyTask = Buffalo4TaskManager.batchModifyMarketInfo(
                    taskIdArray, null, "spark", hadoopEngineVersion,
                    commonBean.getBuffaloApiUserToken(), "buffalo4.bdp.jd.local");
            logger.info("ForkTaskId: " + forkTaskId + " ModifySparkVersion: " + modifyTask.toJSONString());
            BDPController.TestClusterConf testClusterConf = BDPController.getTestClusterConf(originalCluster);
            JSONObject rewroteShellObj = BDPUtils.rewriteSql(null, originalSQLCommand,
                    commonBean.getForkBuffaloTaskAppendParams() + " " + testClusterConf.hiveMetastoreUrl,
                    random, testClusterConf.newDBName);
            logger.info("ForkTaskId: " + forkTaskId + " rewroteShellObj: " + rewroteShellObj.toJSONString());
            String fileName = "script_" + originTaskId + ".sh";
            JSONObject uploadScript = BDPUtils.uploadScript(YamlUtil.sqlTemplate.get("gitProjectId"),
                    YamlUtil.sqlTemplate.get("projectSpaceId"),
                    rewroteShellObj.getString("rewrote"), YamlUtil.sqlTemplate.get("applicationId"), fileName);
            logger.info("ForkTaskId: " + forkTaskId + " UploadScript: " + uploadScript.toJSONString());

            JSONObject fork_task_id = Buffalo4TaskManager.buffalo4CreateTaskInstance(forkTaskId, null, null, null);
            logger.info("ForkTaskId: " + forkTaskId + " CreateInstance: " + fork_task_id.toJSONString());
            logStatus = SUCCESS;
        } catch (Exception e) {
            logger.warning(CommonUtil.exceptionToString(e));
        }
        return logStatus;
    }

    public static Map<String, String> sparkMigrateSQL = YamlUtil.loadYaml("sparkMigrateSQL.yaml", HashMap.class);

}
