package com.jd.bdp.scheduler;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.SparkUpgradeExplainBean;
import com.jd.bdp.bean.domain.ExplainLogicPlanResult;
import com.jd.bdp.bean.enums.SparkUpgradeExplainEnum;
import com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.mapper.spark.SparkUpgradeExplainMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
import com.jd.bdp.spark.web.ShellUtil;
//import com.jd.bdp.utils.MybatisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.logging.Logger;

/**
 * author: xiaowei79
 * description: 使用最新版spark3.4运行逻辑计划，校验逻辑计划是否出错
 * date: 2024-10-29 17:10:00
 */
@Component
public class ExplainLogicalPlanUtils {

    private static final Logger logger = Logger.getLogger(ExplainLogicalPlanUtils.class.getName());
    
    @Autowired
    private SparkUpgradeTaskMapper taskMapper;
    
    @Autowired
    private SparkUpgradeExplainMapper explainMapper;
    
    public ExplainLogicPlanResult execute(String taskId){
        try {
            logger.info(String.format("=== 开始进行执行计划校验, taskId: %s", taskId));
            if(taskId == null || StringUtils.isBlank(taskId)){
                logger.info(String.format("=== 执行计划校验失败, taskId: %s为空", taskId));
                return new ExplainLogicPlanResult(taskId, false, false, "taskId为空");
            }
            
            // 1、修改任务状态
//            SparkUpgradeTaskMapper mapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);
            int affectedSize = taskMapper.updateStatusByCondition(taskId, Collections.singletonList(SparkUpgradeTaskStatusEnum.UN_EXE.getCode()), SparkUpgradeTaskStatusEnum.EXPLAINING.getCode());
            if(affectedSize == 0){
                logger.info(String.format("=== 执行计划校验失败, taskId: %s， 更新状态UN_EXE => EXPLAINING失败", taskId));
                return new ExplainLogicPlanResult(taskId, false, false, "更新状态UN_EXE => EXPLAINING失败");
            }

            // 2、执行逻辑计划校验jar包程序
            String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/double_run/";
            String scriptName = "explain.sh";
            String sparkVersion = "3.4";
            String resultStr = ShellUtil.runCmdWithRes("sh " + baseDir + scriptName + " " + sparkVersion + " " + taskId);
            JSONObject result = JSON.parseObject(resultStr);
            Integer status = result.getInteger("status");
            if(status != 0){
                logger.info(String.format("=== 执行计划校验失败, taskId: %s， 调用sparkDemo.jar执行逻辑计划校验失败", taskId));
                int size = taskMapper.updateStatusByCondition(taskId, Collections.singletonList(SparkUpgradeTaskStatusEnum.EXPLAINING.getCode()), SparkUpgradeTaskStatusEnum.EXPLAIN_FAILED.getCode());
                if(size <= 0){
                    logger.info(String.format("=== 执行计划校验失败, taskId: %s, 更新状态： EXPLAINING => EXPLAIN_FAILED失败", taskId));
                    return new ExplainLogicPlanResult(taskId, false, false, "调用sparkDemo.jar执行逻辑计划校验失败, EXPLAINING => EXPLAIN_FAILED失败");
                }else{
                    return new ExplainLogicPlanResult(taskId, false, false, "调用sparkDemo.jar执行逻辑计划校验失败");
                }
            }

            // 3、获取最新逻辑计划执行的结果
//            SparkUpgradeExplainMapper explainMapper = MybatisUtils.getMapper(SparkUpgradeExplainMapper.class);
            List<SparkUpgradeExplainBean> explainList = explainMapper.selectLatestListByTaskId(Integer.parseInt(taskId));
            if(explainList.size() == 0){
                logger.info(String.format("=== 执行计划校验失败, taskId: %s，非HiveTask任务", taskId));
                int size = taskMapper.updateStatusByCondition(taskId, Collections.singletonList(SparkUpgradeTaskStatusEnum.EXPLAINING.getCode()), SparkUpgradeTaskStatusEnum.EXPLAIN_FAILED.getCode());
                if(size <= 0){
                    logger.info(String.format("=== 执行计划校验失败, taskId: %s, 非HiveTask任务, 更新状态： EXPLAINING => EXPLAIN_FAILED 失败", taskId));
                    return new ExplainLogicPlanResult(taskId, false, false, "非HiveTask任务, EXPLAINING => EXPLAIN_FAILED失败");
                }else{
                    return new ExplainLogicPlanResult(taskId, false, false, "非HiveTask任务");
                }
            }

            for(SparkUpgradeExplainBean bean: explainList){
                if(SparkUpgradeExplainEnum.getFailedStatus().contains(bean.getStatus())){
                    logger.info(String.format("=== 执行计划校验失败, taskId: %s， explain_id: %s, spark3.4执行计划校验不通过", taskId, bean.getId()));
                    int size = taskMapper.updateStatusByCondition(taskId, Collections.singletonList(SparkUpgradeTaskStatusEnum.EXPLAINING.getCode()), SparkUpgradeTaskStatusEnum.EXPLAIN_FAILED.getCode());
                    if(size <= 0){
                        logger.info(String.format("=== 执行计划校验失败, taskId: %s, spark3.4执行计划校验不通过, 更新状态： EXPLAINING => EXPLAIN_FAILED 失败", taskId));
                        return new ExplainLogicPlanResult(taskId, false, true, "spark3.4执行计划校验不通过, EXPLAINING => EXPLAIN_FAILED失败");
                    }else{
                        return new ExplainLogicPlanResult(taskId, false, true, "非HiveTask任务");
                    }
                }
            }

            logger.info(String.format("=== 执行计划校验成功, taskId: %s， spark3.4执行计划校验通过!", taskId));
            int size = taskMapper.updateStatusByCondition(taskId, Collections.singletonList(SparkUpgradeTaskStatusEnum.EXPLAINING.getCode()), SparkUpgradeTaskStatusEnum.EXPLAIN_SUCCESS.getCode());
            if(size <= 0){
                logger.info(String.format("=== 执行计划校验失败, taskId: %s, spark3.4执行计划校验通过, 更新状态： EXPLAINING => EXPLAIN_FAILED 失败", taskId));
                return new ExplainLogicPlanResult(taskId, false, true, "sspark3.4执行计划校验通过, 更新状态： EXPLAINING => EXPLAIN_SUCCESS 失败");
            }else{
                return new ExplainLogicPlanResult(taskId, true, false, "spark3.4执行计划校验成功");
            }
        } catch (Exception e) {
            logger.info(String.format("=== 执行计划校验发生异常: taskId: %s, exception: %s !!!", taskId, CommonUtil.exceptionToString(e)));
            return new ExplainLogicPlanResult(taskId, false, false, "执行计划校验发生异常");
        }
    }


    /**
     * 跳过执行explain环节,直接将task状态由UN_EXE => EXPLAINING => EXPLAIN_SUCCESS
     * @param taskId
     * @return
     */
    public ExplainLogicPlanResult executeBypassExplain(String taskId){
        try {
            logger.info(String.format("=== 开始进行执行计划校验, taskId: %s", taskId));
            if(taskId == null || StringUtils.isBlank(taskId)){
                logger.info(String.format("=== 执行计划校验失败, taskId: %s为空", taskId));
                return new ExplainLogicPlanResult(taskId, false, false, "taskId为空");
            }

            // 1、修改任务状态
            int affectedSize = taskMapper.updateStatusByCondition(taskId, Collections.singletonList(SparkUpgradeTaskStatusEnum.UN_EXE.getCode()), SparkUpgradeTaskStatusEnum.EXPLAINING.getCode());
            if(affectedSize == 0){
                logger.info(String.format("=== 执行计划校验失败, taskId: %s， 更新状态: UN_EXE => EXPLAINING失败", taskId));
                return new ExplainLogicPlanResult(taskId, false, false, "更新状态: UN_EXE => EXPLAINING失败");
            }else{
                logger.info(String.format("=== 执行计划校验失败, taskId: %s， 更新状态: UN_EXE => EXPLAINING成功", taskId));
            }

            // 2、修改任务状态
            int size = taskMapper.updateStatusByCondition(taskId, Collections.singletonList(SparkUpgradeTaskStatusEnum.EXPLAINING.getCode()), SparkUpgradeTaskStatusEnum.EXPLAIN_SUCCESS.getCode());
            if(size == 0){
                logger.info(String.format("=== 执行计划校验失败, taskId: %s, spark3.4执行计划校验通过, 更新状态： EXPLAINING => EXPLAIN_SUCCESS 失败", taskId));
                return new ExplainLogicPlanResult(taskId, false, true, "sspark3.4执行计划校验通过, 更新状态： EXPLAINING => EXPLAIN_SUCCESS 失败");
            }else{
                logger.info(String.format("=== 执行计划校验失败, taskId: %s, spark3.4执行计划校验通过, 更新状态： EXPLAINING => EXPLAIN_SUCCESS 成功", taskId));
                return new ExplainLogicPlanResult(taskId, true, false, "spark3.4执行计划校验成功");
            }
        } catch (Exception e) {
            logger.info(String.format("=== 执行计划校验发生异常: taskId: %s, exception: %s !!!", taskId, CommonUtil.exceptionToString(e)));
            return new ExplainLogicPlanResult(taskId, false, false, "执行计划校验发生异常");
        }
    }
}
