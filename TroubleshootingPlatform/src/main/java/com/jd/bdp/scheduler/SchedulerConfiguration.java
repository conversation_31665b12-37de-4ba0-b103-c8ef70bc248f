package com.jd.bdp.scheduler;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

/**
 * author: xiaowei79
 * description: 将Spring默认的定时调度器的线程数设置为50（默认是单线程调度器），使@scheduled注解标注的方法，可能同时被调度起来。
 * date: 2024-11-01 09:58:00
 */
@Configuration
public class SchedulerConfiguration implements SchedulingConfigurer {
    /**
     * 任务执行线程池大小
     */
    private static final int TASK_POOL_SIZE = 10;
    /**
     * 线程名
     */
    private static final String TASK_THREAD_PREFIX = "spark-upgrade-task-";

    
    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        // 指定多个线程的线程池
        ThreadPoolTaskScheduler taskPool = new ThreadPoolTaskScheduler();
        taskPool.setPoolSize(TASK_POOL_SIZE);
        taskPool.setThreadNamePrefix(TASK_THREAD_PREFIX);
        taskPool.initialize();
        // set方法来设置
        scheduledTaskRegistrar.setTaskScheduler(taskPool);
    }
}
