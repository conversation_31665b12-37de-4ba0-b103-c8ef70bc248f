package com.jd.bdp.scheduler;


import com.jd.bdp.bean.*;
import com.jd.bdp.bean.enums.SparkUpgradeExplainEnum;
import com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.common.HbaseUtils;
import com.jd.bdp.mapper.buffalo.SparkUpgradeBuffaloMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeErrorCodeMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeExplainMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskErrorCodeRltMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
import com.jd.bdp.utils.MapperManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Connection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;

/**
 * author: xiaowei79
 * description: 使用最新版spark3.4运行逻辑计划，校验逻辑计划是否出错
 * date: 2024-10-29 17:10:00
 */
@Component
public class ErrorCodeUtils {

    private static final Logger logger = Logger.getLogger(ErrorCodeUtils.class.getName());
    
    @Autowired
    private SparkUpgradeTaskMapper taskMapper;
    
    @Autowired
    private SparkUpgradeTaskErrorCodeRltMapper rltMapper;
    
    @Autowired
    private SparkUpgradeExplainMapper explainMapper;
    
    @Autowired
    private SparkUpgradeErrorCodeMapper errorMapper;
    
    @Autowired
    private SparkUpgradeBuffaloMapper buffaloMapper;

    /**
     * 为执行逻辑计划校验失败的任务获取对应错误码
     * @param taskId
     */
    public void getErrorCodeForExplain(String taskId){
        try {
            logger.info(String.format("=== 开始获取任务explain的错误码： taskId: %s", taskId));
            
            if(StringUtils.isBlank(taskId) || !StringUtils.isNumeric(taskId)){
                logger.info(String.format("=== 开始获取任务explain的错误码失败： taskId: %s, 任务Id为空或非数字", taskId));
                return;
            }

            // 1、判断任务状态，只有EXPLAIN_FAILED的失败的任务才需要获取错误码
            SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(taskId);
            if(task == null){
                logger.info(String.format("=== 开始获取任务explain的错误码失败： taskId: %s, 任务不存在", taskId));
                return;
            }

            if(SparkUpgradeTaskStatusEnum.EXPLAIN_SUCCESS.getCode().equalsIgnoreCase(task.getStatus())){
                logger.info(String.format("=== taskId: %s, status: %s， 任务explain成功，清除taskId对应的错误码", taskId, task.getStatus()));
                rltMapper.deleteRltsByTaskId(Integer.parseInt(taskId));
            }else if(SparkUpgradeTaskStatusEnum.EXPLAIN_FAILED.getCode().equalsIgnoreCase(task.getStatus())){
                // 2、通过taskId获取最新的explain的输出日志
                List<SparkUpgradeExplainBean> explainList = explainMapper.selectLatestListByTaskId(Integer.parseInt(taskId));
                if(explainList == null || explainList.size() == 0){
                    logger.info(String.format("=== taskId: %s, explain失败，但是explain错误日志为空", taskId));
                }else{
                    // 3、获取所有的错误码集合
                    List<SparkUpgradeErrorCodeBean> allErrorCodes = errorMapper.selectAll();
    
                    // 4、在内存中比较错误码
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String now = sdf.format(new Date());

                    List<SparkUpgradeTasKErrorRltBean> rltList = new ArrayList<>();
                    for(SparkUpgradeExplainBean explainBean: explainList){
                        if(SparkUpgradeExplainEnum.getFailedStatus().contains(explainBean.getStatus())){
                            Set<String> matchedErrorCodes = new HashSet<>();
                            String details = explainBean.getDetails();
                            if(StringUtils.isBlank(details)){
                                logger.info(String.format("=== taskId: %s, explain输出日志为空，explain id: %s, 直接跳过", taskId, explainBean.getId()));
                                continue;
                            }
                            // 遍历每一个错误码
                            for(SparkUpgradeErrorCodeBean errorCodeBean: allErrorCodes){
                                String keyword = errorCodeBean.getExceptionKeyword();
                                if(details.contains(keyword)){
                                    matchedErrorCodes.add(errorCodeBean.getErrorCode());
                                }
                            }
                            
                            // 收集未命中的异常日志
                            String unmatchedLogDetails = null;
                            if(matchedErrorCodes.size() == 0){
                                logger.info(String.format("=== taskId: %s, explain_id: %s,  无命中explain的错误码，添加默认错误码：err_0000", taskId, explainBean.getId()));
                                matchedErrorCodes.add("err_000000");
                                unmatchedLogDetails = details;
                            }else{
                                logger.info(String.format("=== taskId: %s, explain_id: %s, 任务命中explain的错误码集合为: %s", taskId, explainBean.getId(), MapperManager.writeValueAsString(matchedErrorCodes)));
                            }

                            for(String errorCode: matchedErrorCodes){
                                SparkUpgradeTasKErrorRltBean rltBean = new SparkUpgradeTasKErrorRltBean();
                                rltBean.setOriginTaskId(Integer.parseInt(taskId));
                                rltBean.setErrorCode(errorCode);
                                rltBean.setCreateTime(now);
                                rltBean.setExplainId(explainBean.getId());
                                rltBean.setDetails(unmatchedLogDetails);
                                rltList.add(rltBean);
                            }
                        }
                    }
                    
                    // 4、保存到数据库
                    rltMapper.deleteRltsByTaskId(Integer.parseInt(taskId));
                    logger.info(String.format("=== taskId: %s, 清空任务对应explain的错误码关系成功", taskId));
                    if(rltList.size() > 0){
                        rltMapper.batchInsertTaskErrorRlts(rltList);
                        rltMapper.batchInsertTaskErrorRltsHistory(rltList);
                        logger.info(String.format("=== taskId: %s, 批量插入任务对应explain的错误码成功", taskId));
                    }
                }
            }else{
                logger.info(String.format("=== taskId: %s, status: %s, 任务处于其他状态，跳过获取explain错误码", taskId, task.getStatus()));
            }
        } catch (Exception e) {
            logger.info(String.format("=== taskId: %s, getErrorCodeForExplain，获取任务explain的错误码异常: exception !!! %s", taskId, CommonUtil.exceptionToString(e)));
        }
    }

    /**
     * 为双跑失败的任务获取对应错误码
     * @param originTaskId 原始任务id
     * @return
     */
    public void getErrorCodeForDoubleRun(String originTaskId){
        // 1、校验任务id
        try {
            logger.info(String.format("=== 开始获取任务doubleRun的错误码： taskId: %s", originTaskId));

            if(StringUtils.isBlank(originTaskId)){
                logger.info(String.format("=== 开始获取任务doubleRun的错误码失败： taskId: %s, 任务Id为空", originTaskId));
                return;
            }

            originTaskId = originTaskId.trim();

            // 2、判断任务状态，只有DOUBLE_RUN_FAILED的失败的任务才需要获取错误码
            SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(originTaskId);
            if(task == null){
                logger.info(String.format("=== 开始获取任务doubleRun的错误码失败： taskId: %s, 任务不存在", originTaskId));
                return;
            }

            if(SparkUpgradeTaskStatusEnum.DOUBLE_RUN_SUCCESS.getCode().equalsIgnoreCase(task.getStatus())){
                logger.info(String.format("=== taskId: %s, status: %s， 任务doubleRun成功，清除taskId对应的错误码", originTaskId, task.getStatus()));
                rltMapper.deleteRltsByTaskId(Integer.parseInt(originTaskId));
            }else if(SparkUpgradeTaskStatusEnum.DOUBLE_RUN_FAILED.getCode().equalsIgnoreCase(task.getStatus())){
                // 1、根据原始taskId获取对应双跑任务最近失败instanceId和日志logId
                String doubleRunTaskId = task.getDoubleRunTaskId();
                if(StringUtils.isBlank(doubleRunTaskId)){
                    logger.info(String.format("=== taskId: %s, 对应的双跑任务id为空，跳过获取错误码", originTaskId));
                    return;
                }

                List<String> doubleRunTaskIds = Arrays.asList(doubleRunTaskId.split(","));
                List<SparkUpgradeBuffaloLogBean> failedList = buffaloMapper.selectFailedDoubleRunTaskLogByDoubleRunTaskIds(doubleRunTaskIds);
                
                if(failedList == null || failedList.size() == 0){
                    logger.info(String.format("=== taskId: %s, doubleRun失败，但是doubleRun错误日志为空", originTaskId));
                }else{
                    getErrorCodeFromHBase(originTaskId, failedList);
                }
            }else{
                logger.info(String.format("=== taskId: %s, status: %s, 任务处于其他状态，跳过获取doubleRun错误码", originTaskId, task.getStatus()));
            }
        } catch (Exception e) {
            logger.info(String.format("=== taskId: %s, getErrorCodeForDoubleRun，获取任务doubleRun的错误码异常: exception!!! %s", originTaskId, CommonUtil.exceptionToString(e)));
        }
    }


    /**
     * 更新历史的双跑失败的任务错误码
     * @param originTaskId
     */
    public void getErrorCodeForHistoryDoubleRunTask(String originTaskId){
        // 1、校验任务id
        try {
            logger.info(String.format("=== 开始获取任务doubleRun的错误码： taskId: %s", originTaskId));

            if(StringUtils.isBlank(originTaskId)){
                logger.info(String.format("=== 开始获取任务doubleRun的错误码失败： taskId: %s, 任务Id为空", originTaskId));
                return;
            }

            originTaskId = originTaskId.trim();

            // 2、判断任务状态，只有DOUBLE_RUN_FAILED的失败的任务才需要获取错误码
            SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(originTaskId);
            if(task == null){
                logger.info(String.format("=== 开始获取任务doubleRun的错误码失败： taskId: %s, 任务不存在", originTaskId));
                return;
            }

            if(SparkUpgradeTaskStatusEnum.DOUBLE_RUN_SUCCESS.getCode().equalsIgnoreCase(task.getStatus())){
                logger.info(String.format("=== taskId: %s, status: %s， 任务doubleRun成功，清除taskId对应的错误码", originTaskId, task.getStatus()));
                rltMapper.deleteRltsByTaskId(Integer.parseInt(originTaskId));
            }else if(SparkUpgradeTaskStatusEnum.DOUBLE_RUN_FAILED.getCode().equalsIgnoreCase(task.getStatus())){
                
                List<SparkUpgradeBuffaloLogBean> failedList = new ArrayList<>();
                List<SparkUpgradeTasKErrorRltBean> rltBeanList = rltMapper.selectErrorRltListByTaskId(Integer.parseInt(originTaskId));
                HashSet<Long> logIds = new HashSet<>();
                for(SparkUpgradeTasKErrorRltBean rltBean: rltBeanList){
                    // doubleRunlogId去重
                    if(!logIds.contains(rltBean.getDoubleRunLogId())){
                        SparkUpgradeBuffaloLogBean logBean = new SparkUpgradeBuffaloLogBean();
                        logBean.setDoubleRunLogId(rltBean.getDoubleRunLogId());
                        logBean.setDoubleRunInstanceId(rltBean.getDoubleRunInstanceId());
                        logBean.setDoubleRunTaskId(rltBean.getDoubleRunTaskId());
                        failedList.add(logBean);
                        logIds.add(rltBean.getDoubleRunLogId());
                    }
                }
                if(failedList.size() == 0){
                    logger.info(String.format("=== taskId: %s, doubleRun历史错误日志为空", originTaskId));
                }else{
                    getErrorCodeFromHBase(originTaskId, failedList);
                }
            }else{
                logger.info(String.format("=== taskId: %s, status: %s, 任务处于其他状态，跳过获取doubleRun错误码", originTaskId, task.getStatus()));
            }
        } catch (Exception e) {
            logger.info(String.format("=== taskId: %s, getErrorCodeForDoubleRun，获取任务doubleRun的错误码异常: exception!!! %s", originTaskId, CommonUtil.exceptionToString(e)));
        }
    }
    
    private void getErrorCodeFromHBase(String originTaskId, List<SparkUpgradeBuffaloLogBean> failedList) throws Exception {
        // 3、获取Hbase连接
        Connection connection = HbaseUtils.createConnection();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = sdf.format(new Date());

        // 4、获取所有的错误码集合
        List<SparkUpgradeErrorCodeBean> allErrorCodes = errorMapper.selectAll();

        // 5、获取每一个环节实例的日志，然后在内存中比较错误码
        List<SparkUpgradeTasKErrorRltBean> rltList = new ArrayList<>();
        for(SparkUpgradeBuffaloLogBean logBean: failedList){
            // 6、通过logId获取到对应的buffalo日志
            List<String> logs = HbaseUtils.scanByLogId(connection, Integer.parseInt(originTaskId), logBean.getDoubleRunLogId());
            Set<String> matchedErrorCodes = new HashSet<>();
            for(String log: logs){
                if(StringUtils.isBlank(log)){
                    logger.info(String.format("=== taskId: %s,  doubleRunLogId: %s, doubleRun输出日志为空，直接跳过", originTaskId, logBean.getDoubleRunLogIdentity()));
                    continue;
                }
                // 遍历每一个错误码
                for(SparkUpgradeErrorCodeBean errorCodeBean: allErrorCodes){
                    String keyword = errorCodeBean.getExceptionKeyword();
                    if(log.contains(keyword)){
                        matchedErrorCodes.add(errorCodeBean.getErrorCode());
                    }
                }
            }
            
            // 收集未命中的异常日志
            String unMatchedLogDetails = null;
            if(matchedErrorCodes.size() == 0){
                logger.info(String.format("=== taskId: %s, doubleRunLogId: %s 无命中doubleRun的错误码，添加默认错误码：err_000000", originTaskId, logBean.getDoubleRunLogIdentity()));
                matchedErrorCodes.add("err_000000");
                unMatchedLogDetails = StringUtils.join(logs, ",");
            }else{
                logger.info(String.format("=== taskId: %s, doubleRunLogId: %s, 任务命中doubleRun的错误码集合为: %s", originTaskId, logBean.getDoubleRunLogIdentity(), MapperManager.writeValueAsString(matchedErrorCodes) ));
            }

            for(String errorCode: matchedErrorCodes){
                SparkUpgradeTasKErrorRltBean rltBean = new SparkUpgradeTasKErrorRltBean();
                rltBean.setOriginTaskId(Integer.parseInt(originTaskId));            // 原始任务id
                rltBean.setErrorCode(errorCode);                
                rltBean.setDoubleRunTaskId(logBean.getDoubleRunTaskId());           // 双跑任务id
                rltBean.setDoubleRunInstanceId(logBean.getDoubleRunInstanceId());   // 双跑任务实例id
                rltBean.setDoubleRunLogId(logBean.getDoubleRunLogId());             // 双跑日志id
                rltBean.setDetails(unMatchedLogDetails);
                rltBean.setCreateTime(now);
                rltList.add(rltBean);
            }
        }

        // 7、保存数据库
        rltMapper.deleteRltsByTaskId(Integer.parseInt(originTaskId));
        logger.info(String.format("=== taskId: %s, 清空任务对应doubleRun的错误码关系成功", originTaskId));
        if(rltList.size() > 0){
            rltMapper.batchInsertTaskErrorRlts(rltList);
            rltMapper.batchInsertTaskErrorRltsHistory(rltList);
            logger.info(String.format("=== taskId: %s, 批量插入任务对应doubleRun的错误码成功", originTaskId));
        }
    }


}
