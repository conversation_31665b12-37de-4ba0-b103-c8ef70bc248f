package com.jd.bdp.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.SparkUpgradeImportResultBean;
import com.jd.bdp.bean.SparkUpgradeReporterBean;
import com.jd.bdp.bean.bo.DoubleRunErrorCodeAndSizeBo;
import com.jd.bdp.bean.bo.DoubleRunTaskStatusSizeBo;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.mapper.buffalo.SparkUpgradeBuffaloMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeBigGraphMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeSysConfigMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
import com.jd.bdp.spark.web.*;
import com.jd.bdp.utils.MapperManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum.DOUBLE_RUN_FAILED;
import static com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum.DOUBLE_RUN_SUCCESS;
import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * author: xiaowei79
 * description: 后台线程
 * date: 2024-11-01 10:05:00
 */
@Component
@EnableScheduling
public class SparkCommonTask {
    private static final Logger logger = Logger.getLogger(SparkCommonTask.class.getName());
    private static final String SPARK_UPGRADE_REPORT_INTERVAL_IN_DAYS = "spark_upgrade_report_interval_in_days";
    private static final String SPARK_UPGRADE_TEAM_MEMBER = "spark_upgrade_team_member";
    public static final String EXPLAIN_DOUBLE_RUN_TASK_ENABLE = "explain_double_run_task_enable";
    public static final String CREATE_DOUBLE_RUN_TASK_ENABLE = "create_double_run_task_enable";
    public static final String RUN_DOUBLE_RUN_TASK_ENABLE = "run_double_run_task_enable";

    // 手动模式提交任务时，用来自动同步buffalo数据库状态开关
    private static final String SPARK_UPGRADE_AUTO_SYNC_BUFFALO_STATUS_ENABLE = "spark_upgrade_auto_sync_buffalo_status_enable";

    // 每天凌晨1点开始同步所有处于DOUBLE_RUNNING状态的任务开关
    private static final String SPARK_UPGRADE_AUTO_SYNC_DOUBLE_RUNNING_ENABLE = "spark_upgrade_auto_sync_double_running_enable";

    // 自动清理双跑HDFS临时数据开关
    private static final String SPARK_UPGRADE_AUTO_CLEAN_HDFS_TEMP_DIRS_ENABLE = "spark_upgrade_auto_clean_hdfs_temp_dirs_enable";

    // 自动删除双跑表开关
    private static final String SPARK_UPGRADE_AUTO_CLEAN_DOUBLE_RUN_TABLES_ENABLE = "spark_upgrade_auto_clean_double_run_tables_enable";

    // 跳过双跑的日期
    private static final String SPARK_UPGRADE_SKIP_DOUBLE_RUN_DTS = "spark_upgrade_skip_double_run_dts";

    // 取topN的错误码
    private static final String SPARK_UPGRADE_ERROR_CODE_TOP_N = "spark_upgrade_error_code_top_n";

    // 每天12点自动发送双跑周报开关
    private static final String SPARK_UPGRADE_AUTO_REPORT_WEEKLY_ENABLE = "spark_upgrade_auto_report_weekly_enable";

    // 每天12点自动发送双跑周报开关
    private static final String SPARK_UPGRADE_AUTO_REPORT_DAILY_ENABLE = "spark_upgrade_auto_report_daily_enable";

    // 每天23:30自动关闭双跑开关
    private static final String SPARK_UPGRADE_CLOSE_DOUBLE_RUN_ENABLE = "spark_upgrade_close_double_run_enable";

    // 每天12:30自动开启双跑开关
    private static final String SPARK_UPGRADE_OPEN_DOUBLE_RUN_ENABLE = "spark_upgrade_open_double_run_enable";

    // 春节期间自动发送双跑实时情况开关
    private static final String SPARK_UPGRADE_SPRING_FESTIVAL_SEND_REALTIME_ENABLE = "spark_upgrade_spring_festival_send_realtime_enable";

    // 春节启动自动发送错误码实时情况开关
    private static final String SPARK_UPGRADE_SPRING_FESTIVAL_ERROR_CODE_ENABLE = "spark_upgrade_spring_festival_error_code_enable";

    @Autowired
    private SparkUpgradeSysConfigMapper configMapper;

    @Autowired
    private SparkUpgradeBuffaloMapper buffaloMapper;

    @Autowired
    private SparkUpgradeBigGraphMapper bigGraphMapper;

    /**
     * cron = 秒 分 时 日 月 星期
     * 自动发送spark升级周报，每12小时执行一次
     * 若在调度任务执行时,上一次任务还未执行完毕,会加worker队列,等待上一次执行完成后立即执行下一次任务
     */
    @Scheduled(cron = "0 0 12 * * ?")
    public void runAutoSparkUpgradeReporter() {
        try {
            // 自动发送spark升级周报开关
            String reportEnable = configMapper.getConfigValue(SPARK_UPGRADE_AUTO_REPORT_WEEKLY_ENABLE);
            if (StringUtils.isBlank(reportEnable)) {
                reportEnable = "false";
            }
            logger.info(String.format("=== 自动发送spark升级周报开关: '%s' is %s", SPARK_UPGRADE_AUTO_REPORT_WEEKLY_ENABLE, reportEnable));

            if (Boolean.parseBoolean(reportEnable)) {
                // 确定时间范围
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date endTime = new Date();
                Date startTime = new Date(endTime.getTime() - 7 * 24 * 3600 * 1000);
                String intervalDayStr = configMapper.getConfigValue(SPARK_UPGRADE_REPORT_INTERVAL_IN_DAYS);
                if (StringUtils.isNotBlank(intervalDayStr)) {
                    logger.info(String.format("=== 自动发送spark升级报告: '%s' is %s", SPARK_UPGRADE_REPORT_INTERVAL_IN_DAYS, intervalDayStr));
                    int intervalDay = Integer.parseInt(intervalDayStr);
                    startTime = new Date(endTime.getTime() - intervalDay * 24 * 3600 * 1000);
                }

                // 统计spark升级结果
                SparkUpgradeReporterController reporter = new SparkUpgradeReporterController();
                SparkUpgradeReporterBean summary = reporter.getRunTaskSummary(sdf.format(startTime), sdf.format(endTime));
                logger.info(String.format("=== spark自动升级报告结果： %s", summary));
                StringBuilder builder = new StringBuilder();
                builder.append("【本周统计】").append("\n");
                builder.append("【统计时间范围】").append(String.format("%s~%s", sdf.format(startTime), sdf.format(endTime))).append("\n");
                builder.append("【统计结果汇总】").append("\n");
                builder.append(summary).append("\n");

                // 发送咚咚消息
                String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";
                String configSparkTeamUser = configMapper.getConfigValue(SPARK_UPGRADE_TEAM_MEMBER);
                if (StringUtils.isNotBlank(configSparkTeamUser)) {
                    logger.info(String.format("=== 自动发送spark升级报告: '%s' is %s", SPARK_UPGRADE_TEAM_MEMBER, configSparkTeamUser));
                    sparkTeamUser = configSparkTeamUser;
                }
                String title = "Spark自动升级-本周统计结果信息汇总";
                sendTimeline(builder.toString(), sparkTeamUser, title);
            }
        } catch (Exception e) {
            logger.info(String.format("=== 自动发送spark升级报告异常 exception !!!, %s", CommonUtil.exceptionToString(e)));
        }
    }


    /**
     * cron = 秒 分 时 日 月 星期
     * 手动模式提交任务时，用来自动同步buffalo数据库状态： 从今日0点~此刻运行的任务状态同步
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void runAutoSyncBuffaloStatus() {
        try {
            // 手动模式提交任务时，自动同步buffalo数据库最新状态开关
            String syncEnable = configMapper.getConfigValue(SPARK_UPGRADE_AUTO_SYNC_BUFFALO_STATUS_ENABLE);
            if (StringUtils.isBlank(syncEnable)) {
                syncEnable = "false";
            }
            logger.info(String.format("=== 本次同步buffalo数据库最新状态，开关: '%s' is %s", SPARK_UPGRADE_AUTO_SYNC_BUFFALO_STATUS_ENABLE, syncEnable));
            if (Boolean.parseBoolean(syncEnable)) {
                // 获取今天运行的双跑任务进行同步: 昨天0点到此刻的状态同步
                String startTime = new SimpleDateFormat("yyyy-MM-dd 00:00:00").format(new Date());  // 今日0点
                String endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                List<Integer> taskIds = buffaloMapper.selectOriginTaskInInterval(startTime, endTime); // 获取从现在到前一天0点之间所有的双跑的任务集合

                logger.info("=== 开始同步buffalo数据库最新状态");
                if (taskIds.size() > 0) {
                    logger.info(String.format("=== 时间范围：【%s~%s】内双跑的任务数量为: %s", startTime, endTime, taskIds.size()));
                    SparkUpgradeTaskAssessmentController assessmentController = new SparkUpgradeTaskAssessmentController();
                    SparkUpgradeImportResultBean syncResult = assessmentController.batchSyncBuffaloTask(null, null, taskIds, null, true, false, false);
                    logger.info(String.format("=== 本次同步状态的量结果: %s", MapperManager.writeValueAsString(syncResult)));
                } else {
                    logger.info(String.format("=== 时间范围：【%s~%s】内无双跑的任务, 直接跳过同步buffalo状态", startTime, endTime));
                }
            }
        } catch (Exception e) {
            logger.info(String.format("=== 自动同步buffalo任务状态异常 exception !!!, %s", CommonUtil.exceptionToString(e)));
        }
    }


    /**
     * cron = 秒 分 时 日 月 星期
     * 每天凌晨1点开始同步所有处于DOUBLE_RUNNING状态的任务
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void syncBuffaloStatusForDoubleRunning() {
        try {
            // 每天凌晨自动同步DOUBLE_RUNNING任务buffalo数据库最新状态
            String syncEnable = configMapper.getConfigValue(SPARK_UPGRADE_AUTO_SYNC_DOUBLE_RUNNING_ENABLE);
            if (StringUtils.isBlank(syncEnable)) {
                syncEnable = "false";
            }
            logger.info(String.format("=== 本次同步DOUBLE_RUNNING任务buffalo状态，开关: '%s' is %s", SPARK_UPGRADE_AUTO_SYNC_DOUBLE_RUNNING_ENABLE, syncEnable));
            if (Boolean.parseBoolean(syncEnable)) {// 获取今天运行的双跑任务进行同步: 昨天0点到此刻的状态同步
                SparkUpgradeTaskAssessmentController assessmentController = new SparkUpgradeTaskAssessmentController();
                SparkUpgradeImportResultBean syncResult = assessmentController.batchSyncBuffaloTask(null, null, null, "t2.status='DOUBLE_RUNNING'", true, false, false);
                logger.info(String.format("=== 本次同步DOUBLE_RUNNING任务buffalo状态结果: %s", MapperManager.writeValueAsString(syncResult)));
            }
        } catch (Exception e) {
            logger.info(String.format("=== 本次同步DOUBLE_RUNNING任务buffalo状态异常 exception !!!, %s", CommonUtil.exceptionToString(e)));
        }
    }

    /**
     * cron = 秒 分 时 日 月 星期
     * 自动同步并发送spark升级日报: 从今日0点~此刻的统计数据
     */
    @Scheduled(cron = "0 0 0/2 * * ?")
    public void runAutoSendDailyReport() {
        // 自动发送日报开关
        String reportEnable = configMapper.getConfigValue(SPARK_UPGRADE_AUTO_REPORT_DAILY_ENABLE);
        if (StringUtils.isBlank(reportEnable)) {
            reportEnable = "false";
        }
        logger.info(String.format("=== 自动发送spark升级日报开关: '%s' is %s", SPARK_UPGRADE_AUTO_REPORT_DAILY_ENABLE, reportEnable));

        if (Boolean.parseBoolean(reportEnable)) {
            // 同步完成后，发送当天统计日报
            String startTime = new SimpleDateFormat("yyyy-MM-dd 00:00:00").format(new Date());
            String endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            SparkUpgradeReporterController reporter = new SparkUpgradeReporterController();
            SparkUpgradeReporterBean summary = reporter.getRunTaskSummary(startTime, endTime);
            logger.info(String.format("=== spark自动升级报告结果： %s", summary));
            StringBuilder builder = new StringBuilder();
            builder.append("【本日统计】").append("\n");
            builder.append("【统计时间范围】").append(String.format("%s~%s", startTime, endTime)).append("\n");
            builder.append("【统计结果汇总】").append("\n");
            builder.append(summary).append("\n");

            // 发送咚咚消息
            String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";
            String configSparkTeamUser = configMapper.getConfigValue(SPARK_UPGRADE_TEAM_MEMBER);
            if (StringUtils.isNotBlank(configSparkTeamUser)) {
                logger.info(String.format("=== 自动发送spark升级报告: '%s' is %s", SPARK_UPGRADE_TEAM_MEMBER, configSparkTeamUser));
                sparkTeamUser = configSparkTeamUser;
            }
            String title = "Spark自动升级-本日统计结果信息汇总";
            sendTimeline(builder.toString(), sparkTeamUser, title);
        }
    }

    /**
     * cron = 秒 分 时 日 月 星期
     * 自动关闭双跑任务，并发送咚咚通知，每天23:30:00开始执行
     */
    @Scheduled(cron = "0 30 23 * * ?")
    public void closeDoubleRun() {
        String title = "Spark自动升级-双跑任务关闭通知";
        String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";
        try {
            // 自动关闭双跑开关
            String closeEnable = configMapper.getConfigValue(SPARK_UPGRADE_CLOSE_DOUBLE_RUN_ENABLE);
            if (StringUtils.isBlank(closeEnable)) {
                closeEnable = "false";
            }
            logger.info(String.format("=== 自动关闭双跑开关: '%s' is %s", SPARK_UPGRADE_CLOSE_DOUBLE_RUN_ENABLE, closeEnable));
            if (Boolean.parseBoolean(closeEnable)) {
                logger.info("=== 开始关闭双跑任务所有开关 ===");
                StringBuilder builder = new StringBuilder();
                builder.append("【关闭双跑详情】").append("\n");
                String configKey = String.format("%s,%s,%s", EXPLAIN_DOUBLE_RUN_TASK_ENABLE, CREATE_DOUBLE_RUN_TASK_ENABLE, RUN_DOUBLE_RUN_TASK_ENABLE);
                String configValue = "false";
                String[] keys = configKey.split(",");
                for (String key : keys) {
                    int affectedSize = configMapper.setConfigValue(key, configValue);
                    if (affectedSize > 0) {
                        builder.append(String.format("【开关】:%s 关闭成功", key)).append("\n");
                        logger.info(String.format("=== 设置t_sys_config属性成功, key: %s, value:%s", key, configValue));
                    } else {
                        builder.append(String.format("【开关】:%s 关闭失败，请手动关闭", key)).append("\n");
                        logger.info(String.format("=== 设置t_sys_config属性失败!!!, key: %s, value:%s", key, configValue));
                    }
                }

                // 发送咚咚消息
                String configSparkTeamUser = configMapper.getConfigValue(SPARK_UPGRADE_TEAM_MEMBER);
                if (StringUtils.isNotBlank(configSparkTeamUser)) {
                    sparkTeamUser = configSparkTeamUser;
                }
                sendTimeline(builder.toString(), sparkTeamUser, title);
            }
        } catch (Exception e) {
            logger.info(String.format("=== 自动关闭双跑任务exception !!!, %s", CommonUtil.exceptionToString(e)));
            sendTimeline("自动双跑任务关闭异常，请手动关闭!!!", sparkTeamUser, title);
        }
    }

    /**
     * cron = 秒 分 时 日 月 星期
     * 自动开启双跑任务，并发送咚咚通知，每天12:30:00开始执行
     */
    @Scheduled(cron = "0 30 12 * * ?")
    public void enableDoubleRun() {
        String title = "Spark自动升级-双跑任务开启通知";
        String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";

        try {
            // 自动开启双跑开关
            String openEnable = configMapper.getConfigValue(SPARK_UPGRADE_OPEN_DOUBLE_RUN_ENABLE);
            if (StringUtils.isBlank(openEnable)) {
                openEnable = "false";
            }
            logger.info(String.format("=== 自动开启双跑开关: '%s' is %s", SPARK_UPGRADE_OPEN_DOUBLE_RUN_ENABLE, openEnable));
            if (Boolean.parseBoolean(openEnable)) {
                logger.info("=== 开始开启双跑任务所有开关 ===");
                StringBuilder builder = new StringBuilder();
                builder.append("【开启双跑详情】").append("\n");
                String configKey = String.format("%s,%s,%s", EXPLAIN_DOUBLE_RUN_TASK_ENABLE, CREATE_DOUBLE_RUN_TASK_ENABLE, RUN_DOUBLE_RUN_TASK_ENABLE);
                String configValue = "true";

                // 跳过双跑的日期
                String skipDtsStr = configMapper.getConfigValue(SPARK_UPGRADE_SKIP_DOUBLE_RUN_DTS);
                if (StringUtils.isNotBlank(skipDtsStr)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    List<String> skipDts = Arrays.asList(skipDtsStr.split(","));
                    String now = sdf.format(new Date());
                    if (skipDts.contains(now)) {
                        configValue = "false";
                        logger.info(String.format("=== 跳过本次双跑: %s", now));
                    }
                }


                String[] keys = configKey.split(",");
                for (String key : keys) {
                    int affectedSize = configMapper.setConfigValue(key, configValue);
                    if (affectedSize > 0) {
                        builder.append(String.format("【开关】:%s 启用成功", key)).append("\n");
                        logger.info(String.format("=== 设置t_sys_config属性成功, key: %s, value:%s", key, configValue));
                    } else {
                        builder.append(String.format("【开关】:%s 启用失败，请手动启用", key)).append("\n");
                        logger.info(String.format("=== 设置t_sys_config属性失败!!!, key: %s, value:%s", key, configValue));
                    }
                }

                // 发送咚咚消息
                String configSparkTeamUser = configMapper.getConfigValue(SPARK_UPGRADE_TEAM_MEMBER);
                if (StringUtils.isNotBlank(configSparkTeamUser)) {
                    sparkTeamUser = configSparkTeamUser;
                }
                sendTimeline(builder.toString(), sparkTeamUser, title);
            }
        } catch (Exception e) {
            logger.info(String.format("=== 自动启用双跑任务exception !!!, %s", CommonUtil.exceptionToString(e)));
            sendTimeline("自动双跑任务启用异常，请手动启用!!!", sparkTeamUser, title);
        }
    }

    /**
     * cron = 秒 分 时 日 月 星期
     * 每天早上8点自动清理双跑HDFS数据
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void cleanHdfsTempDirs() {
        String title = "Spark自动升级-自动清理双跑HDFS数据通知";
        String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";

        try {
            String cleanEnable = configMapper.getConfigValue(SPARK_UPGRADE_AUTO_CLEAN_HDFS_TEMP_DIRS_ENABLE);
            if (StringUtils.isBlank(cleanEnable)) {
                cleanEnable = "false";
            }
            logger.info(String.format("=== 自动清理双跑HDFS数据，开关: '%s' is %s", SPARK_UPGRADE_AUTO_CLEAN_HDFS_TEMP_DIRS_ENABLE, cleanEnable));
            if (Boolean.parseBoolean(cleanEnable)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                StringBuilder builder = new StringBuilder();
                builder.append("【清理双跑HDFS目录详情】").append("\n");
                logger.info("=== 开始清理双跑HDFS数据 ===");
                String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/clean_wangriyu/";
                String scriptName = "run.sh";
                builder.append(String.format("【清理命令】: %s, sh %s%s", sdf.format(new Date()), baseDir, scriptName)).append("\n");
                builder.append(String.format("【清理进度】: %s, 开始清理HDFS数据", sdf.format(new Date()))).append("\n");
                sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                // 自动执行清理脚本
                String resultStr = ShellUtil.runCmdWithRes("sh " + baseDir + scriptName);
                JSONObject result = JSON.parseObject(resultStr);
                Integer status = result.getInteger("status");
                if (status != 0) {
                    logger.info("=== 自动清理双跑HDFS数据, 清理失败");
                    builder.append(String.format("【清理进度】: %s, 清理HDFS数据失败", sdf.format(new Date()))).append("\n");
                    sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                } else {
                    logger.info("=== 自动清理双跑HDFS数据, 清理成功");
                    builder.append(String.format("【清理进度】: %s, 清理HDFS数据成功", sdf.format(new Date()))).append("\n");
                    sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                }
            }
        } catch (Exception e) {
            logger.info(String.format("=== 自动清理双跑HDFS数据exception !!!, %s", CommonUtil.exceptionToString(e)));
            sendTimeline("自动清理双跑HDFS数据异常，请手动启用!!!", sparkTeamUser, title);
        }
    }


    /**
     * cron = 秒 分 时 日 月 星期
     * 每天早上8点10分自动清理双跑表
     */
    @Scheduled(cron = "0 10 8 * * ?")
    public void cleanDoubleRunTables() {
        String title = "Spark自动升级-自动清理双跑表通知";
        String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";

        try {
            String cleanEnable = configMapper.getConfigValue(SPARK_UPGRADE_AUTO_CLEAN_DOUBLE_RUN_TABLES_ENABLE);
            if (StringUtils.isBlank(cleanEnable)) {
                cleanEnable = "false";
            }
            logger.info(String.format("=== 自动清理双跑表，开关: '%s' is %s", SPARK_UPGRADE_AUTO_CLEAN_DOUBLE_RUN_TABLES_ENABLE, cleanEnable));
            if (Boolean.parseBoolean(cleanEnable)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                logger.info("=== 开始清理双跑表 ===");
                StringBuilder builder = new StringBuilder();
                builder.append("【清理双跑表详情】").append("\n");
                String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/run_dual_run/";
                String scriptName = "delete_tables.sh";
                builder.append(String.format("【清理命令】: %s, sh %s%s", sdf.format(new Date()), baseDir, scriptName)).append("\n");
                builder.append(String.format("【清理进度】: %s, 开始清理双跑表", sdf.format(new Date()))).append("\n");
                sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                // 自动执行清理脚本
                String resultStr = ShellUtil.runCmdWithRes("sh " + baseDir + scriptName);
                JSONObject result = JSON.parseObject(resultStr);
                Integer status = result.getInteger("status");
                if (status != 0) {
                    logger.info("=== 自动清理双跑表, 清理失败");
                    builder.append(String.format("【清理进度】: %s, 清理双跑表失败", sdf.format(new Date()))).append("\n");
                    sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                } else {
                    logger.info("=== 自动清理双跑表, 清理成功");
                    builder.append(String.format("【清理进度】: %s, 清理双跑表成功", sdf.format(new Date()))).append("\n");
                    sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                }
            }
        } catch (Exception e) {
            logger.info(String.format("=== 自动清理双跑表exception !!!, %s", CommonUtil.exceptionToString(e)));
            sendTimeline("自动清理双跑表，请手动启用!!!", sparkTeamUser, title);
        }
    }


    /**
     * cron = 秒 分 时 日 月 星期
     * 每天早上8点10分自动清理双跑表
     */
    @Scheduled(cron = "0 20 8 * * ?")
    public void cleanDoubleRunHopeTables() {
        String title = "Spark自动升级-自动清理hope集群双跑表通知";
        String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";

        try {
            String cleanEnable = configMapper.getConfigValue(SPARK_UPGRADE_AUTO_CLEAN_DOUBLE_RUN_TABLES_ENABLE);
            if (StringUtils.isBlank(cleanEnable)) {
                cleanEnable = "false";
            }
            logger.info(String.format("=== 自动清理hope集群双跑表，开关: '%s' is %s", SPARK_UPGRADE_AUTO_CLEAN_DOUBLE_RUN_TABLES_ENABLE, cleanEnable));
            if (Boolean.parseBoolean(cleanEnable)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                logger.info("=== 开始清理hope集群双跑表 ===");
                StringBuilder builder = new StringBuilder();
                builder.append("【清理hope集群双跑表详情】").append("\n");
                String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/run_dual_run/";
                String scriptName = "delete_tables_hope.sh";
                builder.append(String.format("【清理命令】: %s, sh %s%s", sdf.format(new Date()), baseDir, scriptName)).append("\n");
                builder.append(String.format("【清理进度】: %s, 开始清理hope集群双跑表", sdf.format(new Date()))).append("\n");
                sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                // 自动执行清理脚本
                String resultStr = ShellUtil.runCmdWithRes("sh " + baseDir + scriptName);
                JSONObject result = JSON.parseObject(resultStr);
                Integer status = result.getInteger("status");
                if (status != 0) {
                    logger.info("=== 自动清理hope集群双跑表, 清理失败");
                    builder.append(String.format("【清理进度】: %s, 清理hope集群双跑表失败", sdf.format(new Date()))).append("\n");
                    sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                } else {
                    logger.info("=== 自动清理hope集群双跑表, 清理成功");
                    builder.append(String.format("【清理进度】: %s, 清理hope集群双跑表成功", sdf.format(new Date()))).append("\n");
                    sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
                }
            }
        } catch (Exception e) {
            logger.info(String.format("=== 自动清理hope集群双跑表exception !!!, %s", CommonUtil.exceptionToString(e)));
            sendTimeline("自动清理hope集群双跑表，请手动启用!!!", sparkTeamUser, title);
        }
    }

    /**
     * 每半小时发送一次运行的实时情况
     */
    @Scheduled(cron = "0 0/30 * * * ?")
    public void sendRunningStatesRealtime() {
        String title = "Spark自动升级-春节实时双跑运行情况";
        String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";

        try {
            String sendEnable = configMapper.getConfigValue(SPARK_UPGRADE_SPRING_FESTIVAL_SEND_REALTIME_ENABLE);
            if (StringUtils.isBlank(sendEnable)) {
                sendEnable = "false";
            }
            logger.info(String.format("=== 自动发送春节实时双跑运行情况开关: '%s' is %s", SPARK_UPGRADE_SPRING_FESTIVAL_SEND_REALTIME_ENABLE, sendEnable));

            if (Boolean.parseBoolean(sendEnable)) {
                StringBuilder builder = new StringBuilder();

                // 双跑数据库中的各状态和数量
                builder.append("【春节增量双跑任务-实时状态和数量】").append("\n");
                List<DoubleRunTaskStatusSizeBo> statusList = bigGraphMapper.selectTaskStatusAndSizeIncr();
                for (DoubleRunTaskStatusSizeBo bo : statusList) {
                    builder.append(String.format("%s: %s", bo.getStatus(), bo.getSize())).append("\n");
                }

                // 当前buffalo运行中的实例数量
                builder.append("【春节增量buffalo实例-实时状态和数量】").append("\n");
                List<DoubleRunTaskStatusSizeBo> buffaloList = buffaloMapper.selectBuffaloInstanceStatusAndSize();
                for (DoubleRunTaskStatusSizeBo bo : buffaloList) {
                    builder.append(String.format("%s: %s", bo.getStatus(), bo.getSize())).append("\n");
                }

                // 进度条汇总
                builder.append("【春节双跑进度条】").append("\n");
                List<DoubleRunTaskStatusSizeBo> successList = statusList.stream().filter(v -> v.getStatus().equalsIgnoreCase(DOUBLE_RUN_SUCCESS.getCode())).collect(Collectors.toList());
                List<DoubleRunTaskStatusSizeBo> failedList = statusList.stream().filter(v -> v.getStatus().equalsIgnoreCase(DOUBLE_RUN_FAILED.getCode())).collect(Collectors.toList());
                int total = statusList.stream().map(DoubleRunTaskStatusSizeBo::getSize).reduce(0, Integer::sum);
                int success = successList.size() > 0 ? successList.get(0).getSize() : 0;
                int failed = failedList.size() > 0 ? failedList.get(0).getSize() : 0;
                builder.append(String.format("成功任务数/总任务数: %s / %s， (%.2f%%)", success, total, success * 1.0 / total * 100)).append("\n");
                builder.append(String.format("失败任务数/总任务数: %s / %s， (%.2f%%)", failed, total, failed * 1.0 / total * 100)).append("\n");
                builder.append(String.format("整体完成进度: %.2f%%", (success + failed) * 1.0 / total * 100)).append("\n");
                sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
            }
        } catch (Exception e) {
            logger.info(String.format("=== 实时发送运行情况exception !!!, %s", CommonUtil.exceptionToString(e)));
            sendTimeline(String.format("实时发送运行异常!!! message: %s", e.getMessage()), sparkTeamUser, title);
        }
    }

    /**
     * 每小时发送一次运行的实时错误码情况
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void sendRunningErrorCodeRealtime() {
        String title = "Spark自动升级-春节当日双跑错误码统计情况";
        String sparkTeamUser = "wuguoxiao,jianghe1,lvfulong,taowenjun1,wangqixiang9,weixiaoxing3,xiaowei12";

        try {

            String errorEnable = configMapper.getConfigValue(SPARK_UPGRADE_SPRING_FESTIVAL_ERROR_CODE_ENABLE);
            if (StringUtils.isBlank(errorEnable)) {
                errorEnable = "false";
            }
            logger.info(String.format("=== 自动发送春节实时双跑运行情况开关: '%s' is %s", SPARK_UPGRADE_SPRING_FESTIVAL_ERROR_CODE_ENABLE, errorEnable));

            if (Boolean.parseBoolean(errorEnable)) {
                StringBuilder builder = new StringBuilder();

                // 双跑实时错误码统计
                List<DoubleRunErrorCodeAndSizeBo> errorCodeList = bigGraphMapper.selectAllErrorCodeFromCurrent();

                List<String> list = new ArrayList<>();

                int previousCount = 0;
                for (DoubleRunErrorCodeAndSizeBo bo : errorCodeList) {
                    list.add(bo.getErrorCode());
                    Integer sumCount = bigGraphMapper.selectSumErrorCodeCountFromCurrent(list);
                    bo.setSize(sumCount - previousCount);
                    previousCount = sumCount;
                }

                int topN = 10;
                String topNstr = configMapper.getConfigValue(SPARK_UPGRADE_ERROR_CODE_TOP_N);
                if (StringUtils.isNotBlank(topNstr)) {
                    topN = Integer.parseInt(topNstr);
                }

                builder.append(String.format("【春节增量双跑任务-当日错误码和数量-TOP%s】", topN)).append("\n");
                errorCodeList.sort((a, b) -> b.getSize() - a.getSize());
                List<DoubleRunErrorCodeAndSizeBo> subList = errorCodeList.subList(0, topN >= errorCodeList.size() ? errorCodeList.size() : topN);
                for (DoubleRunErrorCodeAndSizeBo error : subList) {
                    builder.append(String.format("【%s】:%s（%s）", error.getSize(), error.getErrorDescription(), error.getExceptionKeyword())).append("\n");
                }

                sendTimelineToSparkTeam(builder.toString(), sparkTeamUser, title);
            }
        } catch (Exception e) {
            logger.info(String.format("=== 实时发送错误码情况exception !!!, %s", CommonUtil.exceptionToString(e)));
            sendTimeline(String.format("实时发送错误码异常!!! message: %s", e.getMessage()), sparkTeamUser, title);
        }
    }


    private static void sendTimelineToSparkTeam(String content, String sparkTeamUser, String title) {
        // 发送咚咚消息
        SparkUpgradeSysConfigMapper confMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeSysConfigMapper.class);
        String configSparkTeamUser = confMapper.getConfigValue(SPARK_UPGRADE_TEAM_MEMBER);
        if (StringUtils.isNotBlank(configSparkTeamUser)) {
            sparkTeamUser = configSparkTeamUser;
        }
        sendTimeline(content, sparkTeamUser, title);
    }

    private static void sendTimeline(String content, String erp, String title) {
        try {
            String url = "http://jfc-api.jd.com/jmr/v1/timeline/message/send";
            HashMap<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            JSONObject data = new JSONObject();
            data.put("toErpList", Arrays.asList(erp.split(",")));
            data.put("title", title);
            data.put("content", content);
            String responseText = SimpleHttpClient.sendJsonRequest(data, url, headers);
            logger.info(String.format("=== 通过HTTP接口发送spark升级报告响应: %s", responseText));
        } catch (Exception e) {
            logger.info(String.format("=== 通过HTTP接口发送spark升级报告异常: %s", CommonUtil.exceptionToString(e)));
        }
    }

    public static String getYesterday() {
        LocalDate currentDate = LocalDate.now();
        LocalDate previousDay = currentDate.minusDays(1); // 将日期向前推1天
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00");
        return previousDay.format(formatter);
    }

    public static void main(String[] args) {
        String yesterday1 = getYesterday();
        System.out.println(yesterday1);
        /*SparkUpgradeReporterBean bean = new SparkUpgradeReporterBean();
        bean.setTotalTaskNum(4242);
        bean.setTotalVcoresNum(10000.111);
        bean.setSuccessTaskNum(829);
        bean.setFailTaskNum(2000L);
        bean.setRunningTaskNum(1000L);
        bean.setSuccessQueryNum(1565.65);
        bean.setPerformanceRatio(0.1323);
        bean.setSuccessP95Num(9L);
        bean.setSuccessBaselineNum(52L);
        bean.setTotalSucVcores(4242.12);
        bean.setSaveVcores(609.98);
        System.out.println(bean);
        
        sendTimeline(bean, "xiaowei12", "2024-11-06 00:00:00", "2024-11-12 23:59:59");*/
        /*try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
            String todayStr = sdf.format(new Date());
            Date todayDate = sdf.parse(todayStr);
            Date yesterday = new Date(todayDate.getTime() - 24 * 3600 * 1000);
            System.out.println(sdf.format(todayDate) + "=== " + sdf.format(yesterday));
            System.out.println(todayDate.getTime()+ "=== " + yesterday.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }*/

    }

}
