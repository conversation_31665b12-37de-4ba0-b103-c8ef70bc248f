package com.jd.bdp.scheduler;

import com.jd.bdp.bean.CommonBean;
import com.jd.bdp.common.YamlUtil;
import com.jd.bdp.spark.web.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.*;
import java.util.logging.Logger;

@Component
public class Scheduler {
    private static final Logger logger = Logger.getLogger(Scheduler.class.getName());

    @Value("${SCHEDULER_MIGRATE_ENABLED}")
    private Boolean migrateEnabled;
    @Value("${SCHEDULER_XBP_ENABLED}")
    private Boolean xbpEnabled;
    @Value("${SCHEDULER_CHECK_TASK_PARTITION_ENABLED:true}")
    private Boolean checkTaskPartitionEnabled;

    public static ScheduledThreadPoolExecutor forkBuffaloTaskExecutorService;
    public static ScheduledThreadPoolExecutor monitorAndFillLogExecutorService;
    public static ScheduledThreadPoolExecutor fillSparkVersionExecutorService;
    public static ScheduledThreadPoolExecutor runSpark3ExecutorService;
    public static ScheduledThreadPoolExecutor checkDataExecutorService;
    public static ScheduledThreadPoolExecutor createXbpFlowExecutorService;
    public static ScheduledThreadPoolExecutor logAnalysisExecutorService;
    public static ThreadPoolExecutor executorService;

    public static void initThreadPool(){
        executorService = (ThreadPoolExecutor)Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        createXbpFlowExecutorService = (ScheduledThreadPoolExecutor)Executors.newScheduledThreadPool(1);
        logAnalysisExecutorService = (ScheduledThreadPoolExecutor)Executors.newScheduledThreadPool(1);
    }

    public static void initScheduler() {
        initMonitorAndFillBuffaloLogScheduler();
        reInitScheduler();
        initThreadPool();
    }

    public static CommonBean commonBean = YamlUtil.loadYaml("common.yaml", CommonBean.class);

    /**
     * 初始化ForkTaskExecutorService
     */
    public static void initForkTaskExecutorService() {
        forkBuffaloTaskExecutorService = (ScheduledThreadPoolExecutor)Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors());
    }

    /**
     * 重新初始化调度器
     */
    public static void reInitScheduler() {
        initForkTaskExecutorService();
        runSpark3ExecutorService = (ScheduledThreadPoolExecutor)Executors.newScheduledThreadPool(1);
        checkDataExecutorService = (ScheduledThreadPoolExecutor)Executors.newScheduledThreadPool(1);
    }

    /**
     * 初始化监视器并填充水牛日志调度程序
     */
    public static void initMonitorAndFillBuffaloLogScheduler(){
        monitorAndFillLogExecutorService = (ScheduledThreadPoolExecutor)Executors.newScheduledThreadPool(1);
    }


    @PostConstruct
    public void init() {
        initScheduler();
        logger.info("migrateEnabled = " + migrateEnabled);

        if (xbpEnabled) {
            XbpProcessServiceForPress xbpProcessServiceForPress = new XbpProcessServiceForPress();
            XbpProcessServiceForPresto xbpProcessServiceForPresto = new XbpProcessServiceForPresto();

            createXbpFlowExecutorService.scheduleAtFixedRate(XBPController::schedulerCreateXbpFlow, 20,
                    commonBean.getCreateXbpFlowSchedulerPeriodSecond(), TimeUnit.SECONDS);
            createXbpFlowExecutorService.scheduleAtFixedRate(XBPController::schedulerUpdateClusterMarket, 20,
                    commonBean.getCreateXbpFlowSchedulerPeriodSecond(), TimeUnit.SECONDS);
            createXbpFlowExecutorService.scheduleAtFixedRate(XBPController::schedulerCreateXbpFlowForInspection, 20,
                    commonBean.getCreateXbpFlowSchedulerPeriodSecond(), TimeUnit.SECONDS);
            createXbpFlowExecutorService.scheduleAtFixedRate(
                    () -> XBPController.schedulerCreateXbpFlow(xbpProcessServiceForPress, "t_nature_press_status", "10"), 20,
                    commonBean.getCreateXbpFlowSchedulerPeriodSecond(), TimeUnit.SECONDS);
            createXbpFlowExecutorService.scheduleAtFixedRate(
                    () -> XBPController.schedulerCreateXbpFlow(xbpProcessServiceForPresto, "t_nature_presto_press", "50"), 20,
                    commonBean.getCreateXbpFlowSchedulerPeriodSecond(), TimeUnit.SECONDS);
        }
    }
}