package com.jd.bdp.common;

import com.jd.common.util.StringUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.ParseException;
import java.util.Date;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.util.logging.Logger;

public class CommonUtil {

    private static final Logger logger = Logger.getLogger(CommonUtil.class.getName());

    /**
     * 将异常转换为字符串
     * @param ex 要转换的异常
     * @return 包含异常堆栈信息的字符串
     */
    public static String exceptionToString(Throwable ex){
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        ex.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * 将数据大小转换为人类可读的格式
     * @param bytes 输入的字节数
     * @return 返回转换后的数据大小字符串
     */
    public static String dataSiteInHuman(Long bytes) {
        if(bytes != null) {
            double kb = bytes / 1024d;
            if(kb >= 1024) {
                double mb = kb / 1024d;
                if(mb >= 1024) {
                    double gb = mb / 1024d;
                    if(gb>=1024) {
                        double tb = gb / 1024d;
                        return (long)tb + " TB";
                    } else {
                        return (long)gb + " GB";
                    }
                } else {
                    return (long)mb + " MB";
                }
            } else {
                return (long)kb + " KB";
            }
        }
        return "";
    }

    /**
     * 从任务ID字符串中获取任务ID集合
     * @param taskIdsStr 任务ID字符串
     * @return 包含任务ID的集合
     */
    public static Set<String> getTaskIds(String taskIdsStr) {
        Set<String> taskSet = new HashSet<>();
        for(String en : taskIdsStr.split(",")) {
            if(StringUtils.isNotEmpty(en)) {
                for (String taskIdStr : en.trim().split(" ")) {
                    if(StringUtils.isNotEmpty(taskIdStr)) {
                        try {
                            Long.parseLong(taskIdStr.trim());
                            taskSet.add(taskIdStr.trim());
                        } catch (Exception e) {
                            logger.fine(CommonUtil.exceptionToString(e));
                        }
                    }
                }
            }
        }
        return taskSet;
    }

    /**
     * 获取日期时间的格式化字符串
     * @param datetime 要格式化的日期时间字符串
     * @return 格式化后的日期字符串
     */
    public static String getDt(String datetime) {
        try {
            Date parse = SDFThreadLocal.get().parse(datetime);
            String format = SDFThreadLocal.getyyyyMMdd().format(parse);
            return format;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static Long convertToLong(String str) {
        try {
            return Long.parseLong(str);
        } catch (NumberFormatException e) {
            System.out.println("Invalid input: " + str);
            return null;
        }
    }

    public static int getTaskNodeId(String doubleRunTaskId) {
        try {
            int mod = Integer.parseInt(doubleRunTaskId) % 2;
            return mod == 0? 1402:1302;
        } catch (Exception e) {
            logger.warning(String.format("=== getTaskNodeId error, doubleRunTaskId=%s", doubleRunTaskId));
            Random random = new Random();
            return random.nextBoolean()? 1402:1302;
        }
    }
}
