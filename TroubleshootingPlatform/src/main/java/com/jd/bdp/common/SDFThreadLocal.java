package com.jd.bdp.common;

import java.text.SimpleDateFormat;
import java.util.Locale;

public class SDFThreadLocal {

    private final static ThreadLocal<SimpleDateFormat> threadLocalValue =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.SIMPLIFIED_CHINESE));

    private final static ThreadLocal<SimpleDateFormat> threadLocalyyyyMMddValue =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd", Locale.SIMPLIFIED_CHINESE));

    private final static ThreadLocal<SimpleDateFormat> threadLocalyyyyMMdd2Value =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd", Locale.SIMPLIFIED_CHINESE));

    private final static ThreadLocal<SimpleDateFormat> threadLocalyyyyMMValue =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM", Locale.SIMPLIFIED_CHINESE));

    private final static ThreadLocal<SimpleDateFormat> threadLocalyyyyValue =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy", Locale.SIMPLIFIED_CHINESE));

    public static SimpleDateFormat get() {
        return threadLocalValue.get();
    }

    public static SimpleDateFormat getyyyyMMdd() {
        return threadLocalyyyyMMddValue.get();
    }

    public static SimpleDateFormat getyyyyMM(){
        return threadLocalyyyyMMValue.get();
    }

    public static SimpleDateFormat getyyyy(){
        return threadLocalyyyyValue.get();
    }
}
