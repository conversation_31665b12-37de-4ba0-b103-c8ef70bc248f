package com.jd.bdp.common;

import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.Md5Util;
import com.jd.bdp.spark.web.SimpleHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.logging.Logger;

import static com.jd.bdp.scheduler.Scheduler.commonBean;

public class UGDAPManager {
    private static final CloseableHttpClient httpclient = HttpClients.createDefault();
    private static final Logger logger = Logger.getLogger(UGDAPManager.class.getName());


    /**
     * 数据套餐-erp获取集市信息v2
     * @param erp erp账号
     * @param status 1.数据套餐，2.urm，3.数据套餐+urm
     * @return
     * @throws IOException
     */
    public static JSONObject getMarketByErpV2(String erp, Integer status) throws IOException {
        String time = System.currentTimeMillis() + "";
        String sign = Md5Util.getMD5Str(commonBean.getUgdapApiAppid() + commonBean.getUgdapApiToken() + time);
        JSONObject data = new JSONObject();
        data.put("erp", erp);
        String url = "http://ugdap.jd.com/ugdapRestApi/market/getMarketByErpV2?"
                + "erp=" + erp
                + "&status=" + (status == null ? 1 : status)
                + "&appId=" + commonBean.getUgdapApiAppid()
                + "&sign=" + sign
                + "&time=" + time;
        String response = SimpleHttpClient.sendRequest(httpclient, url);
        JSONObject jsonObject = JSONObject.parseObject(response);
        logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
        return jsonObject;
    }

    /**
     * erp有权限的业务线
     * @param erp erp账号
     * @param martCode 集市code
     * @return
     * @throws IOException
     */
    public static JSONObject getBusinessLineByErp(String erp, String martCode) throws IOException {
        String time = System.currentTimeMillis() + "";
        String sign = Md5Util.getMD5Str(commonBean.getUgdapApiAppid() + commonBean.getUgdapApiToken() + time);
        JSONObject data = new JSONObject();
        data.put("erp", erp);
        String url = "http://ugdap.jd.com/ugdapRestApi/market/getBusinessLineByErp?"
                + "erp=" + erp
                + "&martCode=" + martCode
                + "&appId=" + commonBean.getUgdapApiAppid()
                + "&sign=" + sign
                + "&time=" + time;
        String response = SimpleHttpClient.sendRequest(httpclient, url);
        JSONObject jsonObject = JSONObject.parseObject(response);
        logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
        return jsonObject;
    }

    /**
     * 通过erp获取生产账号v2
     * @param erp 用户erp
     * @param martCode 集市code
     * @return
     * @throws IOException
     */
    public static JSONObject queryProductionAccountPowerV2(String erp, String martCode) throws IOException {
        String time = System.currentTimeMillis() + "";
        String sign = Md5Util.getMD5Str(commonBean.getUgdapApiAppid() + commonBean.getUgdapApiToken() + time);
        JSONObject data = new JSONObject();
        data.put("erp", erp);
        String url = "http://ugdap.jd.com/ugdapRestApi/AccountApi/queryProductionAccountPowerV2.json?"
                + "erp=" + erp
                + "&martUser=" + martCode
                + "&appId=" + commonBean.getUgdapApiAppid()
                + "&sign=" + sign
                + "&time=" + time;
        String response = SimpleHttpClient.sendRequest(httpclient, url);
        JSONObject jsonObject = JSONObject.parseObject(response);
        logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
        return jsonObject;
    }

    public static JSONObject queryQueueByErpV2(String erp, String martCode, String productionAccount) throws IOException {
        String time = System.currentTimeMillis() + "";
        String sign = Md5Util.getMD5Str(commonBean.getUgdapApiAppid() + commonBean.getUgdapApiToken() + time);
        String url = "http://ugdap.jd.com/ugdapRestApi/QueueApi/queryQueueByErpV2?"
                + "erp=" + erp
                + "&martUser=" + martCode
                + "&productionAccount=" + StringUtils.trimToEmpty(productionAccount)
                + "&appId=" + commonBean.getUgdapApiAppid()
                + "&sign=" + sign
                + "&time=" + time;
        String response = SimpleHttpClient.sendRequest(httpclient, url);
        JSONObject jsonObject = JSONObject.parseObject(response);
        logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
        return jsonObject;
    }
}
