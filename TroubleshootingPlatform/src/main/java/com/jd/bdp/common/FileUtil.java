package com.jd.bdp.common;

import com.jd.utils.StringUtil;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by root on 15-11-6.
 */
public class FileUtil {

    public static void createDirectory(String paths) {
        int i = paths.lastIndexOf("/");
        if (i == -1) {
            System.out.println("FileUtil.createDirectory InitDirectory Error");
            return;
        }
        File file = new File(paths.substring(0, i));
        if (!file.exists()) {
            file.mkdirs();
        } else {
            if (file.isFile()) {
                file.delete();
                file.mkdirs();
            }
        }
    }

    public static boolean fileExists(String path) {
        File file = new File(path);
        if (file.exists()) {
            if (file.isDirectory()) {
                boolean delete = file.delete();
                System.out.println(StringUtil.formatStr("Directory[{0}] delete status[{1}]", path, String.valueOf(delete)));
                return false;
            }

            if (file.isFile()) {
                return true;
            }
        }
        return false;
    }

    public static List<String> readFile(String filepath, Integer maxLine) {
        List<String> lines = new ArrayList<>();
        try {
            FileReader reader = new FileReader(filepath);
            BufferedReader br = new BufferedReader(reader);
            String str;
            int i = 0;
            while ((str = br.readLine()) != null) {
                if (maxLine != null && i++ > maxLine) {
                    break;
                }
                lines.add(str);
            }
            br.close();
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return lines;
    }

    public static List<String> readFile(String filepath) {
        return readFile(filepath, null);
    }

    public static CommonResult readContent(String filePath) {
        StringBuilder builder = new StringBuilder();
        File file = new File(filePath);
        if (!file.isFile()) {
            return new CommonResult("path[" + filePath + "] is not a file or directory not found.");
        }
        FileReader fileReader;
        try {
            fileReader = new FileReader(file);
        } catch (FileNotFoundException e) {
            return new CommonResult("path[" + filePath + "] " + e.getMessage());
        }
        BufferedReader bufferedReader = new BufferedReader(fileReader);
        String line;
        try {
            while ((line = bufferedReader.readLine()) != null) {
                builder.append(line).append(System.lineSeparator());
            }
        } catch (IOException e) {
            return new CommonResult("path[" + filePath + "] " + e.getMessage());
        }
        return new CommonResult(true, builder.toString());
    }


    /**
     * 写入文件
     * @param filePath 文件路径
     * @param content 文件内容
     * @param append 是否追加
     * @return 写入是否成功
     */
    public static boolean writeFile(String filePath, String content, Boolean append) {
        return writeFile(filePath, content, append, "utf-8");
    }

    /**
     * 写入文件
     * @param filePath 文件路径
     * @param content 文件内容
     * @param append 是否追加
     * @param encode 编码格式
     * @return 是否写入成功
     * @throws IOException 写入异常
     */
    public static boolean writeFile(String filePath, String content, Boolean append, String encode) {
        if (append == null) {
            append = false;
        }
        FileOutputStream fileOutputStream1;
        try {
            fileOutputStream1 = new FileOutputStream(filePath, append);
            fileOutputStream1.write(content.getBytes(encode));
            fileOutputStream1.flush();
            fileOutputStream1.close();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 获取文章内容
     * @param f 文件对象
     * @param charset 字符编码
     * @return 文章内容的字符串
     * @throws IOException 如果发生I/O错误
     */
    public static String getContent(File f,String charset) throws IOException {
        if(f.exists())
        {
            FileInputStream input=new FileInputStream(f);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            try {
                int n = 0;
                byte[] b = new byte[1024 * 10];
                while (true) {
                    n = input.read(b);
                    if (n < 0) break;
                    out.write(b, 0, n);
                }
                return out.toString(charset);
            }
            catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            finally {
                input.close();
            }

        }
        return null;
    }

    /**
     * 将字符串写入到指定的文件中
     * @param f 要写入的文件
     * @param str 要写入的字符串
     * @param charset 字符编码
     * @throws FileNotFoundException 文件未找到异常
     * @throws UnsupportedEncodingException 不支持的字符编码异常
     * @throws IOException IO异常
     */
    public static void putString(File f,String str,String charset) {
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(f);
            out.write(str.getBytes(charset));
        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        finally{
            try {
                out.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    public static class CommonResult {
        private boolean isSuccess = false;
        private String errorMessage;
        private String content;

        public CommonResult(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public CommonResult(boolean isSuccess, String content) {
            this.isSuccess = isSuccess;
            this.content = content;
        }

        public boolean isSuccess() {
            return isSuccess;
        }

        public void setSuccess(boolean success) {
            isSuccess = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        @Override
        public String toString() {
            return "CommonResult{" +
                    "isSuccess=" + isSuccess +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", content='" + content + '\'' +
                    '}';
        }
    }


}
