package com.jd.bdp.common;

import com.jd.bdp.spark.web.KongmingService;

import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class DorisDao {

    private static final Logger logger = Logger.getLogger(DorisDao.class.getName());

    static {
        try {
            Class.forName("com.mysql.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }

    /**
     * 执行指定数据库中的 SQL 语句
     * @param sql 要执行的 SQL 语句
     * @param dbName 数据库名称
     * @param params SQL 语句中的参数列表
     * @return 执行 SQL 后返回的结果列表
     */
    public static List<Map<String, Object>> executeSql(String sql, String dbName, List<Object> params) {
        return KongmingService.executeSql(sql, dbName, params);
    }

}
