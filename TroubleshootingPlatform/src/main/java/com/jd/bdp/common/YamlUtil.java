package com.jd.bdp.common;

import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.Map;

public class YamlUtil {
    /**
     * 加载YAML文件并将其转换为指定类型的对象
     * @param yamlName YAML文件的名称
     * @param clazz 目标对象的类
     * @return 转换后的目标对象
     * @throws NullPointerException 如果输入流为空
     */
    public static <T> T loadYaml(String yamlName, Class<T> clazz){
        Yaml yaml = new Yaml();
        InputStream inputStream = YamlUtil.class
                .getClassLoader()
                .getResourceAsStream(yamlName);
        return yaml.loadAs(inputStream, clazz);
    }

    private static final String tuningTemplateFileName = "tuningTemplate.yaml";

    public static Map<String,String> sqlTemplate = YamlUtil.loadYaml(tuningTemplateFileName, Map.class);
}
