package com.jd.bdp.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jd.bdp.bean.Md5Util;
import com.jd.bdp.spark.web.OkHttp;
import com.jd.bdp.spark.web.SimpleHttpClient;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.*;
import java.util.logging.Logger;

import static com.jd.bdp.scheduler.Scheduler.*;

/**
 * 文档地址：https://cf.jd.com/pages/viewpage.action?pageId=105299295
 * 新的文档地址：https://cf.jd.com/pages/viewpage.action?pageId=*********
 */
public class Buffalo4TaskManager {

  private static final CloseableHttpClient httpclient = HttpClients.createDefault();
  private static final Logger logger = Logger.getLogger(Buffalo4TaskManager.class.getName());

  private static Map<String, Object> buildDefActionData(String actionName,
      String actionType,
      String marketCode, Integer taskNodeType, String taskNodeId,
      Integer scriptId, String scriptStartFile,
      String args, String clusterCode, String queueCode, String accountCode,CgroupConfigEnum cgroupConfig) {
    Map<String, Object> data = new HashMap<>();
    List<Integer> cpuAndMemLimit = cgroupConfig.getCpuAndMemLimit();
    assert cpuAndMemLimit.size()==2;
    data.put("cpuLimitSize", cpuAndMemLimit.get(0));
    data.put("memLimitSize", cpuAndMemLimit.get(1) * 1024);
    data.put("actionName", actionName);
    data.put("actionType", actionType);
    data.put("marketCode", marketCode);
    data.put("taskNodeType", taskNodeType);
    data.put("taskNodeId", taskNodeId);
    data.put("scriptId", scriptId);
    data.put("scriptStartFile", scriptStartFile);
    data.put("args", args);
    data.put("clusterCode", clusterCode);
    data.put("queueCode", queueCode);
    data.put("accountCode", accountCode);
    return data;
  }

  private static Map<String, Object> buildActionDepend(String pActionName, String actionName) {
    Map<String, Object> data = new HashMap<>();
    data.put("pActionName", pActionName);
    data.put("actionName", actionName);
    return data;
  }


  private static Map<String, Object> buildDefTaskData(String taskName, String taskType, String description,
      Integer appGroupId, String managers, String periodicType, Integer failRetry, Integer status) {
    Map<String, Object> data = new HashMap<>();
    data.put("taskName", taskName);
    data.put("description", description);
    data.put("appGroupId", appGroupId);
    data.put("taskType", taskType);
    data.put("periodicType", periodicType);
    data.put("managers", managers);
    data.put("failRetry", failRetry);
    data.put("status", status);
    return data;
  }

  /**
   * buffalo4-任务管理-查询工作流环节列表 https://cf.jd.com/pages/viewpage.action?pageId=*********
   *
   * @param taskId
   * @return
   * @throws IOException
   */
  public static JSONObject getActionListByTaskId(String taskId) throws IOException {
    CallerInfo callerInfo = Profiler.registerInfo("bdp.spark3.scheduler4.getActionListByTaskId",
            "SparkMonitorApp", false, true);
    try {
      String time = System.currentTimeMillis() + "";
      String sign = Md5Util.getMD5Str(
              commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
      JSONObject data = new JSONObject();
      data.put("taskId", taskId);
      String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/task/info?"
              + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
              + commonBean.getBuffaloApiUserToken() + "&time=" + time
              + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
      String response = SimpleHttpClient.sendRequest(httpclient, url);
      JSONObject jsonObject = JSONObject.parseObject(response);
      logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
      return jsonObject;
    } catch (Exception e) {
      Profiler.functionError(callerInfo);
      throw e;
    } finally {
      Profiler.registerInfoEnd(callerInfo);
    }
  }

  /**
   * buffalo4-日志管理-查询任务详细日志 https://cf.jd.com/pages/viewpage.action?pageId=182730542
   */
  public static JSONObject getRunLogInfo(Long runLogId, Integer start, Integer limit)
      throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(
        commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("runLogId", runLogId);
    data.put("start", start);
    data.put("limit", limit);
    String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/instance/getRunLogInfo?"
        + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
        + commonBean.getBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    JSONObject jsonObject = JSONObject.parseObject(response);
    logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
    return jsonObject;
  }

  /**
   * buffalo4-任务管理-批量修改任务/环节执行引擎或版本 https://cf.jd.com/pages/viewpage.action?pageId=368206985
   *
   * @param taskIds
   * @param actionIds
   * @param hadoopEngineType
   * @param hadoopEngineVersion
   * @param userTokenOrTicket
   * @return
   * @throws IOException
   */
  public static JSONObject batchModifyMarketInfo(
      List<Integer> taskIds, List<Integer> actionIds,
      String hadoopEngineType, String hadoopEngineVersion, String userTokenOrTicket,
      String buffaloEnv) throws IOException {
    CallerInfo callerInfo = Profiler.registerInfo("bdp.spark3.scheduler4.modifySparkVersion",
        "SparkMonitorApp", false, true);
    if (StringUtils.isEmpty(buffaloEnv)) {
      buffaloEnv = "buffalo4.bdp.jd.local";
    }
    try {
      String time = System.currentTimeMillis() + "";
      String sign = Md5Util.getMD5Str(
          commonBean.getBuffaloApiAppToken() + userTokenOrTicket + time);
      JSONObject data = new JSONObject();
      data.put("taskIds", taskIds);
      data.put("actionIds", actionIds);
      data.put("hadoopEngineType", hadoopEngineType);
      data.put("hadoopEngineVersion", hadoopEngineVersion);
      String url = "http://" + buffaloEnv + "/api/v2/buffalo4/task/batchModifyMarketInfo?"
          + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken=" + userTokenOrTicket
          + "&time=" + time
          + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
      String response = SimpleHttpClient.sendRequest(httpclient, url);
      JSONObject jsonObject = JSONObject.parseObject(response);
      logger.info("Request url is " + url + " data is " + data.toJSONString()
          + " response is " + jsonObject.toJSONString());
      return jsonObject;
    } catch (Exception e) {
      Profiler.functionError(callerInfo);
      throw e;
    } finally {
      Profiler.registerInfoEnd(callerInfo);
    }
  }

  /**
   * buffalo4-任务运行记录-根据任务ID查询任务下任务实例的运行记录 https://cf.jd.com/pages/viewpage.action?pageId=167669522
   *
   * @param taskId
   * @return
   * @throws IOException
   */
  public static JSONObject getRunLogByTaskId(Long taskId, String startTime, String endTime)
      throws IOException {
    CallerInfo callerInfo = Profiler.registerInfo("bdp.buffalo.getRunLogByTaskId",
        "SparkMonitorApp", false, true);
    try {
      String time = System.currentTimeMillis() + "";
      String sign = Md5Util.getMD5Str(
          commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
      JSONObject data = new JSONObject();
      data.put("taskId", taskId);
      data.put("startTime", startTime);
      data.put("endTime", endTime);
      String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/instance/getTaskInstRunLogList?"
          + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
          + commonBean.getBuffaloApiUserToken() + "&time=" + time
          + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
      String response = SimpleHttpClient.sendRequest(httpclient, url);
      JSONObject jsonObject = JSONObject.parseObject(response);
      logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
      return jsonObject;
    } catch (Exception e) {
      Profiler.functionError(callerInfo);
      throw e;
    } finally {
      Profiler.registerInfoEnd(callerInfo);
    }
  }

  /**
   * buffalo5-任务管理-更新任务信息 https://cf.jd.com/pages/viewpage.action?pageId=1064818500
   * @param buffalo4TaskId
   * @param appGroupId
   * @param managers
   * @param taskName
   * @param description
   * @return
   * @throws IOException
   *
   * {cgroupSetType:1, cpuLimitSize:1, memLimitSize:1}
   * {cgroupSetType:101, cpuLimitSize:1, memLimitSize:2}
   * {cgroupSetType:2, cpuLimitSize:2, memLimitSize:2}
   * {cgroupSetType:102, cpuLimitSize:2, memLimitSize:4}
   * {cgroupSetType:3, cpuLimitSize:3, memLimitSize:3}
   * {cgroupSetType:103, cpuLimitSize:3, memLimitSize:6}
   * {cgroupSetType:4, cpuLimitSize:4, memLimitSize:4}
   * {cgroupSetType:104, cpuLimitSize:4, memLimitSize:8}
   * {cgroupSetType:6, cpuLimitSize:6, memLimitSize:6}
   * {cgroupSetType:105, cpuLimitSize:6, memLimitSize:12}
   * {cgroupSetType:8, cpuLimitSize:8, memLimitSize:8}
   * {cgroupSetType:106, cpuLimitSize:8, memLimitSize:16}
   * {cgroupSetType:10, cpuLimitSize:10, memLimitSize:10}
   * {cgroupSetType:107, cpuLimitSize:10, memLimitSize:20}
   * {cgroupSetType:12, cpuLimitSize:12, memLimitSize:12}
   * {cgroupSetType:16, cpuLimitSize:16, memLimitSize:16}
   * {cgroupSetType:20, cpuLimitSize:20, memLimitSize:20}
   * {cgroupSetType:25, cpuLimitSize:25, memLimitSize:25}
   * {cgroupSetType:30, cpuLimitSize:30, memLimitSize:30}
   *
   */
  public static JSONObject buffalo5ModifyTask(Long buffalo4TaskId,Long appGroupId,
      String managers, String taskName, String description) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId() + commonBean.getShiftHillsBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskId", buffalo4TaskId);
    data.put("managers", managers);
    data.put("appGroupId", appGroupId);
    data.put("taskName", taskName);
    data.put("description",description);
    String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/task/modify?"
        + "appId=" + commonBean.getShiftHillsBuffaloApiAppId() + "&userToken="
        + commonBean.getShiftHillsBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    JSONObject jsonObject = JSONObject.parseObject(response);
    logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
    return jsonObject;
  }

  /**
   * buffalo5-任务管理-删除环节信息 https://cf.jd.com/pages/viewpage.action?pageId=1080518716
   * @param buffalo4TaskId
   * @param actionId
   * @return
   * @throws IOException
   */
  public static JSONObject buffalo5DeleteAction(Long buffalo4TaskId, Long actionId) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId() + commonBean.getShiftHillsBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskId",buffalo4TaskId);
    data.put("actionId",actionId);
    String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/task/action/delete?"
        + "appId=" + commonBean.getShiftHillsBuffaloApiAppId() + "&userToken="
        + commonBean.getShiftHillsBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    JSONObject jsonObject = JSONObject.parseObject(response);
    logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
    return jsonObject;
  }

  /**
   * buffalo4-任务管理-查询任务详情 https://cf.jd.com/pages/viewpage.action?pageId=105299295
   * 新接口文档：https://cf.jd.com/pages/viewpage.action?pageId=485105888
   *
   * @param buffalo4TaskId
   * @return
   * @throws IOException
   */
  public static JSONObject buffalo4GetTaskInfo(Long buffalo4TaskId) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(
        commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskId", buffalo4TaskId);
    String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/task/info?"
        + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
        + commonBean.getBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    JSONObject jsonObject = JSONObject.parseObject(response);
    logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
    return jsonObject;
  }

  /**
   * buffalo4-实例管理-创建实例 https://cf.jd.com/pages/viewpage.action?pageId=235502524
   *
   * @param taskId
   * @param scheTime 可为空
   * @param follower
   * @return
   * @throws IOException
   */
  public static JSONObject buffalo4CreateTaskInstance(Integer taskId, String args, String scheTime, String follower)
      throws IOException {
    JSONObject actionListByTaskId = getActionListByTaskId(taskId + "");
    JSONArray actionList = null;
    if (actionListByTaskId.getBooleanValue("success")) {
      JSONObject jsonObject = actionListByTaskId.getJSONObject("obj");
      if (jsonObject == null) {
        BuffaloTaskDelete.deleteBuffaloTask(taskId);
        throw new RuntimeException("Abnormal task, lacking any action, taskId = " + taskId);
      }
      actionList = jsonObject.getJSONArray("actionList");
      if(actionList == null || actionList.isEmpty()) {
        BuffaloTaskDelete.deleteBuffaloTask(taskId);
        throw new RuntimeException("Abnormal task, lacking any action, taskId = " + taskId);
      }
    }
    String appendFollower = "";
    if(StringUtils.isNotEmpty(follower)) {
      appendFollower = ";" + follower;
    }
    JSONObject actionArgsObj = new JSONObject();
    for (int i = 0; i < actionList.size(); i++) {
      JSONObject jsonObject = actionList.getJSONObject(i);
      String actionName = jsonObject.getString("actionName");
      String actionArgs = jsonObject.getString("args");
      actionArgsObj.put(actionName, actionArgs + appendFollower);
    }

    CallerInfo callerInfo = Profiler.registerInfo("bdp.spark3.scheduler4.createInstance",
        "SparkMonitorApp", false, true);
    try {
      String time = System.currentTimeMillis() + "";
      String sign = Md5Util.getMD5Str(
          commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
      JSONObject data = new JSONObject();
      data.put("taskId", taskId);
      data.put("scheTime",
          StringUtils.defaultIfEmpty(scheTime, SDFThreadLocal.get().format(new Date())));
      data.put("args", args);
      data.put("actionArgs", actionArgsObj);
      String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/instance/createTestInstance?"
          + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
          + commonBean.getBuffaloApiUserToken() + "&time=" + time
          + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
      String response = SimpleHttpClient.sendRequest(httpclient, url);
      JSONObject jsonObject = JSONObject.parseObject(response);
      logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
      return jsonObject;
    } catch (Exception e) {
      Profiler.functionError(callerInfo);
      throw e;
    } finally {
      Profiler.registerInfoEnd(callerInfo);
    }
  }

  public static JSONObject buffalo4CreateTaskInstanceN(Integer taskId, String args, String scheTime)
          throws IOException {
    CallerInfo callerInfo = Profiler.registerInfo("bdp.spark3.scheduler4.createInstance",
            "SparkMonitorApp", false, true);
    try {
      String time = System.currentTimeMillis() + "";
      String sign = Md5Util.getMD5Str(
              commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
      JSONObject data = new JSONObject();
      data.put("taskId", taskId);
      data.put("scheTime",
              StringUtils.defaultIfEmpty(scheTime, SDFThreadLocal.get().format(new Date())));
      data.put("args", args);
      String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/instance/createTestInstance?"
              + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
              + commonBean.getBuffaloApiUserToken() + "&time=" + time
              + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
      String response = SimpleHttpClient.sendRequest(httpclient, url);
      JSONObject jsonObject = JSONObject.parseObject(response);
      logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
      return jsonObject;
    } catch (Exception e) {
      Profiler.functionError(callerInfo);
      throw e;
    } finally {
      Profiler.registerInfoEnd(callerInfo);
    }
  }

  /**
   * buffalo4-实例管理-通过实例ID重跑实例 https://cf.jd.com/pages/viewpage.action?pageId=105299378
   *
   * @param taskInstId
   * @return
   * @throws IOException
   */
  public static JSONObject buffalo4RerunInstanceByInstId(String taskInstId) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(
        commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskInstId", taskInstId);
    String url =
        "http://buffalo4.bdp.jd.local/api/v3/buffalo5/instance/instanceReRunInfoByCondition?"
            + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
            + commonBean.getBuffaloApiUserToken() + "&time=" + time
            + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    logger.info("Request url is " + url + " response is " + response);
    return JSONObject.parseObject(response);
  }

  /**
   * 任务依赖关系定义-查询任务父依赖 https://cf.jd.com/pages/viewpage.action?pageId=485105894
   *
   * @param taskId
   * @param limit
   * @return
   * @throws IOException
   */
  public static JSONObject getTaskParentDepends(Integer taskId, int limit) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId()
        + commonBean.getShiftHillsBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskId", taskId);
    data.put("page", 1);
    data.put("limit", limit);
    String url = "http://buffalo4.jbdp.jd.com/api/v3/buffalo5/task/depend/getTaskParentDependList?"
        + "appId=" + commonBean.getShiftHillsBuffaloApiAppId() + "&userToken="
        + commonBean.getShiftHillsBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    logger.info("Request url is " + url + " response is " + response);
    return JSONObject.parseObject(response);
  }

  /**
   * 任务定义-新建任务 https://cf.jd.com/pages/viewpage.action?pageId=1021062762
   *
   * @param taskName
   * @param description
   * @param appGroupId
   * @param taskType
   * @param periodicType
   * @param managers
   * @param failRetry
   * @param status
   * @return
   * @throws IOException
   */
  public static JSONObject buffalo5TaskCreate(String taskName, String description,
      Integer appGroupId, String taskType,
      String periodicType, String managers, Integer failRetry, Integer status)
      throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId()
        + commonBean.getShiftHillsBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskName", taskName);
    data.put("description", description);
    data.put("appGroupId", appGroupId);
    data.put("taskType", taskType);
    data.put("periodicType", periodicType);
    data.put("managers", managers);
    data.put("failRetry", failRetry);
    data.put("status", status);
    String url = "http://buffalo4.jbdp.jd.com/api/v3/buffalo5/task/create?"
        + "appId=" + commonBean.getShiftHillsBuffaloApiAppId() + "&userToken="
        + commonBean.getShiftHillsBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    logger.info("Request url is " + url + " response is " + response);
    return JSONObject.parseObject(response);
  }

  public static JSONObject stopInstanceByInstId(String taskInstIds)
      throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId()
        + commonBean.getShiftHillsBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskInstIds", taskInstIds);
    String url = "http://buffalo4.jbdp.jd.com/api/v3/buffalo5/instance/stopInstanceByInstId?"
        + "appId=" + commonBean.getShiftHillsBuffaloApiAppId() + "&userToken="
        + commonBean.getShiftHillsBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    logger.info("Request url is " + url + " response is " + response);
    return JSONObject.parseObject(response);
  }

  public static JSONObject buffalo5TaskActionCreate(Integer taskId, String actionName,
      String actionType,
      String marketCode, Integer taskNodeType, String taskNodeId,
      Integer scriptId, String scriptStartFile, String parentIds,
      String args, String clusterCode, String queueCode, String accountCode) {
    return buffalo5TaskActionCreate(taskId,actionName,actionType,marketCode,taskNodeType,taskNodeId,
        scriptId,scriptStartFile,parentIds,args,clusterCode,queueCode,accountCode,CgroupConfigEnum._6C12G);
  }

  /**
   * 工作流环节定义-新建环节 https://cf.jd.com/pages/viewpage.action?pageId=**********
   *
   * @param taskId
   * @param actionName
   * @param actionType
   * @param marketCode
   * @param taskNodeType
   * @param taskNodeId
   * @param scriptId
   * @param scriptStartFile
   * @return
   * @throws IOException
   */
  public static JSONObject buffalo5TaskActionCreate(Integer taskId, String actionName,
      String actionType,
      String marketCode, Integer taskNodeType, String taskNodeId,
      Integer scriptId, String scriptStartFile, String parentIds,
      String args, String clusterCode, String queueCode, String accountCode,CgroupConfigEnum cgroupConfig) {
    JSONObject data = new JSONObject();
    CallerInfo callerInfo = Profiler.registerInfo("bdp.buffalo.buffalo5TaskActionCreate",
            "SparkMonitorApp", false, true);
    try {
      String time = System.currentTimeMillis() + "";
      String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId()
              + commonBean.getShiftHillsBuffaloApiAppToken() + time);
      data.put("taskId", taskId);
      data.put("actionName", actionName);
      data.put("actionType", actionType);
      data.put("marketCode", marketCode);
      data.put("taskNodeType", taskNodeType);
      data.put("taskNodeId", taskNodeId);
      data.put("scriptId", scriptId);
      data.put("scriptStartFile", scriptStartFile);
      data.put("parentIds", parentIds);
      data.put("args", args);
      data.put("clusterCode", clusterCode);
      data.put("queueCode", queueCode);
      data.put("accountCode", accountCode);
      data.put("cgroupSetType", cgroupConfig.cgroupSetType);
      String url = "http://buffalo4.jbdp.jd.com/api/v3/buffalo5/task/action/create?"
              + "appId=" + commonBean.getShiftHillsBuffaloApiAppId() + "&userToken="
              + commonBean.getShiftHillsBuffaloApiUserToken() + "&time=" + time
              + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
      String response = SimpleHttpClient.sendRequest(httpclient, url);
      logger.info("Request url is " + url + " response is " + response);
      return JSONObject.parseObject(response);
    } catch (Exception e) {
      Profiler.functionError(callerInfo);
      return data;
    } finally {
      Profiler.registerInfoEnd(callerInfo);
    }
  }

  public static JSONObject buffalo5TaskActionModify(Integer taskId, Integer actionId,
      Integer taskNodeType,
      Integer taskNodeId,CgroupConfigEnum cgroupConfigEnum)
      throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId()
        + commonBean.getShiftHillsBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskId", taskId);
    data.put("actionId", actionId);
    data.put("taskNodeType", taskNodeType);
    data.put("taskNodeId", taskNodeId);
    data.put("cgroupSetType",cgroupConfigEnum.getSetType());
    String url = "http://buffalo4.jbdp.jd.com/api/v3/buffalo5/task/action/modify?"
        + "appId=" + commonBean.getShiftHillsBuffaloApiAppId() + "&userToken="
        + commonBean.getShiftHillsBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    logger.info("Request url is " + url + " response is " + response);
    return JSONObject.parseObject(response);
  }

  public static JSONObject buffalo5ScriptUpdate(int fileId, String description, String managers,
      String scriptPackagePath, String calcEngine,
      String verDescription, InputStream inputStream, String fileName) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId()
        + commonBean.getShiftHillsBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("fileid", fileId);
    data.put("description", description);
    data.put("managers", managers);
    data.put("scriptPackagePath", scriptPackagePath);
    data.put("calEngine", calcEngine);
    data.put("verDescription", verDescription);
    Map<String, Object> params = new HashMap<>();
    params.put("appId", commonBean.getShiftHillsBuffaloApiAppId());
    params.put("token", commonBean.getShiftHillsBuffaloApiAppToken());
    params.put("userToken", commonBean.getShiftHillsBuffaloApiUserToken());
    params.put("data",
        JSONObject.toJSONString(data, SerializerFeature.DisableCircularReferenceDetect));
    params.put("file", inputStream);
    params.put("sign", sign);
    params.put("time", time);
    String response = OkHttp.sendMultipartPost(
        "http://buffalo4.jbdp.jd.com//api/v3/buffalo5/script/update", params, fileName);
    return JSONObject.parseObject(response);
  }

  /**
     * 脚本管理-新建脚本
     * https://cf.jd.com/pages/viewpage.action?pageId=1066861626
     * @param jsdAppgroupId
     * @param managers
     * @param scriptPackagePath
     * @param inputStream
     * @param filename
     * @return
     * @throws IOException
     */public static JSONObject buffalo5ScriptAdd(Integer jsdAppgroupId, String managers,
      String scriptPackagePath, InputStream inputStream, String filename) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(commonBean.getShiftHillsBuffaloApiAppId()
        + commonBean.getShiftHillsBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("jsdAppgroupId", jsdAppgroupId);
    data.put("managers", managers);
    data.put("scriptPackagePath", scriptPackagePath);
    Map<String, Object> params = new HashMap<>();
    params.put("appId", commonBean.getShiftHillsBuffaloApiAppId());
    params.put("token", commonBean.getShiftHillsBuffaloApiAppToken());
    params.put("userToken", commonBean.getShiftHillsBuffaloApiUserToken());
    params.put("sign", sign);
    params.put("time", time);
    params.put("data",
        JSONObject.toJSONString(data, SerializerFeature.DisableCircularReferenceDetect));
    params.put("file", inputStream);
    String response = OkHttp.sendMultipartPost(
        "http://buffalo4.jbdp.jd.com/api/v3/buffalo5/script/add", params, filename);
//        logger.info("Request url is " + url + " response is " + response);
    return JSONObject.parseObject(response);
  }

  /**
   * 从字符串中提取数字并排序返回
   *
   * @param str 包含数字的字符串，以逗号分隔
   * @return 排序后的整数列表
   */
  public static List<Integer> sortNumberFromStr(String str) {
    List<Integer> numbers = new ArrayList<>();
    String[] parts = str.split(",");
    for (String part : parts) {
      part = part.trim();
      try {
        int number = Integer.parseInt(part);
        numbers.add(number);
      } catch (NumberFormatException e) {
        e.printStackTrace();
      }
    }
    Collections.sort(numbers);
    return numbers;
  }

  public enum CgroupConfigEnum {
    _1C1G(1),
    _1C2G(101),
    _2C2G(2),
    _2C4G(102),
    _3C3G(3),
    _3C6G(103),
    _4C4G(4),
    _4C8G(104),
    _6C6G(6),
    _6C12G(105),
    _8C8G(8),
    _8C16G(106),
    _10C10G(10),
    _10C20G(107),
    _12C12G(12),
    _16C16G(16),
    _20C20G(20),
    _25C25G(25),
    _30C30G(30);

    private final int cgroupSetType;

    CgroupConfigEnum(int setType) {
      this.cgroupSetType = setType;
    }
    public int getSetType() {
      return cgroupSetType;
    }

    public List<Integer> getCpuAndMemLimit() {
      List<Integer> numbers = new ArrayList<>();
      Pattern pattern = Pattern.compile("\\d+");
      Matcher matcher = pattern.matcher(toString());
      while (matcher.find()) {
        numbers.add(Integer.parseInt(matcher.group()));
      }
      assert numbers.size()==2;
      return numbers;
    }

    public static CgroupConfigEnum getFrom(Integer setType){
      CgroupConfigEnum[] values = CgroupConfigEnum.values();
      for(CgroupConfigEnum item: values){
        if(item.getSetType() == setType){
          return item;
        }
      }

      return _6C12G;
    }

    public static CgroupConfigEnum getFrom(String desc) {
      switch (desc) {
        case "1c1g":
        return _1C1G;
        case "1c2g":
            return _1C2G;
        case "2c2g":
            return _2C2G;
        case "2c4g":
            return _2C4G;
        case "3c3g":
            return _3C3G;
        case "3c6g":
            return _3C6G;
        case "4c4g":
            return _4C4G;
        case "4c8g":
          return _4C8G;
        case "6c6g":
            return _6C6G;
        case "6c12g":
            return _6C12G;
        case "8c8g":
            return _8C8G;
        case "8c16g":
            return _8C16G;
        case "10c10g":
          return _10C10G;
        case "10c20g":
          return _10C20G;
        case "12c12g":
            return _12C12G;
        case "16c16g":
            return _16C16G;
        case "20c20g":
            return _20C20G;
        case "25c25g":
            return _25C25G;
        case "30c30g":
            return _30C30G;
      }
      return _6C12G;
    }
  }

  public static JSONObject updateTaskNameAndActionsNew(String testFlag, String source,String originTaskIdAndIns,
      String indexes, String cluster, String doubleRunTaskId, CgroupConfigEnum cgroupConfig, String originVersion, String compareVersion) throws IOException {
    // Fixed params, no adjustment needed.
    Integer checkScriptId = 1021007; // 原脚本 984047 oss版本 1021007
    Integer runScriptId = 1021046; // 原脚本 984048 oss版本 1021046
    String scriptStartFile = "data_compare_oss3.sh"; // 原脚本 data_compare3.sh oss版本 data_compare_oss3.sh
    String runStartFile = "run_spark_oss3.sh"; // 原脚本 run_spark3.sh oss版本 run_spark_oss3.sh
    String clusterCode = "cairne";
    String martCode = "mart_sc";
    String queueCode = "root.benchmark.benchmark_dev";
    String accountCode = "mart_hgcs_benchmark_task";
    String tableLeft = "";
    String tableRight = "";
    String runJar = "";
    String actionOptType = "replace";
    String actionDependOptType = "replace";
    String saveType = "prod";
    String buffaloEnv = "buffalomanage.dp.jd.com";
    JSONObject buffaloTaskInfo =  buffalo4GetTaskInfo(Long.parseLong(doubleRunTaskId));
    if (!buffaloTaskInfo.getBooleanValue("success")) {
      throw new RuntimeException("Buffalo task not found, taskId = " + doubleRunTaskId);
    }
    Integer appGroupId = buffaloTaskInfo.getJSONObject("obj").getInteger("appGroupId");
    String managers = buffaloTaskInfo.getJSONObject("obj").getString("managers");
    if(appGroupId != 106020) {
      throw new RuntimeException("Non-personal tasks cannot be update, taskId = " + doubleRunTaskId);
    }
    String taskName = source + "_" + originTaskIdAndIns + testFlag;
    Map<String, Object> task = buildDefTaskData(taskName, "wf", "Dual-run task for Spark 3.4 upgrade.",
        appGroupId, managers,"manual", 0 ,3);
    JSONObject returnObj = new JSONObject();
    List<Integer> indexList = sortNumberFromStr(indexes);
    if (indexList.isEmpty()) {
      returnObj.put("msg", "Incorrect `indexes` parameter value. Please check.");
      return returnObj;
    }
    List<Map<String, Object>> actionList = new ArrayList<>();
    List<Map<String, Object>> actionDepend = new ArrayList<>();
    String parentCompareName = "";
    String taskNodeId = String.valueOf(CommonUtil.getTaskNodeId(doubleRunTaskId));
    for (Integer index : indexList) {
      String _24ActionName = "2_4__" + index;
      actionList.add(buildDefActionData(_24ActionName,
          "pyscript", martCode, 3, taskNodeId, runScriptId, runStartFile,
              originVersion + ";" + originTaskIdAndIns + "_" + index + ";" + cluster, clusterCode,
          queueCode, accountCode,cgroupConfig));
      String _34ActionName = "3_4__" + index;
      actionList.add(buildDefActionData(_34ActionName,
          "pyscript", martCode, 3, taskNodeId, runScriptId, runStartFile,
              compareVersion + ";" + originTaskIdAndIns + "_" + index + ";" + cluster, clusterCode,
          queueCode, accountCode,cgroupConfig));
      if(!parentCompareName.isEmpty()) {
        actionDepend.add(buildActionDepend(parentCompareName, _24ActionName));
        actionDepend.add(buildActionDepend(parentCompareName, _34ActionName));
      }
      String _compareActionName = "DataCompare__" + index;
      actionList.add(buildDefActionData(_compareActionName, "pyscript", martCode, 3,
          taskNodeId, checkScriptId, scriptStartFile,
          tableLeft + ";" + tableRight + ";" + originTaskIdAndIns + "_" + index + ";" + runJar + ";" + cluster,
          clusterCode, queueCode, accountCode,cgroupConfig));
      actionDepend.add(buildActionDepend(_24ActionName, _compareActionName));
      actionDepend.add(buildActionDepend(_34ActionName, _compareActionName));
      parentCompareName = _compareActionName;
    }
    return saveTaskConfig(doubleRunTaskId, task, actionOptType, actionList,
        actionDependOptType, actionDepend, saveType, buffaloEnv);
  }

  public static JSONObject saveTaskConfig(String taskId, Map<String,Object> task, String actionOptType, List<Map<String,Object>> actionList,
      String actionDependOptType,List<Map<String,Object>> actionDependList, String saveType, String buffaloEnv) throws IOException {
    CallerInfo callerInfo = Profiler.registerInfo("bdp.spark3.scheduler5.saveTaskConfig",
        "SparkMonitorApp", false, true);
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(
        commonBean.getBuffaloNewApiAppId() + commonBean.getBuffaloNewApiAppToken() + time);
    try {
      JSONObject data = new JSONObject();
      data.put("taskId", taskId);
      data.put("task", task);
      data.put("actionOptType", actionOptType);
      data.put("actionList", actionList);
      data.put("actionDependOptType", actionDependOptType);
      data.put("actionDependList", actionDependList);
      data.put("saveType", saveType);
      String url = "http://" + buffaloEnv + "/api/v3/buffalo5/task/saveTaskConfig?"
          + "appId="+commonBean.getBuffaloNewApiAppId()  + "&userToken="
          + commonBean.getShiftHillsBuffaloApiUserToken() + "&time=" + time
          + "&sign=" + sign;
      Map<String,String> paramMap = new HashMap<>();
      paramMap.put("data", data.toJSONString());
      String response = SimpleHttpClient.sendPost(httpclient,null, url, paramMap);
      JSONObject jsonObject = JSONObject.parseObject(response);
      logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
      return jsonObject;
    } catch (Exception e) {
      Profiler.functionError(callerInfo);
      throw e;
    } finally {
      Profiler.registerInfoEnd(callerInfo);
    }
  }


  public static JSONObject updateTaskNameAndActions(String testFlag, String source,String originTaskIdAndIns,
      String indexes, String cluster, String doubleRunTaskId, CgroupConfigEnum cgroupConfig) throws IOException {
    // Fixed params, no adjustment needed.
    Integer checkScriptId = 1021007; // 原脚本 984047 oss版本 1021007
    Integer runScriptId = 1021046; // 原脚本 984048 oss版本 1021046
    String scriptStartFile = "data_compare_oss3.sh"; // 原脚本 data_compare3.sh oss版本 data_compare_oss3.sh
    String runStartFile = "run_spark_oss3.sh"; // 原脚本 run_spark3.sh oss版本 run_spark_oss3.sh
    String clusterCode = "cairne";
    String martCode = "mart_sc";
    String queueCode = "root.benchmark.benchmark_dev";
    String accountCode = "mart_hgcs_benchmark_task";
    String tableLeft = "";
    String tableRight = "";
    String runJar = "";
    JSONObject buffaloTaskInfo =  buffalo4GetTaskInfo(Long.parseLong(doubleRunTaskId));
    if (!buffaloTaskInfo.getBooleanValue("success")) {
      throw new RuntimeException("Buffalo task not found, taskId = " + doubleRunTaskId);
    }
    Integer appGroupId = buffaloTaskInfo.getJSONObject("obj").getInteger("appGroupId");
    String managers = buffaloTaskInfo.getJSONObject("obj").getString("managers");
    if(appGroupId != 106020) {
      throw new RuntimeException("Non-personal tasks cannot be update, taskId = " + doubleRunTaskId);
    }

    String description = buffaloTaskInfo.getJSONObject("obj").getString("description");
    String taskName = source + "_" + originTaskIdAndIns + testFlag;
    JSONObject modifyInfo = buffalo5ModifyTask(Long.parseLong(doubleRunTaskId), appGroupId.longValue(),managers,
        taskName,description);
    if (!modifyInfo.getBooleanValue("success")) {
      throw new RuntimeException("update task name failed, taskId = " + doubleRunTaskId);
    }
    System.out.println("modify task finished");
    justSleep(1000);

    JSONObject actionListByTaskId = getActionListByTaskId(doubleRunTaskId);
    JSONArray actionList = null;
    if (actionListByTaskId.getBooleanValue("success")) {
      JSONObject jsonObject = actionListByTaskId.getJSONObject("obj");
      if (jsonObject == null) {
        throw new RuntimeException("Abnormal task, lacking any action, taskId = " + doubleRunTaskId);
      }
      actionList = jsonObject.getJSONArray("actionList");
    }
    List<Integer> actions = new ArrayList<>();
    for (int i = 0; i < actionList.size(); i++) {
      JSONObject jsonObject = actionList.getJSONObject(i);
      Integer actionId = jsonObject.getInteger("actionId");
      actions.add(actionId);
    }
    Collections.reverse(actions);
    for (Integer actionId : actions) {
      JSONObject deleteObj = buffalo5DeleteAction(Long.parseLong(doubleRunTaskId),
          actionId.longValue());
      if (!deleteObj.getBooleanValue("success")) {
        throw new RuntimeException(
            "Delete action id failed, taskId = " + doubleRunTaskId + ", actionId = " + actionId);
      }
      justSleep(1000);
    }
    System.out.println("delete all actions finished");


    JSONObject returnObj = new JSONObject();
    List<Integer> indexList = sortNumberFromStr(indexes);
    if (indexList.isEmpty()) {
      returnObj.put("msg", "Incorrect `indexes` parameter value. Please check.");
      return returnObj;
    }

    returnObj.put("dual-run ID", doubleRunTaskId);
    String actionId = null;
    for (Integer index : indexList) {
      JSONObject jsonObject2 = Buffalo4TaskManager.buffalo5TaskActionCreate(Integer.parseInt(doubleRunTaskId),
          "2_4__" + index,
          "pyscript", martCode, 3, "1302", runScriptId, runStartFile,
          actionId, "2_4;" + originTaskIdAndIns + "_" + index + ";" + cluster, clusterCode,
          queueCode, accountCode,cgroupConfig);
      System.out.println("actionCreate = " + jsonObject2);
      String actionIdLeft = jsonObject2.getJSONObject("obj").getString("actionId");
      returnObj.put("2.4 action - " + index, actionIdLeft);
      justSleep(1000);

      JSONObject jsonObject3 = Buffalo4TaskManager.buffalo5TaskActionCreate(Integer.parseInt(doubleRunTaskId),
          "3_4__" + index,
          "pyscript", martCode, 3, "1302", runScriptId, runStartFile,
          actionId, "3_4;" + originTaskIdAndIns + "_" + index + ";" + cluster, clusterCode,
          queueCode, accountCode,cgroupConfig);
      System.out.println("actionCreate = " + jsonObject3);
      String actionIdRight = jsonObject3.getJSONObject("obj").getString("actionId");
      returnObj.put("3.4 action - " + index, actionIdLeft);
      justSleep(1000);

      JSONObject jsonObjectCompare = Buffalo4TaskManager.buffalo5TaskActionCreate(Integer.parseInt(doubleRunTaskId),
          "DataCompare__" + index,
          "pyscript", martCode, 3, "1302", checkScriptId,
          scriptStartFile, actionIdLeft + "," + actionIdRight,
          tableLeft + ";" + tableRight + ";" + originTaskIdAndIns + "_" + index + ";" + runJar + ";" + cluster,
          clusterCode,
          queueCode, accountCode,cgroupConfig);
      System.out.println("actionCreate = " + jsonObject3);
      actionId = jsonObjectCompare.getJSONObject("obj").getString("actionId");
      returnObj.put("compare action - " + index, actionIdLeft);
      justSleep(1000);
    }
    System.out.println("create new actions finished");
    return returnObj;
  }

  public static JSONObject createDoubleRunBuffaloTask(String testFlag, String source, String taskid,
      String indexes, String cluster, String originVersion, String compareVersion) throws IOException {
    JSONObject returnObj = new JSONObject();
    // Fixed params, no adjustment needed.
    Integer checkScriptId = 1021007; // 原脚本 984047 oss版本 1021007
    Integer runScriptId = 1021046; // 原脚本 984048 oss版本 1021046
    String scriptStartFile = "data_compare_oss3.sh"; // 原脚本 data_compare3.sh oss版本 data_compare_oss3.sh
    String runStartFile = "run_spark_oss3.sh"; // 原脚本 run_spark3.sh oss版本 run_spark_oss3.sh

    Integer appGroupId = 106020;
    String clusterCode = "cairne";
    String martCode = "mart_sc";
    String queueCode = "root.benchmark.benchmark_dev";
    String accountCode = "mart_hgcs_benchmark_task";
    Integer failRetry = 0;
    String tableLeft = "";
    String tableRight = "";
    String runJar = "";

    List<Integer> indexList = sortNumberFromStr(indexes);
    if (indexList.isEmpty()) {
      returnObj.put("msg", "Incorrect `indexes` parameter value. Please check.");
      return returnObj;
    }

    JSONObject jsonObject = Buffalo4TaskManager.buffalo5TaskCreate(source + "_" + taskid + testFlag,
        "Dual-run task for Spark 3.4 upgrade.", appGroupId, "wf", "manual",
        "wuguoxiao", failRetry, 3);
    System.out.println("saveWFTask = " + jsonObject);
    Integer buffaloTaskId = jsonObject.getJSONObject("obj").getInteger("taskId");
    returnObj.put("dual-run ID", buffaloTaskId);
    justSleep(1000);

    String actionId = null;
    for (Integer index : indexList) {
      JSONObject jsonObject2 = Buffalo4TaskManager.buffalo5TaskActionCreate(buffaloTaskId,
          "2_4__" + index,
          "pyscript", martCode, 3, "1302", runScriptId, runStartFile,
          actionId, originVersion + ";" + taskid + "_" + index + ";" + cluster, clusterCode,
          queueCode, accountCode);
      System.out.println("actionCreate = " + jsonObject2);
      String actionIdLeft = jsonObject2.getJSONObject("obj").getString("actionId");
      returnObj.put("2.4 action - " + index, actionIdLeft);
      justSleep(1000);

      JSONObject jsonObject3 = Buffalo4TaskManager.buffalo5TaskActionCreate(buffaloTaskId,
          "3_4__" + index,
          "pyscript", martCode, 3, "1302", runScriptId, runStartFile,
          actionId, compareVersion + ";" + taskid + "_" + index + ";" + cluster, clusterCode,
          queueCode, accountCode);
      System.out.println("actionCreate = " + jsonObject3);
      String actionIdRight = jsonObject3.getJSONObject("obj").getString("actionId");
      returnObj.put("3.4 action - " + index, actionIdLeft);
      justSleep(1000);

      JSONObject jsonObjectCompare = Buffalo4TaskManager.buffalo5TaskActionCreate(buffaloTaskId,
          "DataCompare__" + index,
          "pyscript", martCode, 3, "1302", checkScriptId,
          scriptStartFile, actionIdLeft + "," + actionIdRight,
          tableLeft + ";" + tableRight + ";" + taskid + "_" + index + ";" + runJar + ";" + cluster,
          clusterCode,
          queueCode, accountCode);
      System.out.println("actionCreate = " + jsonObject3);
      actionId = jsonObjectCompare.getJSONObject("obj").getString("actionId");
      returnObj.put("compare action - " + index, actionIdLeft);
      justSleep(1000);
    }
    return returnObj;
  }

  /**
   * 休眠1s,防止频繁调用buffalo接口，触发限流，导致有的环节为空
   * @param millis
   */
  private static void justSleep(long millis){
    try {
      Thread.sleep(millis);
      logger.info(String.format("=== just sleep: %s ...", millis));
    } catch (Exception e) {
      logger.info(String.format("=== just sleep: %s ... exception: %s !!!", millis, CommonUtil.exceptionToString(e)));
    }
  }




  /**
   * buffalo4-任务管理-查询各环节的env信息 https://cf.jd.com/pages/viewpage.action?pageId=*********
   *
   * @param taskId
   * @return
   * @throws IOException
   */
  public static JSONObject getActionEnvByTaskId(Integer taskId) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(
        commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("taskId", taskId);
    String url = "http://buffalo4.bdp.jd.local/api/v3/buffalo5/task/action/getActionsByTaskId?"
        + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
        + commonBean.getBuffaloApiUserToken() + "&time=" + time
        + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    JSONObject jsonObject = JSONObject.parseObject(response);
    logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
    return jsonObject;
  }

  public static JSONObject getInstanceParentDependList(Long instanceId) throws IOException {
    String time = System.currentTimeMillis() + "";
    String sign = Md5Util.getMD5Str(
        commonBean.getBuffaloApiAppId() + commonBean.getBuffaloApiAppToken() + time);
    JSONObject data = new JSONObject();
    data.put("instanceId", instanceId);
    data.put("page", 1);
    data.put("limit", 100);
    String url =
        "http://buffalo4.bdp.jd.local/api/v3/buffalo5/instance/depend/getInstanceParentDependList?"
            + "appId=" + commonBean.getBuffaloApiAppId() + "&userToken="
            + commonBean.getBuffaloApiUserToken() + "&time=" + time
            + "&sign=" + sign + "&data=" + URLEncoder.encode(data.toJSONString(), "UTF-8");
    String response = SimpleHttpClient.sendRequest(httpclient, url);
    JSONObject jsonObject = JSONObject.parseObject(response);
    logger.info("Request url is " + url + " response is " + jsonObject.toJSONString());
    return jsonObject;
  }


}
