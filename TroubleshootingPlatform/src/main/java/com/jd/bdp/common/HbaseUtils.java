package com.jd.bdp.common;

import com.jd.bdp.scheduler.Scheduler;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.PrefixFilter;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Logger;

public class HbaseUtils {
    private static final Logger logger = Logger.getLogger(HbaseUtils.class.getName());

    public static String logTableName = "bdp_buffalo_master:dispatchLog";
    
    private static Configuration conf = null;
    private static volatile Connection conn = null;

    /**
     * 获取全局唯一的Configuration实例
     *
     * @return
     */
    public static synchronized Configuration getConfiguration() {
        if (conf == null) {
            // 此处从配置文件读取配置信息，配置文件在classpath下的hbase-site.xml。
            conf = HBaseConfiguration.create();
        }
        return conf;
    }



   

    /**
     * 获取全局唯一的Connection实例
     * Connection对象自带连接池，请使用单例模式获取连接。
     *
     * @return
     * @throws Exception
     */
    public static Connection createConnection() throws Exception {
        if (conn == null || conn.isClosed() || conn.isAborted()) {
            synchronized (HbaseUtils.class) {
                if (conn == null || conn.isClosed() || conn.isAborted()) {
                    /*
                     * * 创建一个Connection
                     */
                    //第一种方式：通过配置文件
                    Configuration configuration = getConfiguration();
                    //第二种方式：代码中指定
                    //Configuration configuration = new Configuration();
                    //configuration.set("bdp.hbase.instance.name", "SL1000000003014");//申请的实例名称
                    //configuration.set("bdp.hbase.accesskey", "MZYH5UIKEY3BU7CNB5FWLS2OTA");//实例对应的accesskey，请妥善保管你的AccessKey
                    conn = ConnectionFactory.createConnection(configuration);
                }
            }
        }
        return conn;
    }
    

    /**
     * 创建一个数据库连接
     * @return conn 数据库连接对象
     * @throws IOException 输入输出异常
     */
    /*public static Connection createConnection() {
        Connection conn = null;
        try {
            Configuration hbaseConf = HBaseConfiguration.create();
            conn = ConnectionFactory.createConnection(hbaseConf);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return conn;
    }*/

    /**
     * 根据日志ID扫描
     * @param conn 数据库连接
     * @param logid 日志ID
     * @return 扫描结果
     */
    public static String scanByLogId(Connection conn, Long logid) {
        String reverse = new StringBuffer(logid.toString()).reverse().toString();
        return scanByFilter(conn, logTableName,  reverse, logid);
    }

    public static void scanByLogId(Connection conn, Long logid, OutputStream outputStream) {
        String reverse = new StringBuffer(logid.toString()).reverse().toString();
        scanByFilter(conn, logTableName,  reverse, outputStream);
    }

    public static List<String> scanByLogId(Connection connection, Integer originTaskId, Long doubleRunLogId){
        String reverse = new StringBuffer(doubleRunLogId.toString()).reverse().toString();
        return scanByFilter(connection, logTableName, reverse, originTaskId, doubleRunLogId);
    }
    
    /**
     * 从指定的HBase表中根据给定的过滤条件扫描数据，并将扫描结果写入文件
     * @param conn HBase连接对象
     * @param tableName 表名
     * @param filter 过滤条件
     * @param logid 日志ID
     * @return filePath 写入数据的文件路径
     * @throws IOException 如果发生I/O异常
     */
    public static String scanByFilter(Connection conn, String tableName, String filter, Long logid){
        CallerInfo logAnalysisCaller = Profiler.registerInfo("bdp.bdpUtils.logAnalysis.hbase", "SparkMonitorApp", false, true);
        String filePath = Scheduler.commonBean.getDownloadBuffaloLogDir() + File.separator + "buffalo_logs" + File.separator + logid + ".txt";
        try {
            FileUtil.createDirectory(filePath);
            File file = new File(filePath);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            Table table = conn.getTable(TableName.valueOf(tableName));
            byte[] prefix = Bytes.toBytes(filter);
            Scan scan = new Scan(prefix);
            scan.setFilter(new PrefixFilter(prefix));
            ResultScanner rs = table.getScanner(scan);
            Iterator<Result> resultScan = rs.iterator();
            while (resultScan.hasNext()) {
                Cell[] result = resultScan.next().rawCells();
                String s = Bytes.toString(result[0].getValueArray(), result[0].getValueOffset(), result[0].getValueLength());
                fileOutputStream.write(result[0].getValueArray(), result[0].getValueOffset(), result[0].getValueLength());
            }
            fileOutputStream.close();
            rs.close();
            table.close();
        } catch (IOException e) {
            e.printStackTrace();
            Profiler.functionError(logAnalysisCaller);
        } finally {
            Profiler.registerInfoEnd(logAnalysisCaller);
        }
        return filePath;
    }

    public static void scanByFilter(Connection conn, String tableName, String filter, OutputStream outputStream){
        CallerInfo logAnalysisCaller = Profiler.registerInfo("bdp.bdpUtils.logAnalysis.hbase", "SparkMonitorApp", false, true);
        try {
            Table table = conn.getTable(TableName.valueOf(tableName));
            byte[] prefix = Bytes.toBytes(filter);
            Scan scan = new Scan(prefix);
            scan.setFilter(new PrefixFilter(prefix));
            ResultScanner rs = table.getScanner(scan);
            Iterator<Result> resultScan = rs.iterator();
            while (resultScan.hasNext()) {
                Cell[] result = resultScan.next().rawCells();
                String s = Bytes.toString(result[0].getValueArray(), result[0].getValueOffset(), result[0].getValueLength());
                outputStream.write(result[0].getValueArray(), result[0].getValueOffset(), result[0].getValueLength());
            }
            outputStream.flush();
            rs.close();
            table.close();
        } catch (IOException e) {
            e.printStackTrace();
            Profiler.functionError(logAnalysisCaller);
        } finally {
            Profiler.registerInfoEnd(logAnalysisCaller);
        }
    }


    /**
     * 根据原始任务id获取双跑任务的日志
     * @param connection
     * @param tableName
     * @param filter
     * @param originTaskId 原始任务id
     * @param doubleRunLogId 双跑运行日志id
     * @return
     */
    private static List<String> scanByFilter(Connection connection, String tableName, String filter, Integer originTaskId, Long doubleRunLogId) {
        CallerInfo logAnalysisCaller = Profiler.registerInfo("bdp.bdpUtils.logAnalysis.hbaseErrorCode", "SparkMonitorApp", false, true);
        List<String> buffer = new ArrayList<>();
        try {
            Table table = connection.getTable(TableName.valueOf(tableName));
            byte[] prefix = Bytes.toBytes(filter);
            Scan scan = new Scan(prefix);
            scan.setMaxVersions(1);
            scan.setFilter(new PrefixFilter(prefix));
            ResultScanner rs = table.getScanner(scan);
            Iterator<Result> resultScan = rs.iterator();
            while (resultScan.hasNext()) {
                Cell result = resultScan.next().rawCells()[0];
                buffer.add(Bytes.toString(result.getValueArray(), result.getValueOffset(), result.getValueLength()));
            }
            rs.close();
            table.close();
        } catch (Exception e){
            logger.info(String.format("=== 查询Hbase失败, taskId: %s, logId: %s, exception !!! %s", originTaskId, doubleRunLogId, CommonUtil.exceptionToString(e)));
            Profiler.functionError(logAnalysisCaller);
            throw new RuntimeException(e);
        } finally {
            Profiler.registerInfoEnd(logAnalysisCaller);
        }
        return buffer;
    }
}
