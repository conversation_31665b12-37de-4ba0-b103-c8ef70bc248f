package com.jd.bdp.common;

import com.jd.common.web.LoginContext;

import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Set;
import java.util.logging.Logger;

public class SSOFilter {
    private static final Logger logger = Logger.getLogger(SSOFilter.class.getName());

    /**
     * 对请求进行过滤，检查登录上下文中的用户是否在白名单中
     * @param whiteErps 白名单ERP集合
     * @param servletRequest Servlet请求
     * @param servletResponse Servlet响应
     * @param filterChain 过滤器链
     * @throws ServletException 如果servlet出现错误
     * @throws IOException 如果IO操作出现错误
     */
    public void doFilter(Set<String> whiteErps, ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) {
        try {
            HttpServletRequest servletRequest1 = (HttpServletRequest) servletRequest;
            String requestUrl = servletRequest1.getRequestURL().toString();
            LoginContext loginContext = LoginContext.getLoginContext();
            logger.info("RequestUrl = " + requestUrl + " currentUser = " + (loginContext != null ? loginContext.getPin() : ""));
            if (loginContext != null && whiteErps.contains(loginContext.getPin())) {
                filterChain.doFilter(servletRequest, servletResponse);
            } else {
                servletRequest.getRequestDispatcher("/error.jsp").forward(servletRequest, servletResponse);
            }
        } catch (Exception ex) {
            logger.warning("Failed to get SpringSSOInterceptor from spring context. Full stacktrace is "
                    + CommonUtil.exceptionToString(ex));
        }
    }
}
