package com.jd.bdp.common;

import com.jd.bdp.spark.web.KongmingService;
import com.jd.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class Buffalo4Dao {
    private static final Logger logger = Logger.getLogger(Buffalo4Dao.class.getName());

    /**
     * 获取SLA任务
     * @param taskId 任务ID
     * @return 返回包含Map对象的列表
     * @throws NullPointerException 如果参数为null
     */
    public static List<Map<String, Object>> getSLATask(Long taskId) {
        String sql = "select \n" +
                "case when task_region = 'SZSJ' then '商智商家' \n" +
                "when task_region = 'SZPP' then '商智品牌'\n" +
                "when task_region = 'SZLS' then '商智零售'\n" +
                "ELSE task_region END AS task_region \n" +
                "from t_gdm_xtyh_nature_analysis_task_list where buffalo_id = ?";
        try {
            StringBuilder builder = new StringBuilder();
            List<Object> params = new ArrayList<>();

            builder.append(sql);
            params.add(taskId);
            return KongmingService.executeSql(builder.toString(), "nature", params.toArray());
        } catch (Exception e) {
            logger.warning("Execute sql[" + sql + "] error. Full stacktrace is " + CommonUtil.exceptionToString(e));
        }
        return new ArrayList<>();
    }

    /**
     * 根据任务ID、日志ID和限制获取运行日志列表
     * @param taskId 任务ID
     * @param logId 日志ID
     * @param limit 限制数量
     * @return 运行日志列表
     */
    public static List<Map<String, Object>> getRunLogByTaskId(Long taskId, Long logId, Long limit) {
        StringBuilder builder = new StringBuilder();
        builder.append("select * from b_run_log where instance_type = 2 ");//等于2代表环节任务
        List<Object> params = new ArrayList<>();
        if (taskId != null ) {
            builder.append(" AND task_def_id = ?");
            params.add(taskId);
        }
        if (logId != null) {
            builder.append(" AND id = ?");
            params.add(logId);
        }
        builder.append(" order by id desc");
        builder.append(" limit ").append(limit == null ? 100 : limit);
        List<Map<String, Object>> buffalo = KongmingService.executeSql(builder.toString(), "buffalo", params.toArray());
        logger.info("taskId: " + taskId + " logId: " + logId + " response: " + JSON.toJSONString(buffalo));
        return buffalo;
    }


    /**
     * 根据任务ID、开始运行时间获取满足条件的运行记录
     * @param taskId 任务ID
     * @param runTime 实例开始执行时间
     * @return 运行日志列表
     */
    public static List<Map<String, Object>> getRunLogByTaskIdAndTime(Long taskId, String runTime, Long limit) {
        StringBuilder builder = new StringBuilder();
        builder.append("select * from b_run_log where instance_type = 2  and env is not null");//等于2代表环节任务
        List<Object> params = new ArrayList<>();
        if (taskId != null ) {
            builder.append(" AND task_def_id = ?");
            params.add(taskId);
        }
        if(StringUtils.isNotBlank(runTime)){
            builder.append(" AND run_time <= ?");
            params.add(runTime);
        }
        builder.append(" order by id desc");
        builder.append(" limit ").append(limit == null ? 100 : limit);
        List<Map<String, Object>> buffalo = KongmingService.executeSql(builder.toString(), "buffalo", params.toArray());
        logger.info("taskId: " + taskId + " runTime: " + runTime + " response: " + JSON.toJSONString(buffalo));
        return buffalo;
    }
}
