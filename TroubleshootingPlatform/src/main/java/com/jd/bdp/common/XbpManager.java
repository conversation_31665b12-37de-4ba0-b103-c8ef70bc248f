package com.jd.bdp.common;

import com.jd.bdp.bean.Constants;
import com.jd.bdp.scheduler.Scheduler;
import com.jd.jsf.gd.util.RpcContext;
import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.xbp.jsf.api.TicketService;
import com.jd.xbp.jsf.api.request.ticket.*;
import com.jd.xbp.jsf.api.response.XbpResponse;
import com.jd.xbp.jsf.api.response.ticket.Ticket;
import lombok.NonNull;

import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

public class XbpManager {
    private static final Logger logger = Logger.getLogger(XbpManager.class.getName());

    public static final TicketService ticketService = APPLICATION_CONTEXT.getBean("ticketService", TicketService.class);

    /**
     * 创建Xbp流程
     * @param createParam 创建参数
     * @return 响应的参数描述
     * @throws Exception 如果发生异常
     */
    public static XbpResponse<Integer> createXbpFlow(CreateParam createParam) {
        RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
        RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
        CallerInfo jsfTicketServiceCaller = Profiler.registerInfo("jsf.ticketService.create", "SparkMonitorApp", false, true);
        try {
            XbpResponse<Integer> response = ticketService.create(createParam);
            logger.info("Create Xbp Flow: Response: " + response + " Request: " + createParam);
            return response;
        } catch (Throwable e) {
            logger.warning("CreateParams: " + JSON.toJSONString(createParam)
                    + " Result: " + CommonUtil.exceptionToString(e));
            Profiler.functionError(jsfTicketServiceCaller);
        } finally {
            Profiler.registerInfoEnd(jsfTicketServiceCaller);
        }
        return null;
    }

    /**
     * 撤销票务
     * @param ticketId 票务ID
     * @param reason 撤销原因
     * @return XbpResponse<Boolean> 撤销结果
     * @throws 任何可能抛出的异常
     */
    public static XbpResponse<Boolean> revokeTicket(int ticketId, String reason) {
        RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
        RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
        try {
            RevokeParam revokeParam = new RevokeParam();
            revokeParam.setReason(reason);
            XbpResponse<Boolean> revoke = ticketService.revoke(ticketId, revokeParam);
            logger.info("Revoke: request: " + ticketId + " response: " + JSON.toJSONString(revoke));
            return revoke;
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 重启工单
     * @param ticketId 工单ID
     * @param erp ERP系统信息
     * @return XbpResponse<Boolean> 返回重启结果
     * @throws 任何可能抛出的异常
     */
    public static XbpResponse<Boolean> restartTicket(int ticketId, String erp) {
        RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
        RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
        try {
            RestartParam restartParam = new RestartParam();
            restartParam.setTicketId(ticketId);
            restartParam.setUsername(erp);
            restartParam.setFromStage(1);
            XbpResponse<Boolean> revoke = ticketService.restart(restartParam);
            logger.info("Revoke: request: " + ticketId + " response: " + JSON.toJSONString(revoke));
            return revoke;
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 重新创建XbpResponse
     * @param ticketId 门票ID
     * @param processId 进程ID
     * @param username 用户名
     * @return XbpResponse<Boolean> 重新创建的响应
     * @throws Throwable 可能抛出的异常
     */
    public static XbpResponse<Boolean> recreateXbpResponse(int ticketId, Integer processId, String username) {
        RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
        RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
        try {
            CreateParam createParam = new CreateParam();
            createParam.setProcessId(processId);
            createParam.setUsername(username);
            XbpResponse<Boolean> remind = ticketService.recreate(ticketId, createParam);
            logger.info("JSF: ticketId: " + ticketId + " response: " + JSON.toJSONString(remind));
            return remind;
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 提醒XbpResponse
     * @param ticketId 门票ID
     * @return remind 响应的参数描述
     * @throws 可能会抛出异常
     */
    public static XbpResponse<Boolean> remindXbpResponse(int ticketId) {
        RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
        RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
        try {
            XbpResponse<Boolean> remind = ticketService.remind(new RemindParam(ticketId));
            logger.info("JSF: ticketId: " + ticketId + " response: " + JSON.toJSONString(remind));
            return remind;
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 重置Xbp响应
     * @param ticketId 票号
     * @param username 用户名
     * @param reason 原因
     * @return Xbp响应
     * @throws 可能抛出的异常
     */
    public static XbpResponse<Boolean> resetXbpResponse(int ticketId, String username, String reason) {
        RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
        RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
        try {
            ResetFlowParam resetFlowParam = new ResetFlowParam();
            resetFlowParam.setTicketId(ticketId);
            resetFlowParam.setUsername(username);
            resetFlowParam.setReason(reason);
            XbpResponse<Boolean> remind = ticketService.resetFlow(resetFlowParam);
            logger.info("JSF: ticketId: " + ticketId + " response: " + JSON.toJSONString(remind));
            return remind;
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    public static XbpResponse<Ticket> getTicketXbpResponse(int ticketId) {
        RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
        RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
        try {
            XbpResponse<Ticket> ticketXbpResponse = ticketService.get(ticketId);
            logger.info("JSF: ticketId: " + ticketId + " response: " + JSON.toJSONString(ticketXbpResponse));
            return ticketXbpResponse;
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    public static XbpResponse<Boolean> approveTicket(int ticketId){
        RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
        RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
        try {
            OperateParam operateParam = new OperateParam();
            operateParam.setTicketId(ticketId);
            operateParam.setStage(2);
            operateParam.setStatus(1);
            operateParam.setUsername("wuguoxiao");
            operateParam.setOpinion("同意");
            return ticketService.operate(operateParam);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    public static XbpResponse<Integer> xbpComment(Integer ticketId, String comment) {
        XbpResponse<Integer> response = null;
        try {
            CommentParam commentParam = new CommentParam();
            commentParam.setTicketId(ticketId);
            commentParam.setUsername(Scheduler.commonBean.getXbpNatureUsername());
            commentParam.setComment(comment);
            RpcContext.getContext().setAttachment("Xbp-Api-User", Constants.XBP_API_USER);
            RpcContext.getContext().setAttachment("Xbp-Api-Sign", Constants.XBP_API_SIGN);
            response = ticketService.comment(commentParam);
            logger.info("commentParam: " + JSON.toJSONString(commentParam)
                    + " response: " + JSON.toJSONString(response));
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return response;
    }
}
