package com.jd.bdp.common;

import com.alibaba.fastjson.JSONObject;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.logging.Logger;


public class BuffaloTaskDelete {
    private static final Logger logger = Logger.getLogger(BuffaloTaskDelete.class.getName());
    public static String deleteBuffaloTask(Integer taskId) throws IOException {
        JSONObject buffaloTaskInfo = Buffalo4TaskManager.buffalo4GetTaskInfo(Long.parseLong(taskId + ""));
        if (buffaloTaskInfo == null || !buffaloTaskInfo.getBooleanValue("success")) {
            throw new RuntimeException("Buffalo task not found, taskId = " + taskId);
        }
        Integer appGroupId = buffaloTaskInfo.getJSONObject("obj").getInteger("appGroupId");
        String managers = buffaloTaskInfo.getJSONObject("obj").getString("managers");
        if(!"wuguoxiao".equalsIgnoreCase(managers) || appGroupId != 106020) {
            throw new RuntimeException("Non-personal tasks cannot be deleted, taskId = " + taskId);
        }
        CallerInfo callerInfo = Profiler.registerInfo("bdp.spark3.scheduler4.deleteBuffaloTask",
                "SparkMonitorApp", false, true);
        try {
            if (taskId != null) {
                String str = "http://dp.jd.com/buffalo/webapi/task/delete.ajax";
                try {
                    HttpURLConnection connection = (HttpURLConnection) new URL(str).openConnection();
                    logger.info(str);

                    connection.setRequestMethod("POST");

                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
                    connection.setRequestProperty("Cookie", "ssa.jbdp-urm=ec2d6a2c28a1af5feff913d2a2c02e3c080e1043b5e747b56a6e65ec4de8ae8896eee69f5dc498d71858260211806f1217f12a9d0d1e512f9ce26b26e48ba76260b381ab9931848fe4539eae7f11f7ded6e6095dd14fbe26f3e4dddac05223e59abd7bc60fd6e9ab23dab02cba2a4e19d394b34ba70f4d93c21593d9ee869e09; __jda=240971990.1708247612624868306672.1708247613.1708325055.1708395674.9; __jdc=240971990; SearchPageGray=true; doSqlToolCheck=false; xingtuMenuGrey=true; rememberMe=zan2B5n05peqaAIf8JmEVqK066ipyubNFX51nH8AwfHMJBm3/+VmwHS+B1pSPkuufn0W/EZ3tSilue0EcNLlt684QmxUP0pg9j9YUsTa3aathWWzjyGnYgAJjpA5asXMLXaDF2r5a7NjXoXQIh5JhrcZiVJDbVRZc4uw6F/CqHaWwXtU3FZGj3j/OltrZ2ZcQ1sc/LbwgBSQtQJLdvQJdaxTDti6Go47QTUCceNXQM6SY1zlUx1OKK2dDh+//pKaWFOMnBzHsrDrbokEWHRRduvTg/qHXQfaQY+TD2+3QXq/ZO1PRioF8IbdNurFpbBu+ahp5cb/CzoY3Bb1TEySUgCqPt/W+Y0CrU3k4y19ikB23Enbe27jSRGBgaTkh2UCgjtEE/Pzol1PmzedBf1wQ/qZHaP7xDShGwZTSQv+UJbqfi2q2VCWf5x430vAMMM6p8VeGmEkPp9lynacXzv1e0dpoQ+nVpvvaXGPhM4sgE3pAgOyygUtXWUcWkqIO/vngGezptD4KXNUKbI=; jd.erp.lang=zh_CN; jdd69fo72b8lfeoe=2HRQI7XXEYD3QU5TMRFQECSBZK7WGFDD3PRQ4NHXZY3N2TK3WZYUPAGD5QLCFBWBR3YAV24RLZBQD54YROYWAYAJ7U; __jdu=1708247612624868306672; logbook_u=wuguoxiao; mba_muid=1708247612624868306672; erp=wuguoxiao; focus-client=WEB; lng=zh; erp_erp=wuguoxiao; zhibo_erp=wuguoxiao; SSOID=b4a29aa05313a59f5ee962021b8524e945918c76f40e66f94a16c70d630729af,wuguoxiao; qid_uid=a80e792a-919f-455d-b619-9d123548fac4; qid_fs=1730287695259; qid_ls=1730287695264; qid_ts=1730300410054; qid_vis=2; focus-token=ee.ff28642f8484ac7baa62b2679c215dfb; focus-team-id=00046419; me_token=ee.ff28642f8484ac7baa62b2679c215dfb; jdd69fo72b8lfeoeTK=jdd032HRQI7XXEYD3QU5TMRFQECSBZK7WGFDD3PRQ4NHXZY3N2TK3WZYUPAGD5QLCFBWBR3YAV24RLZBQD54YROYWAYAJ7UAAAAMTC67AGXIAAAAACLPGLBTDGSOP44X; __jdv=240971990|direct|-|none|-|1731332962231; RT=\"z=1&dm=jd.com&si=b1tsx0pi4bd&ss=m3eek8el&sl=1&tt=1pr&ld=2hv&ul=pv9&hd=pve\"; sso.jd.com=BJ.929BC23C9219D6FEEC1AD3C95811F12F.3420241113103802; ssa.bdp=d1547d577d72e5a445a29dc8f72552e82cd47d5d867ce44f0d4dc93f0417b4b18c17fb7298838e91ab8722e0752586acb46f9d156c82fb0c0e1aea21738ac3faaa0963a2e067b931b3f6000e849376bb221fd71cfd96265b244c5c76eb446cb4da29c8f51d8418f448da067bd0c46bcab933c8cc072a49de4a4c571d9339d110; ssa.origin-gateway=66fd8fd459a60a025a1896a88dc4816396e7ff5a941190c30dc342eb01da4b863b770ed6f4fc34b0cf2ba5da8eb3eab774dbabc51f4938a83c8464a84db8d5463436dadc56a83fbdae256e7f9b012138f12b582a7222b9a4df29583023cee7a66a9ab45d6dae919871a602d8238ed6713f8c1dd155488b79aadb72a7ca52ca62; JSESSIONID=EECB3A7BC6706EDE0B0C7928CFA76786.s1; ssa.origin-api=2b16bf126a49e58d7aaf7d6b8a0030b3da865650edfd658a6e4fa069eba5afaffe17f28eaba917ad2b28ec58cd43ac8c296e223e37eb48b9ac7e55b3074c939f7c3de75355d2f656b4252e58f59aa5123cb01e3ce40e50effdeca5788c5582901e4abb83223707062dc662fe8193b34de9a797680561a7bded608e10cb6295fe; ssa.global.ticket=d1fa7b84343d9608cc42086c6b8fac6a625681939d87181ca8d93bb00bb6b204; token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjhiTS15cE1TWThqX2VJY2ZGX0w4IiwiZXhwIjoxNzMyMTEwNjUzLCJpYXQiOjE3MzE1MDU4NTN9.pm4ApnoFavT7iqTvU8lj73FKhn93TtQy1JZSh4OeCQo; ssa.bdpops.state=sp4jZ6rGhIgEF6vio-9G4xSTqOQsKb1wOOr7oIJ3nYg; ssa.bdpops=7df2aa546568a02ce3c3d9893bae5940c48010597a124c1004a113e7c23854882ea4c256701e8e65c171fbac248d0898d849c3e86068ba52316d438a6b489945ae8a9b5fbb6a240a5ba996b7afc2725addfa9e84b351fff2339c74c435779e8814c4237902376cc8be61ec046b9c378a27f8024a1a7c9f1169c4651baaf44dbd; SSAID=52b51c31f711d395d40059f53dc830ce9cb6a524241ae2625d6f4bb690498dc0143d0ed9dc83265c5c4b37a318451d71d0efd1ab75a72d6b0b839d95b752e9782c1e64c6df0488a2a75bf3238c554af54847a0eb2560df244de7af44311fb55be78459797fa2fff5dbbdd2490f0d6a77e789a09aaf380dffa1c3b60da8a75229a0ee227d18c36dcc9c4a1e5fd1143ae7c17e931caa0b9fbed1fa5bb0673e09b0df3ab526260ee9db1ebd2a916a9bba3c827e42a680c4f3a35d336fa9e20f5050; X-JACP-TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***************************************************************************************************************************************************************************************************************************.Fy0qEc8ZGlq5IzTD4_fOHH0XGpe6oT4x5haWWttlDtBQtOoXaJWevfIV69LPWYR1ceso-4qFlf63qJmpZmV8FRD6eMIEX73-Hq5Fib7Vjb1aIeYIuagUPOSf5BawDd5IZsp7z0eYKz--cCN6GBwZtqiAfPN8egTtnze_wjDTDPmQ5YpaNpy7YpP6Aev62yRiCEj91LCN_DBRfdKZbP2DyhiHDIIFrJRiRcweo6kN6dfWnHUiArUh9i6g3Cu8bOl1XGZgWiLPESFM8wId0Dx2o2fmHPfVqrjyxhdKuqw48Z4gmHf1X_5Hbr6RAJFDu_b2WPsEnjdthal2hvFtlLiWqg; __jda=101385626.1708247612624868306672.1708247613.1708325055.1708395674.9; __jdc=101385626; __jdb=101385626.28.1708247612624868306672|9.1708395674");
                    connection.setRequestProperty("taskEnvIdentity", "prod");
                    connection.setRequestProperty("Sgm-Context", "810120134814411500");
                    connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");
                    connection.setDoOutput(true);

                    // 构建参数并写入请求体
                    String params = "taskId=" + URLEncoder.encode(taskId.toString(), "UTF-8");

                    try (OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream(), "UTF-8")) {
                        writer.write(params);
                        writer.flush();
                    }

                    // 读取响应
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                        StringBuilder response = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            response.append(line);
                        }
                        String responseStr = response.toString().replaceAll("\n", " ");
                        logger.info("taskid = " + taskId + " response = " + responseStr);
                        return responseStr;
                    } finally {
                        connection.disconnect();
                    }
                } catch (Exception e) {
                    logger.warning("Exception occurred: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return "Error: Dual run id is null";
    }
}
