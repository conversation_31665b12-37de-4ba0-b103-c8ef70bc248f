package com.jd.bdp.common;

import com.jd.jim.cli.Cluster;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

public class JimDBUtils {
    public static final Cluster JIM_CLIENT = APPLICATION_CONTEXT.getBean("jimClient", Cluster.class);

    public static void incrPV() {
        JIM_CLIENT.incr("pv." + SDFThreadLocal.getyyyyMMdd().format(new Date()));
        JIM_CLIENT.incr("pv." + SDFThreadLocal.getyyyyMM().format(new Date()));
        JIM_CLIENT.incr("pv." + SDFThreadLocal.getyyyy().format(new Date()));
    }

    public static String getPV(String key){
        return JIM_CLIENT.get(key);
    }

    public static void incrUV(String pin){
        incrUVByKey(pin, "uv." + SDFThreadLocal.getyyyyMMdd().format(new Date()));
        incrUVByKey(pin, "uv." + SDFThreadLocal.getyyyyMM().format(new Date()));
        incrUVByKey(pin, "uv." + SDFThreadLocal.getyyyy().format(new Date()));
    }

    public static void incrUVByKey(String pin, String hkey) {
        if(JIM_CLIENT.hExists(hkey, pin)) {
            JIM_CLIENT.hIncrBy(hkey, pin, 1);
        } else {
            JIM_CLIENT.hSet(hkey, pin, "1");
        }
    }

    public static Long getUV(String hkey){
        return JIM_CLIENT.hLen(hkey);
    }

    public static Map<String, String> getUVDetails(){
        String hkey = "uv." + SDFThreadLocal.getyyyyMMdd().format(new Date());
        return JIM_CLIENT.hGetAll(hkey);
    }

    public static List<String> getHashVal(String taskId, String logId, String key) {
        return JIM_CLIENT.hMGet(taskId + "_" + logId, key);
    }

    public static Map<String, String> getHash(String taskId, String logId) {
        return JIM_CLIENT.hGetAll(taskId + "_" + logId);
    }

    public static String getFirstEle(List<String> running_spark_version){
        if(running_spark_version!= null && !running_spark_version.isEmpty()) {
            return StringUtils.trimToEmpty(running_spark_version.get(0));
        }
        return "";
    }
}
