package com.jd.bdp.spark.web;

import com.jd.bdp.bean.SparkUpgradeAssessmentBean;
import com.jd.bdp.bean.SparkUpgradeImportResultBean;
import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.mapper.buffalo.SparkUpgradeBuffaloMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeAssessmentMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeBigGraphMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
import com.jd.bdp.scheduler.ErrorCodeUtils;
import com.jd.bdp.utils.MapperManager;
import com.jd.common.web.LoginContext;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark升级-buffalo4任务摸底全景同步到固定task表
 */
@WebServlet("/sparkUpgradeBigGraphImport")
public class SparkUpgradeBigGraphImportController extends HttpServlet {
    
    private SparkUpgradeBuffaloMapper buffaloMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeBuffaloMapper.class);
    
    private SparkUpgradeBigGraphMapper assessmentMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeBigGraphMapper.class);

    private SparkUpgradeTaskMapper taskMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeTaskMapper.class);
    
    private ErrorCodeUtils errorCodeUtils = APPLICATION_CONTEXT.getBean(ErrorCodeUtils.class);
    
    
    private static final Logger logger = Logger.getLogger(SparkUpgradeBigGraphImportController.class.getName());

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        Integer offset = req.getParameter("offset") == null ? null : Integer.parseInt(req.getParameter("offset"));   // 导入task的数量开始位置
        Integer length = req.getParameter("length") == null ? null : Integer.parseInt(req.getParameter("length"));   // 导入task的数量
        String importTaskId = req.getParameter("taskId");
        String condition = req.getParameter("condition");
        String startTime = req.getParameter("startTime");
        String endTime = req.getParameter("endTime");
        Boolean skipEngineType = Boolean.parseBoolean( StringUtils.isBlank(req.getParameter("skipEngineType")) ? "true" : req.getParameter("skipEngineType") );
        Boolean skipEngineVersion = Boolean.parseBoolean( StringUtils.isBlank(req.getParameter("skipEngineVersion")) ? "false" : req.getParameter("skipEngineVersion") );
        Boolean skipErrorCode = Boolean.parseBoolean( StringUtils.isBlank(req.getParameter("skipErrorCode")) ? "false" : req.getParameter("skipErrorCode") );

        logger.info(String.format("=== 本次导入的摸底任务范围为: offset: %s, length: %s", offset, length));
        StringBuilder buffer = new StringBuilder("");
        List<Integer> taskIds = new ArrayList<>();
        if(StringUtils.isNotBlank(importTaskId)){
            taskIds = Arrays.stream(importTaskId.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            logger.info(String.format("=== 本次导入taskId集合, 数量: %s, taskIds: %s", taskIds.size(), importTaskId));
        }
        
        if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
            logger.info(String.format("=== 本次导入指定运行时间范围的双跑任务: 【%s~%s】", startTime, endTime));
            taskIds = buffaloMapper.selectOriginTaskInInterval(startTime, endTime);
            logger.info(String.format("=== 本次导入指定运行时间范围的双跑任务: 【%s~%s】, 任务数量: %s", startTime, endTime, taskIds.size()));
        }
        
        SparkUpgradeImportResultBean result = batchSyncBuffaloTask(offset, length, taskIds, condition, skipEngineType, skipEngineVersion, skipErrorCode);
        logger.info(String.format("=== 本次导入的摸底任务总数量结果: %s",  MapperManager.writeValueAsString(result)));
        
        for(Map.Entry<String, String> entry: result.getFailedMap().entrySet()){
            buffer.append("<br>").append(String.format("%s:%s", entry.getKey(), entry.getValue())).append("</br>");
        }
        
        String message =  String.format("导入结果汇总：总任务数:%s, 成功任务数: %s，失败任务数: %s, 失败详情: %s, 导入成功任务id: %s",
                result.getTotalImportTaskNum(), result.getImportSuccessTaskNum(), result.getImportFailedTaskNum(), buffer.toString(), StringUtils.join(result.getImportSucTaskList(), ","));
        logger.info(String.format("=== 本次导入结果： %s", message));
        req.setAttribute("msg", message);
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }
    
    public SparkUpgradeImportResultBean batchSyncBuffaloTask(Integer offset, Integer length, List<Integer> importTaskId, String condition, Boolean skipEngineType, Boolean skipEngineVersion, Boolean skipErrorCode){

        SparkUpgradeImportResultBean importResult = new SparkUpgradeImportResultBean();
        
        Map<String, String> failedMap = new HashMap<>();
        List<SparkUpgradeAssessmentBean> list = assessmentMapper.selectListByPage(offset, length, importTaskId, condition);
        logger.info(String.format("=== 本次导入的摸底任务真实数量: %s", list.size()));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = sdf.format(new Date());
        String pin = "xiaowei12";
        if( LoginContext.getLoginContext() != null && StringUtils.isNotBlank(LoginContext.getLoginContext().getPin())){
            pin = LoginContext.getLoginContext().getPin();
        }
        
        importResult.setTotalImportTaskNum(list.size());
        int sucImportNum = 0;
        
        for(int i = 0 ; i < list.size() ; i++){
            SparkUpgradeAssessmentBean assessmentBean = list.get(i);
            String taskId = assessmentBean.getTaskId();  // 原始任务id
            logger.info(String.format("=== 当前摸底任务导入进度: taskId: %s, 进度: %s/%s ...", taskId, i, list.size()));
            try {
                if(StringUtils.isBlank(taskId)){
                    logger.info("=== taskId为空，跳过");
                    continue;
                }

                // 是否跳过判断任务类型，判断任务类型，跳过hive任务(有的任务包含多个环节，既有spark也有hive),
                if(!skipEngineType){
                    if( !((assessmentBean.getIsSpark() != null && assessmentBean.getIsSpark() == 1 ) &&
                            (assessmentBean.getIsHive() != null && assessmentBean.getIsHive() == 0)) ){
                        logger.info(String.format("=== taskId: %s, 任务非全spark任务，不导入", taskId));
                        failedMap.put(taskId, "摸底任务导入失败: 任务非全spark任务，不导入");
                        assessmentMapper.updateImportStatusByTaskId(Integer.parseInt(taskId), "Y");
                        continue;
                    }
                }else{
                    logger.info(String.format("=== taskId: %s, 跳过hive和spark引擎类型判断", taskId));
                }

                SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(taskId);
                String doubleRunTaskIds = buffaloMapper.selectDoubleTaskIdByOriginTaskId(Integer.parseInt(taskId)); // 根据原始任务id获取最新一批双跑任务id
                logger.info(String.format("=== 摸底任务: taskId: %s, 最新双跑任务id:%s", taskId, doubleRunTaskIds));
                // 1、更新最新的双跑任务id
                if(task != null && StringUtils.isNotBlank(doubleRunTaskIds) && !doubleRunTaskIds.equalsIgnoreCase(task.getDoubleRunTaskId())){
                    taskMapper.updateDoubleRunTaskIdByOriginTaskId(doubleRunTaskIds, Integer.parseInt(taskId));  
                }
                
                if(task != null && SparkUpgradeTaskStatusEnum.UPGRADED.getCode().equalsIgnoreCase(task.getStatus())){
                    // 任务已升级，无需重复导入
                    logger.info(String.format("=== taskId: %s, 任务已升级，无需重复导入", taskId));
                    failedMap.put(taskId, "摸底任务导入失败: 任务已升级，无需重复导入");
                    assessmentMapper.updateImportStatusByTaskId(Integer.parseInt(taskId), "Y");
                    continue;
                }

                if(task != null && SparkUpgradeTaskStatusEnum.ROLLBACKED.getCode().equalsIgnoreCase(task.getStatus())){
                    // 任务已存回滚，无需重复导入
                    logger.info(String.format("=== taskId: %s, 任务已回滚，无需重复导入", taskId));
                    failedMap.put(taskId, "摸底任务导入失败: 任务已回滚，无需重复导入");
                    assessmentMapper.updateImportStatusByTaskId(Integer.parseInt(taskId), "Y");
                    continue;
                }

               
                String status = SparkUpgradeTaskStatusEnum.UN_EXE.getCode();
                if(assessmentBean.getExplainIsFailed() != null ){
                    if(assessmentBean.getExplainIsFailed().intValue() == 0){
                        status = SparkUpgradeTaskStatusEnum.EXPLAIN_SUCCESS.getCode();
                    }else{
                        status = SparkUpgradeTaskStatusEnum.EXPLAIN_FAILED.getCode();
                    }
                }

                // 判断双跑任务是否已创建，是否已经双跑成功
                if(StringUtils.isNotBlank(doubleRunTaskIds)){
                    status = SparkUpgradeTaskStatusEnum.DOUBLE_RUN_TASK_CREATED.getCode();
                    // 根据双跑任务id获取双跑任务最新的状态
                    List<String> doubleRunStatus = buffaloMapper.selectLatestInstRunStatusByDoubleTaskIdFromYesterday(Arrays.asList(doubleRunTaskIds.split(",")));  // 最近两天的状态： 从昨天到此刻的最新状态
                    logger.info(String.format("=== 摸底任务: task_id: %s, 双跑状态: %s", taskId, StringUtils.join(doubleRunStatus, ",")));
                    if(doubleRunStatus != null && doubleRunStatus.size() > 0){
                        // buffalo实例状态：(wait=等待, queue=队列中, taken=已领取, run=执行中, success=成功, fail=失败）
                        long failCount = doubleRunStatus.stream().filter(item -> "fail".equalsIgnoreCase(item)).count();
                        long sucCount = doubleRunStatus.stream().filter(item -> "success".equalsIgnoreCase(item)).count();
                        
                        if(sucCount == doubleRunStatus.size()){
                            status = SparkUpgradeTaskStatusEnum.DOUBLE_RUN_SUCCESS.getCode();  // 所有的环节-双跑任务都成功，双跑才算成功
                        }else if(failCount > 0){
                            status = SparkUpgradeTaskStatusEnum.DOUBLE_RUN_FAILED.getCode();  // 只要有一个环节-双跑任务失败，双跑就失败
                        }else {
                            status = SparkUpgradeTaskStatusEnum.DOUBLE_RUNNING.getCode();
                        }
                    }
                }

                // 2、判断任务状态是否重复
                if(task != null && task.getStatus() != null && task.getStatus().equalsIgnoreCase(status)){
                    logger.info(String.format("=== taskId: %s, 状态为:%s, 未发生变动，直接跳过，无需重复导入摸底任务!", taskId, status));
                    failedMap.put(taskId, "摸底任务导入失败：状态未发生变动，直接跳过");
                    assessmentMapper.updateImportStatusByTaskId(Integer.parseInt(taskId), "Y");
                    continue;
                }

                // 3、如果任务之前导入过，则不需要重复获取任务的原始引擎版本
                String originVersion = "default";
                if(!skipEngineVersion){
                    if(task == null){
                        originVersion = SparkUpgradeTaskImportController.getTaskEngineOriginVersion(taskId);
                        logger.info(String.format("=== taskId: %s, originVersion: %s", taskId, originVersion));
                        if(StringUtils.isBlank(originVersion)){
                            // 无法获取到任务的原始引擎版本
                            logger.info(String.format("=== taskId: %s, 无法获取任务的原始引擎版本", taskId));
                            failedMap.put(taskId, "摸底任务导入失败：无法获取任务的原始引擎版本");
                            assessmentMapper.updateImportStatusByTaskId(Integer.parseInt(taskId), "Y");
                            continue;
                        }
                    }else{
                        originVersion = task.getOriginVersion();
                    }
                }else{
                    logger.info(String.format("=== taskId: %s, 跳过获取原始引擎版本环节", taskId));
                }

                // 4、保存到数据库
                SparkUpgradeTaskBean bean = new SparkUpgradeTaskBean();
                bean.setTaskId(Integer.parseInt(taskId));
                bean.setOriginVersion(originVersion);
                bean.setDoubleRunTaskId(doubleRunTaskIds);
                bean.setStatus(status);
                bean.setCreateTime(now);
                bean.setCreator(pin);

                int affectedSize = taskMapper.insertSparkUpgradeTaskV2(bean);
                if(affectedSize > 0){
                    logger.info(String.format("=== taskId: %s, 摸底任务导入成功", taskId));
                    assessmentMapper.updateImportStatusByTaskId(Integer.parseInt(taskId), "Y");
                    importResult.getImportSucTaskList().add(taskId);
                    sucImportNum ++;
                    
                    // 导入成功后开始获取错误码
                    if(!skipErrorCode){
                        if(SparkUpgradeTaskStatusEnum.EXPLAIN_FAILED.getCode().equalsIgnoreCase(status)){
                            errorCodeUtils.getErrorCodeForExplain(taskId);
                        }else if(SparkUpgradeTaskStatusEnum.DOUBLE_RUN_FAILED.getCode().equalsIgnoreCase(status)){
                            errorCodeUtils.getErrorCodeForDoubleRun(taskId);
                        }
                    }else{
                        logger.info(String.format("=== taskId: %s, 跳过获取错误码环节", taskId));
                    }
                }else{
                    logger.info(String.format("=== taskId: %s, 摸底任务导入失败, 影响条数为：0", taskId));
                    failedMap.put(taskId, "摸底任务导入失败: 写入mysql失败");
                    assessmentMapper.updateImportStatusByTaskId(Integer.parseInt(taskId), "Y");
                }
            } catch (Exception e) {
                logger.info(String.format("=== 摸底任务导入异常: %s, exception !!!: %s", assessmentBean.getTaskId(), CommonUtil.exceptionToString(e)));
            }

        }

        importResult.setImportSuccessTaskNum(sucImportNum);
        importResult.setFailedMap(failedMap);
        importResult.setImportFailedTaskNum(failedMap.size());
        return importResult;
    }
   
}
