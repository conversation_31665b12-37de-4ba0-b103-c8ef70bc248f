package com.jd.bdp.spark.web;

import com.jd.bdp.common.SDFThreadLocal;

import javax.websocket.Session;
import java.io.IOException;
import java.util.Date;

public class WebSocket {
    public synchronized static void send(Session session, String message) {
        if(session == null) {
            return;
        }
        try {
            session.getBasicRemote().sendText(SDFThreadLocal.get().format(new Date()) + " " + message);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
