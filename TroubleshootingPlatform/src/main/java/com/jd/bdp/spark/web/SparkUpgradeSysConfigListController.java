package com.jd.bdp.spark.web;

import com.jd.bdp.bean.PageInfoList;
import com.jd.bdp.bean.domain.SparkUpgradeSysConfigBean;
import com.jd.bdp.mapper.spark.SparkUpgradeSysConfigMapper;
//import com.jd.bdp.utils.MybatisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark升级-buffalo4任务列表展示
 */
@WebServlet("/sparkUpgradeConfigList")
public class SparkUpgradeSysConfigListController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(SparkUpgradeSysConfigListController.class.getName());
    
    private SparkUpgradeSysConfigMapper configMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeSysConfigMapper.class);
    
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        PageInfoList<SparkUpgradeSysConfigBean> result = batchSsyGetConfigListTask();
        req.setAttribute("result", result);
        req.getRequestDispatcher("/spark-upgrade-sys-config.jsp").forward(req, resp);
    }


    /**
     * 批量查询升级任务信息
     * @return
     */
    private PageInfoList<SparkUpgradeSysConfigBean> batchSsyGetConfigListTask(){
//        SparkUpgradeSysConfigMapper mapper = MybatisUtils.getMapper(SparkUpgradeSysConfigMapper.class);
        List<SparkUpgradeSysConfigBean> list = configMapper.selectAllConfigs();
        PageInfoList<SparkUpgradeSysConfigBean> result = new PageInfoList<>();
        result.setList(list);
        logger.info(String.format("=== batchSsyGetConfigListTask result: %s", list.size()));
        return result;
    }
}
