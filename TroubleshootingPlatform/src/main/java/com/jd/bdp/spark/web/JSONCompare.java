package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class JSONCompare {

    public static JSONObject jsonCompare(JSONArray left, JSONArray right) {
        JSONObject diffObj = new JSONObject();
        JSONObject leftDiff = new JSONObject();
        JSONObject rightDiff = new JSONObject();
        JSONArray diff = new JSONArray();
        diffObj.put("left", leftDiff);
        diffObj.put("right", rightDiff);
        diffObj.put("diff", diff);
        Map<String, String> leftMap = convertToMap(left);
        Map<String, String> rightMap = convertToMap(right);
        for (Map.Entry<String, String> entry : leftMap.entrySet()) {
            if (rightMap.containsKey(entry.getKey())) {
                if (!entry.getValue().equals(rightMap.get(entry.getKey()))) {
                    JSONObject jsonObject = new JSONObject();
                    JSONArray objects = new JSONArray();
                    objects.add(entry.getValue());
                    objects.add(rightMap.get(entry.getKey()));
                    jsonObject.put(entry.getKey(), objects);
                    diff.add(jsonObject);
                }
                rightMap.remove(entry.getKey());
            } else {
                leftDiff.put(entry.getKey(),entry.getValue());
            }
        }
        for(Map.Entry<String,String> entry : rightMap.entrySet()){
            rightDiff.put(entry.getKey(),entry.getValue());
        }
        return diffObj;
    }

    private static Map<String,String> convertToMap(JSONArray array) {
        Map<String,String> map = new HashMap<>();
        for (int i=0;i<array.size(); i++) {
            JSONArray jsonArray = array.getJSONArray(i);
            map.put(jsonArray.getString(0),jsonArray.getString(1));
        }
        return map;
    }
}

