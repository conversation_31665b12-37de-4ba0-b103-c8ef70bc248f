package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

@WebServlet("/sparkTaskAnalysis")
public class SparkTaskAnalysisController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkTaskAnalysisController.class.getName());
    private static final CloseableHttpClient httpClient;
    private static final RequestConfig requestConfig;
    
    static {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100);
        connectionManager.setDefaultMaxPerRoute(20);
        
        requestConfig = RequestConfig.custom()
                .setConnectTimeout(10000)
                .setSocketTimeout(30000)
                .build();
        
        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setConnectionManagerShared(true)
                .evictExpiredConnections()
                .evictIdleConnections(60, TimeUnit.SECONDS)
                .build();
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.getRequestDispatcher("/sparkTaskAnalysis.jsp").forward(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        resp.setContentType("application/json;charset=UTF-8");
        
        String applicationUrl = req.getParameter("historyUrl");
        Map<String, Object> result = new HashMap<>();
        
        if (StringUtils.isBlank(applicationUrl)) {
            result.put("success", false);
            result.put("message", "Application URL 不能为空");
            resp.getWriter().write(JSON.toJSONString(result));
            return;
        }

        // 验证 URL 格式
        if (!applicationUrl.startsWith("http://") && !applicationUrl.startsWith("https://")) {
            result.put("success", false);
            result.put("message", "请输入有效的 HTTP/HTTPS URL");
            resp.getWriter().write(JSON.toJSONString(result));
            return;
        }

        try {
            logger.info("开始分析 Spark Application: " + applicationUrl);
            
            // 分析 Spark Application 的 task 信息
            TaskAnalysisResult analysisResult = analyzeSparkTasks(applicationUrl);
            
            result.put("success", true);
            result.put("data", analysisResult);
            result.put("message", "分析完成");
            
            logger.info("Spark Application 分析完成: " + analysisResult.getAppId() + 
                       ", 总任务数: " + analysisResult.getTotalTasks() +
                       ", 推测任务数: " + analysisResult.getSpeculativeTasks() +
                       ", 重试任务数: " + analysisResult.getRetryTasks());
            
        } catch (Exception e) {
            logger.severe("分析 Spark Application 失败: " + e.getMessage());
            result.put("success", false);
            result.put("message", "分析失败: " + e.getMessage());
        }
        
        resp.getWriter().write(JSON.toJSONString(result));
    }

    private TaskAnalysisResult analyzeSparkTasks(String applicationUrl) throws Exception {
        TaskAnalysisResult result = new TaskAnalysisResult();

        if (applicationUrl.endsWith("/")) {
            applicationUrl = applicationUrl.substring(0, applicationUrl.length() - 1);
        }
        
        result.setHistoryUrl(applicationUrl);

        String appId = extractAppIdFromUrl(applicationUrl);
        if (StringUtils.isBlank(appId)) {
            throw new Exception("无法从 URL 中提取 Application ID");
        }

        logger.info("获取 Application 信息: " + applicationUrl);
        String appInfoResponse = SimpleHttpClient.sendRequest(httpClient, requestConfig, applicationUrl, "SparkTaskAnalysis.getAppInfo");
        JSONObject app = JSON.parseObject(appInfoResponse);
        
        if (app == null) {
            throw new Exception("无法获取 Application 信息，请检查 URL 是否正确");
        }
        
        String appName = app.getString("name");
        
        result.setAppId(appId);
        result.setAppName(appName != null ? appName : "Unknown");
        
        // 构建 stages URL
        String stagesUrl = applicationUrl + "/stages";
        logger.info("获取 Stages 信息: " + stagesUrl);
        
        String stagesResponse = SimpleHttpClient.sendRequest(httpClient, requestConfig, stagesUrl, "SparkTaskAnalysis.getStages");
        JSONArray stages = JSON.parseArray(stagesResponse);
        
        if (stages == null) {
            throw new Exception("无法获取 Stages 信息");
        }
        
        int totalTasks = 0;
        int speculativeTasks = 0;
        int retryTasks = 0;
        int processedStages = 0;

        List<Map<String, Object>> speculativeTaskDetails = new ArrayList<>();
        List<Map<String, Object>> retryTaskDetails = new ArrayList<>();
        
        for (int i = 0; i < stages.size(); i++) {
            JSONObject stage = stages.getJSONObject(i);

            String stageStatus = stage.getString("status");
            if ("SKIPPED".equals(stageStatus)) {
                logger.info("跳过 SKIPPED 状态的 Stage: " + stage.getIntValue("stageId"));
                continue;
            }
            
            int numTasks = stage.getIntValue("numTasks");
            totalTasks += numTasks;
            
            int stageId = stage.getIntValue("stageId");
            int attemptId = stage.getIntValue("attemptId");
            
            List<JSONObject> allTasks = new ArrayList<>();
            int offset = 0;
            int pageSize = 1000;
            
            while (true) {
                String tasksUrl = applicationUrl + "/stages/" + stageId + "/" + attemptId + "/taskList?offset=" + offset + "&length=" + pageSize;
                
                try {
                    logger.info("获取 Stage " + stageId + " 的 Task 信息 (offset=" + offset + "): " + tasksUrl);
                    String tasksResponse = SimpleHttpClient.sendRequest(httpClient, requestConfig, tasksUrl, "SparkTaskAnalysis.getTasks");
                    JSONArray tasks = JSON.parseArray(tasksResponse);
                    
                    if (tasks == null || tasks.isEmpty()) {
                        break;
                    }

                    for (int j = 0; j < tasks.size(); j++) {
                        allTasks.add(tasks.getJSONObject(j));
                    }

                    if (tasks.size() < pageSize) {
                        break;
                    }

                    offset += pageSize;
                    
                } catch (Exception e) {
                    logger.warning("获取 stage " + stageId + " 的 task 信息失败 (offset=" + offset + "): " + e.getMessage());
                    break;
                }
            }

            for (JSONObject task : allTasks) {
                if (task.getBooleanValue("speculative")) {
                    speculativeTasks++;
                    
                    Map<String, Object> taskDetail = new HashMap<>();
                    taskDetail.put("taskId", task.getLongValue("taskId"));
                    taskDetail.put("stageId", stageId);
                    taskDetail.put("attemptId", attemptId);
                    taskDetail.put("index", task.getIntValue("index"));
                    taskDetail.put("status", task.getString("status"));
                    taskDetail.put("duration", task.getLongValue("duration"));
                    speculativeTaskDetails.add(taskDetail);
                }
                
                if (task.getIntValue("attempt") > 0) {
                    retryTasks++;
                    
                    Map<String, Object> taskDetail = new HashMap<>();
                    taskDetail.put("taskId", task.getLongValue("taskId"));
                    taskDetail.put("stageId", stageId);
                    taskDetail.put("attemptId", attemptId);
                    taskDetail.put("attempt", task.getIntValue("attempt"));
                    taskDetail.put("index", task.getIntValue("index"));
                    taskDetail.put("status", task.getString("status"));
                    taskDetail.put("duration", task.getLongValue("duration"));
                    retryTaskDetails.add(taskDetail);
                }
            }
            
            processedStages++;
        }
        
        result.setTotalTasks(totalTasks);
        result.setSpeculativeTasks(speculativeTasks);
        result.setRetryTasks(retryTasks);
        result.setProcessedStages(processedStages);
        result.setTotalStages(stages.size());
        result.setSpeculativeTaskDetails(speculativeTaskDetails);
        result.setRetryTaskDetails(retryTaskDetails);
        
        return result;
    }

    /**
     * 从 Application URL 中提取 appId
     * 支持格式：
     * - http://host:port/api/v1/applications/application_xxx_xxx
     * - http://host:port/history/application_xxx_xxx
     */
    private String extractAppIdFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        
        int appIndex = url.indexOf("application_");
        if (appIndex == -1) {
            return null;
        }
        
        String appIdPart = url.substring(appIndex);
        int slashIndex = appIdPart.indexOf("/");
        if (slashIndex != -1) {
            appIdPart = appIdPart.substring(0, slashIndex);
        }
        
        return appIdPart;
    }

    public static class TaskAnalysisResult {
        private String appId;
        private String appName;
        private String historyUrl;
        private int totalTasks;
        private int speculativeTasks;
        private int retryTasks;
        private int processedStages;
        private int totalStages;
        private List<Map<String, Object>> speculativeTaskDetails;
        private List<Map<String, Object>> retryTaskDetails;

        public String getAppId() { return appId; }
        public void setAppId(String appId) { this.appId = appId; }

        public String getAppName() { return appName; }
        public void setAppName(String appName) { this.appName = appName; }

        public String getHistoryUrl() { return historyUrl; }
        public void setHistoryUrl(String historyUrl) { this.historyUrl = historyUrl; }

        public int getTotalTasks() { return totalTasks; }
        public void setTotalTasks(int totalTasks) { this.totalTasks = totalTasks; }
        
        public int getSpeculativeTasks() { return speculativeTasks; }
        public void setSpeculativeTasks(int speculativeTasks) { this.speculativeTasks = speculativeTasks; }
        
        public int getRetryTasks() { return retryTasks; }
        public void setRetryTasks(int retryTasks) { this.retryTasks = retryTasks; }
        
        public int getProcessedStages() { return processedStages; }
        public void setProcessedStages(int processedStages) { this.processedStages = processedStages; }
        
        public int getTotalStages() { return totalStages; }
        public void setTotalStages(int totalStages) { this.totalStages = totalStages; }
        
        public List<Map<String, Object>> getSpeculativeTaskDetails() { return speculativeTaskDetails; }
        public void setSpeculativeTaskDetails(List<Map<String, Object>> speculativeTaskDetails) { this.speculativeTaskDetails = speculativeTaskDetails; }
        
        public List<Map<String, Object>> getRetryTaskDetails() { return retryTaskDetails; }
        public void setRetryTaskDetails(List<Map<String, Object>> retryTaskDetails) { this.retryTaskDetails = retryTaskDetails; }
    }
} 