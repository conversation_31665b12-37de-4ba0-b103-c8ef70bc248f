package com.jd.bdp.spark.web;


import com.jd.bdp.scheduler.ErrorCodeUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark升级-buffalo4任务执行计划校验
 */
@WebServlet("/sparkUpgradeTaskErrorCode")
public class SparkUpgradeTaskErrorCodeController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeTaskErrorCodeController.class.getName());

    private ErrorCodeUtils errorCodeUtils = APPLICATION_CONTEXT.getBean(ErrorCodeUtils.class);
    
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String taskId = req.getParameter("taskId");   // 导入task的数量开始位置
        boolean explainErrorCode = !StringUtils.isBlank(req.getParameter("explainErrorCode")) && Boolean.parseBoolean(req.getParameter("explainErrorCode"));   // 默认false
        boolean doubleRunErrorCode = StringUtils.isBlank(req.getParameter("doubleRunErrorCode")) || Boolean.parseBoolean(req.getParameter("doubleRunErrorCode"));  // 默认true

        int total = 0;
        if(StringUtils.isNotBlank(taskId)){
            String[] split = taskId.split(",|\\n");
            total = split.length;
            for (int i = 0; i < split.length; i++) {
                String originTaskId = split[i];
                if(StringUtils.isNotEmpty(originTaskId)) {
                    originTaskId = originTaskId.trim();
                    if(explainErrorCode){
                        logger.info(String.format("=== 开始本次进行explain错误码匹配， originTaskId: %s ... %d/%d", originTaskId, i, total));
                        errorCodeUtils.getErrorCodeForExplain(originTaskId);
                        logger.info(String.format("=== 本次进行explain错误码匹配成功： originTaskId: %s ... %d/%d",  originTaskId, i, total));
                    }
                    if(doubleRunErrorCode){
                        logger.info(String.format("=== 开始本次进行doubleRun错误码匹配， originTaskId: %s ... %d/%d", originTaskId, i, total));
//                        errorCodeUtils.getErrorCodeForDoubleRun(originTaskId);
                        errorCodeUtils.getErrorCodeForHistoryDoubleRunTask(originTaskId);
                        logger.info(String.format("=== 本次进行doubleRun错误码匹配成功： originTaskId: %s ... %d/%d",  originTaskId, i, total));
                    }
                }
            }
        }

        req.setAttribute("msg", String.format("本次进行错误码匹配成功,task总数: %s", total));
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }
}
