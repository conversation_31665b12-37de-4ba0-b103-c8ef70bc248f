package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLDecoder;

/**
 * Created by wuguoxiao on 2017/10/30.
 */
@ServerEndpoint(value = "/websocket")
public class WebsocketController {

    @OnOpen
    public void start(Session session) {
        System.out.println("Starting");
    }

    @OnMessage
    public void incoming(String message, Session session) {
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            String aClass = jsonObject.getString("class");
            String method = jsonObject.getString("method");
            String jsonParam = jsonObject.getString("json");
            try {
                String json = URLDecoder.decode(jsonParam,"utf-8");
                Class<?> aClass1 = Class.forName(aClass);
                Method declaredMethod = aClass1.getDeclaredMethod(method, String.class, Session.class);
                Object o = aClass1.newInstance();
                declaredMethod.invoke(o, json, session);
            } catch (InstantiationException | IllegalAccessException | ClassNotFoundException | NoSuchMethodException | InvocationTargetException e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @OnClose
    public void onClose() {
        System.err.println("webSocket closed.");
    }

    @OnError
    public void onError(Throwable t) {
        t.printStackTrace();
    }
}
