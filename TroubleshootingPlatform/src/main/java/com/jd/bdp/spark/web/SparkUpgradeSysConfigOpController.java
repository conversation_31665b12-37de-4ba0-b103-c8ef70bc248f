package com.jd.bdp.spark.web;



import com.jd.bdp.mapper.spark.SparkUpgradeSysConfigMapper;
//import com.jd.bdp.utils.MybatisUtils;
import com.jd.common.web.LoginContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark升级-buffalo4任务周报结果
 */
@WebServlet("/sparkUpgradeSysConfig")
public class SparkUpgradeSysConfigOpController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeSysConfigOpController.class.getName());

    private SparkUpgradeSysConfigMapper configMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeSysConfigMapper.class);
    
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");
        
        String configKey = req.getParameter("configKey");
        String configValue = req.getParameter("configValue");
        String description = req.getParameter("description");
        logger.info(String.format("=== 开始设置t_sys_config属性, configKey: %s, configValue:%s", configKey, configValue));
        
//        SparkUpgradeSysConfigMapper configMapper = MybatisUtils.getMapper(SparkUpgradeSysConfigMapper.class);
        String pin = LoginContext.getLoginContext().getPin();
        Map<String, String> result = new HashMap<>();
        if(StringUtils.isBlank(configKey) || StringUtils.isBlank(configValue)){
            logger.info("=== 设置t_sys_config失败, 配置名、配置值存在空值");
            req.setAttribute("msg", "设置t_sys_config失败, 配置名、配置值存在空值");
            req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
            return;
        }
        
        if(configKey.contains(",")){
            String[] keys = configKey.split(",");
            for(String key: keys){
                addKeyValue(configMapper, key.trim(), configValue.trim(), description.trim(), pin, result);
            }
        }else{
            addKeyValue(configMapper, configKey.trim(), configValue.trim(), description.trim(), pin, result);
        }
        
        StringBuilder builder = new StringBuilder("");
        for(String key: result.keySet()){
            builder.append(String.format("<br>【%s】属性【%s】<br/>", key, result.get(key)));
        }
        
        req.setAttribute("msg", builder.toString());
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }
    
    public void addKeyValue(SparkUpgradeSysConfigMapper mapper, String key, String value, String description, String erp, Map<String, String> result){
        String oldValue = mapper.getConfigValue(key);
        if(StringUtils.isBlank(oldValue)){
            int addSize = mapper.addConfig(key, value, description, erp);
            if(addSize > 0){
                logger.info(String.format("=== 新增t_sys_config属性成功, key: %s, value:%s", key, value));
                result.put(key, String.format("新增属性成功: 【%s】", value));
            }else{
                logger.info(String.format("=== 新增t_sys_config属性失败, key: %s, value:%s", key, value));
                result.put(key, String.format("新增属性失败: 【%s】", value));
            }
        }else{
            int affectedSize = mapper.setConfigValue(key, value);
            if(affectedSize > 0){
                logger.info(String.format("=== 设置t_sys_config属性成功, key: %s, value:%s", key, value));
                result.put(key, String.format("设置成【%s】成功", value));
            }else{
                logger.info(String.format("=== 设置t_sys_config属性失败!!!, key: %s, value:%s", key, value));
                result.put(key, String.format("设置成【%s】失败", value));
            }
        }
        
    }
   
}
