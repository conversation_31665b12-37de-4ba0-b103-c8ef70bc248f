package com.jd.bdp.spark.web;

import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.bean.enums.SparkUpgradeOpTypeEnum;
import com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
import com.jd.bdp.utils.MapperManager;
//import com.jd.bdp.utils.MybatisUtils;
import com.jd.bdp.utils.SparkUpgradeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark升级-任务操作： 一键升级、一键回滚
 */
@WebServlet("/sparkUpgradeTaskOperate")
public class SparkUpgradeTaskOperateController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeTaskOperateController.class.getName());

    private SparkUpgradeTaskMapper taskMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeTaskMapper.class);
    
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String taskId = req.getParameter("taskId");
        String opType = req.getParameter("opType");
        String result = operateBuffaloTask(taskId, opType);
        req.setAttribute("msg", result);
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }


    /**
     * 操作任务信息：升级、回滚任务版本
     * @return
     */
    private String operateBuffaloTask(String taskId, String opType) {
        logger.info(String.format("=== operateBuffaloTask params: %s, %s", taskId, opType));

        if(StringUtils.isBlank(taskId)){
            logger.info(String.format("=== taskId: %s, 不能为空", taskId));
            return "taskId不能为空";
        }

        SparkUpgradeOpTypeEnum action = null;
        try {
            action = SparkUpgradeOpTypeEnum.valueOf(opType.toUpperCase());
        } catch (Exception e) {
            logger.info(String.format("=== taskId: %s, 不支持操作: %s", taskId, opType));
            return String.format("taskId: %s, 不支持操作: %s", taskId, opType);
        }

        try {
            Integer.parseInt(taskId);
        } catch (Exception e) {
            logger.info(String.format("=== taskId: %s, 存在非法字符", taskId));
            return String.format("taskId: %s, 存在非法字符", taskId);
        }
        
        try {
//            SparkUpgradeTaskMapper taskMapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);
            
            if(SparkUpgradeOpTypeEnum.UPGRADE.equals(action)){
                
                // 查询任务信息
                SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(taskId);
                if(task == null){
                    logger.info(String.format("=== taskId: %s, 查询任务信息失败, 不能升级", taskId));
                    return String.format("taskId: %s, 查询任务信息失败, 不能升级", taskId);
                }
                logger.info(String.format("=== operateBuffaloTask, task: %s", MapperManager.writeValueAsString(task)));
                if(!SparkUpgradeTaskStatusEnum.ROLLBACKED.getCode().equalsIgnoreCase(task.getStatus())){
                    logger.info(String.format("=== taskId: %s, 当前任务状态为：%s, 不能升级", taskId, task.getStatus()));
                    return String.format("taskId: %s, 当前任务状态为：%s, 不能升级", taskId, task.getStatus());
                }
                
                // 升级任务
                boolean isSuc = SparkUpgradeUtils.upgradeSparkUpgradeTask(taskId);
                if(!isSuc){
                    logger.info(String.format("=== taskId: %s, 升级时调用buffalo接口修改引擎版本失败", taskId));
                    return String.format("taskId: %s, 升级时调用buffalo接口修改引擎版本失败", taskId);
                }

                // 更新状态
                task.setStatus(SparkUpgradeTaskStatusEnum.UPGRADED.getCode());
                taskMapper.updateTask(task);
                logger.info(String.format("=== taskId: %s: 升级成功", taskId));
                return String.format("taskId: %s: 升级成功", taskId);
            }else if(SparkUpgradeOpTypeEnum.ROLLBACK.equals(action)){
                // 查询任务信息
                SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(taskId);
                if(task == null){
                    logger.info(String.format("=== taskId: %s: 查询任务信息失败, 不能回滚", taskId));
                    return String.format("taskId: %s: 查询任务信息失败, 不能回滚", taskId);
                }

                logger.info(String.format("=== operateBuffaloTask, task: %s", MapperManager.writeValueAsString(task)));
                if(!SparkUpgradeTaskStatusEnum.UPGRADED.getCode().equalsIgnoreCase(task.getStatus())){
                    logger.info(String.format("=== taskId: %s: 当前任务状态为：%s, 不能回滚",taskId, task.getStatus()));
                    return String.format("taskId: %s: 当前任务状态为：%s, 不能回滚",taskId, task.getStatus());
                }

                // 回滚任务
                boolean isSuc = SparkUpgradeUtils.rollbackSparkUpgradeTask(taskId, task.getOriginVersion());
                if(!isSuc){
                    logger.info(String.format("=== taskId: %s, 回滚时调用buffalo接口修改引擎版本失败或当前任务spark版本非3.4", taskId));
                    return String.format("taskId: %s, 回滚时调用buffalo接口修改引擎版本失败或当前任务spark版本非3.4", taskId);
                }

                // 更新状态
                task.setStatus(SparkUpgradeTaskStatusEnum.ROLLBACKED.getCode());
                taskMapper.updateTask(task);
                logger.info(String.format("=== taskId: %s: 回滚成功", taskId));
                return String.format("%s: 回滚成功", taskId);
            }else{
                logger.info(String.format("=== %s: 不支持操作: %s", taskId, opType));
                return String.format("%s: 不支持操作: %s", taskId, opType); 
            }
        } catch (Exception e) {
            logger.info(String.format("=== taskId: %s, operateBuffaloTask exception, %s", taskId, CommonUtil.exceptionToString(e)));
            return String.format("taskId: %s: %s发生异常,原因: %s", taskId, action.getName(), CommonUtil.exceptionToString(e));
        }
    }
}
