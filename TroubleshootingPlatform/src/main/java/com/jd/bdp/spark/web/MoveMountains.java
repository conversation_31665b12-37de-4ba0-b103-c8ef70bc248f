package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.CommonUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.logging.Logger;

@WebServlet("/moveMountains")
public class MoveMountains extends HttpServlet {
    private static final Logger logger = Logger.getLogger(MoveMountains.class.getName());

    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setContentType("text/html;charset=utf-8");
        PrintWriter writer = resp.getWriter();
        JSONObject responseObj = new JSONObject();
        responseObj.put("isSuccess", "false");
        try {
            String op = req.getParameter("op");
            String taskId = req.getParameter("taskId");
            if ("add".equals(op)) {
                responseObj.put("sparkMsg", XBPController.addKongmingPackage(taskId, null, "BUFFALO4",
                        "SimpleHttpClient.KongMing.addHudiPackage", "HIVE_TASK", "SPARK_SUPPORT_HUDI_PG"));
                responseObj.put("hiveMsg", XBPController.addKongmingPackage(taskId, null, "BUFFALO4",
                        "SimpleHttpClient.KongMing.addHudiPackage", "HIVE_TASK", "HIVE_SUPPORT_HUDI_PG"));
                responseObj.put("isSuccess", "true");
            } else if ("delete".equals(op)) {
                responseObj.put("sparkMsg", XBPController.deleteKongmingPackage(taskId, null, "BUFFALO4",
                        "SimpleHttpClient.KongMing.deleteHudiPackage", "HIVE_TASK", "SPARK_SUPPORT_HUDI_PG"));
                responseObj.put("hiveMsg", XBPController.deleteKongmingPackage(taskId, null, "BUFFALO4",
                        "SimpleHttpClient.KongMing.deleteHudiPackage", "HIVE_TASK", "HIVE_SUPPORT_HUDI_PG"));
                responseObj.put("isSuccess", "true");
            }
        } catch (Throwable e) {
            logger.warning(CommonUtil.exceptionToString(e));
        }
        writer.write(responseObj.toJSONString());
    }
}
