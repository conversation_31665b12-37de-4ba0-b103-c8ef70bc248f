package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.FileUtil;
import com.jd.bdp.common.DorisDao;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

@WebServlet("/dataVirtual")
public class DataVirtualController extends HttpServlet {
    /**
     * 覆盖父类方法，处理HTTP POST请求
     * @param req HTTP请求对象
     * @param resp HTTP响应对象
     */
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

    /**
     * 处理HTTP GET请求，根据请求参数执行不同的操作
     * @param req HTTP请求对象
     * @param resp HTTP响应对象
     * @throws IOException 输入输出异常
     * @throws ServletException Servlet异常
     */
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        String type = req.getParameter("type");
        resp.setContentType("text/html;charset=utf-8");
        PrintWriter writer = resp.getWriter();

        if(StringUtils.isEmpty(type)){

        } else if ("run".equalsIgnoreCase(type)) {
            String id = req.getParameter("id");
            String doubleRunType = req.getParameter("double_run_type");
            String doubleRunComment = req.getParameter("double_run_comment");
            String sql = "update downstream_task set double_run_type = ?, double_run_comment = ? where id = ? ";
            int nature = KongmingService.executeUpdate(sql, "datavirtual",
                    doubleRunType, doubleRunComment, id);
            writer.write(nature);
        } else if("similar_sync_to_local".equalsIgnoreCase(type)) {
            int a = 0;
            for (Map<String, Object> stringObjectMap : KongmingService.executeSql("select * from similar_model where similar_result != '' or is_similar_type != ''", "nature")) {
                Long id = Long.parseLong(String.valueOf(stringObjectMap.get("id")));
                String isSimilarType = String.valueOf(stringObjectMap.getOrDefault("is_similar_type", ""));
                String similarResult = String.valueOf(stringObjectMap.getOrDefault("similar_result", ""));

                boolean b = FileUtil.writeFile("/root/wuguoxiao/wuguoxiao/doubleRun/similar_sync_to_local.sql",
                        "update similar_model set is_similar_type = '" + isSimilarType + "', similar_result = '" + similarResult + "' where id = " + id + ";\n",
                        true
                );
                if (b) a++;
            }
            writer.write("SUCCESS. CNT: " + a);
        } else if("query_by_sql".equalsIgnoreCase(type)) {
            String sql = req.getParameter("sql");
            List<Map<String, Object>> nature = KongmingService.executeSql(sql, "nature");
            JSONArray array = new JSONArray();
            array.addAll(nature);
            writer.write(array.toJSONString());
        } else if ("query_by_presto".equalsIgnoreCase(type)) {
            String sql = req.getParameter("sql");
            System.out.println("sql = " + sql);
            List<Map<String, Object>> presto = DorisDao.executeSql(sql, "doris", new ArrayList<>());
            JSONArray array = new JSONArray();
            array.addAll(presto);
            writer.write(array.toJSONString());
        } else if ("saveFile".equalsIgnoreCase(type)) {
            String fileContent = req.getParameter("fileContent");
            String filePath = req.getParameter("filePath");
            boolean returnCode = FileUtil.writeFile(filePath, fileContent, false);
            writer.write("save file return code is " + returnCode);
        } else if ("runCmd".equalsIgnoreCase(type)) {
            String filePath = req.getParameter("filePath");
            Integer returnCode = ShellUtil.runCmd("sh " + filePath, null);
            writer.write("execute command return code is " + returnCode);
        } else if ("runCmdWithRes".equalsIgnoreCase(type)) {
            JSONObject response = new JSONObject();
            StringBuffer errorOut = new StringBuffer();
            String filePath = req.getParameter("filePath");
            if(StringUtils.isEmpty(filePath)){
                errorOut.append("filePath cannot be empty");
            }
            String param = req.getParameter("param");
            if(StringUtils.isEmpty(param)){
                if(errorOut.length() > 0){
                    errorOut.append(";");
                }
                errorOut.append("param cannot be empty.");
            }
            if(errorOut.length() == 0){
                String result = ShellUtil.runCmdWithRes("sh " + filePath + " " + param);
                writer.write(result);
            } else if(errorOut.length() > 0){
                response.put("status", 1);
                response.put("errorOut", errorOut.toString());
                writer.write(response.toJSONString());
            }
        }
    }
}
