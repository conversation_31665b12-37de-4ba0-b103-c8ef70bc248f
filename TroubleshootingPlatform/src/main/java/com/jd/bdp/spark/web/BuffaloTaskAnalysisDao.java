package com.jd.bdp.spark.web;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BuffaloTaskAnalysisDao {
    private Integer id;
    private Integer taskId;
    private Long logId;
    private String appId;
    private String site;
    private String outputTableName;
    private Integer returnCode;
    private String illegalArgument;
    private String errorInQueryInvalidUsage;
    private Integer status;
    private String isAmbiguous;
    private String fileNotFound;
    private String sparkVersion;
    private String retryWithHive;
}
