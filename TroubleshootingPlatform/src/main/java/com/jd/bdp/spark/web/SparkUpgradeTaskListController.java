package com.jd.bdp.spark.web;

import com.jd.bdp.bean.PageInfoList;
import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
//import com.jd.bdp.utils.MybatisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark升级-buffalo4任务列表展示
 */
@WebServlet("/sparkUpgradeTaskList")
public class SparkUpgradeTaskListController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeTaskListController.class.getName());

    private SparkUpgradeTaskMapper taskMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeTaskMapper.class);
    
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String currentPageStr = req.getParameter("currentPage");
        String pageSizeStr = req.getParameter("pageSize");
        String taskIds = req.getParameter("taskIds");  // 根据taskId查询
        String status = req.getParameter("status");  // 根据状态查询
        
        logger.info(String.format("=== 查询任务列表: currentPageStr: %s, pageSizeStr: %s, taskIds: %s, status: %s", currentPageStr, pageSizeStr, taskIds, status));
        
        Integer currentPage = 1;
        Integer pageSize = 20;
        if(StringUtils.isNotBlank(currentPageStr)){
            currentPage = Integer.parseInt(currentPageStr);
        }
        
        if(StringUtils.isNotBlank(pageSizeStr)){
            pageSize = Integer.parseInt(pageSizeStr);
        }

        List<Integer> taskIdList = new ArrayList<>();
        if(StringUtils.isNotBlank(taskIds)){
            for(String taskId: taskIds.split(",")){
                try {
                    taskIdList.add(Integer.parseInt(taskId));
                } catch (Exception e) {
                    String msg = String.format("存在非法字符: %s, 必须为数字", taskId);
                    logger.info(String.format("=== 存在非法字符: %s, 必须为数字", taskId));
                    req.setAttribute("msg", msg);
                    req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
                    return;
                }
            }
        }

        PageInfoList<SparkUpgradeTaskBean> result = batchGetBuffaloTask(taskIdList, status, currentPage, pageSize);
        String site = req.getParameter("site");
        
        req.setAttribute("result", result);
        req.setAttribute("taskIds", taskIds);
        req.setAttribute("status", status);
        req.setAttribute("site", "dp.jd.com");
        req.getRequestDispatcher("/spark-upgrade-task-list.jsp").forward(req, resp);
    }


    /**
     * 批量查询升级任务信息
     * @return
     */
    private PageInfoList<SparkUpgradeTaskBean> batchGetBuffaloTask(List<Integer> taskIds, String status, Integer currentPage, Integer pageSize){
//        SparkUpgradeTaskMapper taskMapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);
        Integer offset = (currentPage - 1) * pageSize;
        Long totalSize = taskMapper.selectTotalCount(taskIds, status);
        Long totalPage = totalSize / pageSize + 1;
        List<SparkUpgradeTaskBean> list = taskMapper.selectListByPage(taskIds, status, offset, pageSize);
        PageInfoList<SparkUpgradeTaskBean> result = new PageInfoList<>();
        result.setCurrentPage(currentPage);
        result.setPageSize(pageSize);
        result.setList(list);
        result.setTotalPage(totalPage);
        result.setTotalSize(totalSize);
        logger.info(String.format("=== batchGetBuffaloTask result: %s, currentPage: %s, pageSize; %s", list.size(), currentPage, pageSize));
        return result;
    }
}
