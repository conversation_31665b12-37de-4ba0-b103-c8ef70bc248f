package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.*;
import com.jd.bdp.common.*;
import com.jd.bdp.scheduler.Scheduler;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.utils.Curl;
import com.jd.utils.beans.HttpResponse;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.websocket.Session;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.*;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.jd.bdp.bean.Constants.*;

public class BDPUtils {
    private static final Logger logger = Logger.getLogger(BDPUtils.class.getName());

    public static HttpResponse bdpLogin(String site){
        BasicCookieStore cookieStore = new BasicCookieStore();
        System.setProperty("http.maxConnections", "1");
        Curl.httpClient = HttpClients.custom().useSystemProperties().setDefaultCookieStore(cookieStore).build();
        return BDPUtils.init(site);
    }

    public static HttpResponse init(String site) {
        Map<String,String> authenticate = YamlUtil.loadYaml("authenticate.yaml", HashMap.class);
        Map<String, String> postMap = new HashMap<>();
        postMap.put("erp", authenticate.get("u"));
        postMap.put("password", authenticate.get("p"));
        HttpResponse httpResponse = Curl.request("curl 'http://"+site+"/login-check.html' -H 'Connection: keep-alive' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'Origin: http://dp.jd.com' -H 'Upgrade-Insecure-Requests: 1' -H 'Content-Type: application/x-www-form-urlencoded' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36' -H 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8' -H 'Referer: http://dp.jd.com/login.html' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' -H 'Cookie: shshshfpa=a53fe48a-96e8-dc25-5562-2db3c8478e8f-1540425340; shshshfpb=1b239b465be7348b79115bbeb0a2dd13a0801196a3c187b695bd106989; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; _bdp_erp=wuguoxiao; __jdu=15399307306181995756953; __jdv=122270672|baidu|-|organic|not set|1543769688229; TrackID=1o4AhjRAzoHThadXyvIJCLi8L9Fwq3d7YCyH3y6r29cK_RP3p6TXEf9jaCJdqRjNtyfcEXljWbz6gHPBaf-94sB0ng8Bne2s-Zg5cBAbmguEAjEqw6bLNATaoUg0SC7il4gPQ6T6Tj010kL0oAMdljA; ceshi3.com=201; user-key=e1db80f1-ac96-4f79-ab19-1c2de50794a0; cn=0; PCSYCityID=1; JSESSIONID=E3395CFA76BFE052121B4807A03278DC.s1; mt_xid=V2_52007VwMWUFVQUFsZSxhbBW4DGlpZXlZdF00bbFVmBBJWCVtVRh9PHV4ZYgYQUEFRB1oaVUtZB2QCFgAJXFcPHXkaXQVuHxJRQVlaSx9MEl8CbAYbYl9oUmocSRhaA2ECElVtW1pa; _gcl_au=1.1.1096132535.1544337431; shshshfp=c8ce61d951e9cc081b52aa081dffe083; yl_pcpc=gXNzGvpv1ejAvlbYObkQrnDLH_5J1XEq-0exZfDxOKdVHEhzZeUi50S0Zk9MqWU0RL1qnyMjuMa4hy1sWNyfyQ%3D%3D; jd.erp.lang=zh_CN; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; __jdc=*********; __jda=*********.15399307306181995756953.1539930731.1544421137.1544425282.214; erp1.jd.com=\"\"; __jdb=*********.2.15399307306181995756953|214.1544425282; sso.jd.com=\"\"; _c_k_u=\"\"; _c_v_p=\"\"' --data 'returnUrl=&ReturnUrl=' --compressed", null, postMap, false);
        boolean containsSetCookie = httpResponse.getHeaders().containsKey("Set-Cookie");
        Profiler.registerInfoEnd(Profiler.registerInfo("bdp.troubleshooting.login." + containsSetCookie, "SparkMonitorApp", false, true));
        if (!containsSetCookie) {
            new RuntimeException("Login Failed!").printStackTrace();
        }
        return httpResponse;
    }

    private static CloseableHttpClient httpclient = HttpClients.createDefault();

    /**
     *
     * @param applicationId
     * @return [0] history html url [1] history rest url
     */
    public static Map<String,String> getHistory(String applicationId) {
        Map<String,String> historyUrl = new HashMap<>();
        if(StringUtils.isEmpty(applicationId)) {
            return historyUrl;
        }
        Map<String,String> historyUrlMap = YamlUtil.loadYaml("sparkHistory.yaml", HashMap.class);
        for (String url : historyUrlMap.values()) {
            Map<String, String> historyUrl1 = getHistoryWithRetry(url, applicationId);
            if (!historyUrl1.isEmpty()) return historyUrl1;
        }
        return historyUrl;
    }

    public static Map<String, String> getHistoryWithRetry(String url, String applicationId) {
        Map<String, String> historyUrl = new HashMap<>();
        String applicationInfoUrl = url + "/api/v1/applications/" + applicationId;
        String response = null;
        for (int i = 0; i < 3; i++) {
            try {
                response = SimpleHttpClient.sendRequest(httpclient, applicationInfoUrl);
                break;
            } catch (IOException e) {
                logger.warning(e.getMessage());
            }
        }
        if (response != null && !"".equals(response)) {
            JSONObject attempts = JSON.parseObject(response).getJSONArray("attempts").getJSONObject(0);
            String attemptId = attempts.containsKey("attemptId") ? "/" + attempts.getString("attemptId") : "";
            historyUrl.put("history", url + "/history/" + applicationId + attemptId);
            historyUrl.put("rest", url + "/api/v1/applications/" + applicationId + attemptId);
        }
        return historyUrl;
    }

    public static List<String> convertToList(String content) {
        List<String> hdfsPath = new ArrayList<>();
        Matcher matcher = Pattern.compile("hdfs://.*").matcher(content);
        while(matcher.find()){
            hdfsPath.add(matcher.group(0));
        }
        return hdfsPath;
    }

    //domain=[dp.jd.com, bdp.jd.co.th, bdp.jd.id]
    public static Map<String,String> getUrlBuffaloVersionMap(String domain, String logId, String logType, String buffaloVersion) {
        Map<String,String> urlBuffaloVersionMap = new LinkedHashMap<>();
        if (StringUtils.isNotEmpty(logType)){
            if(logType.equalsIgnoreCase("jbdpEdc")){
                urlBuffaloVersionMap.put("http://" + domain + "/jbdpEdc/openApi/downloadExecLogs?jobDetailId=" + logId, logType);
            }
            if(logType.equalsIgnoreCase("idenew")){
                urlBuffaloVersionMap.put("http://" + domain + "/idenew/test/getRunLog.ajax?runId=" + logId, logType);
            }
        } else {
            if("buffalo3".equalsIgnoreCase(buffaloVersion)) {
                urlBuffaloVersionMap.put("http://" + domain + "/buffalo/taskLog/downloadLogHbase.ajax?logId=" + logId + "&businessType=001&downloadsource=hbase", "BUFFALO3");
            } else if("buffalo4".equalsIgnoreCase(buffaloVersion)) {
                urlBuffaloVersionMap.put("http://" + domain + "/buffalo4/instance/log/downloadLogs.ajax?runLogId=" + logId, "BUFFALO4");
            } else {
                urlBuffaloVersionMap.put("http://" + domain + "/buffalo4/instance/log/downloadLogs.ajax?runLogId=" + logId, "BUFFALO4");
                urlBuffaloVersionMap.put("http://" + domain + "/buffalo/taskLog/downloadLogHbase.ajax?logId=" + logId + "&businessType=001&downloadsource=hbase", "BUFFALO3");
            }

        }
        return urlBuffaloVersionMap;
    }

    public static JSONObject logAnalysis(Map<String, String> urlBuffaloVersionMap, boolean hasKeyWordMatch, Long logId, String logType) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("isSuccess", false);
        Set<Map.Entry<String, String>> entries = urlBuffaloVersionMap.entrySet();
        for(Map.Entry<String, String> entry: entries) {
            String url = entry.getKey();
            String buffaloVersion = entry.getValue();
            JSONObject trackingUrlObj = logAnalysisByUrl(url, hasKeyWordMatch, logId, logType);
            if (trackingUrlObj.getBooleanValue("isSuccess")) {
                jsonObject.put("logUrl", url);
                jsonObject.put("buffaloType", buffaloVersion);
                jsonObject.putAll(trackingUrlObj);
                jsonObject.put("isSuccess", true);
                break;
            }
        }
        logger.info("logAnalysis response: " + jsonObject);
        return jsonObject;
    }

    public static JSONObject uploadScript(String gitProjectId, String projectSpaceId, String rewroteShellScript,
                                          String applicationId, String fileName) {
        JSONObject response = new JSONObject();
        String version;
        JSONObject checkFileExist = checkFileExist(gitProjectId, projectSpaceId, fileName);
        logger.info("CheckFileExist = " + checkFileExist);
        if(checkFileExist.getJSONObject("obj").getBooleanValue("result")) {
            JSONObject info = BDPUtils.getInfo(gitProjectId, fileName, projectSpaceId);
            version = info.getJSONObject("obj").getString("version");
        } else {
            JSONObject addScript = BDPUtils.addScript(gitProjectId, 100);
            logger.info("AddScript = " + addScript);
            if (addScript.isEmpty()) {
                logger.warning("uploadScript: addScript failed.");
                return new JSONObject();
            }
            JSONObject saveScript1 = BDPUtils.saveScript(gitProjectId, rewroteShellScript,
                    addScript.getJSONObject("obj").getString("name"), fileName);
            logger.info("SaveScript = " + saveScript1);
            version = saveScript1.getString("version");
        }
        JSONObject content = BDPUtils.saveContent(gitProjectId, rewroteShellScript, fileName, version);
        logger.info("SaveContent = " + content);
        JSONObject pushFile = BDPUtils.pushFile(gitProjectId, fileName, "commitMessage");
        logger.info("PushFile = " + pushFile);
        if (pushFile.isEmpty() || !pushFile.getBooleanValue("success")) {
            logger.warning("uploadScript: pushFile failed.");
            response.put("isSuccess", false);
            response.put("errMsg", "PushFile: " + pushFile.getString("message"));
            return response;
        }
        response = BDPUtils.publishScript(gitProjectId, fileName, applicationId,
                pushFile.getJSONObject("obj").getString("version"), "", "0");
        logger.info("PublishScript = " + response);
        response.put("isSuccess", true);
        return response;
    }

    public static boolean checkFileExistBool(String gitProjectId, String projectSpaceId, String gitProjectFilePath) {
        JSONObject jsonObject = checkFileExist(gitProjectId, projectSpaceId, gitProjectFilePath);
        return jsonObject.getJSONObject("obj").getBooleanValue("result");
    }

    public static JSONObject checkFileExist(String gitProjectId, String projectSpaceId, String gitProjectFilePath) {
        Map<String, String> postMap = new HashMap<>();
        postMap.put("gitProjectId", gitProjectId);
        postMap.put("projectSpaceId", projectSpaceId);
        postMap.put("gitProjectFilePath", gitProjectFilePath);
        HttpResponse request = Curl.request("curl 'http://dp.jd.com/datadev/script/checkFileExist.ajax' \\\n" +
                "  -H 'Connection: keep-alive' \\\n" +
                "  -H 'Pragma: no-cache' \\\n" +
                "  -H 'Cache-Control: no-cache' \\\n" +
                "  -H 'Accept: application/json, text/javascript, */*; q=0.01' \\\n" +
                "  -H 'X-Requested-With: XMLHttpRequest' \\\n" +
                "  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.128 Safari/537.36' \\\n" +
                "  -H 'Content-Type: application/x-www-form-urlencoded;charset=UTF-8' \\\n" +
                "  -H 'Origin: http://dp.jd.com' \\\n" +
                "  -H 'Referer: http://dp.jd.com/datadev/index.html' \\\n" +
                "  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7,pl;q=0.6' \\\n" +
                "  -H 'Cookie: P_S_I_D=15077; H_C_K_T=%5B%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Faa.sh%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Daa.sh%26gitProjectId%3D45268%22%2C%22label%22%3A%22aa.sh%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22aa.sh%22%2C%22title%22%3A%22aa.sh%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Faa.sh%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%2C%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fwuguoxiao_20210424174557.sql%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dwuguoxiao_20210424174557.sql%26gitProjectId%3D45268%22%2C%22label%22%3A%22%E6%9C%AA%E5%91%BD%E5%90%8D1.sql%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22wuguoxiao_20210424174557.sql%22%2C%22title%22%3A%22%E6%9C%AA%E5%91%BD%E5%90%8D1.sql%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fwuguoxiao_20210424174557.sql%22%7D%2C%22isTmp%22%3Atrue%2C%22dirPath%22%3A%22%22%7D%5D; H_C_K_A_L=45268%2Fwuguoxiao_20210424174557.sql; _bdp_erp=wuguoxiao; shshshfpb=yzCVVxFWQsSoHlj4dkmO4AQ%3D%3D; shshshfpa=7a7fe65b-e8a3-059a-0de0-45bf10419082-1578567436; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; __jdc=*********; jd.erp.lang=zh_CN; user_type=2; smartops_user_type=0; mgt_smartops_user_type=0; pin=83312669-58324517; unick=83312669-58324517; ceshi3.com=201; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; __jda=*********.15782809082781039567813.1578280908.1616478405.1618291393.28; deviceName=Safari; deviceOS=ios; deviceOSVersion=9.4.1; deviceVersion=604.1; equipmentId=LKOMABMZ4AHW5E64WK6MD26XE6HO5F47K6L26YANRFZKPGCPK64KJOCRBCYPOTNBMWWFXLP4PKQO5EGXBEDDJ4QPZQ; fingerprint=95b7606139fa153ed7dde54e425b19c5; wq_logid=1618385857.1792500328; __jdu=15782809082781039567813; logining=1; jdc_art=1618478672758-2RvMr9wG31xPvORSuqNjssQJbSabAmm1; jdc_art.sig=ghR6wBd4zI6Fmpc0xkCZksUiJbw; shshshfp=6f6b0a44563a3caf8443329c0775de16; SSOID=5e02127d7bb6963f4e09ddb2527346e4280632b27e34ea5ff6fdf600bdd80808,wuguoxiao; idc.jd.com=ee4e2655d0f6971fa9ff6ede40d3a810; smartops.jd.com=dc09e3859b1e4a8b3a61d0f802ef8019; mgt.smartops.jd.com=5290fd01a0d962cd2493c65178d8f6d4; __jdv=137790115|direct|-|none|-|1619056180559; jdd69fo72b8lfeoe=BSHRML5U6PIFISKFBA4WKKZHABZVVBQBDQWV7GWGGL2RZYJGPU5NIWNNU7OOOXAATF33PGHMBSCTBJ63HT2RN6XSGE; erp1.jd.com=85813035FB427D0C87009741741135EDBDFBC257E0CC34DE64DB1E646DA48EEE2AB577E6D9497EA741B4A5BE851A7E92D0264788EB070C7B9ECA0C52112DEB4486F7C3A48B13C15A0FF0EF89F7C47970; sso.jd.com=BJ.D67A2C7070FDAAE23348ABFBF2E459CD1920210423091858; RT=\"z=1&dm=jd.com&si=k6ujqzq6q7&ss=kns6zwu6&sl=0&tt=0&ld=nek&ul=vtkoz&hd=vtkwo\"; wlfstk_smdl=xfk0rtthpd4tmh8vop5ljaixylaqz64x; TrackID=1Fx6qJlcA-ykIT6FH2h8lZzh2TIWN8XAItYd8Yqsdy-jTbcS77zW9DPgXIif3cF56xfUezP6BSj8F5854WXP8JtAjuH0TgNxvRz9i4bWCbUNz3HXA5IzNkqfHaa_Fo1we; thor=4C0F377C0C4ED890C4538BA82174E2170C5D39C9C2077C2FC2A93237886D69601A1825EB3C4A0BA2954397CB13D3CC0212288FBDD01CB660768EFD5BEAE0C83F04244E20F0976938B4A60CF9591DBB7E8B14925EC08B55AA516D88673BA9E25FCF564797F38BCAB7CF36DF11BB2904716F6CAFC3A68DE8A68B1C0BBEDDF30B1258707CF4B4B950E5774ACA9F4F8532FB098CEB6DE21D16A69A7317000E3E1E9E; JSESSIONID=275D875F5DB835D0AF0D602EA837836A.s1; 3AB9D23F7A4B3C9B=LKOMABMZ4AHW5E64WK6MD26XE6HO5F47K6L26YANRFZKPGCPK64KJOCRBCYPOTNBMWWFXLP4PKQO5EGXBEDDJ4QPZQ; __jda=*********.15782809082781039567813.1578280908.1616478405.1618291393.28; __jdc=*********; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; __jdb=*********.17.15782809082781039567813|28.1618291393' \\\n" +
                "  --data 'gitProjectId=45268&gitProjectFilePath=ggg.sql&projectSpaceId=15077' \\\n" +
                "  --compressed \\\n" +
                "  --insecure", null, postMap, false);
        if (request.isSuccess()) {
            return JSON.parseObject(request.getContent());
        }
        return new JSONObject(0);
    }

    public static JSONObject getInfo(String gitProjectId, String gitProjectFilePath, String projectSpaceId){
        Map<String, String> postMap = new HashMap<>();
        postMap.put("gitProjectId", gitProjectId);
        postMap.put("projectSpaceId", projectSpaceId);
        postMap.put("gitProjectFilePath", gitProjectFilePath);
        HttpResponse request = Curl.request("curl 'http://dp.jd.com/datadev/scriptFile/getInfo.ajax' \\\n" +
                "  -H 'Connection: keep-alive' \\\n" +
                "  -H 'Pragma: no-cache' \\\n" +
                "  -H 'Cache-Control: no-cache' \\\n" +
                "  -H 'Accept: application/json, text/javascript, */*; q=0.01' \\\n" +
                "  -H 'X-Requested-With: XMLHttpRequest' \\\n" +
                "  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36' \\\n" +
                "  -H 'Content-Type: application/x-www-form-urlencoded;charset=UTF-8' \\\n" +
                "  -H 'Origin: http://dp.jd.com' \\\n" +
                "  -H 'Referer: http://dp.jd.com/datadev/home/<USER>' \\\n" +
                "  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7,pl;q=0.6' \\\n" +
                "  -H 'Cookie: P_S_I_D=15077; H_C_K_A_G_wuguoxiao=45268; H_C_K_T=%5B%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fggg.sh%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dggg.sh%26gitProjectId%3D45268%22%2C%22label%22%3A%22ggg.sh%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22ggg.sh%22%2C%22title%22%3A%22ggg.sh%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fggg.sh%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%2C%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Faa.sh%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Daa.sh%26gitProjectId%3D45268%22%2C%22label%22%3A%22aa.sh%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22aa.sh%22%2C%22title%22%3A%22aa.sh%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Faa.sh%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%5D; H_C_K_A_L=45268%2Faa.sh; _bdp_erp=wuguoxiao; shshshfpb=yzCVVxFWQsSoHlj4dkmO4AQ%3D%3D; shshshfpa=7a7fe65b-e8a3-059a-0de0-45bf10419082-1578567436; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; __jdc=*********; jd.erp.lang=zh_CN; user_type=2; smartops_user_type=0; mgt_smartops_user_type=0; pin=83312669-58324517; unick=83312669-58324517; ceshi3.com=201; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; __jda=*********.15782809082781039567813.1578280908.1616478405.1618291393.28; deviceName=Safari; deviceOS=ios; deviceOSVersion=9.4.1; deviceVersion=604.1; equipmentId=LKOMABMZ4AHW5E64WK6MD26XE6HO5F47K6L26YANRFZKPGCPK64KJOCRBCYPOTNBMWWFXLP4PKQO5EGXBEDDJ4QPZQ; fingerprint=95b7606139fa153ed7dde54e425b19c5; wq_logid=1618385857.1792500328; __jdu=15782809082781039567813; logining=1; jdc_art=1618478672758-2RvMr9wG31xPvORSuqNjssQJbSabAmm1; jdc_art.sig=ghR6wBd4zI6Fmpc0xkCZksUiJbw; shshshfp=6f6b0a44563a3caf8443329c0775de16; SSOID=5e02127d7bb6963f4e09ddb2527346e4280632b27e34ea5ff6fdf600bdd80808,wuguoxiao; idc.jd.com=ee4e2655d0f6971fa9ff6ede40d3a810; smartops.jd.com=dc09e3859b1e4a8b3a61d0f802ef8019; mgt.smartops.jd.com=5290fd01a0d962cd2493c65178d8f6d4; __jdv=137790115|direct|-|none|-|1619056180559; jdd69fo72b8lfeoe=VPZUPYQXVYU24KEM43E64PMADSNYFTV3EFIPEYQZKFSH4DMJNLE7N34LFIQ5GB62L3MM2X6BFBCFCP5OZYWCSGJDQY; erp1.jd.com=46AD2A6A27331E69E7022A9885877D11A813E2BFD2EB1DF8EAF8E3940235749AAADE7D356D254ACAFCFBE6E77CDBDCF002FDC6306A3C53E9E3E948C531DA6FD70D77D85F3A476513A6C9975F2D3D3B62; sso.jd.com=BJ.7F8D627D469A114CBF1906105BC800F22520210426102036; 3AB9D23F7A4B3C9B=LKOMABMZ4AHW5E64WK6MD26XE6HO5F47K6L26YANRFZKPGCPK64KJOCRBCYPOTNBMWWFXLP4PKQO5EGXBEDDJ4QPZQ; wlfstk_smdl=jgm20ulsjxlecng0qsxka4hxmenq2mbl; TrackID=1gOvTG1Iz3HJooDOY0to_EccYYlWWvPZUAK_1lY8UkyiHLKw8bC3EHcy_zafeR9fnVg02suKsfH9KPpD6FN2SnsuPKo0jJSptFVIBhD-qz0eb2TGARGvOmHbgAXlkEUFC; thor=4C0F377C0C4ED890C4538BA82174E2170C5D39C9C2077C2FC2A93237886D6960F10518AD6BE7EF4F349A6E299608CAABB5D1F2A8A65C7D703ED0870EA74099F450EBF47448208A465AEB18FA1C5C887C388A12021C2ED8FF42B2718743CC0F40A0E69931CF3C7D872E257CB2C4F90774D5982D876E7DE341577380F515644C6F754AF5452D8B7C45FF36F9D65759B7D029F9FC1DCDC56752D6825E5FD182F9F2; JSESSIONID=57B55F94DFE57A6F39CCBB6F8598D885.s1; RT=\"z=1&dm=jd.com&si=v0k8qbh52ce&ss=knzdly7s&sl=3&tt=4gs&ld=559j&nu=ed8553192c8d124be55949cc1b2e99dc&cl=59vt&ul=6o3n&hd=6o41\"; SSAID=b34f4a312d8e8b032db0c0b5f2fa1a8dce7e08126c056618eed976344e56ea54115decf80ed3324fee4f17ccccb133569140fb06582ebc8e1732b3734c388891f8efa306d68fe74eb7bc57eb4c85ceae5fcd4f5969a62a05d9b0b63bfbf26314427239bf7346e07d05898156150413e3277342be5c1203d391b60faa6ad9d671260f8982e4ff86480ff102cadf67caeb05e41c736adc1cee016eab009277bad66c48323ae3cf73421d036244d338f70c514a1c765010255dfa19d282743d26ca; __jda=*********.15782809082781039567813.1578280908.1616478405.1618291393.28; __jdc=*********; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; __jdb=*********.25.15782809082781039567813|28.1618291393' \\\n" +
                "  --data 'gitProjectId=45268&gitProjectFilePath=aa.sh&projectSpaceId=15077' \\\n" +
                "  --compressed \\\n" +
                "  --insecure", null, postMap, false);
        if (request.isSuccess()) {
            return JSON.parseObject(request.getContent());
        }
        return new JSONObject(0);
    }

    public static JSONObject addScript(String gitProjectId, int attempt) {
        JSONObject addScript = new JSONObject();
        for (int i = 0; i < attempt; i++) {
            addScript = BDPUtils.addScript(gitProjectId);
            if (!addScript.isEmpty()) {
                logger.info("AddScript: idx: " + i + " " + addScript.getBooleanValue("success"));
                if (addScript.getBooleanValue("success")) {
                    break;
                }
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return addScript;
    }

    /**
     * @param gitProjectId
     * @return
     */
    public static JSONObject addScript(String gitProjectId) {
        Map<String, String> postMap = new HashMap<>();
        postMap.put("type", "2");
        postMap.put("gitProjectId", gitProjectId);
        postMap.put("isShow", "1");
        postMap.put("gitProjectDirPath", "");
        postMap.put("content", "");
        postMap.put("templateId", "0");
        postMap.put("isTemplate", "false");
        postMap.put("pythonType", "");
        HttpResponse request = Curl.request("curl 'http://dp.jd.com/datadev/script/addScript.ajax' -H 'Connection: keep-alive' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'Accept: application/json, text/javascript, */*; q=0.01' -H 'Origin: http://dp.jd.com' -H 'X-Requested-With: XMLHttpRequest' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36' -H 'Content-Type: application/x-www-form-urlencoded;charset=UTF-8' -H 'Referer: http://dp.jd.com/datadev/index.html' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,pl;q=0.6' -H 'Cookie: H_C_K_A_G_wuguoxiao=45268; H_C_K_T=%5B%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fwuguoxiao_20191113103707.sql%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dwuguoxiao_20191113103707.sql%26gitProjectId%3D45268%22%2C%22label%22%3A%22%E6%9C%AA%E5%91%BD%E5%90%8D1.sql%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22wuguoxiao_20191113103707.sql%22%2C%22title%22%3A%22%E6%9C%AA%E5%91%BD%E5%90%8D1.sql%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fwuguoxiao_20191113103707.sql%22%7D%2C%22isTmp%22%3Atrue%2C%22dirPath%22%3A%22%22%7D%2C%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fexe_adm_m14_ol_search_click_log_app_spark_team_268775.py%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dexe_adm_m14_ol_search_click_log_app_spark_team_268775.py%26gitProjectId%3D45268%22%2C%22label%22%3A%22exe_adm_m14_ol_search_click_log_app_spark_team_268775.py%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22exe_adm_m14_ol_search_click_log_app_spark_team_268775.py%22%2C%22title%22%3A%22exe_adm_m14_ol_search_click_log_app_spark_team_268775.py%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fexe_adm_m14_ol_search_click_log_app_spark_team_268775.py%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%2C%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fexe_odm_o01_user_cate3_dept3_new_old_flag_detail_cate2_spark_team_550273.py%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dexe_odm_o01_user_cate3_dept3_new_old_flag_detail_cate2_spark_team_550273.py%26gitProjectId%3D45268%22%2C%22label%22%3A%22exe_odm_o01_user_cate3_dept3_new_old_flag_detail_cate2_spark_team_550273.py%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22exe_odm_o01_user_cate3_dept3_new_old_flag_detail_cate2_spark_team_550273.py%22%2C%22title%22%3A%22exe_odm_o01_user_cate3_dept3_new_old_flag_detail_cate2_spark_team_550273.py%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fexe_odm_o01_user_cate3_dept3_new_old_flag_detail_cate2_spark_team_550273.py%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%2C%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fexe_gdm_m01_user_first_dept_da_service_spark_team_245189.py%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dexe_gdm_m01_user_first_dept_da_service_spark_team_245189.py%26gitProjectId%3D45268%22%2C%22label%22%3A%22exe_gdm_m01_user_first_dept_da_service_spark_team_245189.py%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22exe_gdm_m01_user_first_dept_da_service_spark_team_245189.py%22%2C%22title%22%3A%22exe_gdm_m01_user_first_dept_da_service_spark_team_245189.py%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fexe_gdm_m01_user_first_dept_da_service_spark_team_245189.py%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%2C%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2FtestAAA.sql%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3DtestAAA.sql%26gitProjectId%3D45268%22%2C%22label%22%3A%22testAAA.sql%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22testAAA.sql%22%2C%22title%22%3A%22testAAA.sql%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2FtestAAA.sql%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%5D; H_C_K_A_L=45268%2FtestAAA.sql; _bdp_erp=wuguoxiao; shshshfpa=13d2db3d-d425-365d-184f-e20273dd4eeb-1559034078; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; shshshfpb=ejAaBzIqGzo8jY2i2t%20o4EA%3D%3D; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; user-key=4af2159b-504e-4b4d-85b6-b784ff1a99d6; ipLocation=%u5317%u4eac; __jdu=15580876207071609020619; areaId=1; ipLoc-djd=1-2809-51216-0; unpl=V2_ZzNtbUMER0F2C0dWeUtcUWIBGw9LV0QTc10SVn5KWlZjVxVUclRCFX0URlRnGFQUZAMZWEpcRxdFCEdkexhdBWEDEV5BXnMWdAhOVXMYXwRmAhIzRF9DHXQBKFJzGVQEbjMiXkJnQxRFCEJQchhVA2AAE1tDUUMSdABHV3oeXA1XMxJVRWdzbgB6Gwo%2fTImOx9qNzZTI5SV1AUJTfh1dAWcHIlxyVnNeGwkLVH8dVQRuBRVeQ1FCE3UPR1x6Gl0CZwsiXHJU; __jdv=122270672%7Cmicro-buyer.jcloudec.com%7Ct_1001712228_201909020001_791908_791908%7Cjingfen%7C0b5e22033c1e438c81677de34b7b5e68%7C1573307393896; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; wuguoxiao_m=280; TrackID=1VVMsXtvyUiCUC3yaT1okezACCqsIuhyLBnYMd7sk-PkT1cgbw8zcV8mGGo_ZrVovcwL5iV0DkkT3S4mg0XZU3Qm9F7BHfsIzTyxemXaNp1rV5SaZaFsBoHxXKzcQqe8V; ceshi3.com=201; cn=7; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; __jdc=*********; sso.jd.com=BJ.8b865a25453e4c5cb9634601db696b29; shshshfp=ac4844a25d0a3eeb4b46db9f10d66e53; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; RT=\"z=1&dm=jd.com&si=bn5unf29p3v&ss=k2zmwoab&sl=2&tt=25x&ld=62uo&nu=bdf5f0067ba3e7b25cfdc6dde86316fa&cl=6rw0&ul=10vf7&hd=10vfm\"; __jda=*********.15580876207071609020619.1558087621.**********.1573800036.783; __jdb=*********.1.15580876207071609020619|783.1573800036; __jdc=*********' --data $'type=3&gitProjectId=45268&isShow=1&gitProjectDirPath=&content=%23\\u0021%2Fusr%2Fbin%2Fenv+python3%0A%23+-*-+coding%3A+utf-8+-*-%0A&templateId=0&isTemplate=false&pythonType=2' --compressed --insecure", null, postMap, false);
        if (request.isSuccess()) {
            return JSONObject.parseObject(request.getContent());
        }
        String content = request.getContent();
        System.out.println("AddScript: Response: " + content);
        return new JSONObject();
    }

    public static JSONObject saveScript(String gitProjectId, String script, String gitProjectFilePath, String gitFilename) {
        JSONObject object = new JSONObject();
        object.put("gitProjectId", gitProjectId);
        object.put("gitProjectDirPath", "");
        object.put("gitProjectFilePath", gitProjectFilePath);
        object.put("name", gitFilename);
        object.put("description", gitFilename);
        object.put("content", script);
        HttpPost httpPost = new HttpPost("http://dp.jd.com/datadev/script/save.ajax");
        httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
        StringEntity stringEntity = new StringEntity(object.toJSONString(), "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        httpPost.setEntity(stringEntity);
        JSONObject ret = new JSONObject();
        try {
            CloseableHttpResponse execute = Curl.httpClient.execute(httpPost);
            String s = EntityUtils.toString(execute.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (jsonObject.getBooleanValue("success")) {
                ret = jsonObject.getJSONObject("obj");
            }
            EntityUtils.consume(execute.getEntity());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return ret;
    }

    /**
     * Save Script
     * @param gitProjectId
     * @param script
     * @param gitProjectFilePath
     * @param version
     * @return
     */
    public static JSONObject saveContent(String gitProjectId, String script, String gitProjectFilePath, String version) {
        JSONObject object = new JSONObject();
        object.put("gitProjectId", gitProjectId);
        object.put("gitProjectFilePath", gitProjectFilePath);
        object.put("content", script);
        object.put("version", version);
        HttpPost httpPost = new HttpPost("http://dp.jd.com/datadev/script/saveContent.ajax");
        httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
        StringEntity stringEntity = new StringEntity(object.toJSONString(), "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        httpPost.setEntity(stringEntity);
        try {
            CloseableHttpResponse execute = Curl.httpClient.execute(httpPost);
            String s = EntityUtils.toString(execute.getEntity());
            JSONObject jsonObject = JSON.parseObject(s);
            EntityUtils.consume(execute.getEntity());
            return jsonObject;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Param example:
     * gitProjectId: 45268
     * gitProjectFilePath: test_1.sh
     * commitMessage: ssss
     *
     * @param gitProjectId
     * @param gitProjectFilePath
     * @param commitMessage
     * @return
     */
    public static JSONObject pushFile(String gitProjectId, String gitProjectFilePath, String commitMessage) {
        String command = "curl 'http://dp.jd.com/datadev/script/pushFile.ajax' -H 'Connection: keep-alive' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'Accept: application/json, text/javascript, */*; q=0.01' -H 'Origin: http://dp.jd.com' -H 'X-Requested-With: XMLHttpRequest' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36' -H 'Content-Type: application/x-www-form-urlencoded;charset=UTF-8' -H 'Referer: http://dp.jd.com/datadev/index.html' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,pl;q=0.6' -H 'Cookie: H_C_K_A_G_wuguoxiao=45268; H_C_K_T=%5B%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Ftest_1.sh%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dtest_1.sh%26gitProjectId%3D45268%22%2C%22label%22%3A%22test_1.sh%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22test_1.sh%22%2C%22title%22%3A%22test_1.sh%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Ftest_1.sh%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%5D; H_C_K_A_L=45268%2Ftest_1.sh; _bdp_erp=wuguoxiao; shshshfpa=13d2db3d-d425-365d-184f-e20273dd4eeb-1559034078; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; shshshfpb=ejAaBzIqGzo8jY2i2t%20o4EA%3D%3D; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; user-key=4af2159b-504e-4b4d-85b6-b784ff1a99d6; ipLocation=%u5317%u4eac; __jdu=15580876207071609020619; areaId=1; ipLoc-djd=1-2809-51216-0; unpl=V2_ZzNtbUMER0F2C0dWeUtcUWIBGw9LV0QTc10SVn5KWlZjVxVUclRCFX0URlRnGFQUZAMZWEpcRxdFCEdkexhdBWEDEV5BXnMWdAhOVXMYXwRmAhIzRF9DHXQBKFJzGVQEbjMiXkJnQxRFCEJQchhVA2AAE1tDUUMSdABHV3oeXA1XMxJVRWdzbgB6Gwo%2fTImOx9qNzZTI5SV1AUJTfh1dAWcHIlxyVnNeGwkLVH8dVQRuBRVeQ1FCE3UPR1x6Gl0CZwsiXHJU; __jdv=122270672%7Cmicro-buyer.jcloudec.com%7Ct_1001712228_201909020001_791908_791908%7Cjingfen%7C0b5e22033c1e438c81677de34b7b5e68%7C1573307393896; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; wuguoxiao_m=280; TrackID=1VVMsXtvyUiCUC3yaT1okezACCqsIuhyLBnYMd7sk-PkT1cgbw8zcV8mGGo_ZrVovcwL5iV0DkkT3S4mg0XZU3Qm9F7BHfsIzTyxemXaNp1rV5SaZaFsBoHxXKzcQqe8V; ceshi3.com=201; cn=7; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; __jdc=*********; sso.jd.com=BJ.8b865a25453e4c5cb9634601db696b29; shshshfp=ac4844a25d0a3eeb4b46db9f10d66e53; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; __jdc=*********; RT=\"z=1&dm=jd.com&si=7ma83h9i5ml&ss=k2zn2p7n&sl=0&tt=0&ld=62uo&nu=bdf5f0067ba3e7b25cfdc6dde86316fa&cl=6rw0&ul=10vf7&hd=62ovt\"; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; __jdb=*********.12.15580876207071609020619|782.**********' " +
                "--data 'gitProjectId=45268&gitProjectFilePath=test_1.sh&commitMessage=ssss' --compressed --insecure";
        Map<String, String> postMap = new HashMap<>();
        postMap.put("gitProjectId", gitProjectId);
        postMap.put("gitProjectFilePath", gitProjectFilePath);
        postMap.put("commitMessage", commitMessage);
        HttpResponse request = Curl.request(command, null, postMap, false);
        if (request.isSuccess()) {
            return JSON.parseObject(request.getContent());
        }
        return new JSONObject();
    }

    public static JSONObject publishScript(String gitProjectId, String gitProjectFilePath, String applicationId, String version, String description, String relaveScriptStatus) {
        String command = "curl 'http://dp.jd.com/datadev/buffalo/upLineScript.ajax' -H 'Connection: keep-alive' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'Accept: application/json, text/javascript, */*; q=0.01' -H 'Origin: http://dp.jd.com' -H 'X-Requested-With: XMLHttpRequest' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36' -H 'Content-Type: application/x-www-form-urlencoded;charset=UTF-8' -H 'Referer: http://dp.jd.com/datadev/index.html' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,pl;q=0.6' -H 'Cookie: H_C_K_A_G_wuguoxiao=45268; H_C_K_T=%5B%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Ftest_1.sh%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dtest_1.sh%26gitProjectId%3D45268%22%2C%22label%22%3A%22test_1.sh%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22test_1.sh%22%2C%22title%22%3A%22test_1.sh%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Ftest_1.sh%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%5D; H_C_K_A_L=45268%2Ftest_1.sh; _bdp_erp=wuguoxiao; shshshfpa=13d2db3d-d425-365d-184f-e20273dd4eeb-1559034078; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; shshshfpb=ejAaBzIqGzo8jY2i2t%20o4EA%3D%3D; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; user-key=4af2159b-504e-4b4d-85b6-b784ff1a99d6; ipLocation=%u5317%u4eac; __jdu=15580876207071609020619; areaId=1; ipLoc-djd=1-2809-51216-0; unpl=V2_ZzNtbUMER0F2C0dWeUtcUWIBGw9LV0QTc10SVn5KWlZjVxVUclRCFX0URlRnGFQUZAMZWEpcRxdFCEdkexhdBWEDEV5BXnMWdAhOVXMYXwRmAhIzRF9DHXQBKFJzGVQEbjMiXkJnQxRFCEJQchhVA2AAE1tDUUMSdABHV3oeXA1XMxJVRWdzbgB6Gwo%2fTImOx9qNzZTI5SV1AUJTfh1dAWcHIlxyVnNeGwkLVH8dVQRuBRVeQ1FCE3UPR1x6Gl0CZwsiXHJU; __jdv=122270672%7Cmicro-buyer.jcloudec.com%7Ct_1001712228_201909020001_791908_791908%7Cjingfen%7C0b5e22033c1e438c81677de34b7b5e68%7C1573307393896; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; wuguoxiao_m=280; TrackID=1VVMsXtvyUiCUC3yaT1okezACCqsIuhyLBnYMd7sk-PkT1cgbw8zcV8mGGo_ZrVovcwL5iV0DkkT3S4mg0XZU3Qm9F7BHfsIzTyxemXaNp1rV5SaZaFsBoHxXKzcQqe8V; ceshi3.com=201; cn=7; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; __jdc=*********; sso.jd.com=BJ.8b865a25453e4c5cb9634601db696b29; shshshfp=ac4844a25d0a3eeb4b46db9f10d66e53; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; __jdc=*********; RT=\"z=1&dm=jd.com&si=7ma83h9i5ml&ss=k2zn2p7n&sl=0&tt=0&ld=62uo&nu=bdf5f0067ba3e7b25cfdc6dde86316fa&cl=6rw0&ul=10vf7&hd=62ovt\"; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; __jdb=*********.12.15580876207071609020619|782.**********' " +
                "--data 'gitProjectId=45268&gitProjectFilePath=test_1.sh&applicationId=13459&version=1001&description=dddd&relaveScriptStatus=0' --compressed --insecure";
        Map<String, String> postMap = new HashMap<>();
        postMap.put("gitProjectId", gitProjectId);
        postMap.put("gitProjectFilePath", gitProjectFilePath);
        postMap.put("applicationId", applicationId);
        postMap.put("version", version);
        postMap.put("description", description);
        postMap.put("relaveScriptStatus", relaveScriptStatus);
        HttpResponse request = Curl.request(command, null, postMap, false);
        if (request.isSuccess()) {
            return JSON.parseObject(request.getContent());
        } else {
            return new JSONObject();
        }
    }

    public static HttpResponse removeScript(String gitProjectId, String gitProjectFilePath) {
        String command = "curl 'http://dp.jd.com/datadev/script/remove.ajax' -H 'Connection: keep-alive' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'Accept: application/json, text/javascript, */*; q=0.01' -H 'Origin: http://dp.jd.com' -H 'X-Requested-With: XMLHttpRequest' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36' -H 'Content-Type: application/x-www-form-urlencoded;charset=UTF-8' -H 'Referer: http://dp.jd.com/datadev/index.html' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,pl;q=0.6' -H 'Cookie: H_C_K_A_G_wuguoxiao=45268; H_C_K_A_L=45268%2Fss.txt; H_C_K_T=%5B%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fssss.sh%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dssss.sh%26gitProjectId%3D45268%22%2C%22label%22%3A%22ssss.sh%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22ssss.sh%22%2C%22title%22%3A%22ssss.sh%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fssss.sh%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%2C%7B%22openGitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fss.txt%22%2C%22params%22%3A%7B%22url%22%3A%22%2Fdatadev%2Fhome%2Fhome_open_ide.html%3FgitProjectFilePath%3Dss.txt%26gitProjectId%3D45268%22%2C%22label%22%3A%22ss.txt%22%2C%22canRemove%22%3Atrue%2C%22path%22%3A%22ss.txt%22%2C%22title%22%3A%22ss.txt%22%2C%22gitProjectId%22%3A45268%2C%22key%22%3A%2245268%2Fss.txt%22%7D%2C%22isTmp%22%3Afalse%2C%22dirPath%22%3A%22%22%7D%5D; _bdp_erp=wuguoxiao; shshshfpa=13d2db3d-d425-365d-184f-e20273dd4eeb-1559034078; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; shshshfpb=ejAaBzIqGzo8jY2i2t%20o4EA%3D%3D; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; user-key=4af2159b-504e-4b4d-85b6-b784ff1a99d6; ipLocation=%u5317%u4eac; __jdu=15580876207071609020619; areaId=1; ipLoc-djd=1-2809-51216-0; unpl=V2_ZzNtbUMER0F2C0dWeUtcUWIBGw9LV0QTc10SVn5KWlZjVxVUclRCFX0URlRnGFQUZAMZWEpcRxdFCEdkexhdBWEDEV5BXnMWdAhOVXMYXwRmAhIzRF9DHXQBKFJzGVQEbjMiXkJnQxRFCEJQchhVA2AAE1tDUUMSdABHV3oeXA1XMxJVRWdzbgB6Gwo%2fTImOx9qNzZTI5SV1AUJTfh1dAWcHIlxyVnNeGwkLVH8dVQRuBRVeQ1FCE3UPR1x6Gl0CZwsiXHJU; __jdv=122270672%7Cmicro-buyer.jcloudec.com%7Ct_1001712228_201909020001_791908_791908%7Cjingfen%7C0b5e22033c1e438c81677de34b7b5e68%7C1573307393896; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; wuguoxiao_m=280; TrackID=1VVMsXtvyUiCUC3yaT1okezACCqsIuhyLBnYMd7sk-PkT1cgbw8zcV8mGGo_ZrVovcwL5iV0DkkT3S4mg0XZU3Qm9F7BHfsIzTyxemXaNp1rV5SaZaFsBoHxXKzcQqe8V; ceshi3.com=201; cn=7; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; __jdc=*********; sso.jd.com=BJ.8b865a25453e4c5cb9634601db696b29; shshshfp=ac4844a25d0a3eeb4b46db9f10d66e53; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; __jdc=*********; RT=\"z=1&dm=jd.com&si=7ma83h9i5ml&ss=k2zn2p7n&sl=0&tt=0&ld=62uo&nu=bdf5f0067ba3e7b25cfdc6dde86316fa&cl=6rw0&ul=10vf7&hd=62ovt\"; __jda=*********.15580876207071609020619.1558087621.**********.1573809856.783; __jdb=*********.1.15580876207071609020619|783.1573809856' --data 'gitProjectId=45268&gitProjectFilePath=ssss.sh' --compressed --insecure";
        Map<String, String> postMap = new HashMap<>();
        postMap.put("gitProjectId", gitProjectId);
        postMap.put("gitProjectFilePath", gitProjectFilePath);
        return Curl.request(command, null, postMap, false);
    }

    /**
     * 这是通过爬虫的方式，正式接口是Buffalo4TaskManager.buffalo4GetTaskInfo
     * @param taskId
     * @return
     */
    @Deprecated
    public static JSONObject getBuffalo4Task(String taskId) {
        String command = "curl 'http://dp.jd.com/buffalo4/task/add_task_attr.html?editType=edit&taskId=155046&hisTaskId=&actionType=&paramAppGroupId=&experimentId=' -H 'Connection: keep-alive' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'Upgrade-Insecure-Requests: 1' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36' -H 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3' -H 'Referer: http://dp.jd.com/buffalo4/task/add.html?editType=edit&taskId=155046' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,pl;q=0.6' -H 'Cookie: _bdp_erp=wuguoxiao; shshshfpa=13d2db3d-d425-365d-184f-e20273dd4eeb-1559034078; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; shshshfpb=ejAaBzIqGzo8jY2i2t%20o4EA%3D%3D; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; user-key=4af2159b-504e-4b4d-85b6-b784ff1a99d6; ipLocation=%u5317%u4eac; __jdu=15580876207071609020619; areaId=1; ipLoc-djd=1-2809-51216-0; unpl=V2_ZzNtbUMER0F2C0dWeUtcUWIBGw9LV0QTc10SVn5KWlZjVxVUclRCFX0URlRnGFQUZAMZWEpcRxdFCEdkexhdBWEDEV5BXnMWdAhOVXMYXwRmAhIzRF9DHXQBKFJzGVQEbjMiXkJnQxRFCEJQchhVA2AAE1tDUUMSdABHV3oeXA1XMxJVRWdzbgB6Gwo%2fTImOx9qNzZTI5SV1AUJTfh1dAWcHIlxyVnNeGwkLVH8dVQRuBRVeQ1FCE3UPR1x6Gl0CZwsiXHJU; __jdv=122270672%7Cmicro-buyer.jcloudec.com%7Ct_1001712228_201909020001_791908_791908%7Cjingfen%7C0b5e22033c1e438c81677de34b7b5e68%7C1573307393896; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; wuguoxiao_m=280; TrackID=1VVMsXtvyUiCUC3yaT1okezACCqsIuhyLBnYMd7sk-PkT1cgbw8zcV8mGGo_ZrVovcwL5iV0DkkT3S4mg0XZU3Qm9F7BHfsIzTyxemXaNp1rV5SaZaFsBoHxXKzcQqe8V; ceshi3.com=201; cn=7; __jdc=*********; sso.jd.com=BJ.4480b32fc0ae4fb8bd446f0052cddea1; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; PCSYCityID=CN_110000_110100_110115; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; shshshfp=c75a9ad797578ef0583cc788e75d627e; __jdc=*********; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; __jdb=*********.15.15580876207071609020619|782.**********' --compressed --insecure";
        Map<String, String> getMap = new HashMap<>();
        getMap.put("taskId", taskId);
        HttpResponse request = Curl.request(command, getMap, null, false);
        if (request.isSuccess()) {
            JSONObject ret = new JSONObject();
            String content = request.getContent();
            Document parse = Jsoup.parse(content);
            ret.put("market", parse.select("#marketHid").val());
            ret.put("queue", parse.select("#queueHid").val());
            ret.put("account", parse.select("#account").val());
            ret.put("taskName", parse.select("#taskName").val());
            return ret;
        }
        return new JSONObject();
    }

    public static JSONObject forkBuffalo(String fileName, String gitProjectId, String projectSpaceId, String rewroteShellScript,
                                         String applicationId, String buffaloName, SaveBuffaloBean saveBuffaloBean) {
        JSONObject uploadScriptResp = BDPUtils.uploadScript(gitProjectId, projectSpaceId, rewroteShellScript, applicationId, fileName);
        if(!uploadScriptResp.getBooleanValue("isSuccess")) {
            return uploadScriptResp;
        }
        JSONObject jsonObject = BDPUtils.saveBuffalo4(saveBuffaloBean, fileName, buffaloName, applicationId);
        jsonObject.put("isSuccess", true);
        return jsonObject;
    }

    /**
     * 278 vip_jdh_client_spark_huide NodeType : 2
     * 2780 client-************* NodeType : 1
     *
     * @param scriptFileName
     * @param buffaloName
     * @return
     */
    public static JSONObject saveBuffalo4(SaveBuffaloBean saveBuffaloBean, String scriptFileName,
                                          String buffaloName, String applicationId) {
        JSONObject script = BDPUtils.getScript(scriptFileName, applicationId);
        if (script.isEmpty()) {
            System.err.println("saveBuffalo4: script not found.");
            return new JSONObject();
        }
        saveBuffaloBean.setScriptId(script.getString("fileId"));
        saveBuffaloBean.setScriptPath(script.getString("fileName"));
        saveBuffaloBean.setScriptStartFile(script.getString("fileName"));
        String encode;
        try {
            encode = URLEncoder.encode(JSON.toJSONString(saveBuffaloBean), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        Map<String, String> postMap = new HashMap<>();
        postMap.put("taskName", buffaloName);
        postMap.put("data", encode);
        return BDPUtils.saveBuffalo4(postMap);
    }

    public static JSONObject saveBuffalo4(Map<String, String> postMap) {
        String command = "curl 'http://dp.jd.com/buffalo4/task/save.ajax' -H 'Connection: keep-alive' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'Accept: application/json, text/javascript, */*; q=0.01' -H 'Origin: http://dp.jd.com' -H 'X-Requested-With: XMLHttpRequest' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36' -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' -H 'Referer: http://dp.jd.com/buffalo4/task/add_task_attr.html?editType=add&taskId=&hisTaskId=&actionType=&paramAppGroupId=&experimentId=' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,pl;q=0.6' -H 'Cookie: _bdp_erp=wuguoxiao; shshshfpa=13d2db3d-d425-365d-184f-e20273dd4eeb-1559034078; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; shshshfpb=ejAaBzIqGzo8jY2i2t%20o4EA%3D%3D; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; user-key=4af2159b-504e-4b4d-85b6-b784ff1a99d6; ipLocation=%u5317%u4eac; __jdu=15580876207071609020619; areaId=1; ipLoc-djd=1-2809-51216-0; unpl=V2_ZzNtbUMER0F2C0dWeUtcUWIBGw9LV0QTc10SVn5KWlZjVxVUclRCFX0URlRnGFQUZAMZWEpcRxdFCEdkexhdBWEDEV5BXnMWdAhOVXMYXwRmAhIzRF9DHXQBKFJzGVQEbjMiXkJnQxRFCEJQchhVA2AAE1tDUUMSdABHV3oeXA1XMxJVRWdzbgB6Gwo%2fTImOx9qNzZTI5SV1AUJTfh1dAWcHIlxyVnNeGwkLVH8dVQRuBRVeQ1FCE3UPR1x6Gl0CZwsiXHJU; __jdv=122270672%7Cmicro-buyer.jcloudec.com%7Ct_1001712228_201909020001_791908_791908%7Cjingfen%7C0b5e22033c1e438c81677de34b7b5e68%7C1573307393896; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; wuguoxiao_m=280; TrackID=1VVMsXtvyUiCUC3yaT1okezACCqsIuhyLBnYMd7sk-PkT1cgbw8zcV8mGGo_ZrVovcwL5iV0DkkT3S4mg0XZU3Qm9F7BHfsIzTyxemXaNp1rV5SaZaFsBoHxXKzcQqe8V; ceshi3.com=201; cn=7; __jdc=*********; sso.jd.com=BJ.4480b32fc0ae4fb8bd446f0052cddea1; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; PCSYCityID=CN_110000_110100_110115; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; shshshfp=c75a9ad797578ef0583cc788e75d627e; __jdc=*********; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; __jdb=*********.3.15580876207071609020619|782.**********' --data 'curUserErp=wuguoxiao&isSuper=true&editType=add&taskId=&actionId=&actionTypeHid=&taskTypeHid=&taskNodeId=2780&taskNodeType=1&dayHid=&hourHid=&minuteHid=&startFaHid=&endFaHid=&alarmTypeHid=&marketHid=&queueHid=&hadoopEngineTypeH=spark&managerHid=&moonFallTaskNodeId=194&moonFallTaskNodeName=%E7%99%BB%E6%9C%88%E7%AE%97%E6%B3%95%E8%99%9A%E6%8B%9F%E8%8A%82%E7%82%B9&cpuLimitSizeHid=&memLimitSizeHid=&hasRight=&hasTaskNodeRight=&hasAccountRight=&hasQueueRight=&cgroupDefaultValue=%5B%7B%22type%22%3A%22zipper%22%2C%22value%22%3A%224%22%7D%2C%7B%22type%22%3A%22rtcheck%22%2C%22value%22%3A%224%22%7D%2C%7B%22type%22%3A%22rtchain%22%2C%22value%22%3A%224%22%7D%2C%7B%22type%22%3A%22market2%22%2C%22value%22%3A%224%22%7D%2C%7B%22type%22%3A%22custom%22%2C%22value%22%3A%226%22%7D%2C%7B%22type%22%3A%22pyscript%22%2C%22value%22%3A%226%22%7D%2C%7B%22type%22%3A%22plumber%22%2C%22value%22%3A%228%22%7D%2C%7B%22type%22%3A%22plumber_input%22%2C%22value%22%3A%228%22%7D%2C%7B%22type%22%3A%22plumber_output%22%2C%22value%22%3A%228%22%7D%2C%7B%22type%22%3A%22chain%22%2C%22value%22%3A%224%22%7D%2C%7B%22type%22%3A%22rtchain4%22%2C%22value%22%3A%224%22%7D%2C%7B%22type%22%3A%22moonfall%22%2C%22value%22%3A%224%22%7D%2C%7B%22type%22%3A%22market_sync%22%2C%22value%22%3A%224%22%7D%2C%7B%22type%22%3A%22mlp%22%2C%22value%22%3A%221%22%7D%5D&authorityCenterApproveWorkflowId=10462&hasHive2hiveRight=false&taskType=single&taskName=test_03_exe_adm_s14_traffic_plat_item_shop_di_day_spark_spark_team_temp_55147&actionType=pyscript&market=119&account=84&queue=298&hadoopEngineType=spark&taskNodeName=client-*************&description=Test&appGroupId=13459&manager=wuguoxiao&managers=wuguoxiao&priority=25&scriptId=198433&scriptInfo=script_55116_113460671.sh(1000)&scriptPath=script_55116_113460671.sh&scriptStartFile=script_55116_113460671.sh&args=&periodicType=temp&nextTime=2019-11-16+21%3A25%3A06&cycleType=day&monthCycleStep=1&weekCycleStep=1&hour=0&minute=0&cronExpression=&effectiveTime=&slaTime=&maxRunTime=0&failRetry=0&selfDepend=0&concurrency=999&parentRerunPolicy=0&cgroupSetType=1&cpuLimitSize=12&cgroupCustom=12&dmemLimitSize=12&data=%257B%2522actionName%2522%253A%2522test_03_exe_adm_s14_traffic_plat_item_shop_di_day_spark_spark_team_temp_55147%2522%252C%2522actionType%2522%253A%2522pyscript%2522%252C%2522market%2522%253A%2522119%2522%252C%2522account%2522%253A%252284%2522%252C%2522queue%2522%253A%2522298%2522%252C%2522taskNodeId%2522%253A%********%2522%252C%2522taskNodeType%2522%253A%25221%2522%252C%2522taskId%2522%253A%2522%2522%252C%2522taskNodeName%2522%253A%2522client-*************%2522%252C%2522scriptId%2522%253A%**********%2522%252C%2522scriptPath%2522%253A%2522script_55116_113460671.sh%2522%252C%2522scriptStartFile%2522%253A%2522script_55116_113460671.sh%2522%252C%2522hadoopEngineType%2522%253A%2522spark%2522%252C%2522args%2522%253A%2522%2522%252C%2522description%2522%253A%2522Test%2522%252C%2522maxRunTime%2522%253A%25220%2522%252C%2522cpuLimitSize%2522%253A%252212%2522%252C%2522memLimitSizeDouble%2522%253A%252212%2522%252C%2522startAlarm%2522%253A%25220%2522%252C%2522endAlarm%2522%253A%25220%2522%252C%2522startFa%2522%253A%252200%253A00%2522%252C%2522endFa%2522%253A%252200%253A00%2522%252C%2522failAlarm%2522%253A%25221%2522%252C%2522alarmType%2522%253A%2522email%252Csms%2522%252C%2522alarmId%2522%253A%2522%2522%252C%2522cgroupSetType%2522%253A%252212%2522%252C%2522taskType%2522%253A%2522single%2522%257D' --compressed --insecure";
        HttpResponse request = Curl.request(command, null, postMap, false);
        if (request.isSuccess()) {
            String content = request.getContent();
            JSONObject jsonObject = JSON.parseObject(content);
            return jsonObject;
        }
        return new JSONObject();
    }

    public static JSONObject getScript(String key, String jsdAppgroupId) {
        String command = "curl 'http://dp.jd.com/buffalo/filecentre/list.ajax' -H 'Connection: keep-alive' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' -H 'Accept: application/json, text/plain, */*' -H 'Origin: http://dp.jd.com' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36' -H 'Content-type: application/x-www-form-urlencoded;charset=UTF-8' -H 'Referer: http://dp.jd.com/buffalo/filecentre/list.html?bdp_menu_flag=001&model=001' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,pl;q=0.6' -H 'Cookie: _bdp_erp=wuguoxiao; shshshfpa=13d2db3d-d425-365d-184f-e20273dd4eeb-1559034078; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; shshshfpb=ejAaBzIqGzo8jY2i2t%20o4EA%3D%3D; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; user-key=4af2159b-504e-4b4d-85b6-b784ff1a99d6; ipLocation=%u5317%u4eac; __jdu=15580876207071609020619; areaId=1; ipLoc-djd=1-2809-51216-0; unpl=V2_ZzNtbUMER0F2C0dWeUtcUWIBGw9LV0QTc10SVn5KWlZjVxVUclRCFX0URlRnGFQUZAMZWEpcRxdFCEdkexhdBWEDEV5BXnMWdAhOVXMYXwRmAhIzRF9DHXQBKFJzGVQEbjMiXkJnQxRFCEJQchhVA2AAE1tDUUMSdABHV3oeXA1XMxJVRWdzbgB6Gwo%2fTImOx9qNzZTI5SV1AUJTfh1dAWcHIlxyVnNeGwkLVH8dVQRuBRVeQ1FCE3UPR1x6Gl0CZwsiXHJU; __jdv=122270672%7Cmicro-buyer.jcloudec.com%7Ct_1001712228_201909020001_791908_791908%7Cjingfen%7C0b5e22033c1e438c81677de34b7b5e68%7C1573307393896; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; wuguoxiao_m=280; TrackID=1VVMsXtvyUiCUC3yaT1okezACCqsIuhyLBnYMd7sk-PkT1cgbw8zcV8mGGo_ZrVovcwL5iV0DkkT3S4mg0XZU3Qm9F7BHfsIzTyxemXaNp1rV5SaZaFsBoHxXKzcQqe8V; ceshi3.com=201; cn=7; __jdc=*********; sso.jd.com=BJ.4480b32fc0ae4fb8bd446f0052cddea1; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; PCSYCityID=CN_110000_110100_110115; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; shshshfp=c75a9ad797578ef0583cc788e75d627e; __jdc=*********; __jda=*********.15580876207071609020619.1558087621.1573016914.**********.782; __jdb=*********.14.15580876207071609020619|782.**********' --data 'rows=10&page=1&model=001&key=script_55116_113460671.sh&managers=wuguoxiao&jsdAppgroupId=13459' --compressed --insecure";
        Map<String, String> postMap = new HashMap<>();
        postMap.put("key", key);
        postMap.put("jsdAppgroupId", jsdAppgroupId);
        HttpResponse request = Curl.request(command, null, postMap, false);
        if (request.isSuccess()) {
            String content = request.getContent();
            JSONObject jsonObject = JSON.parseObject(content);
            if (jsonObject.getBooleanValue("success")) {
                JSONArray rows = jsonObject.getJSONArray("rows");
                if (rows != null && !rows.isEmpty()) {
                    JSONObject jsonObject1 = rows.getJSONObject(0);
                    return jsonObject1;
                }
            }
        }
        return new JSONObject();
    }

    /**
     * 根据任务id查询log列表
     * @param taskId
     * @param instanceTaskId Optional
     * @return
     *     "args" : "",
     *     "created" : "2020-08-28 22:48:41",
     *     "cycle" : "2020-08-27",
     *     "deleted" : 0,
     *     "duration" : 187,
     *     "durationStr" : "00:03:00",
     *     "env" : "JDHXXXXX_CLUSTER_NAME=10k,JDHXXXXX_USER=mart_scr,JDHXXXXX_QUEUE=bdp_jmart_cmo_ipc_union.bdp_jmart_ipc_spark,TEAM_USER=cmo_ipc_normal,BEE_HIVETASK_EXEC_ENGINE=spark",
     *     "instanceCreated" : "2020-08-28 22:48:41",
     *     "instanceId" : "3352558273449426945",
     *     "instanceType" : 2,
     *     "instanceTypeStr" : "test",
     *     "isLeaf" : false,
     *     "level" : 0,
     *     "logFile" : "task/2020-08-28/3352558273466204161_action.20200828224845.txt",
     *     "modified" : "2020-08-28 22:48:41",
     *     "operator" : "",
     *     "queueTime" : "2020-08-28 22:48:41",
     *     "runBatchNumber" : "node227.108_78_1597749504319",
     *     "runLogId" : 225205068,
     *     "runStatus" : "run",
     *     "runTime" : "2020-08-28 22:48:45",
     *     "runType" : "retry",
     *     "scheTime" : "2020-08-28 22:19:18",
     *     "script" : 258140,
     *     "scriptName" : "stock_feature.zip",
     *     "scriptPath" : "stock_feature.zip",
     *     "scriptVersion" : "1136",
     *     "suspended" : 0,
     *     "taskInsId" : "3352558273449426945",
     *     "taskNodeId" : 151,
     *     "taskNodeName" : "client-**************",
     *     "taskNodeRun" : 3461,
     *     "taskNodeType" : 2
     *   }
     */
    public static JSONArray getBuffalo4Logs(String site, String taskId, String instanceTaskId,
                                            String taskType, String nodeid, String runTimeRange) {
        JSONArray ret = new JSONArray();
        CallerInfo getBuffalo4Logs = Profiler.registerInfo("bdp.bdpUtils.getBuffalo4Logs", "SparkMonitorApp", false, true);
        try {
            String cmd = "curl 'http://" + site + "/buffalo4/instance/log/getTaskInstRunRecordList.ajax' -H 'Pragma: no-cache' -H 'Origin: http://dp.jd.com' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36' -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' -H 'Accept: application/json, text/javascript, */*; q=0.01' -H 'Cache-Control: no-cache' -H 'X-Requested-With: XMLHttpRequest' -H 'Cookie: shshshfpa=a53fe48a-96e8-dc25-5562-2db3c8478e8f-1540425340; shshshfpb=1b239b465be7348b79115bbeb0a2dd13a0801196a3c187b695bd106989; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; _bdp_erp=wuguoxiao; __jdu=15399307306181995756953; user-key=e1db80f1-ac96-4f79-ab19-1c2de50794a0; _bdp_notice_cookie_hide_=buffalo4:[1544150746000]10530; __jdv=*********|direct|-|none|-|1545095084080; JSESSIONID=FCF8D7CD2840051F8A011222FBCBC72D.s1; PCSYCityID=1; ceshi3.com=201; TrackID=1Ar9kmxBOrQtVkoDmW_LqfYjxi1tS-8KZwmA8fuSzNXQLC8mQgesm3Y2SNxX37n6K_ia8WfXvJBogUQVWRt8wtYLvAz_RqS2IrLAXzSTGwe8wZsuFGKfnstRtCZl0v944; _contrast=5940028.100000180173; ipLoc-djd=1-2810-51081-0.138029193; ipLocation=%u5317%u4eac; _contrast_status=show; mt_xid=V2_52007VwMWUFVQUFsZSxhbBW4DGlpZXlZdF00bbA1hBhQGWwhSRhZBTQkZYgAUB0FRVF1PVU5UBWMLQFMOXFYIFnkaXQVuHxJRQVtQSx9LElgDbAISYl9oUmocTB5VAmcKEldtWFdcGA%3D%3D; _gcl_au=1.1.1062006927.1545544609; __tak=457779b8a450d3c8d4d8116bef0f404fe824e354714e8e7ceef4ef3b6dd71b56eb14d2c714e19936a3c772d1ae636dc7eb5ef4cb40ce448e277d4fb441b5949e1ad8f92a2f426ae92175a833e66c6ba3; cn=15; shshshfp=ac41ecfc23dc49987379a8def8c3cef4; jd.erp.lang=zh_CN; erp1.jd.com=9ABA2D92A18C944BFCA122F476329D6199C7E3B90B0CFB154BB7F6DC395802048E021BB0322D277867B2F206F6AE08BD4991A260EA527D476EF5EF29B08744E6C80EE882D89623EE4E0780FD7D31B531; sso.jd.com=c659640ba2604691b3a6272cf9fbece6; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; __jdc=*********; PM_ACCESS_TOKEN=7e95815c424888976a95b2c251c39715b8a904f9; PM_REFRESH_TOKEN=ea3c17469e10ac8fd6a93dcf48bbf0875c238b87; PM_ACCESS_ERP=wuguoxiao; __jda=*********.15399307306181995756953.1539930731.1545754085.1545760199.301; __jdb=*********.17.15399307306181995756953|301.1545760199' -H 'Connection: keep-alive' -H 'Referer: http://dp.jd.com/buffalo4/instance/log/task_runRecord.html?taskInstanceId=17012526&taskId=23480' " +
                    "--data 'taskId=23480&instanceTaskId=&" +
                    "startTime=&" +
                    "endTime=&" +
                    "actionId=&" +
                    "taskType=&_search=false&nd=1545763289626&rows=10&page=1&sidx=&sord=asc' --compressed";
            Map<String, String> postMap = new HashMap<>();
            postMap.put("taskId", taskId);
            postMap.put("instanceTaskId", instanceTaskId);
            postMap.put("runTimeRange", runTimeRange);
            postMap.put("taskType", taskType); // single or wf
            postMap.put("nodeid", nodeid);
            postMap.put("rows", "1000");
            HttpResponse request = Curl.request(cmd, null, postMap, false);
            if (request.isSuccess()) {
                String content = request.getContent();
                JSONObject recordListResult = JSONObject.parseObject(content);
                if(recordListResult != null) {
                    ret = recordListResult.getJSONArray("rows");
                } else {
                    // 接口的返回值有问题，有时间会因返回值问题导致报NPE，所以加了if判空
                    // at com.jd.bdp.spark.web.BDPUtils.getBuffalo4Logs(BDPUtils.java:671)
                    logger.info("Get record list: site: " + site + " taskId: " + taskId +
                        " instanceId: " + instanceTaskId + " runTimeRange: " + runTimeRange +
                        " taskType: " + taskType + " nodeId: " + nodeid + " result: " + recordListResult);
                }
            }
        } catch (Exception e) {
            Profiler.functionError(getBuffalo4Logs);
            logger.warning(CommonUtil.exceptionToString(e));
        } finally {
            Profiler.registerInfoEnd(getBuffalo4Logs);
        }
        return ret;
    }

    public static Map<String, String> parseEnv(String env) {
        Map<String, String> envMap = new HashMap<>();
        if (StringUtils.isNotEmpty(env)) {
            if(env.contains(",")) {
                String[] split = env.split(",");
                if (env.contains("JDH")) {
                    for (String kvStr : split) {
                        String[] kv = kvStr.split("=");
                        switch (kv[0]) {
                            case "JDHXXXXX_QUEUE":
                                envMap.put("queue", kv[1]);
                                break;
                            case "JDHXXXXX_CLUSTER_NAME":
                                envMap.put("cluster", kv[1]);
                                break;
                            case "JDHXXXXX_USER":
                                envMap.put("market", kv[1]);
                                break;
                            case "TEAM_USER":
                                envMap.put("teamuser", kv[1]);
                                break;
                            case "BEE_HIVETASK_EXEC_ENGINE":
                                envMap.put("engine", kv[1]);
                                break;
                            case "JDHXXXXX_ENGINE_VERSION":
                                envMap.put("version", kv[1]);
                                break;
                            case "BEE_BDP_TASK_LEVEL":
                                envMap.put("level", kv[1]);
                                break;
                        }
                    }
                } else {
//                10k,mart_ofl,bdp_jmart_ofl_union.bdp_jmart_ofl_dev
                    if (split.length > 3) {
                        envMap.put("teamuser", split[3]);
                    }
                    envMap.put("cluster", split[0]);
                    envMap.put("market", split[1]);
                    envMap.put("queue", split[2]);
                }
            }
        }
        return envMap;
    }

    /**
     * 填充状态引擎
     * @param site 站点
     * @param taskId 任务ID
     * @param buffalo4Logs buffalo4日志数组
     */
    public static void fillStatusEngine(String site, String taskId, JSONArray buffalo4Logs) {
        for (int i = 0; i < buffalo4Logs.size(); i++) {
            JSONObject jsonObject = buffalo4Logs.getJSONObject(i);
            try {
                jsonObject.put("buffaloType", "buffalo4");
                jsonObject.putAll(BDPUtils.parseEnv(jsonObject.getString("env"))); //环境变量
                switch (jsonObject.getString("run_status")) { // 执行状态(wait=等待, queue=队列中, taken=已领取, run=执行中, success=成功, fail=失败）
                    case "success":
                        jsonObject.put("runStatusCN", "成功");
                        break;
                    case "fail":
                        jsonObject.put("runStatusStyle", "color: red");
                        jsonObject.put("withRootCause", "true");
                        jsonObject.put("runStatusCN", "失败");
                        break;
                    case "run":
                        jsonObject.put("runStatusCN", "运行中");
                        break;
                    case "timeout":
                        jsonObject.put("runStatusCN", "等待超时");
                        break;
                    default:
                        jsonObject.put("runStatusCN", "未知");
                }
                if (jsonObject.getString("engine") == null) {
                    jsonObject.put("engine", "<a target=\"_blank\" style=\"color: red\" href=\"http://" + site + "/buffalo4/task/add.html?editType=edit&taskId=" + taskId + "\">请设置引擎</a>");
                } else if (jsonObject.getString("engine").equals("hive")) {
                    jsonObject.put("engine", "<a target=\"_blank\" style=\"color: red\" href=\"http://" + site + "/buffalo4/task/add.html?editType=edit&taskId=" + taskId + "\">hive</a>");
                }

                String elapsed_time_dbl = String.format("%.2f", jsonObject.getDoubleValue("duration") / 60);
                jsonObject.put("elapsed_time_dbl", elapsed_time_dbl);
            } catch (Throwable e) {
                logger.warning("data: " + jsonObject.toJSONString() + " error: " + CommonUtil.exceptionToString(e));
            }
        }
    }

    /**
     *
     * @param taskId
     * @return  "beginRunType" : 2,
     *     "created" : "2020-01-08 12:54:12",
     *     "endTime" : "2020-01-08 13:03:25",
     *     "execLong" : 552,
     *     "logFile" : "task/2020-01-08/528664.20200108125413.txt",
     *     "logId" : "535456289",
     *     "msg" : "重跑人(wuguoxiao)",
     *     "queueTime" : "2020-01-08 12:54:12",
     *     "runBatchNumber" : "node65.131_1577349548684",
     *     "runTime" : "2020-01-08 12:54:13",
     *     "scriptId" : 141680,
     *     "scriptInfo" : "141680&1003",
     *     "scriptName" : "dim_fashion_spu_attr.py",
     *     "scriptVersion" : 1003,
     *     "slaTime" : "",
     *     "status" : "wait",
     *     "taskId" : "528664",
     *     "taskNodeId" : "client-*************",
     *     "taskNodeName" : "client-*************",
     *     "taskNodeType" : 1
     */
    public static JSONArray getBuffalo3Logs(String site, String taskId) {
        JSONArray logBeanList = new JSONArray();
        String cmd = "curl 'http://"+site+"/buffalo/taskLog/getRunLogList.ajax' -H 'Pragma: no-cache' -H 'Origin: http://dp.jd.com' -H 'Accept-Encoding: gzip, deflate' -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36' -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' -H 'Accept: application/json, text/javascript, */*; q=0.01' -H 'Cache-Control: no-cache' -H 'X-Requested-With: XMLHttpRequest' -H 'Cookie: shshshfpa=a53fe48a-96e8-dc25-5562-2db3c8478e8f-1540425340; shshshfpb=1b239b465be7348b79115bbeb0a2dd13a0801196a3c187b695bd106989; pinId=G5bftmlclLNKYsk7x6D200D0BemWK_HU; pin=83312669-58324517; unick=83312669-58324517; _tp=rOnVB4P0z3SjANXd3yVtPuIXEX3a5GpsoHdhsnS9aQ0%3D; _pst=83312669-58324517; _bdp_erp=wuguoxiao; __jdu=15399307306181995756953; __jdv=122270672|baidu|-|organic|not set|1543769688229; TrackID=1o4AhjRAzoHThadXyvIJCLi8L9Fwq3d7YCyH3y6r29cK_RP3p6TXEf9jaCJdqRjNtyfcEXljWbz6gHPBaf-94sB0ng8Bne2s-Zg5cBAbmguEAjEqw6bLNATaoUg0SC7il4gPQ6T6Tj010kL0oAMdljA; ceshi3.com=201; user-key=e1db80f1-ac96-4f79-ab19-1c2de50794a0; cn=0; PCSYCityID=1; mt_xid=V2_52007VwMWUFVQUFsZSxhbBW4DGlpZXlZdF00bbFVmBBJWCVtVRh9PHV4ZYgYQUEFRB1oaVUtZB2QCFgAJXFcPHXkaXQVuHxJRQVlaSx9MEl8CbAYbYl9oUmocSRhaA2ECElVtW1pa; _gcl_au=1.1.1096132535.1544337431; shshshfp=c8ce61d951e9cc081b52aa081dffe083; yl_pcpc=gXNzGvpv1ejAvlbYObkQrnDLH_5J1XEq-0exZfDxOKdVHEhzZeUi50S0Zk9MqWU0RL1qnyMjuMa4hy1sWNyfyQ%3D%3D; jd.erp.lang=zh_CN; _bdp_notice_cookie_hide_=buffalo4:[1544150746000]10530; JSESSIONID=897E241C5AA8C3DADD1F45D3940D97AB.s1; erp1.jd.com=C3DF9B7B465C6A0DE78842AB7894763C56E898F3BFDBE348FD0A961B5AACED0252E01EF1D027E614237BC2EA065C063543FD13C85A83681D4C260825B3DA8E30E84C11B93E9375E69308D7324B8EF8AE; sso.jd.com=4656117ca5a649a5898e9500e634a6b2; 3AB9D23F7A4B3C9B=FYX5KOQ3HFQ3OLQQHF4PZZND3PHILK64F7FLRMXFSBRB7JABY2OQ3UT2SCK3Z423HSQWT7P6FYZNG5UXWO2OLDJOF4; _c_k_u=\"niufSpx5AlLcbhelzoneSQ==\"; _c_v_p=\"UYVgny33oF8bpa5j40R/wasD1vAPU3KgoOPNKcq1mW3o5IsiQ8sIBg==\"; __jda=*********.15399307306181995756953.1539930731.1545023793.1545028583.255; __jdc=*********; __jdb=*********.9.15399307306181995756953|255.1545028583' -H 'Connection: keep-alive' -H 'Referer: http://dp.jd.com/buffalo/taskLog/taskLog.html?businessType=001&taskId=446156' --data 'businessType=001&taskId=446156&addDay=20&_search=false&nd=1545029706380&rows=20&page=1&sidx=&sord=asc' --compressed";
        Map<String, String> postMap = new HashMap<>();
        postMap.put("taskId", taskId);
        HttpResponse request = Curl.request(cmd, null, postMap, false);
        if (request.isSuccess()) {
            String content = request.getContent();
            JSONObject object = JSONObject.parseObject(content);
            boolean success = object.getBooleanValue("success");
            if (!success) {
                logger.info("Failed to fill log information, task id " + taskId + " msg is " + object.toJSONString() + ".");
                return logBeanList;
            }
            logBeanList = object.getJSONArray("rows");
        }
        return logBeanList;
    }

    public static JSONObject getBuffalo4ScriptInLog(String buffaloDomain, String logId, String startStr) {
        Map<String, String> urlBuffaloVersionMap = BDPUtils.getUrlBuffaloVersionMap(buffaloDomain,
                logId, null, null);
        Set<Map.Entry<String, String>> entries = urlBuffaloVersionMap.entrySet();
        for(Map.Entry<String, String> entry: entries) {
            String url = entry.getKey();
            try (CloseableHttpResponse response = Curl.httpClient.execute(new HttpGet(url))) {
                if(response.getStatusLine().getStatusCode() != 200) {
                    continue;
                }
                StringBuilder builder;
                HttpEntity entity = response.getEntity();
                InputStream content = entity.getContent();
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(content));
                try {
                    builder = getSparkSql(startStr, 5, "INFO ", bufferedReader);
                } catch (Exception e) {
                    logger.warning("url = " + url);
                    continue;
                }
                content.close();
                return parseScript(builder.toString());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return new JSONObject();
    }

    public static StringBuilder getSparkSql(String startStr, int ignoreBeginningChar, String endStr, BufferedReader bufferedReader) throws IOException {
        String line;
        boolean startFlag = false;
        StringBuilder builder = new StringBuilder();
        while ((line = bufferedReader.readLine()) != null) {
            if (!startFlag && line.contains(startStr)) {
                startFlag = true;
                line = line.substring(line.indexOf(startStr) + ignoreBeginningChar);
            }
            if (startFlag && line.contains(endStr)) {
                break;
            }
            if (startFlag) {
                builder.append(line).append("\n");
            }
        }
        return builder;
    }

    public static JSONObject parseScript(String log) {
        JSONObject ret = new JSONObject();
        int i = log.indexOf("-e");
        if(i == -1) {
            return ret;
        }
        String sparkShellWithParam = log.substring(0, i);
        String originalSqlDraft = log.substring(i);
        ret.put("fullsql", log);
        ret.put("shell", sparkShellWithParam);
        ret.put("sql", originalSqlDraft);
        if (sparkShellWithParam.contains("spark-sql")) {
            ret.put("type", "spark");
        } else if (sparkShellWithParam.contains("hive")) {
            ret.put("type", "hive");
        } else {
            ret.put("type", "UNKNOW");
        }
        return ret;
    }

    private static final Pattern compile = Pattern.compile(".{0,200}tracking URL: (.*application_(\\d+_\\d+))");

    public static JSONObject findTrackingUrl(String line) {
        JSONObject obj = new JSONObject();
        Matcher matcher = compile.matcher(line);
        if (matcher.find()) {
            obj.put("url", matcher.group(1));
            obj.put(LOG_ANALYSIS_APPID, matcher.group(2));
        }
        return obj;
    }
    public static Pattern clusterPattern = Pattern.compile("集群: (.*?),");
    public static Pattern actionIdPattern = Pattern.compile("环节实例ID: (.*)_action");
    public static Pattern amPattern = Pattern.compile("ApplicationMaster host: (?!N/A)(.*)");
    public static Pattern hadoopUserPattern = Pattern.compile(" INFO user: (.*)");
    //有以下三种可能的情况
    //21/03/23 13:58:46 [main] INFO EventLoggingListener: Logging events to hdfs://ns1002/user/spark/log_hope/2021-03-23/spark-application-1616479126581.lz4
    //21/04/13 09:41:13 [main] INFO SingleEventLogFileWriter: Logging events to hdfs://ns1/user/spark/log/2021-04-13/application_1073165109806_66585.lz4.inprogress
    //21/04/13 09:37:19 [main] INFO EventLoggingListener: Logging events to hdfs://ns1/user/spark/log/2021-04-13/application_3958782110806_66380.lz4
    public static Pattern appIdOnJdosPattern = Pattern.compile("Logging events to (hdfs://(.*?)/.*/(.*)\\.lz4)");

    public static JSONObject matchEventLog(String line) {
        JSONObject obj = new JSONObject();
        Matcher matcher = appIdOnJdosPattern.matcher(line);
        if (matcher.find()) {
            obj.put(LOG_ANALYSIS_JDOS_EVENT_PATH, matcher.group(1));
            obj.put(LOG_ANALYSIS_JDOS_EVENT_NS, matcher.group(2));
            obj.put(LOG_ANALYSIS_JDOS_APPID, matcher.group(3));
        }
        return obj;
    }

    public static JSONObject matchKeyword(Pattern amPattern, String key, String line) {
        JSONObject obj = new JSONObject();
        Matcher matcher = amPattern.matcher(line);
        if (matcher.find()) {
            obj.put(key, matcher.group(1));
        }
        return obj;
    }

    public static JSONObject matchLastKeyword(Pattern amPattern, String key, String line) {
        JSONObject obj = new JSONObject();
        Matcher matcher = amPattern.matcher(line);
        while (matcher.find()) {
            obj.put(key, matcher.group(1));
        }
        return obj;
    }

    public static boolean reachEnd(String line) {
        return line.contains("[main] WARN HiveConf") || line.contains("[main] WARN SparkConf") || line.contains("SparkSubmitArguments =>");
    }

    @SneakyThrows
    public static JSONObject logAnalysisByUrl(String url, boolean hasKeywordMatch, Long logId, String logType) {
        JSONObject ret = new JSONObject();
        ret.put("isSuccess", "false");
        if (url == null || "".equals(url.trim())) {
            ret.put("errMsg", "Get log info error: url is empty.");
            return ret;
        }
        long start = System.currentTimeMillis();
        String filePath;
        if(StringUtils.isNotEmpty(logType)){
            filePath = OSUtils.downloadBuffaloLog(url, ret, logType);
        }else {
//            filePath = downloadLogFromHbase(logId);
            filePath = Scheduler.commonBean.getDownloadBuffaloLogDir() + File.separator + "buffalo_logs" + File.separator + logId + ".txt";
            FileUtil.createDirectory(filePath);
            File file = new File(filePath);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            downloadLogFromHbase(logId, fileOutputStream);
            fileOutputStream.close();
        }
        ret.put("Elapsed Download Log", (System.currentTimeMillis() - start) / 1000 + "");
        ret.put("filePath", filePath);
        if (filePath != null) {
            start = System.currentTimeMillis();
            logAnalysisByFile(hasKeywordMatch, ret, filePath);
            ret.put("Elapsed Analysis Log", (System.currentTimeMillis() - start) / 1000 + "");
        }
        return ret;
    }

    public static String downloadLogFromHbase(Long logId) {
        String filePath = "";
        
        try {
            Connection connection = HbaseUtils.createConnection();
            filePath = HbaseUtils.scanByLogId(connection, logId);
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return filePath;
    }
    public static void downloadLogFromHbase(Long logId, OutputStream outputStream) {
        
        try {
            Connection connection = HbaseUtils.createConnection();
            HbaseUtils.scanByLogId(connection, logId, outputStream);
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    public static String getLogIdByUrl(String url){
        Matcher matcher = Pattern.compile("[runLogId|logId]=(\\d+)").matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    public static void logAnalysisByFile(boolean hasKeywordMatch, JSONObject ret, String filePath) {
        File file = new File(filePath);
        try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(file)))) {
            int waitSecondToLaunchAM = 0;
            Keywords keywords = YamlUtil.loadYaml("keywords.yaml", Keywords.class);
            String line;
            int rows = 0;
            boolean startFlag = false;
            boolean endFlag = false;
            String startStr = "spark-sql";
            int ignoreBeginningChar = 0;
            StringBuilder builder = new StringBuilder();
            List<String> errorList = new LinkedList<>();
            while ((line = bufferedReader.readLine()) != null) {
                rows++;
                String commandStr = line;
                if(commandStr.contains("INFO spark-sql   --master")){
                    ret.put("numOfSparkSubmit", ret.getIntValue("numOfSparkSubmit") + 1);
                }
                if (!startFlag && line.contains(startStr)) {
                    startFlag = true;
                    commandStr = line.substring(line.indexOf(startStr) + ignoreBeginningChar);
                }
                if (startFlag && !endFlag && reachEnd(line)) {
                    endFlag = true;
                    ret.put("originalCommand", builder.toString());
                }
                if (startFlag && !endFlag) {
                    builder.append(commandStr).append("\n");
                }
                boolean matchKeyword = false;
                if (hasKeywordMatch) {
                    CallerInfo analysisRowCaller = Profiler.registerInfo("bdp.bdpUtils.logAnalysis.row", "SparkMonitorApp", false, true);
                    for (Keyword keyword : keywords.getKeywordList()) {
                        if(matchKeywordByRow(line, keyword)){
                            matchKeyword = true;
                        }
                    }
                    Profiler.registerInfoEnd(analysisRowCaller);
                }
                if(!matchKeyword && errorList.size() < 20) {
                    if(Pattern.compile("error", Pattern.CASE_INSENSITIVE).matcher(line).find()){
                        errorList.add(line);
                    }
                }
                if (line.contains("state: ACCEPTED")) {
                    waitSecondToLaunchAM++;
                }
                if (!ret.containsKey("datetime")) {
                    Matcher matcher = Pattern.compile("(\\d{4}-\\d{2}-\\d{2}) \\d{2}:\\d{2}:\\d{2}").matcher(line);
                    if (matcher.find()) {
                        ret.put("datetime", matcher.group(1));
                    }
                }
                if (!ret.containsKey("url")) {
                    ret.putAll(findTrackingUrl(line));
                }
                if (!ret.containsKey("applicationMasterIp")) {
                    ret.putAll(matchKeyword(amPattern, "applicationMasterIp", line));
                }
                if (!ret.containsKey(LOG_ANALYSIS_HADOOP_USER)) {
                    ret.putAll(matchKeyword(hadoopUserPattern, LOG_ANALYSIS_HADOOP_USER, line));
                }
                if (!ret.containsKey("Logging events to")) {
                    ret.putAll(matchEventLog(line));
                }
                if(line.contains("平台日志")){
                    Matcher matcher = clusterPattern.matcher(line);
                    if(matcher.find()) {
                        ret.put("cluster", matcher.group(1));
                    }
                    Matcher matcher1 = actionIdPattern.matcher(line);
                    if(matcher1.find()) {
                        ret.put("actionId", matcher1.group(1));
                    }
                }
            }
            if (ret.containsKey("originalCommand")) {
                List<CreateTableBean> insertTables = BDPUtils.findInsertTable(ret.getString("originalCommand"), "T", "_");
                if (!insertTables.isEmpty()) {
                    StringBuilder builder1 = new StringBuilder();
                    for(CreateTableBean insertTable : insertTables) {
                        builder1.append(insertTable.getOriginalDBTableName()).append(",");
                    }
                    ret.put("outputTableName", builder1.substring(0, builder1.length()-1));
                }
                List<CreateTableBean> createTableBeans = BDPUtils.findCreateTableStatement(ret.getString("originalCommand"), "", "");
                if(!createTableBeans.isEmpty()) {
                    StringBuilder builder1 = new StringBuilder();
                    for(CreateTableBean createTableBean : createTableBeans) {
                        builder1.append(createTableBean.getOriginalDBTableName()).append(",");
                    }
                    ret.put("outputTableName", builder1.substring(0, builder1.length()-1));
                }

                ret.putAll(matchLastKeyword(Pattern.compile("spark\\.resource\\.level=\"?(\\w+)\"?"), "HTRes", ret.getString("originalCommand")));
                ret.putAll(matchLastKeyword(Pattern.compile("spark\\.executor\\.cores=\"?(\\d+)\"?"), "ExeCores", ret.getString("originalCommand")));
                ret.putAll(matchLastKeyword(Pattern.compile("spark\\.executor\\.memory=\"?(\\w+)\"?"), "ExeMem", ret.getString("originalCommand")));
                ret.putAll(matchLastKeyword(Pattern.compile("spark\\.executor\\.instances=\"?(\\d+)\"?"), maxExe, ret.getString("originalCommand")));
                ret.putAll(matchLastKeyword(Pattern.compile("spark\\.dynamicAllocation\\.maxExecutors=\"?(\\d+)\"?"), maxExe, ret.getString("originalCommand")));
                ret.putAll(matchLastKeyword(Pattern.compile("spark\\.sql\\.shuffle\\.partitions=\"?(\\d+)\"?"), "Parts", ret.getString("originalCommand")));
                ret.putAll(matchLastKeyword(Pattern.compile("spark\\.shuffle\\.rss\\.v3\\.enabled=\"?(\\w+)\"?"), "RssV3", ret.getString("originalCommand")));
            }
            ret.put("errorList", errorList);
            if (rows < 2) {
                logger.info("日志是空文件!");
            }
            if (hasKeywordMatch) {
                ret.put(KEYWORD, keywords.getKeywordList());
            }
            ret.put(WAIT_AM_DURATION, waitSecondToLaunchAM);
            ret.put("rows", rows);
            ret.put("isSuccess", "true");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static boolean matchKeywordByRow(String line, Keyword keyword) {
        if (keyword.getMatchedContent().size() < keyword.getSamples()) {
            Matcher matcher = Pattern.compile(keyword.getKeyword(), Pattern.CASE_INSENSITIVE).matcher(line);
            if (matcher.find()) {
                String group = line;
                if (keyword.getGroup() != null) {
                    group = matcher.group(keyword.getGroup());
                }
                String escapeLine = keyword.isHtmlEncode() ? StringEscapeUtils.escapeHtml4(group) : group;
                keyword.getMatchedContent().add(escapeLine);
                return true;
            }
        }
        return false;
    }

    public static String removeAlterTableCmd(Session session, String log){
        Pattern compile = Pattern.compile("(alter|drop|truncate)\\s+table(.|\n|\r){0,500}?;", Pattern.CASE_INSENSITIVE);
        Matcher matcher = compile.matcher(log);
        while (matcher.find()) {
            String group = matcher.group(0);
            WebSocket.send(session, "INFO: find alter table keyword.");
            log = log.replace(group, "");
        }
        return log;
    }

    public static String removeLocationKeyword(Session session, String log){
        Pattern compile = Pattern.compile("(location\\s+(.|\\n|\\r)*?)\\s", Pattern.CASE_INSENSITIVE);
        Matcher matcher = compile.matcher(log);
        while (matcher.find()) {
            String group = matcher.group(1);
            WebSocket.send(session, "INFO: find location keyword.");
            log = log.replace(group, "");
        }
        return log;
    }

    public static String rewriteSqlCluster(String log){
        log = log.replace("spark-sql-cluster", "spark-sql");
        log = log.replaceAll("--conf\\s+spark\\.submit\\.deployMode=\\w+", "");
        log = log.replaceAll("--class\\s+org\\.apache\\.spark\\.sql\\.hive\\.thriftserver\\.SparkSqlCluster", "");
        return log;
    }

    public static JSONObject rewriteSql(Session session, String log, String latestConf, String random, String newDBName) {
        JSONObject ret = new JSONObject();
        ret.put("isSuccess", "false");
        log = removeAlterTableCmd(session, log);
        log = removeLocationKeyword(session, log);
        log = rewriteSqlCluster(log);

        List<CreateTableBean> createTableBeans = findCreateTableStatement(log, newDBName, "spark_team_temp_erp_xn_spark_" + random + "_");
        List<CreateTableBean> insertTableBeans = findInsertTable(log, newDBName, "spark_team_temp_erp_xn_spark_" + random + "_");
        List<CreateTableBean> needCreateTable = new ArrayList<>();
        for (CreateTableBean insertTable : insertTableBeans) {
            if(!findTable(createTableBeans, insertTable.getOriginalDBTableName())){
                log = replaceCreateTableName(insertTable.getOriginalDBTableName(), insertTable.getReplacementDBTableName(), log);
                needCreateTable.add(insertTable);
            }
        }
        log = replaceCreateTableName(createTableBeans, log);

        String originalDBTableName = StringUtils.join(getOriginalDBTableName(createTableBeans, insertTableBeans),",");
        String replaceDBTableName = StringUtils.join(getReplaceDBTableName(createTableBeans, insertTableBeans), ",");
        WebSocket.send(session, "INFO: Original table is " + originalDBTableName);
        WebSocket.send(session, "INFO: Replaced table is " + replaceDBTableName);

        StringBuilder builder = new StringBuilder(log);
        int use_ = Math.max(log.indexOf("USE "), log.indexOf("use "));
        int i = log.indexOf(";", use_);
        needCreateTable.forEach(item -> {
            builder.insert(i + 1, "\nDROP TABLE IF EXISTS " + item.getReplacementDBTableName() + ";\n"
                    + "CREATE TABLE IF NOT EXISTS " + item.getReplacementDBTableName()
                    + " LIKE " + item.getOriginalDBTableName() + ";\n");
        });
        createTableBeans.forEach(item -> {
            builder.insert(i + 1, "\nDROP TABLE IF EXISTS " + item.getReplacementDBTableName()+";\n");
        });

        builder.insert(0, StringUtils.join(Scheduler.commonBean.getForkBuffaloTaskAppendParamsAtStart(),"\n") + "\n");
        if (StringUtils.isNotEmpty(latestConf)) {
            latestConf = latestConf.replaceAll("\n", " ");
            int i1 = builder.toString().indexOf(" -e ");
            builder.insert(i1, " " + latestConf);
        }
        ret.put("originalDBTableName", originalDBTableName);
        ret.put("targetTableName", replaceDBTableName);
        ret.put("rewrote", builder.toString());
        ret.put("isSuccess", "true");
        return ret;
    }

    static String insertKeyword = "insert\\s+(?:overwrite|into)\\s+(?:table\\s+)?(?:((?:\\w+\\.)?(\\w+))\\s)";

    public static boolean findTable(List<CreateTableBean> target, String tableDbName) {
        for(CreateTableBean createTableBean : target) {
            if(tableDbName.equals(createTableBean.getOriginalDBTableName())) {
                return true;
            }
        }
        return false;
    }

    public static List<CreateTableBean> findInsertTable(String log, String newDBName, String newTablePrefix) {
        Pattern compile1 = Pattern.compile(insertKeyword, Pattern.CASE_INSENSITIVE);
        Matcher matcher1 = compile1.matcher(log);
        List<CreateTableBean> insertTableBeans = new ArrayList<>();
        while (matcher1.find()) {
            String originalDBTableName = matcher1.group(1);
            String originalTableName = matcher1.group(2);
            String replacement = newDBName + "." + newTablePrefix + originalTableName;

            CreateTableBean createTableBean = new CreateTableBean();
            createTableBean.setOriginalDBTableName(originalDBTableName);
            createTableBean.setOriginalTableName(originalTableName);
            createTableBean.setReplacementDBTableName(replacement);
            insertTableBeans.add(createTableBean);
//            if (bean.getTableName() == null) {
//                bean.setTableName(matcher1.group(2));
//                bean.setTableNameWithSuffix(matcher1.group(1));
//                bean.setWholeText(matcher1.group(0));
//                bean.setSuccess(true);
//            } else {
//                bean.setErrMsg("Has a multi-output table");
//                break;
//            }
        }
//        if (bean.getTableName() == null) {
//            bean.setErrMsg("Output table not found in the source script");
//        }
        return insertTableBeans;
    }

    private static String createTableKeyword = "create\\s+(external\\s+)?table\\s+(if\\s+not\\s+exists\\s+)?(((\\w+)\\.)?(\\w+))";

    public static List<CreateTableBean> findCreateTableStatement(String input, String newDBName, String newTablePrefix) {
        Pattern pattern = Pattern.compile(createTableKeyword, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(input);
        List<CreateTableBean> createTableBeans = new ArrayList<>();
        while (matcher.find()) {
            String originalDBTableName = matcher.group(3);
            String originalTableName = matcher.group(6);
            String replacement = newDBName + "." + newTablePrefix + originalTableName;
            CreateTableBean createTableBean = new CreateTableBean();
            createTableBean.setOriginalDBTableName(originalDBTableName);
            createTableBean.setOriginalTableName(originalTableName);
            createTableBean.setReplacementDBTableName(replacement);
            createTableBeans.add(createTableBean);
//            String group = matcher.group();
//            String replace = group.replace(originalDBTableName, replacement);
//            outputTable.setSuccess(true);
//            input = input.replace(group, replace);
//            StringBuilder builder = new StringBuilder(input);
//            builder.insert(matcher.start(), "DROP TABLE IF EXISTS " + replacement + ";\n");
//            outputTable.setWholeText(builder.toString());
//            outputTable.setTableName(originalTableName);
//            outputTable.setTableNameWithSuffix(originalDBTableName);
//            outputTable.getTableNamesWithSuffix().add(originalDBTableName);
        }
        return createTableBeans;
    }

    public static Set<String> getOriginalDBTableName(List<CreateTableBean> createTableBeans, List<CreateTableBean> insertTableBeans){
        Set<String> originalTableName = new HashSet<>();
        createTableBeans.forEach(item -> originalTableName.add(item.getOriginalDBTableName()));
        insertTableBeans.forEach(item -> originalTableName.add(item.getOriginalDBTableName()));
        return originalTableName;
    }

    public static Set<String> getReplaceDBTableName(List<CreateTableBean> createTableBeans, List<CreateTableBean> insertTableBeans){
        Set<String> replacementDbTableName = new HashSet<>();
        createTableBeans.forEach(item -> replacementDbTableName.add(item.getReplacementDBTableName()));
        insertTableBeans.forEach(item -> replacementDbTableName.add(item.getReplacementDBTableName()));
        return replacementDbTableName;
    }

    public static String replaceCreateTableName(String originalDBTableName, String replaceDBTableName, String script) {
        Matcher matcher = Pattern.compile("\\s" + originalDBTableName + "(\\s|\\()").matcher(script);
        while(matcher.find()) {
            String group = matcher.group();
            String s = group.replaceAll(originalDBTableName, replaceDBTableName);
            script = script.replace(group, s);
        }
        return script;
    }

    public static String replaceCreateTableName(List<CreateTableBean> createTableBeans, String script) {
        for (CreateTableBean createTableBean : createTableBeans) {
            script = replaceCreateTableName(createTableBean.getOriginalDBTableName(), createTableBean.getReplacementDBTableName(), script);
        }
        return script;
    }
    /**
     * Return obj is
     * {"msg":"","scriptPath":"app_ea_sla_warning_trader_d.py","runType":"normal","instanceTypeStr":"normal",
     * "taskNodeType":2,"cycle":"2019-12-03","isLeaf":false,"operator":"","duration":4395,"instanceId":51571867,
     * "taskNodeName":"client-*************","modified":"2019-12-04 07:33:56","runTime":"2019-12-04 06:20:41",
     * "runStatus":"success","taskNodeId":154,"scriptVersion":"1000","instanceCreated":"2019-12-04 06:20:40",
     * "runLogId":118665884,"level":0,"scheTime":"2019-12-04 06:01:00","created":"2019-12-04 06:20:40",
     * "instanceType":2,"taskInsId":51571867,"env":"JDHXXXXX_CLUSTER_NAME=10k,JDHXXXXX_USER=mart_coo,
     * JDHXXXXX_QUEUE=bdp_jmart_coo_union.bdp_jmart_coo_formal,TEAM_USER=mart_coo,BEE_HIVETASK_EXEC_ENGINE=spark",
     * "script":174858,"suspended":0,"queueTime":"2019-12-04 06:20:32","args":"","deleted":0,"taskNodeRun":2444,
     * "logFile":"task/2019-12-04/73791312_action.20191204062041.txt","runBatchNumber":"node107.39_1574924366376",
     * "scriptName":"app_ea_sla_warning_trader_d.py","endTime":"2019-12-04 07:33:56","durationStr":"01:13:15"}
     *
     * @param buffalo4TaskId
     * @param logId
     * @return
     */
    public static JSONObject getLatestLog(Long buffalo4TaskId, Long logId) {
        JSONArray logs = BDPUtils.getBuffalo4Logs("dp.jd.com", buffalo4TaskId + "", null,"single", null, "current");
        if (logId == null) {
            for (int i = 0; i < logs.size(); i++) {
                JSONObject jsonObject = logs.getJSONObject(i);
                if (jsonObject.containsKey("runLogId")) {
                    return jsonObject;
                }
            }
        } else {
            for (int i = 0; i < logs.size(); i++) {
                JSONObject jsonObject = logs.getJSONObject(i);
                if (logId.equals(jsonObject.getLongValue("runLogId"))) {
                    return jsonObject;
                }
            }
        }
        return null;
    }

    public static Map<String, String> analysisLogByLogId(String taskId, String buffaloVersion, String buffaloLogId,
                                                         String logType, String buffaloDomain, boolean hasKeyWordMatch) {
        long start = System.currentTimeMillis();
        Thread.currentThread().setName("AnalysisLog-" + buffaloLogId);
        Map<String, String> logInfoMap = new LinkedHashMap<>();
        CallerInfo analysisLogByLogIdCaller = Profiler.registerInfo("bdp.troubleshooting.analysisLogByLogId", "SparkMonitorApp", false, true);
        try {
            if (StringUtils.isNumeric(buffaloLogId) && StringUtils.isNumeric(taskId)) {
                Long logIdLong = Long.parseLong(buffaloLogId);
                Long taskIdLong = Long.parseLong(taskId);
                Map<String, String> envMap = new HashMap<>();
                List<Map<String, Object>> runLogByTaskId1 = Buffalo4Dao.getRunLogByTaskId(taskIdLong, logIdLong, null);
                for (Map<String, Object> stringObjectMap : runLogByTaskId1) {
                    for (String env : stringObjectMap.get("env").toString().split(",")) {
                        String[] split = env.split("=");
                        if(split.length == 2) {
                            envMap.put(split[0], split[1]);
                        }
                    }
                }
                logInfoMap.putAll(envMap);
                JSONObject buffaloLog = BDPUtils.logAnalysis(BDPUtils.getUrlBuffaloVersionMap(buffaloDomain,
                        buffaloLogId, logType, buffaloVersion), hasKeyWordMatch, logIdLong, logType);
                if (!buffaloLog.isEmpty() && buffaloLog.getBooleanValue("isSuccess")) {
                    String appId = null;
                    String appIdWithoutPrefix = buffaloLog.getString(LOG_ANALYSIS_APPID);
                    String jdosAppid = buffaloLog.getString(LOG_ANALYSIS_JDOS_APPID);
                    if(StringUtils.isNotEmpty(appIdWithoutPrefix)) {
                        appId = "application_" + appIdWithoutPrefix;
                    } else if(StringUtils.isNotEmpty(jdosAppid)) {
                        appId = jdosAppid;
                    }
                    String url = buffaloLog.getString("url");

                    if(appId != null) {
                        logInfoMap.put("appId", appId);
                    }
                    if(buffaloLog.containsKey("cluster")) {
                        logInfoMap.put("cluster", buffaloLog.getString("cluster"));
                    }
                    if(buffaloLog.containsKey("actionId")) {
                        logInfoMap.put("actionId", buffaloLog.getString("actionId"));
                    }
                    if(StringUtils.isEmpty(logType)){
                        logInfoMap.put("客户端资源", "<a target=\"_blank\" href=\"http://dp.jd.com/buffalo4/monitor/cgroup/index.html?taskVersion=1&logId="+buffaloLogId+"&taskId="+taskId+"\">资源使用情况</a>");
                        logInfoMap.put("日志链接", "<a target=\"_blank\" href=\"" + buffaloLog.getString("logUrl") + "\">日志下载</a>");
                    }
                    logInfoMap.put("EventLogPath", buffaloLog.getString(LOG_ANALYSIS_JDOS_EVENT_PATH));
                    logInfoMap.put("TrackingUrl", url == null ? "未找到tracking url关键字" : "<a target=\"_blank\" href=\""+url+"\">"+url+"</a>");
                    logInfoMap.put("启动AM耗时", buffaloLog.getString(WAIT_AM_DURATION) + "s");
                    logInfoMap.put("OutputTableName", buffaloLog.getString("outputTableName"));
                    logInfoMap.put("Hadoop User", buffaloLog.getString(LOG_ANALYSIS_HADOOP_USER));
                    logInfoMap.put("OriginalCommand", buffaloLog.getString("originalCommand"));
                    logInfoMap.put("HTRes", buffaloLog.getString("HTRes"));
                    logInfoMap.put("ExeCores", buffaloLog.getString("ExeCores"));
                    logInfoMap.put("ExeMem", buffaloLog.getString("ExeMem"));
                    logInfoMap.put("MaxExe", buffaloLog.getString("MaxExe"));
                    logInfoMap.put("Parts", buffaloLog.getString("Parts"));
                    logInfoMap.put("RssV3", buffaloLog.getString("RssV3"));

                    List<Keyword> keywords = buffaloLog.getObject(KEYWORD, List.class);
                    if(keywords != null) {
                        for (Keyword entry : keywords) {
                            logInfoMap.putAll(fillSolution(entry, buffaloLog, envMap));
                        }
                    }

                    if ("true".equals(buffaloLog.getString("RssV3"))) {
                        logInfoMap.put("RSS上下线状态", "<label style=\"color: red\">已上线v3</label>");
                    }
                }
                LinkedList errorList = buffaloLog.getObject("errorList", LinkedList.class);
                if(errorList != null) {
                    logInfoMap.put("errorList", String.join("<br/>", errorList));
                }
                logInfoMap.put("Rows", buffaloLog.getString("rows"));
                logInfoMap.put("Elapsed Download Log", buffaloLog.getString("Elapsed Download Log"));
                logInfoMap.put("Elapsed Analysis Log", buffaloLog.getString("Elapsed Analysis Log"));
            }
            logInfoMap.put("Elapsed Second", String.valueOf((System.currentTimeMillis() - start) / 1000));
        } catch (Exception e) {
            Profiler.functionError(analysisLogByLogIdCaller);
            throw e;
        } finally {
            Profiler.registerInfoEnd(analysisLogByLogIdCaller);
        }
        return logInfoMap;
    }

    public static void syncToJimDB(String taskId, String logid, Map<String, String> data){
        if(data == null || data.isEmpty()) {
            return;
        }
        // 将value为空的entry清除掉
        Iterator<Map.Entry<String, String>> iterator;
        for(iterator = data.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<String, String> next = iterator.next();
            if(next.getValue() == null) {
                iterator.remove();
            }
        }
        JimDBUtils.JIM_CLIENT.hMSet(taskId + "_" + logid, data);
    }

    public static String getApplicationDurationStr(JSONObject jsonObject) {
        String durationStr = null;
        try {
            boolean isClusterMode = jsonObject.containsKey("duration");
            /**
//                             http://hope.sparkhs.jd.com/api/v1/applications/application_4481409102802_37813822/1
//                             当isClusterMode = true时，数据如下
//                             {
//                                 "attemptId": "1",
//                                     "startTime": "2020-12-29T03:43:30.505GMT",
//                                     "endTime": "2020-12-29T03:44:09.882GMT",
//                                     "lastUpdated": "2020-12-29T03:44:10.471GMT",
//                                     "duration": 39377,
//                                     "sparkUser": "recpro",
//                                     "completed": true,
//                                     "appSparkVersion": "2.4.5.online-JD2.4.5.16-202012212053",
//                                     "endTimeEpoch": 1609213449882,
//                                     "lastUpdatedEpoch": 1609213450471,
//                                     "startTimeEpoch": 1609213410505
//                             }
//                             http://10k.sparkhs.jd.com/api/v1/applications/application_2082859101801_17547006
//                             当isClusterMode = false时，数据如下
//                             {
//                                 "id": "application_2082859101801_17547006",
//                                     "name": "[xuefei17,10.198.220.71,BUFFALO4,3374902474348429314,301785259,no]_SparkSQL::10.198.220.71",
//                                     "attempts": [
//                                 {
//                                     "startTime": "2020-12-29T22:00:21.255GMT",
//                                         "endTime": "2020-12-29T22:11:33.641GMT",
//                                         "lastUpdated": "2020-12-29T22:11:33.757GMT",
//                                         "duration": 672386,
//                                         "sparkUser": "mart_coo",
//                                         "completed": true,
//                                         "appSparkVersion": "3.0.1.online-JD3.0.1.2-202012181329",
//                                         "endTimeEpoch": 1609279893641,
//                                         "lastUpdatedEpoch": 1609279893757,
//                                         "startTimeEpoch": 1609279221255
//                                 }
//  ]
//                             }
             */
            double durationMinute = isClusterMode ?
                    jsonObject.getDoubleValue("duration") / 1000 / 60 :
                    jsonObject.getJSONArray("attempts").getJSONObject(0).getDoubleValue("duration") / 1000 / 60;
            String durationFmt = new DecimalFormat("#,###.##").format(durationMinute);
            durationStr = durationFmt;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return durationStr;
    }

    public static Map<String, String> fillSolution(Keyword entry, JSONObject buffaloLog, Map<String, String> envMap) {
        Map<String, String> otherInfo = new LinkedHashMap<>();
        String keyword = entry.getKeyword();
        List<String> matchedContent = entry.getMatchedContent();
        String solutionLink = entry.getSolutionLink();
        if (!matchedContent.isEmpty()) {
            if(entry.getId() == 29) {
                Set<String> trackingApplicationSet = new HashSet<>();
                for(String row: matchedContent) {
                    int start = row.indexOf("/proxy/") + 7;
                    trackingApplicationSet.add(row.substring(start, row.length() - 1));
                }
                otherInfo.put("trackingURLs", String.join("<br>", matchedContent));
                StringBuilder builder = new StringBuilder();
                for (String applicationId : trackingApplicationSet) {
                    builder.append("<a target=\"_blank\" href=\"/sparkUI?cluster="
                            +envMap.get("JDHXXXXX_CLUSTER_NAME")
                            +"&logTime="+ buffaloLog.getString("datetime")
                            +"&appId="+applicationId+"\">"+applicationId+"</a>");
                    builder.append("<br>");
                }
                otherInfo.put("TransferEventLogTo spark-history.jd.com", builder.toString());
                if (trackingApplicationSet.size() > 1) {
                    otherInfo.put("Spark Job数过多", "<a style=\"color: red\" target=\"_blank\" href=\"https://cf.jd.com/pages/viewpage.action?pageId=278239925\">解决方案</a> ");
                }
            } else if (entry.getId() == 13 || entry.getId() == 15 || entry.getId() == 74 || entry.getId() == 77) {
                Pattern compile = Pattern.compile("(\\d+\\.\\d+\\.\\d+\\.\\d+)");
                StringBuilder builder = new StringBuilder();
                for(String row: matchedContent) {
                    Matcher matcher = compile.matcher(row);
                    builder.append(row);
                    if(matcher.find()) {
                        String ip = matcher.group(1);
                        builder
                        .append(" <a target=\"_blank\" href=\"http://jcmdb.jd.com/serviceTree?ip=" + ip + "\">J CMDB</a>")
                        .append(" <a target=\"_blank\" href=\"http://" + ip + ":8042/logs/\">NodeManager UI</a>")
                        .append(" <a target=\"_blank\" href=\"http://baizegra-offline.jd.com/d/GZjCKJQWz/1-dan-ji-jian-kong?orgId=2&var-node=" + ip + "\">白泽节点监控</a>")
                        .append(" <a target=\"_blank\" href=\"http://baizegra-offline.jd.com/d/2aZmUypGk/nmduo-tian-dui-bi?orgId=2&var-host="+ip+"&var-offset=1d&var-step=2m&from=1655481600000&to=1655567999000&refresh=&var-datasource=baize-cortex1-querier&var-cluster=hope&var-port=8040&var-port=50086\">NM多天对比</a>")
                        .append(" <a target=\"_blank\" href=\"http://baizegra-offline.jd.com/d/M6TSU2bMk/duo-tian-duo-ip-dui-bi?orgId=2&folderId=217&var-offset=1d&var-node=" + ip + "\">白泽IP多天对比监控</a>")
                        .append(" <a target=\"_blank\" href=\"http://origin.jd.com/monitor/mdc/ipMonitor?ip="+ip+"&frequency=second\">MDC监控</a>");
                    }
                    builder.append("<br/>");
                }
                otherInfo.put(keyword, builder.substring(0, builder.length() - 5));
            } else if (entry.getId() == 21) {
                //2022-06-01 11:59:12 INFO 22/06/01 11:59:12 [main] INFO SparkContext: Running Spark version 2.4.7.online-JD2.4.7.21-20220518-132400
                Pattern compile = Pattern.compile("JD(.*)");
                StringBuilder builder = new StringBuilder();
                for(String row: matchedContent) {
                    Matcher matcher = compile.matcher(row);
                    if(matcher.find()) {
                        String version = matcher.group(1);
                        builder.append(version);
                    }
                    builder.append("<br/>");
                }
                otherInfo.put(keyword, builder.substring(0, builder.length() - 5));
            } else if (entry.getId() == 55 ) {
                StringBuilder builder = new StringBuilder();
                boolean rssEnabled = false;
                String nodeLabel = "无";
                String yarnLevel = "无";
                String c2002 = "否";
                StringBuilder resourceLimit = new StringBuilder();
                for(String row : matchedContent) {
                    String s = StringEscapeUtils.unescapeXml(row);
                    int i = s.indexOf("?>");
                    if (i > 0) {
                        String substring = s.substring(i + 2);
                        InputStream stream = new ByteArrayInputStream(
                                substring.getBytes(StandardCharsets.UTF_8));
                        Properties properties = null;
                        try {
                            properties = parseXmlToProperties(stream);
                        } catch (ParserConfigurationException | SAXException | IOException e) {
                            e.printStackTrace();
                        }
                        if (properties != null) {
                            for (Object key : properties.keySet()) {
                                builder.append(key).append("=").append(properties.get(key)).append("<br/>");
                            }
                            rssEnabled = properties.getProperty("spark.shuffle.rss.enabled", "false").equals("true");
                            String dailyLimit = properties.getProperty("spark.nature.daily.time.task.limit.template.name", "");
                            String coreLimit = properties.getProperty("spark.nature.core.time.task.limit.template.name", "");
                            String dailyLimit2 = properties.getProperty("spark.nature.daily.task.limit.template.name", "");
                            String dailyLimit3 = properties.getProperty("nature.daily.core.time.limit.template.name", "");
                            nodeLabel = properties.getProperty("spark.yarn.am.nodeLabelExpression", "无");
                            yarnLevel = properties.getProperty("spark.hadoop.bdp.job.business-priority", "无");
                            c2002 = properties.getProperty("spark.hadoop.yarn.app.env", "");
                            String maxExec = properties.getProperty("spark.dynamicAllocation.maxExecutors");
                            if(StringUtils.isNotEmpty(dailyLimit)) {
                                resourceLimit.append("触发资源隔离，隔离标识: " + dailyLimit);
                            }
                            if(StringUtils.isNotEmpty(coreLimit)) {
                                resourceLimit.append("触发高峰限流，限流标识: " + coreLimit);
                            }
                            if(StringUtils.isNotEmpty(dailyLimit2)) {
                                resourceLimit.append("触发资源隔离，隔离标识2: " + dailyLimit2);
                            }
                            if(StringUtils.isNotEmpty(dailyLimit3)) {
                                resourceLimit.append("触发资源隔离，隔离标识3: " + dailyLimit3);
                            }
                            if(StringUtils.isNotEmpty(maxExec)) {
                                otherInfo.put(Constants.maxExe, maxExec);
                            }
                        }
                    } else {
                        builder.append(row);
                    }
                    builder.append("<br/>");
                }
                otherInfo.put(keyword, builder.substring(0, builder.length() - 5));
                otherInfo.putIfAbsent("RSS上下线状态", rssEnabled ? "<label style=\"color: red\">已上线</label>" : "未上线");
                otherInfo.put("资源隔离", StringUtils.isNotEmpty(resourceLimit) ? "<label style=\"color: red\">是</label>" : "否");
                if (StringUtils.isNotEmpty(resourceLimit)) {
                    otherInfo.put("资源隔离/降级", "<a style=\"color: red\" target=\"_blank\" href=\"https://cf.jd.com/pages/viewpage.action?pageId=1044034062\">公告</a> <label style=\"color: red\">"+resourceLimit+"</label>");
                }
                otherInfo.put("YARN标签", nodeLabel);
                otherInfo.put("YARN级别", yarnLevel);
                otherInfo.put("重保", c2002.contains("C2002") ? "<label style=\"color: red\">是</label>" : "否");
            } else if (entry.getId() == 32 ) {
                Pattern compile = Pattern.compile("JDHIVETASK_(.*)");
                String row = matchedContent.get(0);
                Matcher matcher = compile.matcher(row);
                if(matcher.find()) {
                    String hiveTaskVersion = matcher.group(1);
                    otherInfo.put("HiveTask版本", hiveTaskVersion);
                }
            } else {
                otherInfo.put(keyword,
                        (solutionLink == null ? "" : "<a style=\"color: red\" target=\"_blank\" href=\"" + solutionLink + "\">解决方案</a> ")
                                + String.join("<br/>", matchedContent));
            }
        }
        return otherInfo;
    }

    public static Properties parseXmlToProperties(InputStream content) throws ParserConfigurationException, SAXException, IOException {
        Properties properties = new Properties();
        DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
        org.w3c.dom.Document document = documentBuilder.parse(content);
        NodeList property = document.getElementsByTagName("property");
        for (int i = 0; i < property.getLength(); i++) {
            Node item = property.item(i);
            NodeList childNodes = item.getChildNodes();
            String key = null;
            String value = null;
            for (int j = 0; j < childNodes.getLength(); j++) {
                Node item1 = childNodes.item(j);
                if("name".equals(item1.getNodeName())) {
                    key = item1.getTextContent();
                } else if("value".equals(item1.getNodeName())) {
                    value = item1.getTextContent();
                }
            }
            if(key != null && value != null) {
                properties.setProperty(key, value);
            }
        }
        return properties;
    }
}
