package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.common.FileUtil;
import com.jd.common.util.StringUtils;

import javax.websocket.Session;
import java.io.*;
import java.util.HashSet;
import java.util.Set;
import java.util.logging.Logger;

public class ShellUtil {

    private static final Logger logger = Logger.getLogger(ShellUtil.class.getName());

    /**
     * 构建新的应用路径
     *
     * @param logTime 日志时间
     * @param appId   应用ID
     * @param cluster 集群
     * @return 构建结果，成功返回100，失败返回-1
     */
    public int buildNewAppPath(String logTime, String appId, String cluster) {
        try {
            String hdfsPath = runShellByFile(
                    String.format("hdfs dfs -ls -R %s/%s | grep %s | awk '{print $8}'",
                            getHdfsRoot(cluster), logTime, appId), appId);
            if (hdfsPath != null && hdfsPath.trim().length() > 0) {
                logger.info("old hdfs path :" + hdfsPath);
            } else {
                return -1;
            }
            String[] split = StringUtils.trim(hdfsPath).split("\n");
            for (int i = 0; i < split.length; i++) {
                String subPath = StringUtils.trim(split[i]);
                runShellByFile(
                        String.format("hdfs dfs -cp -f %s hdfs://ns100/user/spark/mylog/ ", subPath), i + "_" + appId);
            }

            return 100;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return -1;
//        return "重构失败，请检查集群和时间是否正确";
    }

    /**
     * 通过文件运行Shell命令
     *
     * @param cmd   shell命令
     * @param appId 应用ID
     * @return shell命令执行结果
     * @throws IOException 输入输出异常
     */
    public String runShellByFile(String cmd, String appId) throws IOException {
        File shTempFile = new File("/tmp/sparkui/sh_" + appId);
        File resultTempFile = new File("/tmp/sparkui/" + appId);
        String hdfsCmd =
                String.format("export JDHXXXXX_USER=dd_edw;" +
                        "export JDHXXXXX_CLUSTER_NAME=10k;" +
                        "export JDHXXXXX_QUEUE=bdp_jdw_bdr_union.bdp_jdw_bdr_major;" +
                        "source /software/servers/env/env.sh;" +
                        " %s> %s;", cmd, resultTempFile.getPath());

        logger.info("run cmd :" + hdfsCmd);
        FileUtil.putString(shTempFile, hdfsCmd, "UTF-8");
        runCmd("sh " + shTempFile.getPath(), null);
        return FileUtil.getContent(resultTempFile, "UTF-8");
    }

    public String getHdfsRoot(String cluster) {
        switch (cluster) {
            case "cairne":
            case "10k":
                return "hdfs://ns100/user/spark/log";
            case "hope":
            case "rexxar":
                return "hdfs://ns1002/user/spark/log_hope";
            case "tyrande":
                return "hdfs://ns1002/user/spark/log_ad";
            case "dcjdtrcc":
                return "hdfs://ns62001/user/spark/log";
            case "ysera":
                return "hdfs://ns83002/user/spark/log";
            case "move":
                return "hdfs://ns1002/user/spark/log";
            case "jdcfc":
                return "hdfs://ns33002/user/spark/log";
            default:
                return "hdfs://ns100/user/spark/log";
        }
    }

    public static boolean createDualRun(String taskIds, String dateStr) {
        try {
            String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/double_run/";
            String scriptName = "run.sh";
            String fileContent = toString(getTasks(taskIds));
            String filePath = baseDir + "taskids_" + System.currentTimeMillis() + ".txt";
            logger.info(String.format("=== 将待创建双跑的task集合保存到文件中: taskIds: %s , filePath: %s", taskIds, filePath));
            FileUtil.writeFile(filePath, fileContent, false);
            String result = ShellUtil.runCmdWithRes("sh " + baseDir + scriptName + " " + filePath + " " + dateStr);
            JSONObject js = JSON.parseObject(result);
            boolean status = 0 == js.getInteger("status");
            logger.info(String.format("=== 创建双跑任务的sh命令执行结果, taskIds: %s, status:%s", taskIds, status));
            return status;
        } catch (Exception e) {
            logger.info(String.format("=== 创建双跑任务的sh命令执行异常： taskId: %s, exception: %s", taskIds, CommonUtil.exceptionToString(e)));
            return false;
        }
    }

    public static boolean runDualTasks(String taskIds) {
        try {
            String fileContent = toString(getTasks(taskIds));
            String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/run_dual_run/";
            String filePath = baseDir + "run_taskids_" + System.currentTimeMillis() + ".txt";
            String scriptName = "run.sh";
            FileUtil.writeFile(filePath, fileContent, false);
            logger.info(String.format("=== 将触发双跑运行的task集合保存到文件中: taskIds: %s, filePath: %s", taskIds, filePath));
            String result = ShellUtil.runCmdWithRes("sh " + baseDir + scriptName + " false file://" + filePath);
            JSONObject js = JSON.parseObject(result);
            Integer status = js.getInteger("status");
            logger.info(String.format("=== 触发双跑运行的sh命令执行结果, taskIds: %s, status:%s", taskIds, status));
            return status == 0;
        } catch (Exception e) {
            logger.info(String.format("=== 触发双跑运行的sh命令执行异常： taskIds: %s, exception: %s", taskIds, CommonUtil.exceptionToString(e)));
            return false;
        }
    }

    public static String toString(Set<Long> taskids) {
        StringBuilder builder = new StringBuilder();
        for (Long taskid : taskids) {
            builder.append(taskid).append("\n");
        }
        return builder.toString();
    }

    public static Set<Long> getTasks(String str) {
        Set<Long> tasks = new HashSet<>();
        String[] split = str.split(",|\\n|\\s+");
        for (String s : split) {
            try {
                tasks.add(Long.parseLong(s));
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        return tasks;
    }

    public static Integer runCmd(String cmd, Session session) {
        try {
            WebSocket.send(session, "开始执行命令:" + cmd);
            //主要在这步写入后调用命令
            Process process = Runtime.getRuntime().exec(cmd);
            try (PrintWriter printWriter =
                         new PrintWriter(
                                 new BufferedWriter(new OutputStreamWriter(process.getOutputStream())), true);
                 BufferedReader read =
                         new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                WebSocket.send(session, cmd);
                String line;
                while ((line = read.readLine()) != null) {
                    WebSocket.send(session, line);
                }
            }
            int runStatus = process.waitFor();
            WebSocket.send(session, " 执行状态：" + runStatus);
            return runStatus;
        } catch (Exception e) {
            logger.info(e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 执行shell脚本并返回执行结果（执行状态，正常输出，错误输出）
     * {"status": 0,
     * "normalOut": "正常输出",
     * "errorOut": "错误输出"
     * }
     * <p>
     * status等于0表示执行成功；等于1表示执行失败
     *
     * @param cmd
     * @return
     */
    public static String runCmdWithRes(String cmd) {
        JSONObject result = new JSONObject();
        try {
            StringBuffer logBuffer = new StringBuffer();
            StringBuffer errorBuffer = new StringBuffer();
            logger.info("开始执行命令:" + cmd);
            //主要在这步写入后调用命令
            Process process = Runtime.getRuntime().exec(cmd);
            try (BufferedReader read =
                         new BufferedReader(new InputStreamReader(process.getInputStream()));
                 BufferedReader error = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                logger.info(cmd);
                String line;
                while ((line = read.readLine()) != null) {
                    logBuffer.append(line).append('\n');
                    logger.info("line : " + line);
                }

                while ((line = error.readLine()) != null) {
                    errorBuffer.append(line).append('\n');
                    logger.info("error : " + line);
                }
            }
            // 方法阻塞, 等待命令执行完成（成功会返回0）
            int runStatus = process.waitFor();
            result.put("status", runStatus);
            result.put("normalOut", logBuffer.toString());
            result.put("errorOut", errorBuffer.toString());
            logger.info(cmd + " 执行状态：" + runStatus);
            return result.toString();
        } catch (Exception e) {
            logger.info(e.getMessage());
            e.printStackTrace();
            result.put("status", 1);
        }
        return result.toJSONString();
    }
}
