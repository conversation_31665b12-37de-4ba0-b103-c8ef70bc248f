package com.jd.bdp.spark.web;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.bean.domain.CommonOpResult;
import com.jd.bdp.common.Buffalo4TaskManager;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;

import static com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum.UPGRADED;
import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;


/**
 * spark升级-创建双跑任务实例
 */
@WebServlet("/sparkUpgradeCreateInstance")
public class SparkUpgradeCreateInstanceController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeCreateInstanceController.class.getName());
    
    
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String responseType = req.getParameter("responseType");
        String originTaskId = req.getParameter("originTaskId");
        String doubleRunTaskId = StringUtils.trimToNull(req.getParameter("taskid"));
        String follower = StringUtils.trimToNull(req.getParameter("follower"));
        String message = createBuffaloInstance(originTaskId, doubleRunTaskId, follower);

        if("json".equalsIgnoreCase(responseType)) {
            resp.setContentType("text/html;charset=utf-8");
            PrintWriter writer = resp.getWriter();
            writer.write(message);
        } else {
            req.setAttribute("msg", message);
            req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
        }
    }

    /**
     * 运行双跑任务： 判断对应的原始任务是否已经升级，如果已升级则跳过运行双跑任务
     * @param originTaskId 原始任务id
     * @param doubleRunTaskId 双跑任务id
     * @param follower 负责人
     * @return
     */
    private String createBuffaloInstance(String originTaskId, String doubleRunTaskId, String follower){
        logger.info(String.format("=== 开始创建双跑任务实例: originTaskId: %s, doubleRunTaskId: %s, follower: %s", originTaskId, doubleRunTaskId, follower));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        JSONObject responseObj = new JSONObject();
        try {
            if(StringUtils.isBlank(originTaskId)){
                logger.info("=== originTaskId为空，跳过创建双跑任务实例!");
                responseObj.put("code", 1);
                responseObj.put("message", "originTaskId为空，跳过创建双跑任务实例!");
                return responseObj.toJSONString();
            }
            
            if(StringUtils.isBlank(doubleRunTaskId)){
                logger.info("=== doubleRunTaskId为空，跳过创建双跑任务实例!");
                responseObj.put("code", 1);
                responseObj.put("message", "doubleRunTaskId为空，跳过创建双跑任务实例!");
                return responseObj.toJSONString();
            }

            SparkUpgradeTaskMapper taskMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeTaskMapper.class);
            SparkUpgradeTaskBean taskBean = taskMapper.selectOneByTaskId(originTaskId);
            if(taskBean == null){
                logger.info(String.format("=== originTaskId: %s, 在数据库中不存在, 跳过创建双跑任务实例!", originTaskId));
                responseObj.put("code", 1);
                responseObj.put("message", String.format("originTaskId: %s在数据库中不存在, 跳过创建双跑任务实例!", originTaskId));
                return responseObj.toJSONString();
            }
            
            if(UPGRADED.getCode().equalsIgnoreCase(taskBean.getStatus())){
                logger.info(String.format("=== originTaskId: %s, 原始任务已经升级，跳过创建双跑任务实例!", originTaskId));
                responseObj.put("code", 1);
                responseObj.put("message", String.format("originTaskId: %s, 原始任务已经升级，跳过创建双跑任务实例!", originTaskId));
                return responseObj.toJSONString();
            }else{
                // 在创建双跑实例前，需要随机改写环节的运行节点
                /*CommonOpResult modifyResult = modifyActionNode(originTaskId, doubleRunTaskId);
                if(!modifyResult.isSuc()){
                    responseObj.put("code", 1);
                    responseObj.put("modifyActionResult", String.format("originTaskId: %s, 修改运行节点失败, 原因:%s", originTaskId, modifyResult.getMessage()));
                }else{
                    responseObj.put("code", 0);
                    responseObj.put("modifyActionResult", String.format("originTaskId: %s, 修改运行节点成功, 详情: %s", originTaskId, modifyResult.getMessage()));
                }*/
                logger.info(String.format("=== skip modifyActionNode, originTaskId: %s", originTaskId));
                
                JSONObject createResult = Buffalo4TaskManager.buffalo4CreateTaskInstance(Integer.parseInt(doubleRunTaskId),"", sdf.format(Calendar.getInstance().getTime()), follower);
                logger.info(String.format("=== originTaskId: %s, doubleRunTaskId: %s, 创建双跑任务实例响应: %s", originTaskId, doubleRunTaskId, createResult.toJSONString()));
                responseObj.put("createInstResult", createResult);
                return responseObj.toJSONString();
            }
        } catch (Exception e) {
            logger.info(String.format("=== originTaskId: %s, double_run_task_id: %s, 创建双跑任务实例发生异常!!! %s", originTaskId, doubleRunTaskId, CommonUtil.exceptionToString(e)));
            responseObj.put("code", 1);
            responseObj.put("message", String.format("创建双跑任务实例发生异常: %s", CommonUtil.exceptionToString(e)));
            return responseObj.toJSONString();
        }
    }

    /**
     * 修改任务环节的运行节点
     * @param doubleRunTaskId
     * @return
     */
    private CommonOpResult modifyActionNode(String originTaskId, String doubleRunTaskId){
        try {
            int mod = Integer.parseInt(doubleRunTaskId) % 2;
            JSONObject actionListByTaskId = Buffalo4TaskManager.getActionListByTaskId(doubleRunTaskId);
            assert !actionListByTaskId.isEmpty();
            JSONObject jsonObject = actionListByTaskId.getJSONObject("obj");
            JSONArray actionList = jsonObject.getJSONArray("actionList");
            if(actionList == null || actionList.size() == 0){
                logger.info(String.format("=== originTaskId: %s, doubleRunTaskId: %s, 双跑任务环节为空，跳过修改环节运行节点", originTaskId, doubleRunTaskId));
                return new CommonOpResult(false, "双跑任务环节为空，跳过修改环节运行节点");
            }
            
            for (int i = 0; i < actionList.size(); i++) {
                JSONObject actionObject = actionList.getJSONObject(i);
                Integer actionId = actionObject.getInteger("actionId");
                Integer setType = actionObject.getInteger("cgroupSetType") == null ? 105: actionObject.getInteger("cgroupSetType");
                Integer taskNodeId = mod == 0 ? 1402 : 1302;
                JSONObject response = Buffalo4TaskManager.buffalo5TaskActionModify(Integer.parseInt(doubleRunTaskId), actionId, 3, taskNodeId, Buffalo4TaskManager.CgroupConfigEnum.getFrom(setType));
                Integer code = response.getInteger("code");
                String message = response.getString("message");
                if(code != 0){
                    logger.info(String.format("=== originTaskId: %s, doubleRunTaskId: %s, actionId:%s, 修改双跑任务的环节运行节点失败, 原因: %s",originTaskId, doubleRunTaskId,  actionId, message));
                    return new CommonOpResult(false, String.format("修改双跑任务的环节运行节点失败, 原因:%s", message));
                }else{
                    logger.info(String.format("=== originTaskId: %s, doubleRunTaskId: %s, actionId:%s, 修改双跑任务的环节运行节点成功, 结果: %s",originTaskId, doubleRunTaskId,  actionId, message));
                }
            }
            
            logger.info(String.format("=== originTaskId: %s, doubleRunTaskId: %s, 双跑任务所有环节运行节点修改完毕, 环节数量: %s",originTaskId, doubleRunTaskId, actionList.size()));
            return new CommonOpResult(true, "修改双跑任务的环节运行节点成功");
        } catch (Exception e) {
            logger.info(String.format("=== originTaskId: %s, doubleRunTaskId: %s, 修改任务环节的运行节点异常! exception: %s",originTaskId, doubleRunTaskId, CommonUtil.exceptionToString(e)));
            return new CommonOpResult(false, String.format("修改任务环节的运行节点异常: %s", CommonUtil.exceptionToString(e)));
        }
    }
}
