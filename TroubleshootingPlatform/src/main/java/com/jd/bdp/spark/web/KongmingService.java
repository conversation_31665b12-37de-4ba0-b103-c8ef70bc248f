package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.KongMingRecordBean;
import com.jd.bdp.bean.MysqlBean;
import com.jd.bdp.bean.SQLType;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.common.YamlUtil;
import com.jd.bdp.scheduler.ForkBuffaloTaskService;
import com.jd.common.util.StringUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.BooleanUtils;

import java.sql.*;
import java.util.*;
import java.util.logging.Logger;

import static com.jd.bdp.bean.Constants.SPARK_BUFFALO_LOG_INFO;
import static com.jd.bdp.bean.Constants.SPARK_BUFFALO_TASK_INFO;

public class KongmingService {

    private static final Logger logger = Logger.getLogger(KongmingService.class.getName());

    static {
        try {
            Class.forName("com.mysql.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }

    /**
     * 执行更新操作
     * @param sql SQL语句
     * @param dbName 数据库名称
     * @param params 参数列表
     * @return 受影响的行数
     * @throws RuntimeException 运行时异常
     */
    public static int executeUpdate(String sql, String dbName, Object... params) {
        CallerInfo mysqlUpdateCaller = Profiler.registerInfo("bdp.mysql.update", "SparkMonitorApp", false, true);
        Connection root = null;
        try {
            MysqlBean mysqlBean = YamlUtil.loadYaml("db.yaml", MysqlBean.class);
            String defaultDB = StringUtils.defaultIfEmpty(dbName, mysqlBean.getDefaultDB());
            Map<String, String> dbAccount = mysqlBean.getDb().get(defaultDB);
            root = DriverManager.getConnection(dbAccount.get("url"), dbAccount.get("u"), dbAccount.get("p"));
            PreparedStatement preparedStatement = root.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                if (params[i] instanceof String) {
                    preparedStatement.setString(i + 1, (String) params[i]);
                } else if (params[i] instanceof Integer) {
                    preparedStatement.setInt(i + 1, (Integer) params[i]);
                } else if (params[i] instanceof Long) {
                    preparedStatement.setLong(i + 1, (Long) params[i]);
                } else if(params[i] instanceof SQLType) {
                    preparedStatement.setNull(i + 1, (int) params[i]);
                } else if(params[i] instanceof Timestamp) {
                    preparedStatement.setTimestamp(i + 1, (Timestamp) params[i]);
                } else if(params[i] instanceof Double) {
                    preparedStatement.setDouble(i + 1, (Double) params[i]);
                } else {
                    throw new IllegalArgumentException("Unknown data type. Data Type: " + params[i]
                            + " sql: " + sql + " params: " + JSON.toJSONString(params));
                }
            }
            return preparedStatement.executeUpdate();
        } catch (SQLException e) {
            Profiler.functionError(mysqlUpdateCaller);
            throw new RuntimeException("sql = " + sql + " params: " + JSON.toJSONString(params) + " cause = "
                    + e.getMessage(), e);
        } finally {
            if (root != null) {
                try {
                    root.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            Profiler.registerInfoEnd(mysqlUpdateCaller);
        }
    }

    /**
     * 执行更新操作
     * @param sql 要执行的SQL语句
     * @param dbName 数据库名称
     * @return 更新的行数
     * @throws RuntimeException 执行更新操作失败时抛出
     */
    public static int executeUpdate(String sql, String dbName) {
        CallerInfo mysqlUpdateCaller = Profiler.registerInfo("bdp.mysql.update", "SparkMonitorApp", false, true);
        Connection root = null;
        try {
            MysqlBean mysqlBean = YamlUtil.loadYaml("db.yaml", MysqlBean.class);
            String defaultDB = StringUtils.defaultIfEmpty(dbName, mysqlBean.getDefaultDB());
            Map<String, String> dbAccount = mysqlBean.getDb().get(defaultDB);
            root = DriverManager.getConnection(dbAccount.get("url"), dbAccount.get("u"), dbAccount.get("p"));
            PreparedStatement preparedStatement = root.prepareStatement(sql);
            return preparedStatement.executeUpdate();
        } catch (SQLException e) {
            logger.warning("Execute update failed. sql: " + sql + " err: " + CommonUtil.exceptionToString(e));
            Profiler.functionError(mysqlUpdateCaller);
            throw new RuntimeException("Execute update failed. sql: " + sql + " err: "
                    + CommonUtil.exceptionToString(e), e);
        } finally {
            if (root != null) {
                try {
                    root.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            Profiler.registerInfoEnd(mysqlUpdateCaller);
        }
    }

    /**
     * 执行给定的 SQL 语句并返回结果集
     * @param sql 要执行的 SQL 语句
     * @param dbName 数据库名称
     * @return 包含结果集的 Map 列表
     */
    public static List<Map<String, Object>> executeSql(String sql, String dbName) {
        return executeSql(sql, dbName, new ArrayList<>(0));
    }

    /**
     * 执行给定的SQL语句，并返回结果列表
     * @param sql 要执行的SQL语句
     * @param dbName 数据库名称
     * @param params SQL语句中的参数
     * @return 包含查询结果的Map列表
     */
    public static List<Map<String, Object>> executeSql(String sql, String dbName, Object... params) {
        return executeSql(sql, dbName, Arrays.asList(params));
    }

    public static List<Map<String, Object>> executeSql(Connection root, String sql, String dbName, Object... params) {
        return executeSql(root, sql, dbName, Arrays.asList(params));
    }

    /**
     * 数据库名：metastore_mysql
     * 用户名：metastore_mys_rw
     * 密码：cSVLdCeIRh7qRyMj8tyTURYVIheoYQdf
     * 授权网段：10.%    172.%    11.%
     * hive_metastore_1
     * 10.198.39.198    my18001m.mysql.jddb.com
     * mysql -hmy18001m.mysql.jddb.com -P3358 -umetastore_mys_rw -pcSVLdCeIRh7qRyMj8tyTURYVIheoYQdf -Dmetastore_mysql
     *
     * @param sql
     * @param dbName
     * @return
     */
    public static List<Map<String, Object>> executeSql(String sql, String dbName, List<Object> params) {
        CallerInfo mysqlQueryCaller = Profiler.registerInfo("bdp.mysql.query", "SparkMonitorApp", false, true);
        CallerInfo mysqlQueryForDBCaller = Profiler.registerInfo("bdp.mysql.query."+dbName, "SparkMonitorApp", false, true);
        List<Map<String, Object>> list = new ArrayList<>();
        Connection root = null;
        try {
            MysqlBean mysqlBean = YamlUtil.loadYaml("db.yaml", MysqlBean.class);
            String dbtabaseName = StringUtils.defaultIfEmpty(dbName, mysqlBean.getDefaultDB());
            Map<String, String> dbAccount = mysqlBean.getDb().get(dbtabaseName);
            root = DriverManager.getConnection(dbAccount.get("url"), dbAccount.get("u"), dbAccount.get("p"));
            PreparedStatement preparedStatement = root.prepareStatement(sql);
            if(params != null) {
                for (int i = 0; i < params.size(); i++) {
                    if (params.get(i) instanceof String) {
                        preparedStatement.setString(i + 1, (String) params.get(i));
                    } else if (params.get(i) instanceof Integer) {
                        preparedStatement.setInt(i + 1, (Integer) params.get(i));
                    } else if (params.get(i) instanceof Long) {
                        preparedStatement.setLong(i + 1, (Long) params.get(i));
                    } else if(params.get(i) instanceof SQLType) {
                        preparedStatement.setNull(i + 1, (int) params.get(i));
                    } else if(params.get(i) instanceof Timestamp) {
                        preparedStatement.setTimestamp(i + 1, (Timestamp) params.get(i));
                    } else {
                        throw new IllegalArgumentException("Unknown data type. Data Type: " + params.get(i)
                                + " sql: " + sql + " params: " + JSON.toJSONString(params));
                    }
                }
            }
            ResultSet rs = preparedStatement.executeQuery();
            while (rs.next()) {
                Map<String, Object> result = new HashMap<>();
                ResultSetMetaData rsmd = rs.getMetaData();
                int cols = rsmd.getColumnCount();
                for (int i = 1; i <= cols; ++i) {
                    String columnName = rsmd.getColumnLabel(i);
                    if (null == columnName || 0 == columnName.length()) {
                        columnName = rsmd.getColumnName(i);
                    }
                    result.put(columnName, rs.getObject(i));
                }
                list.add(result);
            }
        } catch (SQLException e) {
            Profiler.functionError(mysqlQueryCaller);
            Profiler.functionError(mysqlQueryForDBCaller);
            throw new RuntimeException("sql = " + sql + " cause = " + e.getMessage(), e);
        } finally {
            if (root != null) {
                try {
                    root.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            Profiler.registerInfoEnd(mysqlQueryCaller);
            Profiler.registerInfoEnd(mysqlQueryForDBCaller);
        }
        return list;
    }
    public static List<Map<String, Object>> executeSql(Connection root, String sql, String dbName, List<Object> params) {
        CallerInfo mysqlQueryCaller = Profiler.registerInfo("bdp.mysql.query", "SparkMonitorApp", false, true);
        CallerInfo mysqlQueryForDBCaller = Profiler.registerInfo("bdp.mysql.query."+dbName, "SparkMonitorApp", false, true);
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            PreparedStatement preparedStatement = root.prepareStatement(sql);
            if(params != null) {
                for (int i = 0; i < params.size(); i++) {
                    if (params.get(i) instanceof String) {
                        preparedStatement.setString(i + 1, (String) params.get(i));
                    } else if (params.get(i) instanceof Integer) {
                        preparedStatement.setInt(i + 1, (Integer) params.get(i));
                    } else if (params.get(i) instanceof Long) {
                        preparedStatement.setLong(i + 1, (Long) params.get(i));
                    } else if(params.get(i) instanceof SQLType) {
                        preparedStatement.setNull(i + 1, (int) params.get(i));
                    } else if(params.get(i) instanceof Timestamp) {
                        preparedStatement.setTimestamp(i + 1, (Timestamp) params.get(i));
                    } else {
                        throw new IllegalArgumentException("Unknown data type. Data Type: " + params.get(i)
                                + " sql: " + sql + " params: " + JSON.toJSONString(params));
                    }
                }
            }
            ResultSet rs = preparedStatement.executeQuery();
            while (rs.next()) {
                Map<String, Object> result = new HashMap<>();
                ResultSetMetaData rsmd = rs.getMetaData();
                int cols = rsmd.getColumnCount();
                for (int i = 1; i <= cols; ++i) {
                    String columnName = rsmd.getColumnLabel(i);
                    if (null == columnName || 0 == columnName.length()) {
                        columnName = rsmd.getColumnName(i);
                    }
                    result.put(columnName, rs.getObject(i));
                }
                list.add(result);
            }
        } catch (SQLException e) {
            Profiler.functionError(mysqlQueryCaller);
            Profiler.functionError(mysqlQueryForDBCaller);
            throw new RuntimeException("sql = " + sql + " cause = " + e.getMessage(), e);
        } finally {
            Profiler.registerInfoEnd(mysqlQueryCaller);
            Profiler.registerInfoEnd(mysqlQueryForDBCaller);
        }
        return list;
    }
    public static KongMingRecordBean queryBuffaloTask(
            String pageNo, String pageSize, String taskId, String forkTaskId, String statusCode, String status) {
        KongMingRecordBean kongMingRecordBean = new KongMingRecordBean();
        long start = System.currentTimeMillis();
        int pageSizeInt = pageSize != null ? Integer.parseInt(pageSize) : 100;
        int pageNoInt = pageNo != null ? Integer.parseInt(pageNo) : 0;
        int offsetInt = pageNoInt * pageSizeInt;
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder("select * from " + SPARK_BUFFALO_TASK_INFO);
        sqlBuilder.append(" where 1 = 1");
        if (taskId != null) {
            sqlBuilder.append(" and task_id = ?");
            params.add(taskId);
        }
        if (forkTaskId != null) {
            sqlBuilder.append(" and fork_task_id = ?");
            params.add(forkTaskId);
        }
        if (statusCode != null) {
            sqlBuilder.append(" and status_code = ?");
            params.add(statusCode);
        }
        if (status != null) {
            sqlBuilder.append(" and status like ?");
            params.add("%" + status + "%");
        }
        sqlBuilder.append(" order by update_time desc limit " + offsetInt + "," + pageSizeInt);
        List<Map<String, Object>> rows = executeSql(sqlBuilder.toString(), null, params);
        logger.info("QueryBuffaloTask took " + (System.currentTimeMillis() - start) + " ms.");
        kongMingRecordBean.setCurPageNo(pageSizeInt);
        kongMingRecordBean.setPageSize(pageNoInt);
        kongMingRecordBean.setRows(rows);
        kongMingRecordBean.setNextPageNo(pageNoInt + 1);
        kongMingRecordBean.setPreviousPageNo(Math.max(pageNoInt - 1, 0));
        return kongMingRecordBean;
    }

    public static KongMingRecordBean queryBuffaloLog(
            String pageNo, String pageSize, String taskId, String forkTaskId, String runStatus,
            String filterFailureRegression) {
        KongMingRecordBean kongMingRecordBean = new KongMingRecordBean();
        long start = System.currentTimeMillis();
        int pageSizeInt = pageSize != null ? Integer.parseInt(pageSize) : 100;
        int pageNoInt = pageNo != null ? Integer.parseInt(pageNo) : 0;
        int offsetInt = pageNoInt * pageSizeInt;
        ArrayList<Object> params = new ArrayList<>();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(BooleanUtils.toBoolean(filterFailureRegression) ?
                ForkBuffaloTaskService.sparkMigrateSQL.get("failureRegerssion")
                : "select * from " + SPARK_BUFFALO_LOG_INFO);
        sqlBuilder.append(" where 1 = 1");
        if (taskId != null) {
            sqlBuilder.append(" and origin_task_id = ?");
            params.add(taskId);
        }
        if (forkTaskId != null) {
            sqlBuilder.append(" and fork_task_id = ?");
            params.add(forkTaskId);
        }
        if (runStatus != null) {
            sqlBuilder.append(" and runStatus = ?");
            params.add(runStatus);
        }
        sqlBuilder.append(" order by update_time desc limit " + offsetInt + "," + pageSizeInt);
        List<Map<String, Object>> rows = executeSql(sqlBuilder.toString(), null, params);
        logger.info("queryBuffaloLog took " + (System.currentTimeMillis() - start) + " ms.");
        kongMingRecordBean.setCurPageNo(pageSizeInt);
        kongMingRecordBean.setPageSize(pageNoInt);
        kongMingRecordBean.setRows(rows);
        kongMingRecordBean.setNextPageNo(pageNoInt + 1);
        kongMingRecordBean.setPreviousPageNo(Math.max(pageNoInt - 1, 0));
        return kongMingRecordBean;
    }

    public static int insertLogInfo(Long runLogId, Long instanceId, String runStatus, Long duration,
                                    String buffalo_version, String origin_task_id, String fork_task_id,
                                    String cluster, String market, String queue, String teamuser,
                                    String engine, int statusCode, String sparkVersion) {
        List<Object> params = new ArrayList<>();
        List<Object> paramsVal = new ArrayList<>();
        params.add("buffalo_version");
        paramsVal.add(buffalo_version);
        params.add("origin_task_id");
        paramsVal.add(origin_task_id);
        params.add("fork_task_id");
        paramsVal.add(fork_task_id);
        params.add("runLogId");
        paramsVal.add(runLogId);
        params.add("instanceId");
        paramsVal.add(instanceId);
        params.add("runStatus");
        paramsVal.add(runStatus);
        params.add("duration");
        paramsVal.add(duration);
        params.add("cluster");
        paramsVal.add(cluster);
        params.add("market");
        paramsVal.add(market);
        params.add("queue");
        paramsVal.add(queue);
        params.add("teamuser");
        paramsVal.add(teamuser);
        params.add("engine");
        paramsVal.add(engine);
        params.add("status_code");
        paramsVal.add(statusCode);
        if (sparkVersion != null) {
            params.add("spark_version");
            paramsVal.add(sparkVersion);
        }
        StringBuilder builderParam = new StringBuilder();
        params.forEach(item -> builderParam.append(item).append(","));
        StringBuilder builderVal = new StringBuilder();
        paramsVal.forEach(item -> builderVal.append("?,"));
        String insertSQL = "insert into " + SPARK_BUFFALO_LOG_INFO +
                " ("+builderParam.substring(0, builderParam.length() -1)+")" +
                " values("+builderVal.substring(0, builderVal.length()-1)+")";
        int result = executeUpdate(insertSQL, null, paramsVal.toArray());
        logger.info(insertSQL + ", result: " + result);
        return result;
    }

    public static int deleteLogInfo(Long runLogId, Long forkTaskId) {
        String deleteSQL = "delete from " + SPARK_BUFFALO_LOG_INFO + " where runLogId = ? and fork_task_id = ?";
        int result = executeUpdate(deleteSQL, null, runLogId, forkTaskId);
        logger.info(deleteSQL + ", result: " + result);
        return result;
    }

    public static boolean checkTaskInfoExists(Long taskId) {
        String sql = "select * from "+SPARK_BUFFALO_TASK_INFO+" where task_id = ?";
        List<Map<String, Object>> kongming = executeSql(sql, null, Arrays.asList(taskId));
        return kongming.isEmpty();
    }

    public static int insertTaskInfo(String buffaloVersion, Long taskId, int statusCode, Long fork_task_id,
                                     String managers, String status, String originalSQLCommand) {
        List<Object> params = new ArrayList<>();
        List<Object> paramsVal = new ArrayList<>();
        params.add("buffalo_version");
        paramsVal.add(buffaloVersion);
        params.add("task_id");
        paramsVal.add(taskId);
        params.add("status_code");
        paramsVal.add(statusCode);
        if(fork_task_id!= null) {
            params.add("fork_task_id");
            paramsVal.add(fork_task_id);
        }
        if(managers!= null) {
            params.add("managers");
            paramsVal.add(managers);
        }
        if(status!= null) {
            params.add("status");
            paramsVal.add(status);
        }
        if(originalSQLCommand!= null) {
            params.add("originalSQLCommand");
            paramsVal.add(originalSQLCommand);
        }

        StringBuilder builderParam = new StringBuilder();
        params.forEach(item -> builderParam.append(item).append(","));
        StringBuilder builderVal = new StringBuilder();
        paramsVal.forEach(item -> builderVal.append("?,"));
        return executeUpdate("insert into " + SPARK_BUFFALO_TASK_INFO + " ("+builderParam.substring(0,builderParam.length() -1)+")" +
                " values ("+builderVal.substring(0,builderVal.length()-1)+")", null, paramsVal.toArray());
    }
}