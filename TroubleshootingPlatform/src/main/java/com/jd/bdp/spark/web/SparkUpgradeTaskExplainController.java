package com.jd.bdp.spark.web;


import com.jd.bdp.bean.domain.ExplainLogicPlanResult;
import com.jd.bdp.scheduler.ExplainLogicalPlanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark升级-buffalo4任务执行计划校验
 */
@WebServlet("/sparkUpgradeTaskExplain")
public class SparkUpgradeTaskExplainController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeTaskExplainController.class.getName());

    private ExplainLogicalPlanUtils explainLogicalPlanUtils = APPLICATION_CONTEXT.getBean(ExplainLogicalPlanUtils.class);
    
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String taskId = req.getParameter("taskId");   // 导入task的数量开始位置
        logger.info(String.format("=== 本次进行执行计划校验， taskId: %s", taskId));
        ExplainLogicPlanResult result = explainLogicalPlanUtils.execute(taskId);
        logger.info(String.format("=== 本次进行执行计划校验： taskId: %s, 结果：%s",  taskId, result.isExplainSuc() ? "explain执行成功": "explain执行失败"));
        
        req.setAttribute("msg", result.isExplainSuc() ? "explain执行成功": "explain执行失败");
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }
}
