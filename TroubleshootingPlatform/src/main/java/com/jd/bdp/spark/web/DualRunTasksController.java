package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.FileUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.websocket.Session;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Set;


@WebServlet("/dualRun")
public class DualRunTasksController extends HttpServlet {

    public static Set<Long> getTasks(String str) {
        return ShellUtil.getTasks(str);
    }

    public static String toString(Set<Long> taskids) {
        return ShellUtil.toString(taskids);
    }

    public JSONObject runDualTasks(String json, Session session) {
        JSONObject data = JSON.parseObject(json);
        String originTaskIds = data.getString("originTaskIds");
        String fileContent = toString(getTasks(originTaskIds));
        String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/run_dual_run/";
        String filePath = baseDir + "run_taskids_"+System.currentTimeMillis()+".txt";
        String scriptName = "run.sh";
        FileUtil.writeFile(filePath, fileContent, false);
        Integer returnCode = ShellUtil.runCmd("sh "+baseDir+scriptName+" false file://" +filePath, session);
        return data;
    }

    public JSONObject cleanHdfsTempDirs(String json, Session session) {
        JSONObject data = JSON.parseObject(json);
        String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/clean_wangriyu/";
        String scriptName = "run.sh";
        ShellUtil.runCmd("sh "+baseDir+scriptName, session);
        return data;
    }

    public JSONObject v34Upgrade(String json, Session session) {
        JSONObject data = JSON.parseObject(json);
        String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/run_dual_run/";
        String scriptName = "v34.sh";
        ShellUtil.runCmd("sh "+baseDir+scriptName, session);
        return data;
    }

    public JSONObject deleteDualRunTables(String json, Session session) {
        JSONObject data = JSON.parseObject(json);
        String baseDir = "/root/wuguoxiao/wuguoxiao/doubleRun/run_dual_run/";
        String scriptName = "delete_tables.sh";
        ShellUtil.runCmd("sh "+baseDir+scriptName, session);
        return data;
    }
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.setCharacterEncoding("utf-8");
        resp.setContentType("text/html;charset=UTF-8");
        PrintWriter writer = resp.getWriter();
        Set<Long> originTaskIds = getTasks(req.getParameter("originTaskIds"));
        String logTime = req.getParameter("logTime");
        String scriptName = req.getParameter("scriptName");
        String baseDir = req.getParameter("baseDir");
        if(!originTaskIds.isEmpty()) {
            String fileContent = toString(originTaskIds);
            String filePath = baseDir + "taskids_"+System.currentTimeMillis()+".txt";
            FileUtil.writeFile(filePath, fileContent, false);
            // sh /root/wuguoxiao/wuguoxiao/doubleRun/double_run/run.sh taskids_1729014151383.txt 2024-10-10
            Integer returnCode = ShellUtil.runCmd("sh "+baseDir+scriptName+" " + filePath + " " + logTime, null);
            writer.write("Code: "+returnCode);
        } else {
            writer.write("Timeout Or Error");
        }
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }
    
}
