package com.jd.bdp.spark.web;

import okhttp3.*;

import java.io.IOException;
import java.util.Map;

import java.io.InputStream;
import java.util.concurrent.TimeUnit;

public class OkHttp {

    public static String sendMultipartPost(String url, Map<String, Object> params, String fileName) throws IOException {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        //封装请求参数
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object obj = entry.getValue();
            if (obj instanceof InputStream) {
                byte[] bytes = FileCopyUtils.copyToByteArray((InputStream)obj);
                RequestBody requestBody = RequestBody.create(MediaType.parse("*/*"), bytes);
                builder.addFormDataPart(key, fileName, requestBody);
            } else {
                builder.addFormDataPart(key, obj.toString());
            }
        }
        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .build();
        try (Response response = getOkHttpClient().newCall(request).execute()) {
            return response.body() != null ? response.body().string() : null;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    private static OkHttpClient getOkHttpClient(){
    return new OkHttpClient()
            .newBuilder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();
    }
}
