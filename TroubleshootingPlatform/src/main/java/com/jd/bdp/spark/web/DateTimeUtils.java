package com.jd.bdp.spark.web;

public class DateTimeUtils {
    /**
     * 获取时间字符串中的秒数
     * @param hourMinuteSecond 包含时分秒的时间字符串，例如"1h30m15s"
     * @return 返回总秒数
     * @throws NumberFormatException 如果时间字符串格式不正确，抛出NumberFormatException异常
     */
    public static Integer getSecond(String hourMinuteSecond) {
        int hIdx = hourMinuteSecond.indexOf("h");
        int mIdx = hourMinuteSecond.indexOf("m");
        int sIdx = hourMinuteSecond.indexOf("s");
        if(hIdx > 0) {
            int h = Integer.parseInt(hourMinuteSecond.substring(0, hIdx));
            int m = Integer.parseInt(hourMinuteSecond.substring(hIdx + 1, mIdx));
            int s = Integer.parseInt(hourMinuteSecond.substring(mIdx + 1, sIdx));
            return h*3600 + m*60 + s;
        } else {
            if (mIdx > 0) {
                int m = Integer.parseInt(hourMinuteSecond.substring(0, mIdx));
                int s = Integer.parseInt(hourMinuteSecond.substring(mIdx + 1, sIdx));
                return m*60 + s;
            } else {
                int s = Integer.parseInt(hourMinuteSecond.substring(0, sIdx));
                return s;
            }
        }
    }
}
