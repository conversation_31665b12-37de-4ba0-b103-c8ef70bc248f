package com.jd.bdp.spark.web;

import com.jd.bdp.bean.SparkMetricBean;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@WebServlet("/sparkMetric")
public class SparkMetricController extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        resp.setContentType("text/html;charset=utf-8");
        PrintWriter writer = resp.getWriter();

        String appid = req.getParameter("appid");
        String history = req.getParameter("history");

        SparkMetricBean outputBytes = ComputeBdcu.getOutputBytes(history, appid);

        if (outputBytes == null) {
            resp.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        } else {
            writer.write(outputBytes.toString());
        }
    }
}
