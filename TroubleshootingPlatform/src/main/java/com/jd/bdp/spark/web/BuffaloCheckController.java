package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.Buffalo4TaskManager;
import com.jd.bdp.common.Buffalo4TaskManager.CgroupConfigEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.logging.Logger;

@WebServlet("/buffaloDoubleRun")
public class BuffaloCheckController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(BuffaloCheckController.class.getName());

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        StringBuilder msg = new StringBuilder();
        JSONObject responseObj = new JSONObject();
        String responseType = StringUtils.trimToNull(req.getParameter("responseType"));
        String id1 = req.getParameter("id");
        String db;
        String tbl;
        String dbTables = req.getParameter("dbTables");
        String follower = req.getParameter("follower");

        Integer limit = Integer.parseInt(StringUtils.defaultIfEmpty(req.getParameter("limit"), "1"));
        String checkType = StringUtils.defaultIfEmpty(req.getParameter("checkType"), "COUNT");
        boolean reCheck = Boolean.parseBoolean(StringUtils.defaultIfEmpty(req.getParameter("reCheck"), "TRUE"));
        boolean reRunBuffalo = Boolean.parseBoolean(StringUtils.defaultIfEmpty(req.getParameter("reRunBuffalo"), "TRUE"));
        Integer buffaloId = Integer.parseInt(StringUtils.defaultIfEmpty(req.getParameter("buffaloId"), "1360823"));
        boolean modifyNode = Boolean.parseBoolean(StringUtils.defaultIfEmpty(req.getParameter("modifyRunNode"), "FALSE"));
        Integer taskNodeId = Integer.parseInt(StringUtils.defaultIfEmpty(req.getParameter("taskNodeId"), "1402"));
        Calendar calendar = Calendar.getInstance();

        String originVersion = StringUtils.defaultIfEmpty(req.getParameter("originVersion"), "2_4");
        String compareVersion = StringUtils.defaultIfEmpty(req.getParameter("compareVersion"), "3_4");
        System.out.println("originVersion is " + originVersion + ", and compareVersion is " + compareVersion);
        String source = req.getParameter("source");
        String taskId = StringUtils.trimToNull(req.getParameter("taskid"));
        String instanceId = StringUtils.trimToNull(req.getParameter("instanceId"));
        String logId = req.getParameter("logid");
        String indexes = req.getParameter("indexes");
        String testFlag = StringUtils.trimToEmpty(req.getParameter("testFlag"));
        String replaceDualRunId = req.getParameter("replaceDualRunId");
        String cgroupConfig = StringUtils.defaultIfEmpty(req.getParameter("cgroupConfig"),"6c6g");
        System.out.println("replaceDualRunId is " + (replaceDualRunId==null? "null":replaceDualRunId)+", and cgroupConfig is "+cgroupConfig);
        if(Arrays.asList("BUFFALO", "IDEONLINE").contains(source) && NumberUtils.isDigits(taskId)
                && NumberUtils.isDigits(logId)) {
//            String cluster = getCluster(taskId, logId);
            if(replaceDualRunId == null) {
                System.out.println("replaceDualRunId is null, start create new task");
                JSONObject result = Buffalo4TaskManager.createDoubleRunBuffaloTask(testFlag, source.toUpperCase(),
                    taskId + "_" + logId, indexes, null, originVersion, compareVersion);
                msg.append(result.toJSONString());
            } else {
                System.out.println("replaceDualRunId is not null, start replace task " + replaceDualRunId);
                CgroupConfigEnum cgroupConfigEnum = CgroupConfigEnum.getFrom(cgroupConfig);
                JSONObject result = Buffalo4TaskManager.updateTaskNameAndActionsNew(testFlag, source.toUpperCase(),
                    taskId + "_" + logId, indexes, null,replaceDualRunId, cgroupConfigEnum, originVersion, compareVersion);
                msg.append(result.toJSONString());
            }
        } else if(taskId != null && modifyNode) {
            String[] taskList = taskId.split(",");
            for(String taskIdStr: taskList){
                logger.info(String.format("=== 开始修改任务: %s 的环节属性", taskIdStr));
                JSONObject actionListByTaskId = Buffalo4TaskManager.getActionListByTaskId(taskIdStr);
                assert !actionListByTaskId.isEmpty();
                JSONObject jsonObject = actionListByTaskId.getJSONObject("obj");
                JSONArray actionList = jsonObject.getJSONArray("actionList");
                for (int i = 0; i < actionList.size(); i++) {
                    JSONObject jsonObject1 = actionList.getJSONObject(i);
                    Integer actionId = jsonObject1.getInteger("actionId");
                    System.out.println("actionId = " + actionId);
                    CgroupConfigEnum cgroupConfigEnum = CgroupConfigEnum.getFrom(cgroupConfig);
                    Buffalo4TaskManager.buffalo5TaskActionModify(Integer.parseInt(taskIdStr), actionId, 3, taskNodeId,cgroupConfigEnum);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                msg.append(actionList.toJSONString());
            }
        } else if (taskId != null) {
            responseObj.put("code", 1);
            responseObj.put("msg", "接口已停用,请使用新接口:/sparkUpgradeCreateInstance");
            req.setAttribute("msg", responseObj.toJSONString());
            resp.setStatus(HttpServletResponse.SC_SERVICE_UNAVAILABLE);
            return;
        } else if(instanceId != null) {
            JSONObject jsonObject = Buffalo4TaskManager.stopInstanceByInstId(instanceId);
            logger.info(jsonObject.toJSONString());
            responseObj = jsonObject;
            msg.append(jsonObject.toJSONString());
        }
        if("json".equalsIgnoreCase(responseType)) {
            resp.setContentType("text/html;charset=utf-8");
            PrintWriter writer = resp.getWriter();
            writer.write(responseObj.toJSONString());
        } else {
            req.setAttribute("msg", msg);
            req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
        }
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        this.doGet(req, resp);
    }

}
