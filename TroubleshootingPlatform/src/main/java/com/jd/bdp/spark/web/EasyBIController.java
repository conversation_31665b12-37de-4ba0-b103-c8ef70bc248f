package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.JimDBUtils;
import com.jd.bdp.common.YamlUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@WebServlet("/easyBI")
public class EasyBIController extends HttpServlet {

    public static Map<String, String> sparkMigrateSQL = YamlUtil.loadYaml("sparkMigrateSQL.yaml", HashMap.class);
    public static ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {

        resp.setContentType("text/html;charset=utf-8");
        PrintWriter writer = resp.getWriter();
        String type = req.getParameter("type");
        boolean init = false;
        if ("init".equalsIgnoreCase(type)) {
            init = true;
        }
        JSONArray data = new JSONArray();
        Map<String, String> map = YamlUtil.loadYaml("errorAnalysis.yaml", Map.class);
        Enumeration headerNames = req.getHeaderNames();
        System.out.println("headerNames = " + JSON.toJSONString(headerNames));
        Map<String, String[]> parameterMap = req.getParameterMap();
        System.out.println("JSON.toJSONString(parameterMap) = " + JSON.toJSONString(parameterMap));

        String pageOffset = req.getParameter("pageOffset");
        String pageSize = "50000"; //req.getParameter("pageSize");

        if("detail".equalsIgnoreCase(type)) {
            String dualRunDetail = sparkMigrateSQL.get("dual_run_detail");
            List<Map<String, Object>> rows = KongmingService.executeSql(dualRunDetail, "buffalo");
            data.addAll(rows);
        } else {
            String dualRun = sparkMigrateSQL.get("dual_run");
            dualRun = String.format(dualRun, " AND f.run_2_4_logid IS NOT NULL " +
                    "AND (run_2_4 = 'fail' or run_3_4 = 'fail' or data_check = 'fail')");
            if (pageOffset != null && pageSize != null) {
                dualRun += " limit " + pageOffset + "," + pageSize;
            }
            List<Map<String, Object>> rows = KongmingService.executeSql(dualRun, "buffalo");
            if (!rows.isEmpty()) {
                for (Map<String, Object> row : rows) {
                    String dualRunId = String.valueOf(row.get("dual_run_id"));

                    row.put("run_2_4_msg", "no err");
                    row.put("run_3_4_msg", "no err");
                    row.put("run_check_msg", "no err");

                    extracted(map, row, dualRunId, (String) row.get("run_2_4"), row.get("run_2_4_logid") + "", "run_2_4_msg", init);
                    extracted(map, row, dualRunId, (String) row.get("run_3_4"), row.get("run_3_4_logid") + "", "run_3_4_msg", init);
                    extracted(map, row, dualRunId, (String) row.get("data_check"), row.get("run_check_logid") + "", "run_check_msg", init);
                }
                data.addAll(rows);
            }
        }
        JSONObject jsonObject = new JSONObject()
                .fluentPut("code", 200)
                .fluentPut("msg", "成功")
                .fluentPut("data", data);
        writer.write(JSON.toJSONString(jsonObject));
        writer.flush();
        writer.close();
    }

    private static void extracted(Map<String, String> map, Map<String, Object> row, String dualRunId, String run24,
                                  String run24LogId, String run24Msg, boolean init) {
        if("fail".equalsIgnoreCase(run24)) {
            if (init) {
                executorService.submit(() -> BDPUtils.syncToJimDB(dualRunId, run24LogId,
                        BDPUtils.analysisLogByLogId(dualRunId, "buffalo4",
                                run24LogId, null, "dp.jd.com", true)));
            }
            Map<String, String> hash = JimDBUtils.getHash(dualRunId, run24LogId);
            List<String> run24Msgs = new ArrayList<>();
            if(hash != null && !hash.isEmpty()) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    if(hash.get(entry.getKey()) != null) {
                        run24Msgs.add(entry.getValue());
                    }
                }
            }
            String msg = String.join(",", run24Msgs);
            if (run24Msgs.isEmpty()) {
                msg = "unknow";
            }
            row.put(run24Msg, msg);

        }
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        this.doGet(req, resp);
    }
}
