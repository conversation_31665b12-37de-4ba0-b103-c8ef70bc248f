package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.Buffalo4Dao;
import com.jd.bdp.common.CommonUtil;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

@WebServlet("/buffaloLogs")
public class BuffaloLogsController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(BuffaloLogsController.class.getName());

    static {
        System.setProperty("ump.app_name", "sparkmonitor");
        Profiler.registerJVMInfo("sparkmonitor1111");
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        String type = req.getParameter("type");
        String logId = req.getParameter("logId");
        if ("getBuffaloLog".equals(type) && logId != null) {
            String filePath = BDPUtils.downloadLogFromHbase(Long.valueOf(logId));
            FileInputStream inputStream = new FileInputStream(filePath);  //read the file
            resp.setHeader("Content-Disposition","attachment; filename="+logId+".log");
            try {
                int c;
                while ((c = inputStream.read()) != -1) {
                    resp.getWriter().write(c);
                }
            } finally {
                if (inputStream != null) {
                    inputStream.close();
                }
                resp.getWriter().close();
            }
        } else if ("stream".equals(type) && logId != null) {
            resp.setContentType("application/octet-stream");
            resp.setHeader("Content-Disposition", "attachment; filename=\"stream_" + logId + ".log\"");
            ServletOutputStream outputStream = resp.getOutputStream();
            BDPUtils.downloadLogFromHbase(Long.valueOf(logId), outputStream);
        } else if ("getEnv".equals(type)) {
            resp.setContentType("text/html;charset=utf-8");
            PrintWriter writer = resp.getWriter();
            JSONObject jsonObject = new JSONObject();
            Long taskId = Long.parseLong(req.getParameter("taskId"));
            Long runLogId = CommonUtil.convertToLong(req.getParameter("runLogId"));
            List<Map<String, Object>> runLogByTaskId1 = Buffalo4Dao.getRunLogByTaskId(taskId, runLogId, null);
            for (Map<String, Object> stringObjectMap : runLogByTaskId1) {
                StringBuilder builder = new StringBuilder();
                String taskDefId = stringObjectMap.get("task_def_id").toString();
                builder.append("export BEE_SOURCE=BUFFALO4;");
                builder.append("export BUFFALO_ENV_TASK_DEF_ID=").append(taskDefId).append(";");
                for (String env : stringObjectMap.get("env").toString().split(",")) {
                    builder.append("export ").append(env).append(";");
                }
                builder.append("source /software/servers/env/env.sh;");
                jsonObject.put("source", builder.toString());
            }
            jsonObject.put("ENV", runLogByTaskId1);
            writer.write(jsonObject.toJSONString());
        } else {
            CallerInfo buffaloLogsCaller = Profiler.registerInfo("bdp.troubleshooting.controller.buffaloLogs", "SparkMonitorApp", false, true);
            try {
                String site = req.getParameter("site");
                String taskId = req.getParameter("taskId");
                req.setAttribute("site", site);
                req.setAttribute("taskId", taskId);
                JSONArray buffalo4Logs = new JSONArray();
                long taskId1 = Long.parseUnsignedLong(taskId);
                List<Map<String, Object>> runLogByTaskId1 = Buffalo4Dao.getRunLogByTaskId(taskId1, null, null);
                buffalo4Logs.addAll(runLogByTaskId1);
                BDPUtils.fillStatusEngine(site, taskId, buffalo4Logs);
                List<Map<String, Object>> slaTask = Buffalo4Dao.getSLATask(taskId1);
                String slaFlag = "";
                if (!slaTask.isEmpty()) {
                    List<String> slaRegion = new LinkedList<>();
                    for (Map<String, Object> stringObjectMap : slaTask) {
                        slaRegion.add(stringObjectMap.get("task_region") + "");
                    }
                    slaFlag = String.join(",", slaRegion);
                }
                req.setAttribute("buffalo4Logs", buffalo4Logs);
                req.setAttribute("slaFlag", slaFlag);
            } catch (Exception e) {
                logger.warning(CommonUtil.exceptionToString(e));
                Profiler.functionError(buffaloLogsCaller);
            } finally {
                Profiler.registerInfoEnd(buffaloLogsCaller);
            }
            req.getRequestDispatcher("/buffalo-logs.jsp").forward(req, resp);
        }
    }
}
