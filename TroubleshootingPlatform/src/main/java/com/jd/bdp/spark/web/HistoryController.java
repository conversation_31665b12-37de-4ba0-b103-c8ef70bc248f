package com.jd.bdp.spark.web;

import com.jd.common.util.StringUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.logging.Logger;

@WebServlet("/historyController")
public class HistoryController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(HistoryController.class.getName());

    /**
     * 处理 HTTP GET 请求的方法
     * @param req HTTP请求对象
     * @param resp HTTP响应对象
     * @throws IOException 输入输出异常
     * @throws ServletException Servlet异常
     * @return 无返回值
     */
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        CallerInfo sparkMonitorApp = Profiler.registerInfo("bdp.history.query", "SparkMonitorApp", false, true);
        String clusterDomain = StringUtils.trimToNull(req.getParameter("clusterDomain"));
        String applicationId = StringUtils.trimToNull(req.getParameter("applicationId"));
        if(clusterDomain == null || applicationId == null) {
            Profiler.registerInfoEnd(Profiler.registerInfo("bdp.history.illegalArgument", "SparkMonitorApp", false, true));
            req.setAttribute("msg", "请选择集群并填写applicationId！");
            req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
        } else {
            Map<String, String> historyMap = BDPUtils.getHistoryWithRetry(clusterDomain, applicationId);
            logger.info("historyMap = " + historyMap);
            Profiler.registerInfoEnd(sparkMonitorApp);
            if (historyMap.isEmpty()) {
                Profiler.registerInfoEnd(Profiler.registerInfo("bdp.history.notFound", "SparkMonitorApp", false, true));
                req.setAttribute("msg", "未找到对应的application（有可能已经过期，仅保留近2天的日志），请确认applicationId是否正确，若确认无误请稍候重新访问（直接刷新本页面即可）！");
                req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
            } else {
                resp.sendRedirect(historyMap.get("history"));
            }
        }
    }

    /**
     * 重写父类方法，处理POST请求
     * @param req 请求对象
     * @param resp 响应对象
     * @throws IOException 输入输出异常
     * @throws ServletException Servlet异常
     */
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        doGet(req, resp);
    }
}