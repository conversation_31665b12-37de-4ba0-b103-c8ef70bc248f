package com.jd.bdp.spark.web;

import com.jd.bdp.common.XbpManager;
import com.jd.bdp.scheduler.Scheduler;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.xbp.jsf.api.request.ticket.CreateParam;
import com.jd.xbp.jsf.api.response.XbpResponse;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import static com.jd.bdp.bean.XbpForm.FLOW_NAME_PRESTO;
import static com.jd.bdp.spark.web.XBPController.updateMysqlForTicket;

public class XbpProcessServiceForPresto implements XbpProcessService {

    private static final Logger logger = Logger.getLogger(XBPController.class.getName());

    /**
     * 创建XbpProcess方法
     * @param builder 用于构建字符串的StringBuilder对象
     * @param row 包含数据行信息的Map对象
     * @param tableName 表名
     */
    public void createXbpProcess(StringBuilder builder, Map<String, Object> row, String tableName) {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow."+tableName+".scheduler.createFlow", "SparkMonitorApp", false, true);
        try {
            Integer id = Integer.parseInt(row.get("id") + "");
            String account = (String) row.get("account");
            String appName = (String) row.get("app_name");
            String checkErp = (String) row.get("check_erp");
            String ips = (String) row.get("ips");
            String checkMsg = (String) row.get("check_msg");
            Integer processId = (Integer) row.get("processId");
            XbpResponse<Integer> integerXbpResponse = xbpPress(processId, account, appName, ips, checkErp, checkMsg);
            updateMysqlForTicket(builder, integerXbpResponse, "update " + tableName +
                " set xbp_status = ?, ticket_id = ?, xbp_msg = ?, check_erp = ?" +
                " where id = ?", "nature", checkErp, id
            );
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
            throw e;
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
    }

    public XbpResponse<Integer> xbpPress(Integer processId, String account, String appName, String ips, String checkErp, String checkMsg) {
        CreateParam createParam = new CreateParam();
        createParam.setProcessId(processId);
        createParam.setUsername(Scheduler.commonBean.getXbpNatureUsername());
        createParam.setMailCopyAddresses(Scheduler.commonBean.getXbpMailCopyAddressesForInspection());
        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put("Presto账号", account);
        createParam.setApplicationInfo(applicationInfo);
        HashMap<String, List<String>> approvers = new HashMap<>();
        if (StringUtils.isNotEmpty(appName)) {
            applicationInfo.put("业务方的应用名称", appName);
        }
        if (StringUtils.isNotEmpty(ips)) {
            applicationInfo.put("业务方应用的 IP 列表", ips);
        }
        approvers.put(FLOW_NAME_PRESTO.get(0), Arrays.asList(checkErp.split(",")));
        createParam.setApprovers(approvers);
        Map<String, String> rtfMap = new HashMap<>();
        rtfMap.put("Presto JDBC 用户完善业务等级报备", checkMsg);
        rtfMap.put("查询来源(使用Presto查询的业务平台) ", checkMsg);
        createParam.setRtf(rtfMap);
        XbpResponse<Integer> integerXbpResponse = XbpManager.createXbpFlow(createParam);
        logger.info("JSF: create: " + integerXbpResponse + " params: " + createParam);
        return integerXbpResponse;
    }

    public List<Map<String, Object>> getXbpFlow(String account) {
        String sql = "select * from t_nature_presto_press where account = ?";
        return KongmingService.executeSql(sql, "nature", account);
    }
}
