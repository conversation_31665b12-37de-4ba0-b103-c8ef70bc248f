package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.jd.bdp.bean.SparkMetricBean;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.IOException;

public class ComputeBdcu {

    public static SparkMetricBean getOutputBytes(String stagesPageRestApi) {
        long inputBytes = 0;
        long inputRecords = 0;
        long outputBytes = 0;
        long outputRecords = 0;
        long executorRunTime = 0;
        boolean isSuccess = false;
        try {
            String result = getJsonFromUrl(stagesPageRestApi);
            JSONArray jsonArray = JSONObject.parseArray(result);
            for (int i = 0; i < jsonArray.size(); ++i) {
                JSONObject stage = (JSONObject)jsonArray.get(i);
                inputBytes += Long.parseLong(stage.getString("inputBytes"));
                inputRecords += Long.parseLong(stage.getString("inputRecords"));
                outputBytes += Long.parseLong(stage.getString("outputBytes"));
                outputRecords += Long.parseLong(stage.getString("outputRecords"));
                executorRunTime += Long.parseLong(stage.getString("executorRunTime"));
                isSuccess = true;
            }
        } catch (IOException|com.alibaba.fastjson.JSONException e) {
            e.printStackTrace();
        }
        if(isSuccess) {
            return new SparkMetricBean(inputBytes, inputRecords, outputBytes, outputRecords, executorRunTime);
        } else {
            return null;
        }
    }

    public static SparkMetricBean getOutputBytes(String domain, String buffaloApplicationId) {
        return getOutputBytes("http://" + domain + "/api/v1/applications/" + buffaloApplicationId + "/stages");
    }

    // 用来访问网页
    private static String getJsonFromUrl(String url) throws IOException{
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet(url);
        HttpResponse response = httpclient.execute(httpGet);
        return EntityUtils.toString(response.getEntity(), "utf-8");
    }
}