package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.Buffalo4TaskManager;
import com.jd.bdp.common.SDFThreadLocal;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.logging.Logger;

import static com.jd.bdp.bean.Constants.COMPRESS_DB;
import static com.jd.bdp.bean.Constants.CRITICAL_PATH_TASKS;

@WebServlet("/criticalPath")
public class TaskCriticalPath extends HttpServlet {
    private static final Logger logger = Logger.getLogger(TaskCriticalPath.class.getName());
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        PrintWriter writer = resp.getWriter();
        req.setCharacterEncoding("utf-8");
        resp.setContentType("text/html;charset=UTF-8");
        String dt = req.getParameter("dt");
        if(dt == null) {
            dt = SDFThreadLocal.getyyyyMMdd().format(new Date());
        }
        if ("init".equals(req.getParameter("type"))){
            String tableName = CRITICAL_PATH_TASKS;
            KongmingService.executeUpdate("create table if not exists "+tableName+" (taskid int, is_critical_path varchar(2) ,UNIQUE (taskid))",COMPRESS_DB);
            String tasks = "552199,542177,640794,963757,1063853,488311,929940,845397,609337,1120278,757450,929929,269084,300267,748604,613962,620091,969818,1223034,752997,947780,1078649,1098954,928907,982953,1002730,871964,1002526,871690,1001979,858584,1001982,917027,932333,719237,932833,198900,708547,796091,706025,832404,404694,370706,837825,604998,324026,303168,712915,305500,800943";

            for (String s : tasks.split(",")) {
                ArrayList<Long> criticalPath = TaskCriticalPath.getCriticalPath(Long.parseLong(s), dt, 100);
                for (Long aLong : criticalPath) {
                    try {
                        KongmingService.executeUpdate("insert into " + tableName +
                                "(taskid, is_critical_path) values (?, ?)", COMPRESS_DB, Integer.parseInt(aLong+""), "Y");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } else {
            Long taskid = Long.parseLong(req.getParameter("taskid"));
            ArrayList<Long> longs = getCriticalPath(taskid, dt, 100);
            writer.write(longs.toString());
        }

    }

    public static ArrayList<Long> getCriticalPath(Long taskid, String dt, int limit) throws IOException {
        ArrayList<Long> longs = new ArrayList<>();
        longs.add(taskid);
        for (;;) {
            Long parentLast = getParentLast(taskid, dt, limit);
            if(parentLast == null) {
                break;
            }
            longs.add(parentLast);
            taskid = parentLast;
        }
        return longs;
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

    public static Long getParentLast(Long taskid, String dt, int limit) throws IOException {
        JSONObject taskParentDepends = Buffalo4TaskManager.getTaskParentDepends(Integer.parseInt(taskid+""), limit);
        JSONArray list = taskParentDepends.getJSONArray("list");
        if(list == null || list.isEmpty()) {
            return null;
        }
        Long latestTaskId = null;
        long maxDuration = 0;
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            Long taskId = jsonObject.getLong("taskId");

            JSONArray taskDependRanges = jsonObject.getJSONArray("taskDependRanges");
            if(taskDependRanges == null || taskDependRanges.isEmpty()) {
                continue;
            }
            JSONObject jsonObject2 = taskDependRanges.getJSONObject(0);
            int startRange = jsonObject2.getIntValue("startRange");
            if(startRange != 0) {
                continue;
            }

            JSONObject runLogByTaskId = Buffalo4TaskManager.getRunLogByTaskId(taskId, dt + " 00:00:00", dt + " 23:59:59");
            JSONArray list1 = runLogByTaskId.getJSONArray("list");
            if(list1 == null || list1.isEmpty()) {
                // 今天没有运行
                continue;
            }
            for (int j = 0; j < list1.size(); j++) {
                JSONObject jsonObject1 = list1.getJSONObject(j);
                // "runType": "normal",
                if ("normal".equalsIgnoreCase(jsonObject1.getString("runType")) ) {
                    long maxEndTime = jsonObject1.getLongValue("endTime");
                    System.out.println("-- taskId = " + taskId + "\tmaxEndTime = " + maxEndTime);
                    if(maxEndTime > maxDuration) {
                        latestTaskId = taskId;
                        maxDuration = maxEndTime;
                    }
                }
            }
        }
        return latestTaskId;
    }
}
