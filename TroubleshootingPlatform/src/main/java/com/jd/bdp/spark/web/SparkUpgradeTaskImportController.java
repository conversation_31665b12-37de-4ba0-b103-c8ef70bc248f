package com.jd.bdp.spark.web;

import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.bean.enums.SparkUpgradeOpTypeEnum;
import static com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum.*;
import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

import com.jd.bdp.common.Buffalo4Dao;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
import com.jd.bdp.utils.MapperManager;
//import com.jd.bdp.utils.MybatisUtils;
import com.jd.bdp.utils.SparkUpgradeUtils;
import com.jd.common.web.LoginContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;

/**
 * spark升级-buffalo4任务批量导入、批量回滚
 */
@WebServlet("/sparkUpgradeTaskImport")
public class SparkUpgradeTaskImportController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeTaskImportController.class.getName());

    private SparkUpgradeTaskMapper taskMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeTaskMapper.class);
    
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String taskIds = req.getParameter("taskIds");
        String opType = req.getParameter("opType");
        String pin = LoginContext.getLoginContext().getPin();
        String msg = "";
        
        if(StringUtils.isBlank(taskIds)){
            msg = "任务id不能为空, 多个id使用逗号分隔";
        }else{
            logger.info(String.format("=== sparkUpgradeTaskImport, 开始批量操作任务：%s", taskIds));
            
            // 判断是否存在非法字符
            Set<String> set = new HashSet<>();
            for(String taskId: taskIds.split(",") ){
               try{
                   Integer.parseInt(taskId);
                   set.add(taskId);
               }catch (Exception e){
                   msg = String.format("存在非法字符: %s, 必须为数字", taskId);
                   logger.info(String.format("=== 存在非法字符: %s, 必须为数字", taskId));
                   req.setAttribute("msg", msg);
                   req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
                   return;
               }
            }
            logger.info(String.format("=== 本次待升级的任务总数量为: %s", set.size()));
            
            StringBuilder buffer = new StringBuilder("");
            Map<String, String> failedMap = new HashMap<>();
            SparkUpgradeOpTypeEnum opTypeEnum = SparkUpgradeOpTypeEnum.valueOf(opType.toUpperCase());
            switch(opTypeEnum){
                case UPGRADE: failedMap = batchRegisterBuffaloTask(set, pin); break;
                case ROLLBACK: failedMap = batchRollbackBuffaloTask(set, pin); break;
                case RESET: failedMap = batchResetBuffaloTask(set, pin); break;
                case OTHER: 
                    default: 
                        msg = String.format("批量操作类型:%s不正确，必须是:UPGRADE、ROLLBACK", opType);
                        logger.info(msg);
                        req.setAttribute("msg", msg);
                        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
                        return;
            }
            
            if(failedMap.size() > 0){
                logger.info(String.format("=== 批量操作：%s, 失败: %s", opTypeEnum.getName(),  MapperManager.writeValueAsString(failedMap)));
                for(Map.Entry<String, String> entry: failedMap.entrySet()){
                    buffer.append("<br>").append(String.format("%s:%s", entry.getKey(), entry.getValue())).append("</br>");
                }
                msg = buffer.toString();
            }else{
                msg = String.format("本次共%s: %s个成功", opTypeEnum.getName(), set.size());
            }
            logger.info(String.format("=== 本次批量操作结果： %s", msg));
        }
        req.setAttribute("msg", msg);
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }


    /**
     * 批量保存升级任务信息
     * @param taskIds 任务id
     * @return
     */
    private Map<String, String> batchRegisterBuffaloTask(Set<String> taskIds, String pin){
        Map<String, String> failedMap = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = sdf.format(new Date());
//        SparkUpgradeTaskMapper taskMapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);
        for(String taskId: taskIds){
            SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(taskId);
            if(task != null && UPGRADED.getCode().equalsIgnoreCase(task.getStatus())){
                // 任务已存在，无需重复导入
                logger.info(String.format("=== taskId: %s, 任务已升级，无需重复导入", taskId));
                failedMap.put(taskId, "导入失败: 任务已升级，无需重复导入");
                continue;
            }

            String originVersion = getTaskEngineOriginVersion(taskId);
            logger.info(String.format("=== taskId: %s, originVersion: %s", taskId, originVersion));
            if(StringUtils.isBlank(originVersion)){
                // 无法获取到任务的原始引擎版本
                logger.info(String.format("=== taskId: %s, 无法获取任务的原始引擎版本", taskId));
                failedMap.put(taskId, "导入失败：无法获取任务的原始引擎版本");
                continue;
            }

            // 调用buffalo接口修改引擎版本
            try {
                boolean isSuc = SparkUpgradeUtils.upgradeSparkUpgradeTask(taskId);
                if(!isSuc){
                    logger.info(String.format("=== taskId: %s, 调用buffalo修改引擎版本失败", taskId));
                    failedMap.put(taskId, "导入失败: 调用buffalo修改引擎版本失败");
                    continue;
                }
            } catch (Exception e) {
                logger.info(String.format("=== taskId: %s, 调用buffalo修改引擎版本发生异常: %s", taskId, CommonUtil.exceptionToString(e)));
                failedMap.put(taskId, "导入失败: 调用buffalo修改引擎版本失败"+e.getMessage());
                continue;
            }

            // 保存到数据库
            SparkUpgradeTaskBean bean = new SparkUpgradeTaskBean();
            bean.setTaskId(Integer.parseInt(taskId));
            bean.setOriginVersion(originVersion);
            bean.setStatus(UPGRADED.getCode());
            bean.setCreateTime(now);
            bean.setCreator(pin);
            
            int affectedSize = taskMapper.insertSparkUpgradeTask(bean);
            if(affectedSize > 0){
                logger.info(String.format("=== taskId: %s, 升级任务导入成功", taskId));
            }else{
                logger.info(String.format("=== taskId: %s, 保存mysql失败, 影响条数为：0", taskId));
                failedMap.put(taskId, "导入失败: 写入mysql失败");
            }
        }
        
        return failedMap;
    }


    /**
     * 获取buffalo任务的引擎原始版本， 递归寻找距2024-08-31起最近一天非空的实例引擎版本作为任务的原始版本（9月1才开始升级）
     * @param taskId 任务id
     * @return 可能无法获取到引擎的原始版本
     */
    public static String getTaskEngineOriginVersion(String taskId){
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Long beginTime = sdf.parse("2024-08-30 23:59:59").getTime();
            Long endTime = new Date().getTime();

            // 递归寻找距2024-08-31起最近一天非空的实例引擎版本
            List<Map<String, Object>> runLogResult = new ArrayList<>();
            while( (runLogResult == null || runLogResult.size() == 0) && beginTime < endTime ){
                String runTime = sdf.format(new Date(beginTime));
                logger.info(String.format("=== taskId: %s, run_time: %s", taskId, runTime));
                runLogResult = Buffalo4Dao.getRunLogByTaskIdAndTime(Long.parseLong(taskId), runTime, 1L);
                beginTime = beginTime + 24 * 3600 * 1000;  // 每次加一天
            }

            if(runLogResult != null && runLogResult.size() > 0){
                logger.info(String.format("=== getRunLogByTaskIdAndTime result: %s", MapperManager.writeValueAsString(runLogResult)));
                Map<String, Object> item = runLogResult.get(0);
                String envStr = String.valueOf(item.get("env"));
                logger.info(String.format("=== taskId: %s, env: %s", taskId, envStr));
                for(String kv: envStr.split(",")) {
                    if (kv.contains("JDHXXXXX_ENGINE_VERSION") && kv.split("=").length > 1) {
                        return kv.split("=")[1];
                    }
                }
                
                // 默认返回default版本
                return "default";
            }else{
                logger.info(String.format("=== getRunLogByTaskIdAndTime查询结果为空，可能是新任务， taskId: %s runTime: %s", taskId, sdf.format(new Date(endTime))));
                return "default";
            }
        } catch (Exception e) {
            logger.info(String.format("=== 获取taskId: %s 至2024-09-01起最近一次非空的引擎版本异常: %s", taskId, CommonUtil.exceptionToString(e)));
            return null;
        }
    }

    /**
     * 批量回滚已升级的任务
     * @param taskIds 任务集合
     * @param pin 操作人
     * @return
     */
    private Map<String, String> batchRollbackBuffaloTask(Set<String> taskIds, String pin){
        Map<String, String> failedMap = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = sdf.format(new Date());
//        SparkUpgradeTaskMapper taskMapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);

        for(String taskId: taskIds){
            SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(taskId);
            if(task == null){
                // 任务不存在，请先导入
                logger.info(String.format("=== taskId: %s, 任务不存在，请先导入", taskId));
                failedMap.put(taskId, "回滚失败: 任务不存在，请先导入");
                continue;
            }
            
            if(!UPGRADED.getCode().equalsIgnoreCase(task.getStatus())){
                // 任务的状态不对，只有UPGRADED状态的任务才能回滚
                logger.info(String.format("=== taskId: %s, 必须是已升级的任务才能回滚，当前任务状态:%s", taskId, task.getStatus()));
                failedMap.put(taskId, "回滚失败: 必须是已升级的任务才能回滚");
                continue;
            }

            if(StringUtils.isBlank(task.getOriginVersion())){
                // 任务的原始引擎版本为空，无法回滚
                logger.info(String.format("=== taskId: %s, 任务的原始引擎版本为空，无法回滚", taskId));
                failedMap.put(taskId, "回滚失败: 任务的原始引擎版本为空，无法回滚");
                continue;
            }

            // 调用buffalo接口修改引擎版本
            try {
                boolean isSuc = SparkUpgradeUtils.rollbackSparkUpgradeTask(taskId, task.getOriginVersion());
                if(!isSuc){
                    logger.info(String.format("=== taskId: %s, 回滚失败， 调用buffalo修改引擎版本失败", taskId));
                    failedMap.put(taskId, "回滚失败: 调用buffalo修改引擎版本失败");
                    continue;
                }
            } catch (Exception e) {
                logger.info(String.format("=== taskId: %s, 回滚失败， 调用buffalo修改引擎版本发生异常: %s", taskId, CommonUtil.exceptionToString(e)));
                failedMap.put(taskId, "回滚失败: 调用buffalo修改引擎版本失败"+e.getMessage());
                continue;
            }

            // 修改任务状态
            SparkUpgradeTaskBean newTask = new SparkUpgradeTaskBean();
            newTask.setStatus(ROLLBACKED.getCode());
            newTask.setUpdateTime(now);
            newTask.setTaskId(task.getTaskId());
            int affectedSize = taskMapper.updateTask(newTask);
            if(affectedSize > 0){
                logger.info(String.format("=== taskId: %s, 任务回滚成功", taskId));
            }else{
                logger.info(String.format("=== taskId: %s, 回滚失败，修改mysql中任务状态失败, 影响条数为：0", taskId));
                failedMap.put(taskId, "回滚失败: 修改mysql中任务状态失败");
            }
        }
        
        return failedMap;
        
    }


    /**
     * 批量重置任务
     * @param taskIds 任务集合
     * @param pin 操作人
     * @return
     */
    private Map<String, String> batchResetBuffaloTask(Set<String> taskIds, String pin){
        Map<String, String> failedMap = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = sdf.format(new Date());
//        SparkUpgradeTaskMapper taskMapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);

        for(String taskId: taskIds){
            SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(taskId);
            if(task == null){
                // 任务不存在，请先导入
                logger.info(String.format("=== taskId: %s, 任务不存在，请先导入", taskId));
                failedMap.put(taskId, "重置失败: 任务不存在，请先导入");
                continue;
            }

            if(UPGRADED.getCode().equalsIgnoreCase(task.getStatus()) 
                    || ROLLBACKED.getCode().equalsIgnoreCase(task.getStatus()) 
                    || DOUBLE_RUN_SUCCESS.getCode().equalsIgnoreCase(task.getStatus())
                    || EXPLAINING.getCode().equalsIgnoreCase(task.getStatus())
                    || DOUBLE_RUN_TASK_CREATING.getCode().equalsIgnoreCase(task.getStatus()) 
                    || DOUBLE_RUNNING.getCode().equalsIgnoreCase(task.getStatus())){
                // 任务的状态不对，跳过UPGRADED、ROLLBACKED、DOUBLE_RUN_SUCCESS、DOUBLE_RUN_TASK_CREATING、DOUBLE_RUNNING、EXPLAINING 状态的任务
                logger.info(String.format("=== taskId: %s, 重置失败: %s的任务无法重置状态", taskId, task.getStatus()));
                failedMap.put(taskId, String.format("重置失败: %s的任务无法重置状态", task.getStatus()));
                continue;
            }

            // 修改任务状态
            SparkUpgradeTaskBean newTask = new SparkUpgradeTaskBean();
            newTask.setStatus(UN_EXE.getCode());
            newTask.setUpdateTime(now);
            newTask.setTaskId(task.getTaskId());
            int affectedSize = taskMapper.updateTask(newTask);
            if(affectedSize > 0){
                logger.info(String.format("=== taskId: %s, 任务重置成功成功", taskId));
            }else{
                logger.info(String.format("=== taskId: %s, 任务重置失败，修改mysql中任务状态失败, 影响条数为：0", taskId));
                failedMap.put(taskId, "重置失败: 修改mysql中任务状态失败");
            }
        }

        return failedMap;

    }
   
}
