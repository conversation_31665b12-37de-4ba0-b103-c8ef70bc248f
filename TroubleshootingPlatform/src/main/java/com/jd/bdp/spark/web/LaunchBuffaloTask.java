package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.common.Buffalo4TaskManager;
import com.jd.bdp.common.SDFThreadLocal;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Calendar;

@WebServlet("/launchTask")
public class LaunchBuffaloTask extends HttpServlet {

    /**
     * 重写父类方法，处理HTTP的GET请求
     * @param req HTTP请求对象
     * @param resp HTTP响应对象
     */
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req,resp);
    }

    /**
     * 重写doPost方法，处理HTTP POST请求
     * @param req HTTP请求对象
     * @param resp HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        req.setCharacterEncoding("utf-8");
        resp.setContentType("text/html;charset=utf-8");
        PrintWriter writer = resp.getWriter();
        String buffaloId = req.getParameter("buffaloId");
        String args = req.getParameter("args");
        String s = launchTask(buffaloId, args).toJSONString();
        writer.write(s);
    }

    /**
     * 启动任务
     * @param taskId 任务ID
     * @return HTTP响应
     * @throws Exception 异常
     */
    public JSONObject launchTask(String taskId, String args) throws IOException {
        Calendar calendar = Calendar.getInstance();
        JSONObject jsonObject = Buffalo4TaskManager.buffalo4CreateTaskInstanceN(Integer.parseInt(taskId),args,
                SDFThreadLocal.get().format(calendar.getTime()));
        return jsonObject;
    }

}
