package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

/**
 * 白泽回调接口文档： https://joyspace.jd.com/pages/mmN1OAj2oZiQDXogxTdR
 *
 */
@WebServlet("/TaskFailCallback")
public class TaskFailCallbackController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(TaskFailCallbackController.class.getName());
    private static final String CONTENT_TYPE = "text/html;charset=utf-8";
    private static final String ENCODING = "UTF-8";
    private static final String ENGINE = "SPARK_SHELL";
    private static final String BUFFALO_VERSION = "BUFFALO4";
    private static final String TARGET_PACKAGE = "SPARK_SHELL_DISABLED_HUDI_PG";
    private static final String UMP_KEY = "SimpleHttpClient.KongMing.AddBuffaloTemplate";
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        resp.setContentType(CONTENT_TYPE);
        req.setCharacterEncoding(ENCODING);
        String requestBody = readRequestBody(req);
        logger.info("Request body: " + requestBody);
        JSONObject jsonObject = JSON.parseObject(requestBody);
        if (jsonObject == null) {
            logger.warning("Failed to parse request body as JSON");
            return;
        }
        processAlerts(jsonObject.getJSONArray("alerts"));
    }

    private void processAlerts(JSONArray alerts) {
        if (alerts == null || alerts.isEmpty()) {
            logger.warning("Alerts array is null or empty");
            return;
        }
        for (int i = 0; i < alerts.size(); i++) {
            JSONObject alert = alerts.getJSONObject(i);
            if (alert == null) {
                logger.warning("Alert at index "+i+" is null");
                continue;
            }
            JSONObject labels = alert.getJSONObject("labels");
            if (labels == null) {
                logger.warning("Labels for alert at index "+i+" are null");
                continue;
            }
            String taskId = labels.getString("taskId");
            if (taskId == null) {
                logger.warning("Task ID for alert at index "+i+" is null");
                continue;
            }

            // 禁用以下功能，需与架构师评审才可启用
//            Set<String> kongmingPackage = XBPController.getKongmingPackage(ENGINE, taskId, null, BUFFALO_VERSION);
//            logger.info("Task ID: "+taskId+" Retrieved package: " + kongmingPackage);
//
//            if (!kongmingPackage.contains(TARGET_PACKAGE)) {
//                logger.info("Adding package: " + TARGET_PACKAGE + " for Task ID: " + taskId);
//                XBPController.addKongmingPackage(taskId, null, BUFFALO_VERSION, UMP_KEY, ENGINE, TARGET_PACKAGE);
//                logger.info("Package "+TARGET_PACKAGE+" added successfully for Task ID: " + taskId);
//            } else {
//                logger.info(TARGET_PACKAGE + " already exists for Task ID: " + taskId);
//            }
        }
    }

    private String readRequestBody(HttpServletRequest req) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = req.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }
}
