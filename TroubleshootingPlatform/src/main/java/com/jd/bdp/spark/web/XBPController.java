package com.jd.bdp.spark.web;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.bdp.bean.*;
import com.jd.bdp.common.*;
import com.jd.bdp.filter.HrUserServiceImpl;
import com.jd.bdp.filter.IdeJobServiceImpl;
import com.jd.bdp.scheduler.Scheduler;
import com.jd.common.util.StringUtils;
import com.jd.jbdp.edc.api.extract.model.dto.JSFResultDTO;
import com.jd.jbdp.edc.model.vo.extract.JobVO;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.xbp.jsf.api.request.ticket.CreateParam;
import com.jd.xbp.jsf.api.response.XbpResponse;
import com.jd.xbp.jsf.api.response.ticket.Flow;
import com.jd.xbp.jsf.api.response.ticket.Ticket;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.logging.log4j.util.Strings;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Logger;

import static com.jd.bdp.bean.Constants.DB_NAME_NATURE;
import static com.jd.bdp.bean.XbpForm.*;
import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;
import static com.jd.bdp.spark.web.DorisController.*;

@WebServlet("/xbpController")
public class XBPController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(XBPController.class.getName());
    private static final String spark2To3Desc = "<br/>" +
            "<h1>离线计算引擎版本升级至Spark 3.4</h1>\n" +
            "<br/>\n" +
            "<h2>一、背景</h2>\n" +
            "为了提升Spark 引擎的的性能和稳定性，我们将对 Spark 引擎升级为 3.4 版本。<br/>\n" +
            "<br/>" +
            "<ul>\n" +
            "<li>Spark 3.4 主要优势：</li>\n" +
            "<li>更快的查询速度</li>\n" +
            "<li>更优化的资源管理</li>\n" +
            "<li>增强的稳定性</li>\n" +
            "<li>更多新功能，满足您的数据处理需求<br/>" +
                "<ul>" +
                    "<li>AQE（Adaptive Query Execution）：动态调整查询计划以优化执行效率。</li>\n" +
                    "<li>DPP（Dynamic Partition Pruning）：动态分区裁剪，减少不必要的数据扫描。</li>\n" +
                    "<li>Row-level Runtime Filter：行级运行时过滤器，提高数据处理精度。</li>\n" +
                    "<li>PBS（Push-Based Shuffle）：基于推送的 Shuffle 机制，提升数据交换性能。</li>" +
                "</ul>\n" +
            "</li>" +
            "</ul>" +
            "<br/>\n" +
            "Spark3.4版本更详细的介绍：https://joyspace.jd.com/pages/kgHOIf5lsnNkzNRpsBb0<br/>\n" +
            "<br/>\n" +
            "<h2>二、需要注意的项</h2>\n" +
            "（一）存在非幂等函数多次运行结果不同：<br>" +
            "如 row_number partition by存在相同值、collect_set、random、timestamp函数等。<br/>\n" +
            "（二）Scala版本兼容：<br>" +
            "scala版本最低要求是2.12.11<br>" +
            "（三）Python版本最低要求：<br>" +
            "python最低要求是3.9<br>" +
            "<br/>\n" +
            "\n" +
            "其它注意事项：https://joyspace.jd.com/pages/Q4ipeTXCjOdbAru88Orx<br/>\n" +
            "<br/>\n" +
            "<h2>三、联系我们</h2>\n" +
            "<br/>\n" +
            "紧急联系方式：http://dp.jd.com/portal/linkMe 中“离线技术支持-计算引擎(hive&spark)”值班\n";

    private static final String hiveToSpark3Desc = "<br/>" +
            "<h1>离线计算引擎由Hive升级至Spark v3版本</h1>\n" +
            "<br/>\n" +
            "<h2>一、背景</h2>\n" +
            "平台将会把Hive任务交由Spark引擎处理，升级后可预见到的收益有：<br/>\n" +
            "1. 执行效率提升，将更快的完成任务；<br/>\n" +
            "2. 更节省计算资源，意味着有限的资源下可以支持更多的业务；<br/>\n" +
            "3. 支持Adaptive Execution技术，缓解因数据倾斜引发的各类问题；<br/>\n" +
            "<br/>\n" +
            "Spark3.0版本更详细的介绍：https://cf.jd.com/pages/viewpage.action?pageId=326920005<br/>\n" +
            "<br/>\n" +
            "<h2>二、需要注意的项</h2>\n" +
            "相同的程序/脚本在不同的引擎间（Hive引擎、Spark 2.x、Spark 3.x版本）运行，可能存在不兼容的问题。以下梳理了常见的不兼容问题和相应的解决方案。更多的兼容性问题请查阅文档。<br/>\n" +
            "Spark 2.x与Spark 3.x的兼容：https://cf.jd.com/pages/viewpage.action?pageId=330786857<br/>\n" +
            "Spark与Hive的兼容：https://cf.jd.com/pages/viewpage.action?pageId=180370681<br/>\n" +
            "<br/>\n" +
            "遇到问题处理方式及注意事项：https://cf.jd.com/pages/viewpage.action?pageId=348756639<br/>\n" +
            "<br/>\n" +
            "<h2>三、联系我们</h2>\n" +
            "<br/>\n" +
            "紧急联系方式：http://dp.jd.com/portal/linkMe 中“离线技术支持-计算引擎(hive&spark)”值班\n";
private static final String hiveToSpark2Desc = "<br/>" +
            "<h1>离线计算引擎由Hive升级至Spark版本</h1>\n" +
            "<br/>\n" +
            "<h2>一、背景</h2>\n" +
            "平台将会把Hive任务交由Spark引擎处理，升级后可预见到的收益有：<br/>\n" +
            "1. 执行效率提升，将更快的完成任务；<br/>\n" +
            "2. 更节省计算资源，意味着有限的资源下可以支持更多的业务；<br/>\n" +
            "3. 支持Intel Adaptive Execution技术，缓解因数据倾斜引发的各类问题；<br/>\n" +
            "<br/>\n" +
            "Spark3.0版本更详细的介绍：https://cf.jd.com/pages/viewpage.action?pageId=326920005<br/>\n" +
            "<br/>\n" +
            "<h2>二、需要注意的项</h2>\n" +
            "相同的程序/脚本在不同的引擎间（Hive引擎、Spark 2.x）运行，可能存在不兼容的问题。以下梳理了常见的不兼容问题和相应的解决方案。更多的兼容性问题请查阅文档。<br/>\n" +
            "Spark与Hive的兼容：https://cf.jd.com/pages/viewpage.action?pageId=180370681<br/>\n" +
            "<br/>\n" +
            "遇到问题处理方式及注意事项：https://cf.jd.com/pages/viewpage.action?pageId=348756639<br/>\n" +
            "<br/>\n" +
            "<h2>三、联系我们</h2>\n" +
            "<br/>\n" +
            "紧急联系方式：http://dp.jd.com/portal/linkMe 中“离线技术支持-计算引擎(hive&spark)”值班\n";

    private static final String rssDesc = "<br/>" +
            "您好：<br/>" +
            "<h3>您的任务shuffle数据量较大，为保障您的任务运行时效性，现推荐将您的任务使用RSS服务。</h3>" +
            "<br/>一般来说shuffle数据量大于2TB以上都建议使用Remote Shuffle Service。<br/>" +
            "<br/>审批通过后会自动上线至RSS，并且Spark版本会切换到Spark2.4版本，请审批通过后一定要重跑验证，有问题及时联系<br/>" +
            "<h3>如何查看shuffle数据量</h3>" +
            "那么如何查看自己的shuffle数据量，可以打开Spark History页面（如何查看History Server这里有文档https://cf.jd.com/pages/viewpage.action?pageId=253780395）。</br>" +
            "</br>打开history页面以后，点开executor标签页，查看total那一行的shuffle read，依据这一个指标来判断你的任务是否需要上线到RSS。如下图所示。</br>" +
            "<img src=\"https://storage.360buyimg.com/moneta/RSS-shuffle.png\" alt=\"这个任务的shuffle数据量是真的好大。\" /></br>" +
            "<h3>RSS整体架构</h3>接下来通过一张简单的图来介绍一下RSS的工作原理是怎么保障任务的稳定性运行的。如图。</br></br>" +
            "<img src=\"https://storage.360buyimg.com/moneta/RSS.png\" alt=\"这就是RSS的运行机理图，主要解决的是Executor远程拉取Shuffle数据时，因为机器的瓶颈导致拉取失败。那么将数据存储到hdfs以后就不再会有这种失败了。\" />  </br>" +
            "</br>RSS的资料：https://cf.jd.com/pages/viewpage.action?pageId=377586092</br></br>" +
            "紧急联系方式：http://dp.jd.com/portal/linkMe 中“离线技术支持-计算引擎(hive&spark)”值班\n";

    public static final CommonBean commonBean = YamlUtil.loadYaml("common.yaml", CommonBean.class);

    private static final CloseableHttpClient httpClient = HttpClients.createDefault();
    private static HrUserServiceImpl hrUserService = APPLICATION_CONTEXT.getBean(HrUserServiceImpl.class);
    private static IdeJobServiceImpl ideJobService = APPLICATION_CONTEXT.getBean(IdeJobServiceImpl.class);
    private static RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(5000)
            .setConnectionRequestTimeout(10000).setSocketTimeout(10000).build();

    private static final String CREATE_FLOW = "createFlow";
    private static final String CALLBACK_FLOW = "callback";
    private static final String CALLBACK_FLOW_PLATFORM_RECHECK = "callback_platform_recheck";
    private static final String CALLBACK_FLOW_PLATFORM_RECHECK_BACK = "callback_platform_recheck_back";
    private static final String CALLBACK_INSPECTION_FLOW = "callback_inspection";
    private static final String CALLBACK_PRESS_FLOW = "callback_press";
    private static final String CALLBACK_PRESTO_FLOW = "callback_presto";
    private static final String CREATE_XBP_FLOW = "createXbp";

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        StringBuilder msg = new StringBuilder();
        if(CREATE_FLOW.equals(req.getParameter("processType"))) {
            Integer processId = Integer.parseInt(req.getParameter("processId"));
            String flowType = req.getParameter("flowType");
            String taskids = req.getParameter("taskids");
            String taskType = req.getParameter("taskType");
            String taskVersion;
            String source = req.getParameter("source");
            String currentEngine = req.getParameter("currentEngine");
            String engine = req.getParameter("engine");
            String currentEngineVersion = req.getParameter("currentEngineVersion");
            String version = req.getParameter("version");
            String ccSuper = req.getParameter("ccSuper");
            String inspectionItem = StringUtils.defaultString(req.getParameter("inspection_item"), "");//巡检项
            String inspectionDesc = StringUtils.defaultString(req.getParameter("inspection_desc"), "");//问题描述
            String inspectionLevel = StringUtils.defaultString(req.getParameter("inspection_level"), "");//风险等级
            String inspection7day = StringUtils.defaultString(req.getParameter("inspection_7day"), "");//近7天出现次数
            String inspectionPropose = StringUtils.defaultString(req.getParameter("inspection_propose"), "");//治理建议
            String inspectionLastday = StringUtils.defaultString(req.getParameter("inspection_lastday"), "");//截止时间
            String inspectionPlat = StringUtils.defaultString(req.getParameter("inspection_plat"), "");//平台修改能力
            String kongmingTemplate = StringUtils.defaultString(req.getParameter("kongMingTemplate"), "");
            String rtfMsg = req.getParameter("rtfMsg");
            String[] coreTimeLimitNormalL3Templates = req.getParameterValues("CORE_TIME_LIMIT_NORMAL_L3_Template");
            List<String> kongmingTemplates = new ArrayList<>();
            if(SOURCE_ZI_ZHU.equals(source)) {
                taskVersion = "IDE_JOB";
                if ("3.0".equals(version)) {
                    kongmingTemplates.add("HIVE_TASK|HIVETASK_SPARK_VERSION_3_0");
                }
            } else if("9N_BUFFALO4".equals(source)) {
                taskVersion = "9N_BUFFALO4";
            } else {
                taskVersion = "BUFFALO4";
            }
            if (coreTimeLimitNormalL3Templates != null) {
                kongmingTemplates.addAll(Lists.newArrayList(coreTimeLimitNormalL3Templates));
            }
            if (StringUtils.isNotEmpty(kongmingTemplate)) {
                kongmingTemplates.add(kongmingTemplate);
            }
            if ("Hive2Spark".equals(flowType)) {
                rtfMsg += hiveToSpark3Desc;
            } else if ("Hive2SparkV2".equals(flowType)) {
                rtfMsg += hiveToSpark2Desc;
            } else if("Spark2To3".equals(flowType)) {
                rtfMsg += spark2To3Desc;
            } else if("RSS".equals(flowType)) {
                rtfMsg += rssDesc;
            } else {
                rtfMsg += "";
            }
            msg.append("创建审批流成功: ");
            for (String taskId : taskids.split(",")) {
                if(StringUtils.isNotEmpty(taskId)) {
                    String taskIdTrim = taskId.trim();
                    String[] split = taskIdTrim.split(":");
                    String taskIdNew = taskIdTrim;
                    String actionIds = "";
                    if(split.length > 1) {
                        taskIdNew = split[0];
                        actionIds = split[1];
                    }
                    TaskInfo buffaloTaskInfo = getTaskInfo(source, taskIdNew);
                    //String taskOwner = buffaloTaskInfo.getManagers().split(",")[0];
                    String taskOwner = getTaskOwner(buffaloTaskInfo.getManagers());
                    JSONObject superiorBaseInfo = hrUserService.getSuperiorBaseInfo(taskOwner);
                    String superErp = StringUtils.defaultIfEmpty(superiorBaseInfo.getString("userName"), "");
                    String superEmail = StringUtils.defaultIfEmpty(superiorBaseInfo.getString("email"), "");
                    String insertSql = "INSERT INTO `t_km_xbp_flow`\n" +
                            "            (`flow_status`,\n" +
                            "             `flow_status_msg`,\n" +
                            "             `flow_type`,\n" +
                            "             `taskids`,\n" +
                            "             `actionids`,\n" +
                            "             `taskname`,\n" +
                            "             `username`,\n" +
                            "             `buffalo_version`,\n" +
                            "             `source`,\n" +
                            "             `task_type`,\n" +
                            "             `process_id`,\n" +
                            "             `current_engine`,\n" +
                            "             `engine`,\n" +
                            "             `current_engine_version`,\n" +
                            "             `version`,\n" +
                            "             `kongming_template`,\n" +
                            "             `cnt_task`,\n" +
                            "             `inspection_item`,\n" +
                            "             `inspection_desc`,\n" +
                            "             `inspection_level`,\n" +
                            "             `inspection_7day`,\n" +
                            "             `inspection_lastday`,\n" +
                            "             `inspection_propose`,\n" +
                            "             `inspection_plat`,\n" +
                            "             `super_cc`,\n" +
                            "             `super_erp`,\n" +
                            "             `super_email`,\n" +
                            "             `rtfmsg`)" +
                            "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
                    int i = KongmingService.executeUpdate(insertSql, "nature",
                            EnumXbpStatus.CREATING_XBP.statusCode,
                            EnumXbpStatus.CREATING_XBP.description, flowType, taskIdNew, actionIds,
                            buffaloTaskInfo.getTaskName(), buffaloTaskInfo.getManagers(), taskVersion,
                            source,taskType,processId,currentEngine,engine,currentEngineVersion,version,
                            Strings.join(kongmingTemplates, ','), 1, inspectionItem, inspectionDesc,
                            inspectionLevel, inspection7day, inspectionLastday, inspectionPropose,
                            inspectionPlat, ccSuper, superErp, superEmail, rtfMsg);
                    msg.append("taskId: " + taskIdNew + " actionIds: " + actionIds);
                }
            }
        } else if (CALLBACK_FLOW.equals(req.getParameter("processType"))) {
            logger.info("doGet: "+JSON.toJSONString(req.getParameterMap()));
            msg.append("回调时间：" + SDFThreadLocal.get().format(new Date()) + "<br/>");
            Integer ticketId = Integer.parseInt(req.getParameter("ticketId"));
            EnumXbpStatus flowStatus = processCallBack(ticketId, msg, FLOW_NAME, EnumXbpStatus.APPROVED_XBP,
                    EnumXbpStatus.REJECT_XBP);
            XbpResponse<Integer> response = XbpManager.xbpComment(ticketId, msg.toString());
            updateMysqlForComment(ticketId, flowStatus, response, new java.sql.Timestamp(new Date().getTime()));
        } else if (CALLBACK_FLOW_PLATFORM_RECHECK.equals(req.getParameter("processType"))) {
            logger.info(CALLBACK_FLOW_PLATFORM_RECHECK + ": doGet: "+JSON.toJSONString(req.getParameterMap()));
            Integer ticketId = Integer.parseInt(req.getParameter("ticketId"));
            EnumXbpStatus flowStatus = processCallBack(ticketId, msg, FLOW_NAME_PLATFORM_RECHECK,
                    EnumXbpStatus.PLATFORM_RECHECK_APPROVED, EnumXbpStatus.PLATFORM_RECHECK_REJECT);
            if (flowStatus != null) {
                updateMysqlForComment(ticketId, flowStatus, null, new java.sql.Timestamp(new Date().getTime()));
            }
            msg.append(flowStatus);
        } else if (CALLBACK_FLOW_PLATFORM_RECHECK_BACK.equals(req.getParameter("processType"))) {
            logger.info(CALLBACK_FLOW_PLATFORM_RECHECK_BACK + ": doGet: " + JSON.toJSONString(req.getParameterMap()));
            String[] erps = (String[]) req.getParameterMap().get("erp");
            String username = Scheduler.commonBean.getXbpNatureUsername();
            if (erps != null && erps.length != 0) {
                String reqErp = StringUtils.trimToNull(String.valueOf(erps[0]));
                if (reqErp != null) {
                    username = reqErp;
                }
            }
            Integer ticketId = Integer.parseInt(req.getParameter("ticketId"));
            // 回退到上一级
            XbpManager.resetXbpResponse(ticketId, username, "验证不通过，请重新处理!");
            msg.append("验证不通过，请重新处理!");
            XbpResponse<Integer> response = XbpManager.xbpComment(ticketId, msg.toString());
            // 更新数据状态为 待用户审批
            updateXbpFlowStatus(ticketId, EnumXbpStatus.CREATED_XBP);
        } else if (req.getParameter("remindTickets") != null) {
            String statusTickets = req.getParameter("remindTickets");
            for (String ticketId : statusTickets.split(",")) {
                if (StringUtils.isNumeric(ticketId)) {
                    int ticketIdInt = Integer.parseInt(ticketId);
                    XbpResponse<Boolean> ticketXbpResponse = XbpManager.remindXbpResponse(ticketIdInt);
                    if (ticketXbpResponse == null || ticketXbpResponse.getCode() != 0) {
                        msg.append("xbp get ticket接口调用失败，请联系ERP: wuguoxiao");
                    } else {
                        msg.append("remind: ").append(ticketXbpResponse.getCode());
                    }
                }
            }
        } else if (req.getParameter("recreateTickets") != null) {
            String statusTickets = req.getParameter("recreateTickets");
            String processId = req.getParameter("processId");
            for (String ticketId : statusTickets.split(",")) {
                if (StringUtils.isNumeric(ticketId)) {
                    int ticketIdInt = Integer.parseInt(ticketId);
                    XbpResponse<Boolean> ticketXbpResponse = XbpManager.recreateXbpResponse(ticketIdInt,
                            Integer.parseInt(processId), Scheduler.commonBean.getXbpNatureUsername());
                    if (ticketXbpResponse == null || ticketXbpResponse.getCode() != 0) {
                        msg.append("xbp get ticket接口调用失败，请联系ERP: wuguoxiao");
                    } else {
                        msg.append("remind: ").append(ticketXbpResponse.getCode());
                    }
                }
            }
        } else if (req.getParameter("statusTickets") != null) {
            String statusTickets = req.getParameter("statusTickets");
            for (String ticketId : statusTickets.split(",")) {
                if (StringUtils.isNumeric(ticketId)) {
                    int ticketIdInt = Integer.parseInt(ticketId);
                    XbpResponse<Ticket> ticketXbpResponse = XbpManager.getTicketXbpResponse(ticketIdInt);
                    if (ticketXbpResponse == null || ticketXbpResponse.getCode() != 0) {
                        msg.append("xbp get ticket接口调用失败，请联系ERP: wuguoxiao");
                    } else {
                        Ticket data = ticketXbpResponse.getData();
                        Integer status = data.getStatus();
                        // 申请单状态：-1驳回，0进行中，1结束，2撤回
//                        if (status == 1) {
//                            int i = updateXbpFlowStatus(ticketIdInt, EnumXbpStatus.COMPLETED);
//                            msg.append("Xbp completed: ").append(i);
//                        } else
                        if (status == 2) {
                            int i = updateXbpFlowStatus(ticketIdInt, EnumXbpStatus.REVOKE_XBP);
                            msg.append("Xbp revoke: ").append(i);
                        }
                    }
                }
            }
        } else if (req.getParameter("deleteIds") != null) {
            String deleteIds = req.getParameter("deleteIds");
            Set<String> ids = new HashSet<>();
            for (String id : deleteIds.split(",")) {
                if (StringUtils.isNumeric(id)) {
                    ids.add(id);
                }
            }
            if(!ids.isEmpty()) {
                int nature = KongmingService.executeUpdate("delete from t_km_xbp_flow" +
                        " where id in (" + String.join(",", ids) + ")", "nature");
                msg.append("delete: ").append(nature);
            }
        } else if (CREATE_XBP_FLOW.equals(req.getParameter("processType"))) {
            msg.append(schedulerCreateXbpFlow());
        } else if("1".equals(req.getParameter("test"))) {
            if("press".equals(req.getParameter("type"))) {
                XbpProcessServiceForPress xbpProcessServiceForPress = new XbpProcessServiceForPress();
                List<Map<String, Object>> xbpFlowForInspection = xbpProcessServiceForPress.getXbpFlow("965656");
                msg.append(JSON.toJSONString(xbpFlowForInspection));
            } else if ("presto".equals(req.getParameter("type"))) {
                XbpProcessServiceForPresto xbpProcessServiceForPresto = new XbpProcessServiceForPresto();
                List<Map<String, Object>> xbpFlowForInspection = xbpProcessServiceForPresto.getXbpFlow("mart_rmb_dps");
                msg.append(JSON.toJSONString(xbpFlowForInspection));
            } else {
                List<Map<String, Object>> xbpFlowForInspection = getXbpFlowForInspection("965656", "2022-10-12");
                msg.append(JSON.toJSONString(xbpFlowForInspection));
            }
        } else if("3".equals(req.getParameter("test"))) {
            String sql = "select * from t_nature_check_status where check_erp is null ";
            List<Map<String, Object>> nature = KongmingService.executeSql(sql, "nature");
            for(Map<String, Object> row : nature) {
                Integer id = (Integer)row.get("id");
                String bufflaoId = (String)row.get("bufflao_id");
                TaskInfo buffaloTaskInfo = getBuffaloTaskInfo(bufflaoId);
                KongmingService.executeUpdate(
                        "update t_nature_check_status" +
                                " set " +
                                " check_erp = ?" +
                                " where id = ?","nature", buffaloTaskInfo.getManagers(), id);
            }
        } else if("4".equals(req.getParameter("test"))) {
            String sql = "select * from t_km_xbp_flow where username = '' or username is null order by id desc";
            List<Map<String, Object>> nature = KongmingService.executeSql(sql, "nature");
            for(Map<String, Object> row : nature) {
                Long id = (Long)row.get("id");
                String bufflaoId = (String)row.get("taskids");
                if(bufflaoId.split(",").length!=0) {
                    bufflaoId = bufflaoId.split(",")[0];
                }
                TaskInfo buffaloTaskInfo = getBuffaloTaskInfo(bufflaoId);
                KongmingService.executeUpdate(
                        "update t_km_xbp_flow" +
                                " set " +
                                " username = ?" +
                                " where id = ?","nature", buffaloTaskInfo.getManagers(), id);
            }
        } else if (req.getParameter("ticketIds") != null) {
            String ticketIds = req.getParameter("ticketIds");
            String type = req.getParameter("type");
            int days = Integer.parseUnsignedInt(StringUtils.defaultString(req.getParameter("days"), "7"));
            if(Arrays.asList("before", "after").contains(type)) {
                for (String ticketId : ticketIds.split(",")) {
                    processDurationVCores(msg, type, days, ticketId);
                }
            }
        }  else if (req.getParameter("ticketIdsBeforeAfter") != null) {
            String ticketIds = req.getParameter("ticketIdsBeforeAfter");
            int days = Integer.parseUnsignedInt(StringUtils.defaultString(req.getParameter("days"), "7"));
            int coreSize = Integer.parseUnsignedInt(StringUtils.defaultString(req.getParameter("coreSize"), "10"));
            ExecutorService executorService = Executors.newFixedThreadPool(coreSize);
            for (String ticketId : ticketIds.split(",")) {
                executorService.submit(() -> {
                    processDurationVCores(msg, "before", days, ticketId);
                    processDurationVCores(msg, "after", days, ticketId);
                });
                logger.info("ticketIdsBeforeAfter submitted ticket: " + ticketId);
            }
            try {
                executorService.wait();
                executorService.shutdown();
                logger.info("ticketIdsBeforeAfter shutdown threadpool");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } else if (req.getParameter("approveTicketIds") != null) {
            String approveTicketIds = req.getParameter("approveTicketIds");
            for (String ticketId : approveTicketIds.split(",")) {
                XbpResponse<Boolean> response = XbpManager.approveTicket(Integer.parseInt(ticketId));
                msg.append(ticketId).append(":").append(response).append("<br/>");
            }
        } else if (req.getParameter("jobId") != null) {
            String jobId = req.getParameter("jobId");
            String erp = req.getParameter("erp");
            msg.append("request: id: " + jobId + " erp:" + erp);
            JSFResultDTO<JobVO> jobInfoById = ideJobService.getJobInfoById(Integer.parseInt(jobId), erp);
            msg.append("response: jobInfoById: " + JSON.toJSONString(jobInfoById));
            if(req.getParameter("engine") != null) {
                JobVO job = jobInfoById.getObj();
                JSFResultDTO jsfResultDTO = ideJobService.addOrModifyJobInfo(
                        job.getId(), job.getOwner(), job.getJobName(), job.getDescribe(), job.getTargetFileName(),
                        job.getTargetType(), job.getSharedPersons(), job.getContent(), job.getDbName(),
                        job.getRunMarketCode(), job.getQueueCode(), job.getAccountCode(), job.getRunLogicClusterCode(),
                        job.getNoticeType(), req.getParameter("engine"), job.getJobType(), job.getGroupName(),
                        job.getDispatchSystemJobIdNew());
                msg.append("update engine response: jsfResultDTO: " + JSON.toJSONString(jsfResultDTO));
            }
        } else if(req.getParameter("revokeTickets") != null) {
            for (String ticketId : req.getParameter("revokeTickets").split(",")) {
                XbpResponse<Boolean> response = XbpManager.revokeTicket(Integer.parseInt(ticketId), "撤回");
                msg.append(ticketId).append(":").append(response).append("<br/>");
            }
        } else if(req.getParameter("restartTickets") != null) {
            for (String ticketId : req.getParameter("restartTickets").split(",")) {
                XbpResponse<Boolean> response = XbpManager.restartTicket(Integer.parseInt(ticketId), "xnspark");
                msg.append(ticketId).append(":").append(response).append("<br/>");
            }
        } else if (CALLBACK_INSPECTION_FLOW.equals(req.getParameter("processType"))) {
            logger.info("doGet: "+JSON.toJSONString(req.getParameterMap()));
            Integer ticketId = Integer.parseInt(req.getParameter("ticketId"));
            TicketResponse ticketResponse = processCallBackForIspection(ticketId, msg, FLOW_NAME_INSPECTION,
                    EnumXbpStatus.APPROVED_XBP, EnumXbpStatus.REJECT_XBP);
            logger.info("JSF: ticketId: " + ticketId + " response: " + JSON.toJSONString(ticketResponse));
            msg.append("感謝您的支持！");
            KongmingService.executeUpdate(
                        "update t_nature_check_status" +
                            " set xbp_status = ? " +
                            " ,xbp_msg = ?" +
                            " ,end_time = ?" +
                            " where ticket_id = ?","nature",
                    ticketResponse.getEnumXbpStatus().statusCode, ticketResponse.getEnumXbpStatus().description,
                    new java.sql.Timestamp(new Date().getTime()), ticketId);
            XbpResponse<Integer> response = XbpManager.xbpComment(ticketId, msg.toString());
        } else if (CALLBACK_PRESS_FLOW.equals(req.getParameter("processType"))) {
            logger.info("doGet: "+JSON.toJSONString(req.getParameterMap()));
            Integer ticketId = Integer.parseInt(req.getParameter("ticketId"));
            TicketResponse ticketResponse = processCallBackForIspection(ticketId, msg, FLOW_NAME_PRESS, EnumXbpStatus.APPROVED_XBP, EnumXbpStatus.REJECT_XBP);
            logger.info("JSF: ticketId: " + ticketId + " response: " + JSON.toJSONString(ticketResponse));
            msg.append("感謝您的支持！");
            KongmingService.executeUpdate(
                    "update t_nature_press_status" +
                            " set xbp_status = ? " +
                            " ,xbp_msg = ?" +
                            " ,end_time = ?" +
                            " where ticket_id = ?","nature",
                    ticketResponse.getEnumXbpStatus().statusCode, ticketResponse.getEnumXbpStatus().description,
                    new java.sql.Timestamp(new Date().getTime()), ticketId);
//            XbpResponse<Integer> response = XbpManager.xbpComment(ticketId, msg.toString());
        } else if (CALLBACK_PRESTO_FLOW.equals(req.getParameter("processType"))) {
            logger.info("doGet: "+JSON.toJSONString(req.getParameterMap()));
            Integer ticketId = Integer.parseInt(req.getParameter("ticketId"));
            TicketResponse ticketResponse = processCallBackForIspection(ticketId, msg, FLOW_NAME_PRESTO, EnumXbpStatus.APPROVED_XBP, EnumXbpStatus.REJECT_XBP);
            logger.info("JSF: ticketId: " + ticketId + " response: " + JSON.toJSONString(ticketResponse));
            msg.append("感謝您的支持！");
            KongmingService.executeUpdate(
                    "update t_nature_presto_press" +
                            " set xbp_status = ? " +
                            " ,xbp_msg = ?" +
                            " ,end_time = ?" +
                            " where ticket_id = ?","nature",
                    ticketResponse.getEnumXbpStatus().statusCode, ticketResponse.getEnumXbpStatus().description,
                    new java.sql.Timestamp(new Date().getTime()), ticketId);
//            XbpResponse<Integer> response = XbpManager.xbpComment(ticketId, msg.toString());
        } else if (StringUtils.isNotEmpty(req.getParameter("erp"))) {
            String type = req.getParameter("type");
            JSONObject erp;
            if("info".equals(type)) {
                erp = hrUserService.getUserBaseInfoByUserName(req.getParameter("erp"));
            } else {
                erp = hrUserService.getSuperiorBaseInfo(req.getParameter("erp"));
            }
            msg.append(erp.toJSONString());
        } else if (req.getParameter("streamingId") != null &&
            req.getParameter("streamingName") != null &&
            req.getParameter("streamingUrl") != null &&
            req.getParameter("streamingLevel") != null &&
            req.getParameter("streamingErp") != null) {
            CreateParam createParam = new CreateParam();
            createParam.setProcessId(14694);
            createParam.setUsername(Scheduler.commonBean.getXbpUsername());
            createParam.setMailCopyAddresses("<EMAIL>");
            String streamingErp = req.getParameter("streamingErp");

            JSONObject superiorBaseInfo = hrUserService.getSuperiorBaseInfo(streamingErp);
            String superErp = StringUtils.defaultIfEmpty(superiorBaseInfo.getString("userName"), "");

            Map<String, Object> applicationInfo = new HashMap<>();
            String streamingId = req.getParameter("streamingId");
            applicationInfo.put("SparkStreaming任务ID", streamingId);
            applicationInfo.put("任务名称", req.getParameter("streamingName"));
            applicationInfo.put("Streaming任务链接", req.getParameter("streamingUrl"));
            applicationInfo.put("当前任务级别", req.getParameter("streamingLevel"));

            createParam.setApplicationInfo(applicationInfo);
            HashMap<String, List<String>> approvers = new HashMap<>();
            approvers.put("任务负责人审批", Arrays.asList(streamingErp.split(",")));
            approvers.put("直属领导审批", Arrays.asList(superErp.split(",")));
            createParam.setApprovers(approvers);
            XbpResponse<Integer> integerXbpResponse = XbpManager.createXbpFlow(createParam);
            System.out.println("integerXbpResponse = " + integerXbpResponse);
            msg.append("streamingId:").append(streamingId).append("  ").append(integerXbpResponse);
        }
        req.setAttribute("msg", msg);
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }

    private void processDurationVCores(StringBuilder msg, String type, int days, String ticketId) {
        List<Map<String, Object>> rowByTicket = getRowByTicket(Integer.parseInt(ticketId));
        rowByTicket.forEach(row -> {
            String taskid = (String) row.get("taskids");
            Timestamp approveOrReject = (Timestamp) row.get("approve_or_reject");
            if(taskid != null && approveOrReject != null) {
                String approveOrRejectDt = SDFThreadLocal.getyyyyMMdd().format(approveOrReject);
                String join = getDts(approveOrRejectDt, type, days, false);
                List<Map<String, Object>> durationData = getDuration(taskid, join, null);
                if (durationData.isEmpty()) {
                    msg.append("Not found the elapsed of taskid: " + taskid);
                } else {
                    msg.append("taskid: " + taskid);
                    Map<String, Object> row1 = durationData.get(0);
                    String beforeDate = (String) row1.get("dts");
                    Integer duration = ((Double) row1.get("duration")).intValue();
                    KongmingService.executeUpdate(
                            "update t_km_xbp_flow" +
                                    " set " + type + "_elapsed_sec = ? " +
                                    " ," + type + "_date = ?" +
                                    " where ticket_id = ?", "nature",
                            duration, beforeDate, ticketId);
                    msg.append("beforeDate: " + beforeDate + " duration: " + duration);

                    List<Map<String, Object>> vCores = getVCores(taskid, join, null);
                    if (!vCores.isEmpty()) {
                        Map<String, Object> row2 = vCores.get(0);
                        Long reqVcore = (Long) row2.get("req_vcore_c_s");
                        Long reqMemMb = (Long) row2.get("req_mem_mbs_s");
                        KongmingService.executeUpdate(
                                "update t_km_xbp_flow" +
                                        " set " + type + "_vcore = ? " +
                                        " ," + type + "_mem = ?" +
                                        " where ticket_id = ?", "nature",
                                reqVcore, reqMemMb, ticketId);
                        msg.append(" reqVcore: " + reqVcore + " reqMemMb: " + reqMemMb + "<br/>");
                    }
                }
            }
        });
    }

    private void invokeKongMing(StringBuilder msg, Integer ticketId) {
        List<Map<String, Object>> nature = getRowByTicket(ticketId);
        invokeKongMing(msg, nature);
    }

    public List<Map<String, Object>> getRowByTicket(Integer ticketId) {
        List<Map<String, Object>> nature = KongmingService.executeSql(
                "select * from t_km_xbp_flow where ticket_id = ? ",
                "nature", ticketId);
        return nature;
    }

    public static int insertPresto(int processId, String accout, String appName, String checkErp, EnumXbpStatus xbpStatus,
                            String ips, String desc) {
        String sql = "INSERT INTO `t_nature_presto_press` (`account`, `app_name`, `processId`, `ips`, `xbp_status`, `check_erp`, `check_msg`)" +
                " VALUES (?, ?, ?, ?, ?, ?, ?)";
        int nature1 = KongmingService.executeUpdate(sql, "nature", accout, appName, processId, ips, xbpStatus.statusCode, checkErp, desc);
        return nature1;
    }

    public static void invokeKongMing(StringBuilder msg, List<Map<String, Object>> nature) {
        nature.forEach(row -> {
            String kongMingTemplates = (String)row.get("kongming_template");
            String buffaloVersion = (String)row.get("buffalo_version");
            String taskIds = (String) row.get("taskids");
            //如果actionIds为空， 同时查询row中是否存在actionId
            String actionIds_t = (String) row.get("actionids");
            if(StringUtils.isNotEmpty(kongMingTemplates)) {
                String[] split = kongMingTemplates.split(",");
                HashSet<String> kmTemplate = Sets.newHashSet(kongMingTemplates.split(","));
                for (String kongMingTemplate : kmTemplate) {
                    for (String taskIdName : taskIds.split(",")) {
                        String[] taskInfo = parseTaskIds(taskIdName);
                        String taskId = taskInfo[0].split("\\|")[0];
                        String actionIds = taskInfo[1];
                        if (org.apache.commons.lang3.StringUtils.isEmpty(actionIds)) {
                            if (org.apache.commons.lang3.StringUtils.isNotEmpty(actionIds_t)) {
                                for (String actionId : actionIds_t.split("#")) {
                                    sendKongMing(msg, taskId, actionId, buffaloVersion, kongMingTemplate);
                                }
                            } else {
                                sendKongMing(msg, taskId, "", buffaloVersion, kongMingTemplate);
                            }
                        } else {
                            for (String actionId : actionIds.split("#")) {
                                sendKongMing(msg, taskId, actionId, buffaloVersion, kongMingTemplate);
                            }
                        }
                    }
                    //1 日常和核心限流  2 日常限流 3核心时段限流
                    String limitTypeStr = null;
                    Integer limitCode = null;
                    String buffaloType = "BUFFALO4";
                    if ("SPARK_TASK_DAILY_TIME_LIMIT_NORMAL_L3".equals(kongMingTemplate)) {
                        limitTypeStr = "日常限流";
                        limitCode = 2;
                    } else if ("SPARK_TASK_CORE_TIME_LIMIT_NORMAL_L3".equals(kongMingTemplate)) {
                        limitTypeStr = "高峰限流";
                        limitCode = 3;
                    }
                    if (limitTypeStr != null) {
                        for (String taskIdName : taskIds.split(",")) {
                            List<Object> params = new ArrayList<Object>();
                            String taskId = taskIdName.split("\\|")[0];
                            params.add(taskId);
                            params.add(limitTypeStr);
                            params.add(buffaloType);
                            params.add(limitCode);
                            String sql = "insert into t_km_limit_task_list" +
                                    " (id, buffalo_id, task_tag, source, limit_type)" +
                                    " VALUES (uuid(), ?, ?, ?, ?)";
                            int i = KongmingService.executeUpdate(sql, "nature", params.toArray());
                            msg.append("  UpdateTaskLimitTable: ").append(i > 0 ? "成功" : "失败");
                            logger.info("Insert KongMing: SQL: " + sql + " Params: " + JSON.toJSONString(params));
                        }
                    }
                    msg.append("<br/>");
                }
            }
        });
    }

    private int updateMysqlForComment(int ticketId, EnumXbpStatus flowStatus, XbpResponse<Integer> response,
                                       Date approveOrRejectDate) {
        Integer commentSuccess = 1;
        Integer commentCode = -1;
        String commentMsg = "";
        if (response != null) {
            commentSuccess = 0;
            commentCode = response.getCode();
            commentMsg = StringUtils.trimToEmpty(response.getMsg());
        }
        if (flowStatus == EnumXbpStatus.PLATFORM_RECHECK_APPROVED
                || flowStatus == EnumXbpStatus.PLATFORM_RECHECK_REJECT) {
            return updateXbpFlowStatus(ticketId, flowStatus);
        } else {
            return KongmingService.executeUpdate("update t_km_xbp_flow" +
                            " set flow_status = ? " +
                            " ,flow_status_msg = ?" +
                            " ,comment_success = ?" +
                            " ,comment_code = ?" +
                            " ,comment_msg = ?" +
                            " ,approve_or_reject = ?" +
                            " where ticket_id = ?", "nature",
                    flowStatus.statusCode, flowStatus.description, commentSuccess,
                    commentCode, commentMsg, approveOrRejectDate, ticketId);
        }
    }

    public int updateXbpFlowStatus(int ticketId, EnumXbpStatus flowStatus) {
        List<String> params = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        if(flowStatus!= null) {
            params.add("flow_status = ?");
            values.add(flowStatus.statusCode);
            params.add("flow_status_msg = ?");
            values.add(flowStatus.description);
        }
        values.add(new Integer(ticketId));

        return KongmingService.executeUpdate("update t_km_xbp_flow" +
                        " set " + String.join(",", params) +
                        " where ticket_id = ?", "nature", values.toArray());
    }

    private TicketResponse processCallBackForIspection(int ticketId, StringBuilder msg, List<String> flowName,
                                                       EnumXbpStatus approvedXbp, EnumXbpStatus rejectXbp) {
        TicketResponse ticketResponse = new TicketResponse();
        XbpResponse<Ticket> ticketXbpResponse = XbpManager.getTicketXbpResponse(ticketId);
        if (ticketXbpResponse == null || ticketXbpResponse.getCode() != 0) {
            msg.append("xbp get ticket接口调用失败，请联系ERP: wuguoxiao");
        } else {
            Ticket data = ticketXbpResponse.getData();
            ticketResponse.setTicket(data);
            ticketResponse.setEnumXbpStatus(getEnumXbpStatus(data, flowName, approvedXbp, rejectXbp));
        }
        return ticketResponse;
    }

    private EnumXbpStatus processCallBack(int ticketId, StringBuilder msg, List<String> flowName,
                                          EnumXbpStatus approvedXbp, EnumXbpStatus rejectXbp) {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow.processCallBack", "SparkMonitorApp", false, true);
        EnumXbpStatus flowStatus = EnumXbpStatus.FAILED;
        try {
            XbpResponse<Ticket> ticketXbpResponse = XbpManager.getTicketXbpResponse(ticketId);
            if (ticketXbpResponse == null || ticketXbpResponse.getCode() != 0) {
                msg.append("xbp get ticket接口调用失败，请联系ERP: wuguoxiao");
            } else {
                Ticket data = ticketXbpResponse.getData();
                List<Map<String, Object>> nature = getRowByTicket(ticketId);
                Map<String, Object> ticketRow = nature.get(0);
                String actionids = (String)ticketRow.get("actionids");
                String taskids = (String)ticketRow.get("taskids");
                String engine = (String)ticketRow.get("engine");
                String version = (String)ticketRow.get("version");

                flowStatus = getEnumXbpStatus(data, flowName, approvedXbp, rejectXbp);
                Integer batchId = getBatchId(data);
                EnumXbpStatus finishInfo = getFinishInfo(data, flowName);
                if (finishInfo != null) {
                    if (finishInfo == EnumXbpStatus.APPROVED_XBP) {
                        msg.append("您已选择的治理方式是\"授权平台自动调整\"。\n<br/>");
//                      更新引擎（现阶段不可打开注释，平台暂不允许修改用户任务）
//                      processApprovedUpdateEngine(msg, data, taskids, actionids, engine, version);
                        invokeKongMing(msg, nature);
                        msg.append("请自行修改引擎，请点此链接：<a target=\"_blank\" href=\"http://troubleshooting.jd.com/buffaloTaskModify.jsp?taskIds="+taskids
                                +"&engine="+engine+"&version="+version+"\">修改引擎链接</a><br/>");
                    } else if (finishInfo == EnumXbpStatus.REJECT_XBP) {
                        msg.append("您已选择的治理方式是\"自行优化任务\",请尽快优化任务!<br/>");
                        msg.append("请自行修改引擎，请点此链接：<a target=\"_blank\" href=\"http://troubleshooting.jd.com/buffaloTaskModify.jsp?taskIds="+taskids
                                +"&engine="+engine+"&version="+version+"\">修改引擎链接</a><br/>");
                    }
                } else if (flowStatus == EnumXbpStatus.APPROVED_XBP) {
                    if(SOURCE_ZI_ZHU.equals(ticketRow.get("source"))) {
                        engine = "hive".equals(ticketRow.get("engine") + "") ?
                                "jd-hive" : ticketRow.get("engine") + "";
                        String taskid = ticketRow.get("taskids") + "";
                        boolean b = ideJobService.updateVersion(Integer.parseInt(taskid),
                                engine);
                        String url = "http://dp.jd.com/jbdpEdc/withdrawal-manage/task-subscribe?taskId="+taskid+"&check=true";
                        String back = "https://cf.jd.com/pages/viewpage.action?pageId=1176887499";
                        msg.append("更新引擎为" + engine + ": " + (b ? "成功":"失败") + ".<br/>");
                        msg.append("请重跑任务验证: ");
                        msg.append("<a target=\"_blank\" href=\""+url+"\">"+url+"</a><br/>");
                        msg.append("如需回滚请查看文档: ");
                        msg.append("<a target=\"_blank\" href=\""+back+"\">"+back+"</a><br/>");
                        msg.append("如有问题请联系我们: Spark引擎值班(xn_spark)、张晨(zhangchen351)、吴国晓(wuguoxiao) <br/>");
                    } else {
                        processApprovedUpdateEngine(msg, data, taskids, actionids, engine, version);
                    }
                    invokeKongMing(msg, ticketId);
                }
                logger.info("batchId = " + batchId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
        return flowStatus;
    }

    private void processApprovedUpdateEngine(StringBuilder msg, Ticket data, String taskids, String actionIds,
                                             String engine, String version) {
        List<Integer> workFlowIds = new ArrayList<>();
        if (taskids != null) {
            if (StringUtils.isNotEmpty(engine)) {
                if (engineVersionValidate(engine, version)) {
                    logger.info("taskId = " + taskids);
                    try {
                        if (actionIds != null) {
                            for (String workFlowId : actionIds.split("#")) {
                                try {
                                    workFlowIds.add(Integer.parseInt(workFlowId));
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                        BuffaloTaskInfoModifyController.updateEngineInfo(msg, taskids, engine, version,
                                "wuguoxiao", commonBean.getBuffaloApiUserToken(), workFlowIds, null);
                    } catch (Exception e) {
                        msg.append("更新引擎版本异常：").append(CommonUtil.exceptionToString(e));
                    }
                } else {
                    msg.append("引擎类型或版本参数值不正确！").append("<br/>");
                }
            }
        } else {
            for (Ticket.TableInfo tableInfo : data.getTableInfo()) {
                for (Map<String, Object> datum : tableInfo.getData()) {
                    String taskVersion = datum.get("任务版本") + "";
                    String taskId = datum.get("任务id") + "";
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(datum.get("环节ID") + "")) {
                        for (String workFlowId : (datum.get("环节ID") + "").split("#")) {
                            try {
                                workFlowIds.add(Integer.parseInt(workFlowId));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    String engine1 = getFormVal(datum, XbpForm.engineUpdated);
                    String version1 = getFormVal(datum, XbpForm.engineVersionUpdated);
                    if (StringUtils.isEmpty(engine1)) {
                        msg.append("引擎类型和版本不执行修改！").append("<br/>");
                    } else if (engineVersionValidate(engine1, version1)) {
                        logger.info("taskId = " + taskId);
                        try {
                            BuffaloTaskInfoModifyController.updateEngineInfo(msg, taskId, engine1, version1,
                                    "wuguoxiao", commonBean.getBuffaloApiUserToken(), workFlowIds, null);
                        } catch (Exception e) {
                            msg.append("更新引擎版本异常：").append(CommonUtil.exceptionToString(e));
                        }
                    } else {
                        msg.append("引擎类型或版本参数值不正确！").append("<br/>");
                    }
                }
                msg.append("您的任务已成功升级，感谢您的支持，请重跑您的任务进行验证，如有问题请及时联系我们！<br/>");
            }
        }
    }

    private boolean engineVersionValidate(String engine, String version) {
        if(StringUtils.isEmpty(engine) || StringUtils.isEmpty(version)) {
            return false;
        } else if (engine.equals("spark") && !XbpForm.sparkVersions.contains(version + "")) {
            return false;
        } else if (engine.equals("hive") && !XbpForm.hiveVersions.contains(version + "")) {
            return false;
        }
        return true;
    }

    private String getFormVal(Map<String, Object> datum, String[] options) {
        for (String engineUpdatedKey : options) {
            Object engineUpdatedVal = datum.get(engineUpdatedKey);
            if(engineUpdatedVal != null) {
                return engineUpdatedVal + "";
            }
        }
        return null;
    }

    private Integer getBatchId(Ticket data) {
        for (Ticket.ApplicationInfo applicationInfo : data.getApplicationInfo()) {
            String itemName = applicationInfo.getItemName();
            if("批次id".equals(itemName)) {
                return Integer.parseInt(applicationInfo.getItemValue());
            }
        }
        return null;
    }

    private EnumXbpStatus getEnumXbpStatus(Ticket data, List<String> flowName, EnumXbpStatus approvedXbp, EnumXbpStatus rejectXbp) {
        for (Flow flow : data.getApplicationFlow()) {
            if(flowName.contains(flow.getFlowName())) {
                if (flow.getStatus() == 1) {
                    return approvedXbp;
                } else if (flow.getStatus() == 0) {
                    return null;
                } else {
                    return rejectXbp;
                }
            }
        }
        return EnumXbpStatus.FAILED;
    }

    private EnumXbpStatus getFinishInfo(Ticket data, List<String> flowName) {
        for (Flow flow : data.getApplicationFlow()) {
            if(flowName.contains(flow.getFlowName())) {
                for (Flow.FinishInfo finishInfo : flow.getFinishInfo()) {
                    String title = finishInfo.getTitle();
                    String value = finishInfo.getValue();
                    if("治理方式".equals(title)) {
                        return "授权平台自动调整".equals(value) ? EnumXbpStatus.APPROVED_XBP : EnumXbpStatus.REJECT_XBP;
                    }
                }
            }
        }
        return null;
    }

    public static StringBuilder schedulerCreateXbpFlow() {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow.scheduler", "SparkMonitorApp", false, true);
        StringBuilder builder = new StringBuilder();
        try {
            String sql = "select * from t_km_xbp_flow where flow_status = 1 limit 10";
            List<Map<String, Object>> nature = KongmingService.executeSql(sql, "nature");
            for(Map<String, Object> row : nature) {
                createXbpProcess(builder, row);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
        return builder;
    }

    public static StringBuilder schedulerUpdateClusterMarket() {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow.updateClusterMarket.scheduler", "SparkMonitorApp", false, true);
        StringBuilder builder = new StringBuilder();
        try {
            String sql = "select * from t_km_xbp_flow where marketCode is null limit 500";
            List<Map<String, Object>> nature = KongmingService.executeSql(sql, "nature");
            for(Map<String, Object> row : nature) {
                updateClusterAndMarket(builder, row);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
        return builder;
    }

    public static List<Map<String, Object>> getInspectionItem() {
        String sql = "SELECT inspection_item FROM t_km_xbp_flow group by inspection_item";
        List<Map<String, Object>> maps = KongmingService.executeSql(sql, DB_NAME_NATURE);
        return maps;
    }

    public static List<Map<String, Object>> getFlowStatus() {
        String sql = "SELECT flow_status,flow_status_msg FROM t_km_xbp_flow group by flow_status,flow_status_msg";
        List<Map<String, Object>> maps = KongmingService.executeSql(sql, DB_NAME_NATURE);
        return maps;
    }

    public static List<Map<String, Object>> getFlowType() {
        String sql = "SELECT flow_type FROM t_km_xbp_flow group by flow_type";
        List<Map<String, Object>> maps = KongmingService.executeSql(sql, DB_NAME_NATURE);
        return maps;
    }

    public static List<Map<String, Object>> getBill(boolean hasCluster, boolean hasYear, boolean hasMonth,
                                                    boolean hasWeek) {
        ArrayList<String> groups = new ArrayList<>();
        if(hasCluster) {
            groups.add("clusterCode");
        }
        if(hasYear) {
            groups.add("year(create_time)");
        }
        if(hasMonth) {
            groups.add("month(create_time)");
        }
        if(hasWeek) {
            groups.add("week(create_time)");
        }
        String sql = "select \n" +
                (hasCluster ? "\t clusterCode, \n" : "") +
                (hasYear ? "\t year(create_time) as create_year, \n" : "") +
                (hasMonth ? "\t month(create_time) as create_month, \n" : "") +
                (hasWeek ? "\t week(create_time) as create_week, \n" : "") +
                "\t count(distinct ticket_id) as cnt_tickets,\n" +
                "\t count(distinct taskids) as cnt_taskids, \n" +
                "\t sum(before_elapsed_sec) as sum_before_elapsed_sec , \n" +
                "\t sum(after_elapsed_sec) as sum_after_elapsed_sec , \n" +
                "\t sum(before_elapsed_sec) - sum(after_elapsed_sec) as elapsed_diff,\n" +
                "\t round(100 - (sum(after_elapsed_sec) * 100 / sum(before_elapsed_sec)),2)  as elapsed_rate,\n" +
                "\t sum(before_vcore) as sum_before_vcore, \n" +
                "\t sum(after_vcore) as sum_after_vcore ,\n" +
                "\t sum(before_vcore) - sum(after_vcore) as vcore_diff,\n" +
                "\t round(100 - (sum(after_vcore) * 100 / sum(before_vcore)), 2) as vcore_rate,\n" +
                "\t round((sum(before_vcore) - sum(after_vcore)) / 60/60/24,2) as vcore_day\n" +
                "from t_km_xbp_flow \n" +
                "where  before_elapsed_sec is not null\n" +
                "\t and after_elapsed_sec is not null\n" +
                "\t and (flow_type = 'Hive2Spark' or inspection_item = 'Hive任务运行时间超长')\n" +
                "\t and flow_status = 3" +
                (!groups.isEmpty() ? "\t group by " + String.join(",",groups) : "");
        List<Map<String, Object>> result = KongmingService.executeSql(sql, DB_NAME_NATURE);
        logger.info("sql: " + sql + " result: " + JSON.toJSONString(result));
        return result;
    }


    /**
     * 用于 xbp-list.jsp
     * @param pageNoStr
     * @param pageSizeStr
     * @param paramProcessID
     * @param inspectionItem
     * @param flowStatus
     * @param taskIds
     * @param startApprove
     * @param startCreate
     * @param endCreate
     * @param username
     * @param flowType
     * @param ticketIds
     * @return
     */
    public static KongMingRecordBean getXbpList(String pageNoStr, String pageSizeStr, String paramProcessID,
                                                String inspectionItem, String flowStatus, String taskIds,
                                                String startApprove, String startCreate, String endCreate,
                                                String username, String flowType, String ticketIds) {
        String sql = "SELECT COUNT(*) as cnt FROM t_km_xbp_flow  %s ";
        String sqlData = "SELECT *,(before_elapsed_sec - after_elapsed_sec) * 100 / before_elapsed_sec as elapsedRate," +
                "(before_vcore - after_vcore) * 100 / before_vcore as vcoreRate," +
                " TIMESTAMPDIFF(day,approve_or_reject, CURRENT_TIMESTAMP()) as approveDays," +
                " (before_vcore - after_vcore) * TIMESTAMPDIFF(day,approve_or_reject, CURRENT_TIMESTAMP()) as amount_vcores," +
                " week(create_time) as create_week," +
                " month(create_time) as create_month," +
                " year(create_time) as create_year" +
                " FROM t_km_xbp_flow  %s " +
                " order by id desc limit ?,? ";
        KongMingRecordBean kongMingRecordBean = new KongMingRecordBean();
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow.list", "SparkMonitorApp", false, true);
        try {
            Integer pageNo = Integer.parseInt(pageNoStr);
            pageNo = pageNo > 1 ? pageNo : 1;
            Integer pageSize = Integer.parseInt(pageSizeStr);

            String whereCondition = " where 1 = 1 ";
            ArrayList<Object> params = new ArrayList<>();
            if (StringUtils.isNotEmpty(paramProcessID)) {
                whereCondition += " AND process_id = ?";
                params.add(paramProcessID);
            }
            if (StringUtils.isNotEmpty(inspectionItem)) {
                whereCondition += " AND inspection_item = ?";
                params.add(inspectionItem);
            }
            if (StringUtils.isNotEmpty(flowStatus)) {
                whereCondition += " AND flow_status = ?";
                params.add(flowStatus);
            }
            if (StringUtils.isNotEmpty(flowType)) {
                whereCondition += " AND flow_type = ?";
                params.add(flowType);
            }
            if (StringUtils.isNotEmpty(username)) {
                whereCondition += " AND username like ?";
                params.add("%"+username+"%");
            }
            if (StringUtils.isNotEmpty(startApprove)) {
                whereCondition += " AND approve_or_reject >= ?";
                params.add(new java.sql.Timestamp(SDFThreadLocal.getyyyyMMdd().parse(startApprove).getTime()));
            }
            if (StringUtils.isNotEmpty(startCreate)) {
                whereCondition += " AND create_time >= ?";
                params.add(new java.sql.Timestamp(SDFThreadLocal.getyyyyMMdd().parse(startCreate).getTime()));
            }
            if (StringUtils.isNotEmpty(endCreate)) {
                whereCondition += " AND create_time <= ?";
                params.add(new java.sql.Timestamp(SDFThreadLocal.getyyyyMMdd().parse(endCreate).getTime()));
            }
            if (StringUtils.isNotEmpty(taskIds)) {
                whereCondition += " AND taskids in ("+taskIds+")";
            }
            if (StringUtils.isNotEmpty(ticketIds)) {
                whereCondition += " AND ticket_id in ("+ticketIds+")";
            }
            Long numberOfRows = (Long) KongmingService.executeSql(
                    String.format(sql, whereCondition), DB_NAME_NATURE, params).get(0).get("cnt");
            params.add((pageNo - 1) * pageSize);
            params.add(pageSize);
            List<Map<String, Object>> rows =
                    KongmingService.executeSql(String.format(sqlData, whereCondition), DB_NAME_NATURE, params);

            for (Map<String, Object> row : rows) {
                String before_elapsed_sec = getElapsed((Integer)row.get("before_elapsed_sec"));
                row.put("beforeElapsedMin", before_elapsed_sec);
                String after_elapsed_sec = getElapsed((Integer)row.get("after_elapsed_sec"));
                row.put("afterElapsedMin", after_elapsed_sec);
                BigDecimal elapsedRate = (BigDecimal)row.get("elapsedRate");
                if (elapsedRate != null) {
                    row.put("elapsedRate", elapsedRate.setScale(1, RoundingMode.HALF_UP).doubleValue() + "%");
                }
                BigDecimal vcoreRate = (BigDecimal)row.get("vcoreRate");
                if (vcoreRate != null) {
                    row.put("vcoreRate", vcoreRate.setScale(1, RoundingMode.HALF_UP).doubleValue() + "%");
                }
//                if (StringUtils.isNotEmpty(before_elapsed_sec) && StringUtils.isNotEmpty(after_elapsed_sec) &&
//                        !before_elapsed_sec.equals("0")) {
//                    int after = Integer.parseInt(after_elapsed_sec);
//                    int before = Integer.parseInt(before_elapsed_sec);
//                    double diff = before - after;
//                    double rate = diff * 100 / before;
//                    BigDecimal b = new BigDecimal(rate);
//                    rate = b.setScale(1, RoundingMode.HALF_UP).doubleValue();
//                    row.put("elapsedRate", rate + "%");
//                } else {
//                    row.put("elapsedRate", "");
//                }
                row.put("beforeVcore", getVcore(row.get("before_vcore")));
                row.put("afterVcore", getVcore(row.get("after_vcore")));
                row.put("amountVCores", getVcore(row.get("amount_vcores")));
            }
            kongMingRecordBean.setCurPageNo(pageNo);
            kongMingRecordBean.setPageSize(pageSize);
            kongMingRecordBean.setRows(rows);
            kongMingRecordBean.setNextPageNo(pageNo + 1);
            kongMingRecordBean.setPreviousPageNo(Math.max(pageNo - 1, 0));
            kongMingRecordBean.setNumberOfRows(numberOfRows);
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
        return kongMingRecordBean;
    }

    private static String getVcore(Object before_vcore) {
        String vcore = "";
        if (before_vcore != null) {
            try {
                vcore = new DecimalFormat("#,###").format(before_vcore);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return vcore;
    }

    private static String getElapsed(Integer elapsedSec) {
        String elapsed = "";
        if (elapsedSec != null) {
            try {
                elapsed = elapsedSec / 60 + "";
            } catch (Exception e) {
                logger.warning("parseInt error: elapsedSec: " + elapsedSec);
            }
        }
        return elapsed;
    }


    public static List<Map<String, Object>> getXbpFlowForInspection(String buffaloId, String checkDt) {
        String sql = "select * from t_nature_check_status where bufflao_id = ? and check_dt = ?";
        return KongmingService.executeSql(sql, "nature", buffaloId, checkDt);
    }

    public static StringBuilder schedulerCreateXbpFlowForInspection() {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow.inspection.scheduler", "SparkMonitorApp", false, true);
        StringBuilder builder = new StringBuilder();
        try {
            String sql = "select * from t_nature_check_status where xbp_status = 1 limit 10";
            List<Map<String, Object>> nature = KongmingService.executeSql(sql, "nature");
            for(Map<String, Object> row : nature) {
                createXbpProcessForInspection(builder, row);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
        return builder;
    }

    public static void createXbpProcessForInspection(StringBuilder builder, Map<String, Object> row) {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow.inspection.scheduler.createFlow", "SparkMonitorApp", false, true);
        try {
            Integer id = Integer.parseInt(row.get("id") + "");
            String bufflaoId = (String) row.get("bufflao_id");
            Date checkDt = (Date) row.get("check_dt");
            String checkScore =  (String)row.get("check_score");
            String checkLowItem = (String) row.get("check_low_item");
            String checkMidItem = (String) row.get("check_mid_item");
            String checkHighItem = (String) row.get("check_high_item");
            String checkCount = (String) row.get("check_count");
            Date expectFinishTime = (Date) row.get("expect_finish_time");
            TaskInfo buffaloTaskInfo = getBuffaloTaskInfo(bufflaoId);
            XbpResponse<Integer> integerXbpResponse = xbpInspection(10800, bufflaoId, checkDt, checkScore,
                    checkLowItem, checkMidItem, checkHighItem, expectFinishTime, checkCount, buffaloTaskInfo);
            updateMysqlForTicket(builder, integerXbpResponse, "update t_nature_check_status" +
                    " set xbp_status = ?, ticket_id = ?, xbp_msg = ?, check_erp = ?" +
                    " where id = ?", "nature", buffaloTaskInfo.getManagers(), id
            );
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
            throw e;
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
    }

    public static void createXbpProcess(StringBuilder builder, Map<String, Object> row) {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow.scheduler.createFlow", "SparkMonitorApp", false, true);
        try {
            Integer batchId = Integer.parseInt(row.get("id") + "");
            Integer processId = Integer.parseInt(row.get("process_id") + "");
            String flowType = (String) row.get("flow_type");
            String buffaloVersion = (String) row.get("buffalo_version");
            String taskType = (String) row.get("task_type");
            String source = (String) row.get("source");
            String taskids = (String) row.get("taskids");
            String taskname = (String) row.get("taskname");
            String username = (String) row.get("username");
            String inspectionItem = (String) row.get("inspection_item");
            String inspectionDesc = (String) row.get("inspection_desc");
            String inspection7day = (String) row.get("inspection_7day");
            String inspectionLevel = (String) row.get("inspection_level");
            String inspectionLastday = (String) row.get("inspection_lastday");
            String inspectionPropose = (String) row.get("inspection_propose");
            String inspectionPlat = (String) row.get("inspection_plat");
            String currentEngine = (String) row.get("current_engine");
            String currentEngineVersion = (String) row.get("current_engine_version");
            String engine = (String) row.get("engine");
            String version = (String) row.get("version");
            String rtfMsg = (String) row.get("rtfMsg");
            String superCC = (String) row.get("super_cc");
            String superErp = (String) row.get("super_erp");
            String superEmail = (String) row.get("super_email");
            String kongMingConf = (String) row.get("kongming_conf");

            String ccMails = ("Yes".equals(superCC) && StringUtils.isEmpty(superEmail)) ?
                    Scheduler.commonBean.getXbpMailCopyAddressesForInspection() :
                    Scheduler.commonBean.getXbpMailCopyAddressesForInspection() + "," + superEmail;
            XbpResponse<Integer> integerXbpResponse ;
            if(processId == 8720) {
                integerXbpResponse = xbpProcess(batchId, processId, flowType, buffaloVersion, taskType, source,
                        taskids, engine, version, kongMingConf, currentEngine, currentEngineVersion, rtfMsg, ccMails);
            } else {
                integerXbpResponse = xbpProcessForInsjection(processId, username, taskids, taskname, inspectionItem,
                        inspectionDesc, inspection7day, inspectionLevel, inspectionLastday,inspectionPropose,
                        inspectionPlat, rtfMsg, ccMails);
            }
            updateMysqlForTicket(builder, integerXbpResponse, "update t_km_xbp_flow" +
                    " set flow_status = ?, ticket_id = ?, flow_status_msg = ?, username = ? " +
                    " where id = ?", "nature", username, batchId
            );
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
            throw e;
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
    }

    public static void updateClusterAndMarket(StringBuilder builder, Map<String, Object> row) {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow.scheduler.updateClusterMarket", "SparkMonitorApp", false, true);
        try {
            Integer batchId = Integer.parseInt(row.get("id") + "");
            String taskids = (String) row.get("taskids");

            if(taskids!= null) {
                TaskInfo buffaloTaskInfo = getBuffaloTaskInfo(taskids.split(",")[0]);
                String sql = "update t_km_xbp_flow" +
                        " set marketCode = ?, clusterCode = ?, taskStatus = ? " +
                        " where id = ?";
                int mysqlReturnCode = KongmingService.executeUpdate(sql, "nature",
                        StringUtils.trimToEmpty(buffaloTaskInfo.getMarketCode()),
                        StringUtils.trimToEmpty(buffaloTaskInfo.getClusterCode()), buffaloTaskInfo.isSuccess() + "", batchId);
                logger.info("update batchId: " + batchId + " taskids: " + taskids + " buffaloTaskInfo: "
                        + buffaloTaskInfo + " result: " + mysqlReturnCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
            throw e;
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
    }

    public static void updateMysqlForTicket(StringBuilder builder, XbpResponse<Integer> integerXbpResponse,
                                            String sql, String dbName, String erp, Integer id) {
        Integer statusCode = EnumXbpStatus.FAILED.statusCode;
        String description = EnumXbpStatus.FAILED.description;
        Integer ticketId = -1;
        if (integerXbpResponse != null) {
            Integer code = integerXbpResponse.getCode();
            if(code == 0){
                statusCode = EnumXbpStatus.CREATED_XBP.statusCode;
                description = EnumXbpStatus.CREATED_XBP.description;
                ticketId = integerXbpResponse.getData();
            }
        }
        int mysqlReturnCode = KongmingService.executeUpdate(sql, dbName,
                statusCode, ticketId, description, erp, id);
        logger.info("sql: " + sql + " returnCode: " + mysqlReturnCode);
        builder.append(id).append(": ").append(mysqlReturnCode).append("<br/>");
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

    public static XbpResponse<Integer> xbpProcessSendGitTableUpgrade(
            String dbName, String tblName, String tblDesc,
            Integer processId, String managers) {
        CreateParam createParam = new CreateParam();
        createParam.setProcessId(processId);
        createParam.setUsername(Scheduler.commonBean.getXbpWuGuoXiao());
//        createParam.setMailCopyAddresses(ccMails);

        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put("库名", dbName);
        applicationInfo.put("表名", tblName);
        applicationInfo.put("表说明", tblDesc);
        createParam.setApplicationInfo(applicationInfo);

//        Map<String, String> rtfMap = new HashMap<>();
//        rtfMap.put("数据虚拟化（GIT表）背景", background);
//        createParam.setRtf(rtfMap);

        Map<String, List<String>> approvers = new HashMap<>();
        approvers.put(PROCESS_NAME_TABLE_OWNER_APPROVAL, Arrays.asList(managers.split(",")));
        createParam.setApprovers(approvers);

        XbpResponse<Integer> integerXbpResponse = XbpManager.createXbpFlow(createParam);
        logger.info("JSF: create: " + integerXbpResponse + " params: " + createParam);
        return integerXbpResponse;
    }

    public static XbpResponse<Integer> xbpProcess(Integer batchId, Integer processId, String flowType,
                                                  String buffaloVersion, String taskType, String source,
                                                  String taskIds, String engine, String version, String kongMingConf,
                                                  String currentEngine, String currentEngineVersion, String rtfMsg,
                                                  String ccMails) {
        CreateParam createParam = new CreateParam();
        createParam.setProcessId(processId);
        createParam.setUsername(Scheduler.commonBean.getXbpNatureUsername());
        createParam.setMailCopyAddresses(ccMails);

        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put("批次id", batchId);
        applicationInfo.put("确定引擎升级，并知晓引擎间区别", "是");
        createParam.setApplicationInfo(applicationInfo);
        Map<String, CreateParam.TableInfo> tableInfoMap = new HashMap<>();
        CreateParam.TableInfo tableInfo = new CreateParam.TableInfo();
        List<Map<String, String>> maps = new ArrayList<>();
        String managers = "";
        for (String taskIdStr : taskIds.split(",")) {
            if (StringUtils.isEmpty(taskIdStr)) {
                continue;
            }

            String[] taskInfo = parseTaskIds(taskIdStr);
            taskIdStr = taskInfo[0];
            String actionIds = taskInfo[1];
            String actionNames = "";
            if (actionIds.length() > 1) {
                actionNames = getAtcionNameList(taskIdStr, actionIds);
            }

            if (!org.apache.commons.lang3.StringUtils.isNumeric(taskIdStr)) {
                continue;
            }
            TaskInfo taskInfo1 = getTaskInfo(source, taskIdStr);
            managers = StringUtils.defaultIfEmpty(managers, taskInfo1.getManagers());
            Map<String, String> row1 = new HashMap<>();
            row1.put("任务id", taskIdStr);
            row1.put("任务名称", taskInfo1.getTaskName());
            row1.put("环节ID", actionIds);
            row1.put("环节名称", actionNames);
            row1.put("任务版本", buffaloVersion); // buffalo3、buffalo4
            row1.put("任务来源", source); // 自助提数、调度中心
            row1.put("任务类型", taskType); // 数据计算、数据出库、数据拉链
            row1.put("当前引擎类型", currentEngine); //当前引擎类型
            row1.put("当前引擎版本", currentEngineVersion); //当前引擎版本
            row1.put(XbpForm.engineUpdated[0], engine);
            row1.put(XbpForm.engineVersionUpdated[0], version);
            row1.put("下游任务数", "");
            maps.add(row1);
        }
        if (maps.isEmpty()) {
            return null;
        }
        tableInfo.setData(maps);
        tableInfoMap.put("升级的任务清单", tableInfo);
        createParam.setTableInfo(tableInfoMap);
        HashMap<String, List<String>> approvers = new HashMap<>();
        approvers.put(FLOW_NAME.get(0), Arrays.asList(managers.split(",")));
        createParam.setApprovers(approvers);
        Map<String, String> rtfMap = new HashMap<>();
        rtfMap.put("背景", rtfMsg);
        createParam.setRtf(rtfMap);
        XbpResponse<Integer> integerXbpResponse = XbpManager.createXbpFlow(createParam);
        logger.info("JSF: create: " + integerXbpResponse + " params: " + createParam);
        return integerXbpResponse;
    }

    private static TaskInfo getTaskInfo(String source, String taskIdStr) {
        TaskInfo taskInfo1 = new TaskInfo();
        if(SOURCE_ZI_ZHU.equals(source)) {
            JSFResultDTO<JobVO> resultDTO = ideJobService.getJobInfoById(Integer.parseInt(taskIdStr), "liutong24");
            JobVO obj = resultDTO.getObj();
            taskInfo1.setTaskName(obj.getJobName());
            taskInfo1.setManagers(obj.getOwner());
        } else {
            taskInfo1 = getBuffaloTaskInfo(taskIdStr);
        }
        return taskInfo1;
    }

    public static XbpResponse<Integer> xbpProcessForInsjection(Integer processId, String username, String taskid,
                                                               String taskname, String inspectionItem, String inspectionDesc,
                                                               String inspection7day, String inspectionLevel,
                                                               String inspectionLastday, String inspectionPropose,
                                                               String inspectionPlat, String rtfMsg, String ccMails) {
        CreateParam createParam = new CreateParam();
        createParam.setProcessId(processId);
        createParam.setUsername(Scheduler.commonBean.getXbpNatureUsername());

        createParam.setMailCopyAddresses(ccMails);

        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put("任务ID", taskid);
        applicationInfo.put("任务名称", taskname);
        applicationInfo.put("巡检项", inspectionItem);
        applicationInfo.put("问题描述", inspectionDesc);
        applicationInfo.put("风险等级", inspectionLevel);
        applicationInfo.put("近7天出现次数", inspection7day);
        applicationInfo.put("截止日期", inspectionLastday);
        applicationInfo.put("治理建议", inspectionPropose);
        applicationInfo.put("平台修改能力", inspectionPlat);
        createParam.setApplicationInfo(applicationInfo);
        Map<String, CreateParam.TableInfo> tableInfoMap = new HashMap<>();
        createParam.setTableInfo(tableInfoMap);
        HashMap<String, List<String>> approvers = new HashMap<>();
        approvers.put(FLOW_NAME_INSPECTION_2.get(0), Arrays.asList(username.split(",")));
        approvers.put(FLOW_NAME_INSPECTION_2.get(1), Arrays.asList(username.split(",")));
        approvers.put(FLOW_NAME_INSPECTION_2.get(2), Arrays.asList(username.split(",")));
        createParam.setApprovers(approvers);
        Map<String, String> rtfMap = new HashMap<>();
        rtfMap.put("注意事项", rtfMsg);
        createParam.setRtf(rtfMap);
        XbpResponse<Integer> integerXbpResponse = XbpManager.createXbpFlow(createParam);
        logger.info("JSF: create: " + integerXbpResponse + " params: " + createParam);
        return integerXbpResponse;
    }

    public static TaskInfo getBuffaloTaskInfo(String taskIdStr) {
        TaskInfo taskInfo1 = new TaskInfo();
        try {
            JSONObject jsonObject = Buffalo4TaskManager.buffalo4GetTaskInfo(Long.valueOf(taskIdStr));
            boolean success = jsonObject.getBooleanValue("success");
            if (success) {
                JSONObject obj = jsonObject.getJSONObject("obj");
                taskInfo1.setTaskName(obj.getString("taskName"));
                taskInfo1.setManagers(obj.getString("managers"));
                taskInfo1.setMarketCode(obj.getString("marketCode"));
                taskInfo1.setClusterCode(obj.getString("clusterCode"));
            }
            taskInfo1.setSuccess(success);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return taskInfo1;
    }

    public static XbpResponse<Integer> xbpInspection(Integer processId, String buffaloId, Date checkDt,
                                                     String checkScore, String checkLowItem, String checkMidItem,
                                                     String checkHighItem, Date expectFinishTime, String checkCount,
                                                     TaskInfo taskInfo1) {
        CreateParam createParam = new CreateParam();
        createParam.setProcessId(processId);
        createParam.setUsername(Scheduler.commonBean.getXbpNatureUsername());
        createParam.setMailCopyAddresses(Scheduler.commonBean.getXbpMailCopyAddressesForInspection());
        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put("任务id", buffaloId);
//        applicationInfo.put("巡检分数", checkScore);
        applicationInfo.put("巡检日期", checkDt == null ? "" : SDFThreadLocal.getyyyyMMdd().format(checkDt));
//        applicationInfo.put("低危项", checkLowItem);
//        applicationInfo.put("中危项", checkMidItem);
        applicationInfo.put("巡检项", checkHighItem);
        applicationInfo.put("近7天出现次数", checkCount);
        applicationInfo.put("期望改进时间", expectFinishTime == null ? "" : SDFThreadLocal.getyyyyMMdd().format(expectFinishTime));
        createParam.setApplicationInfo(applicationInfo);
        HashMap<String, List<String>> approvers = new HashMap<>();
        applicationInfo.put("任务名称", taskInfo1.getTaskName());
        approvers.put(FLOW_NAME_INSPECTION.get(0), Arrays.asList(taskInfo1.getManagers().split(",")));
        createParam.setApprovers(approvers);
        Map<String, String> rtfMap = new HashMap<>();
        rtfMap.put("参考文档", "" +
                "任务优化参考文档 :https://cf.jd.com/pages/viewpage.action?pageId=1007071530<br/>" +
                "不影响任务正常运行的巡检项可以申请白名单，白名单xbp:http://xbp.jd.com/25/apply/10034<br/>" +
                "如有问题请加群反馈：群号：10200061545 或联系：马焘（matao53）13521225573，吴国晓（wuguoxiao）17710939553<br/>");
        createParam.setRtf(rtfMap);
        XbpResponse<Integer> integerXbpResponse = XbpManager.createXbpFlow(createParam);
        logger.info("JSF: create: " + integerXbpResponse + " params: " + createParam);
        return integerXbpResponse;
    }
    /**
     * 根据任务ID和环节ID，获取环节名称列表
     * @param taskId 任务ID
     * @param actionIds 环节ID，多个用#号分隔，如果111#222
     * @return 名称列表，多个用#号分隔
     */
    public static String getAtcionNameList(String taskId, String actionIds) {
        if (actionIds.length() < 0) {
            return "";
        }

        // 分割环节ID
        Map<String, Integer> mAction = new HashMap<>();
        String[] actionIdList = actionIds.split("#");
        String[] actionNameList = new String[actionIdList.length];
        int pos = 0;
        for (String actionId : actionIdList) {
            mAction.put(StringUtils.trimToEmpty(actionId), pos);
            actionNameList[pos] = "";
            pos++;
        }

        // 获取环节名称
        try {
            JSONObject actionListByTaskId = Buffalo4TaskManager.getActionListByTaskId(taskId);
            if (actionListByTaskId.getBooleanValue("success")) {
                JSONArray list = actionListByTaskId.getJSONArray("list");
                for (int i = 0; i < list.size(); i++) {
                    JSONObject actionObj = list.getJSONObject(i);
                    String actionId = actionObj.getInteger("actionId").toString();
                    String actionName = actionObj.getString("actionName");
                    if (mAction.containsKey(actionId)) {
                        actionNameList[mAction.get(actionId)] = actionName;
                    }
                }
            } else {
                logger.info("Get action list err: " + actionListByTaskId.getString("message") + " taskId: " + taskId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return String.join("#", actionNameList);
    }

    /**
     * 解析环节信息
     * @param taskIds 任务加环切，格式如111 111:101#102
     * @return 任务ID、环节ID
     */
    private static String[] parseTaskIds(String taskIds) {
        String[] taskInfo = new String[]{StringUtils.trimToEmpty(taskIds), ""};
        if (taskIds.contains(":")) {
            String[] arr = taskIds.split(":");
            if (arr.length > 1) {
                taskInfo[0] = StringUtils.trimToEmpty(arr[0]);
                taskInfo[1] = StringUtils.trimToEmpty(arr[1]);
            }
        }

        return taskInfo;
    }

    public static Set<String> getKongmingPackage(String engine, String taskId, String actionId, String buffaloVersion) {
        Set<String> packages = new HashSet<>();
        String getKmPackage = Scheduler.commonBean.getKongMingGetBuffaloTemplateConfUrl()
                + "?jobType=" + engine
                + "&buffaloId=" + StringUtils.trimToEmpty(taskId)
                + "&actionId=" + StringUtils.trimToEmpty(actionId)
                + "&stageId=&source=" + StringUtils.trimToEmpty(buffaloVersion)
                + "&regionName=china"
                + "&msg=test_msg";
        String updateKongMing = SimpleHttpClient.sendRequest(httpClient, requestConfig, getKmPackage,
                "SimpleHttpClient.KongMing.GetBuffaloTemplate");
        JSONObject jsonObject = JSON.parseObject(updateKongMing);
        int status = jsonObject.getIntValue("status");
        if (status == 200) {
            JSONArray data = jsonObject.getJSONArray("data");

            for (int i = 0; i < data.size(); i++) {
                String packageName = data.getString(i);
                packages.add(packageName);
            }
        }
        return packages;
    }

    /**
     * 发送请求到kongming
     * @param msg 消息内容
     * @param taskId 任务ID
     * @param actionId 节点ID
     * @param buffaloVersion buffalo版本
     * @param kongMingTemplate kongming模板
     *                         如 SPARK|XXXX
     */
    public static void sendKongMing(StringBuilder msg, String taskId, String actionId, String buffaloVersion,
                                     String kongMingTemplate) {
        String delPackageUmpKey = "SimpleHttpClient.KongMing.DelBuffaloTemplate";
        String addPackageUmpKey = "SimpleHttpClient.KongMing.AddBuffaloTemplate";
        String[] kongMingEnginePackagePair = kongMingTemplate.split("\\|");
        String engine = "SPARK";
        String kongMingPackage = kongMingTemplate;
        if(kongMingEnginePackagePair.length == 2) {
            engine = kongMingEnginePackagePair[0];
            kongMingPackage = kongMingEnginePackagePair[1];
        }

        if ("SPARK_RSS_DROP_ALL".equals(kongMingPackage)) {
            String getKmPackage = Scheduler.commonBean.getKongMingGetBuffaloTemplateConfUrl()
                    + "?jobType=" + engine
                    + "&buffaloId=" + StringUtils.trimToEmpty(taskId)
                    + "&actionId=" + StringUtils.trimToEmpty(actionId)
                    + "&stageId=&source=" + StringUtils.trimToEmpty(buffaloVersion)
                    + "&regionName=china"
                    + "&msg=test_msg";
            String updateKongMing = SimpleHttpClient.sendRequest(httpClient, requestConfig, getKmPackage,
                    "SimpleHttpClient.KongMing.GetBuffaloTemplate");
            JSONObject jsonObject = JSON.parseObject(updateKongMing);
            int status = jsonObject.getIntValue("status");
            if (status == 200) {
                JSONArray data = jsonObject.getJSONArray("data");
//                http://172.21.55.100:8090/optimizeConf/delBuffaloTemplateConf?jobType=MAPREDUCE&buffaloId=MT_123&actionId=&stageId=&source=BUFFALO4&regionName=china&templateName=MAPREDUCE_TASK_LIMIT&msg=test_msg
                for (int i = 0; i < data.size(); i++) {
                    String delPackageName = data.getString(i);
                    if(delPackageName.contains("RSS")) {
                        String delKongMing = deleteKongmingPackage(taskId, actionId, buffaloVersion, delPackageUmpKey, engine, delPackageName);
                        msg.append("删除孔明配置: source: ").append(buffaloVersion)
                                .append("  engine: ").append(engine)
                                .append("  template: ").append(delPackageName)
                                .append("  taskId: ").append(taskId)
                                .append("  actionId: ").append(actionId)
                                .append("  result: ").append(delKongMing)
                                .append("<br/>");
                    }
                }
            }
        } else if ("REMOVE_ALL_LIMIT".equals(kongMingPackage)){
            String sql = "INSERT INTO t_km_task_template_conf_white_list (buffalo_id, action_id, stage_id, source, job_type, regionName, template_name)" +
                    "VALUES" +
                    "('"+taskId+"', NULL, NULL, 'BUFFALO4', 'SPARK', 'china', 'SPARK_TASK_DAILY_TIME_LIMIT_L3')," +
                    "('"+taskId+"', NULL, NULL, 'BUFFALO4', 'SPARK', 'china', 'SPARK_TASK_CORE_TIME_LIMIT_L3')," +
                    "('"+taskId+"', NULL, NULL, 'BUFFALO4', 'MAPREDUCE', 'china', 'MAPREDUCE_TASK_DAILY_TIME_LIMIT_L3')," +
                    "('"+taskId+"', NULL, NULL, 'BUFFALO4', 'MAPREDUCE', 'china', 'MAPREDUCE_TASK_CORE_TIME_LIMIT_L3')," +
                    "('"+taskId+"', NULL, NULL, 'BUFFALO4', 'SPARK', 'china', 'SPARK_TASK_CORE_TIME_LIMIT_NORMAL_L3')," +
                    "('"+taskId+"', NULL, NULL, 'BUFFALO4', 'SPARK', 'china', 'SPARK_TASK_DAILY_TIME_LIMIT_NORMAL_L3')," +
                    "('"+taskId+"', NULL, NULL, 'BUFFALO4', 'MAPREDUCE', 'china', 'MAPREDUCE_TASK_CORE_TIME_LIMIT_NORMAL_L3')," +
                    "('"+taskId+"', NULL, NULL, 'BUFFALO4', 'MAPREDUCE', 'china', 'MAPREDUCE_TASK_DAILY_TIME_LIMIT_NORMAL_L3')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'HIVE', 'china','HIVE_OBSERVER_NORMAL_L3_PG')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'MAPREDUCE', 'china','MAPREDUCE_OBSERVER_NORMAL_L3_PG')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'SPARK', 'china','SPARK_OBSERVER_NORMAL_L3_PG')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'HIVE', 'china','HIVE_OBSERVER_NORMAL_HOPE_L3_PG')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'MAPREDUCE', 'china','MAPREDUCE_OBSERVER_NORMAL_HOPE_L3_PG')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'SPARK', 'china','SPARK_OBSERVER_NORMAL_HOPE_L3_PG')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'SPARK', 'china','SPARK_TASK_CORE_TIME_CONF_L3')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'HIVE', 'china','HIVE_TASK_CORE_TIME_CONF_L3')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'MAPREDUCE', 'china','MAPREDUCE_TASK_CORE_TIME_CONF_L3')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'SPARK', 'china','SPARK_OBSERVER_NORMAL_TYRANDE_L3_PG')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'HIVE', 'china','HIVE_OBSERVER_NORMAL_TYRANDE_L3_PG')," +
                    "('"+taskId+"',NULL, NULL, 'BUFFALO4', 'MAPREDUCE', 'china','MAPREDUCE_OBSERVER_NORMAL_TYRANDE_L3_PG')";
            int i = KongmingService.executeUpdate(sql, "kongMingNew");
            msg.append("  取消资源等级限制: ").append(i > 0 ? "成功" : "失败");
        } else {
            String updateKongMing = addKongmingPackage(taskId, actionId, buffaloVersion, addPackageUmpKey, engine, kongMingPackage);
            msg.append("增加孔明配置: source: ").append(buffaloVersion)
                    .append("  engine: ").append(engine)
                    .append("  template: ").append(kongMingPackage)
                    .append("  taskId: ").append(taskId)
                    .append("  actionId: ").append(actionId)
                    .append("  result: ").append(updateKongMing)
                    .append("<br/>");
        }
    }

    /**
     *
     * @param taskId
     * @param actionId
     * @param buffaloVersion
     * @param addPackageUmpKey
     * @param engine
     * @param kongMingPackage
     * @return
     */
    public static String addKongmingPackage(String taskId, String actionId, String buffaloVersion, String addPackageUmpKey, String engine, String kongMingPackage) {
        String appendToKMDataPack = Scheduler.commonBean.getKongMingAddBuffaloTemplateConfUrl()
                + "?jobType=" + engine
                + "&buffaloId=" + StringUtils.trimToEmpty(taskId)
                + "&actionId=" + StringUtils.trimToEmpty(actionId)
                + "&stageId=&source=" + StringUtils.trimToEmpty(buffaloVersion)
                + "&regionName=china&templateName=" + StringUtils.trimToEmpty(kongMingPackage)
                + "&msg=test_msg";
        String updateKongMing = SimpleHttpClient.sendRequest(httpClient, requestConfig, appendToKMDataPack,
                addPackageUmpKey);
        logger.info("UpdateKongMing: request: " + appendToKMDataPack + " response: " + updateKongMing);
        return updateKongMing;
    }

    /**
     *
     * @param taskId
     * @param actionId
     * @param buffaloVersion
     * @param umpKey
     * @param engine
     * @param delPackageName
     * @return
     */
    public static String deleteKongmingPackage(String taskId, String actionId, String buffaloVersion, String umpKey,
                                               String engine, String delPackageName) {
        String delKmPackage = Scheduler.commonBean.getKongMingDelBuffaloTemplateConfUrl()
                + "?jobType=" + engine
                + "&buffaloId=" + StringUtils.trimToEmpty(taskId)
                + "&actionId=" + StringUtils.trimToEmpty(actionId)
                + "&stageId=&source=" + StringUtils.trimToEmpty(buffaloVersion)
                + "&regionName=china&templateName=" + StringUtils.trimToEmpty(delPackageName)
                + "&msg=test_msg";
        return SimpleHttpClient.sendRequest(httpClient, requestConfig, delKmPackage, umpKey);
    }

    public static StringBuilder schedulerCreateXbpFlow(XbpProcessService xbpProcessService, String tableName, String limit) {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow."+tableName+".scheduler", "SparkMonitorApp", false, true);
        StringBuilder builder = new StringBuilder();
        try {
            String sql = "select * from " + tableName + " where xbp_status = 1 limit " + limit;
            List<Map<String, Object>> nature = KongmingService.executeSql(sql, "nature");
            for(Map<String, Object> row : nature) {
                xbpProcessService.createXbpProcess(builder, row, tableName);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
        return builder;
    }

    /**
     * 获取任务负责人，取每一个非ext.开头的负责人，如果都是ext.开头，则直接返回第一个
     * @param managers 所有负责人，以逗号分隔
     * @return 负责人erp
     */
    private String getTaskOwner(String managers) {
        if (managers.isEmpty()) {
            return "";
        }

        String[] arr = managers.split(",");
        for (int i = 0; i < arr.length; i++) {
            if (arr[i].startsWith("ext.")) {
                continue;
            }
            return arr[i];
        }

        return arr[0];
    }
}
