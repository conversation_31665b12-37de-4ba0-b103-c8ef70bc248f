package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.utils.beans.HttpResponse;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@WebServlet("/analysis")
public class AnalysisLogController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(AnalysisLogController.class.getName());

    /**
     * 处理HTTP GET请求，获取网站和日志ID，然后通过BDPUtils进行登录并解析日志信息
     * @param req HTTP请求对象
     * @param resp HTTP响应对象
     * @throws ServletException 如果发生Servlet异常
     * @throws IOException 如果发生IO异常
     */
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        resp.setContentType("text/html;charset=utf-8");
        PrintWriter writer = resp.getWriter();
        String site = req.getParameter("site");
        String logId = req.getParameter("logId");

        Map<String, String> stringStringMap = BDPUtils.analysisLogByLogId(null,null, logId, null, site, true);
        writer.write(JSON.toJSONString(stringStringMap));
        writer.flush();
        writer.close();
    }

}
