package com.jd.bdp.spark.web;

import com.jd.bdp.bean.TaskInfo;
import com.jd.bdp.common.XbpManager;
import com.jd.bdp.scheduler.Scheduler;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.xbp.jsf.api.request.ticket.CreateParam;
import com.jd.xbp.jsf.api.response.XbpResponse;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import static com.jd.bdp.bean.XbpForm.FLOW_NAME_PRESS;
import static com.jd.bdp.spark.web.XBPController.getBuffaloTaskInfo;
import static com.jd.bdp.spark.web.XBPController.updateMysqlForTicket;

public class XbpProcessServiceForPress implements XbpProcessService {

    private static final Logger logger = Logger.getLogger(XBPController.class.getName());

    /**
     * 创建XBP流程
     * @param builder StringBuilder对象
     * @param row 包含数据的Map对象
     * @param tableName 表名
     */
    public void createXbpProcess(StringBuilder builder, Map<String, Object> row, String tableName) {
        CallerInfo schedulerCaller = Profiler.registerInfo("xbpFlow." + tableName + ".scheduler.createFlow", "SparkMonitorApp", false, true);
        try {
            Integer id = Integer.parseInt(row.get("id") + "");
            String bufflaoId = (String) row.get("bufflao_id");
            String checkCC = (String) row.get("check_cc");
            String checkMsg = (String) row.get("check_msg");
            Integer processId = (Integer) row.get("processId");
            TaskInfo buffaloTaskInfo = getBuffaloTaskInfo(bufflaoId);
            XbpResponse<Integer> integerXbpResponse = xbpPress(processId, bufflaoId, checkCC,
                    buffaloTaskInfo, checkMsg);
            updateMysqlForTicket(builder, integerXbpResponse, "update " + tableName +
                    " set xbp_status = ?, ticket_id = ?, xbp_msg = ?, check_erp = ?" +
                    " where id = ?", "nature", buffaloTaskInfo.getManagers(), id
            );
        } catch (Exception e) {
            e.printStackTrace();
            Profiler.functionError(schedulerCaller);
            throw e;
        } finally {
            Profiler.registerInfoEnd(schedulerCaller);
        }
    }

    @Override
    public List<Map<String, Object>> getXbpFlow(String buffaloId) {
        String sql = "select * from t_nature_press_status where bufflao_id = ?";
        return KongmingService.executeSql(sql, "nature", buffaloId);
    }

    public static XbpResponse<Integer> xbpPress(Integer processId, String buffaloId, String checkCC, TaskInfo buffaloTaskInfo, String checkMsg) {
        CreateParam createParam = new CreateParam();
        createParam.setProcessId(processId);
        createParam.setUsername(Scheduler.commonBean.getXbpNatureUsername());
        createParam.setMailCopyAddresses(checkCC);
        Map<String, Object> applicationInfo = new HashMap<>();
        applicationInfo.put("压测任务ID", buffaloId);
        createParam.setApplicationInfo(applicationInfo);
        HashMap<String, List<String>> approvers = new HashMap<>();
        applicationInfo.put("任务名称", buffaloTaskInfo.getTaskName());
        approvers.put(FLOW_NAME_PRESS.get(0), Arrays.asList(buffaloTaskInfo.getManagers().split(",")));
        createParam.setApprovers(approvers);
        Map<String, String> rtfMap = new HashMap<>();
        rtfMap.put("填写说明", checkMsg);
        createParam.setRtf(rtfMap);
        XbpResponse<Integer> integerXbpResponse = XbpManager.createXbpFlow(createParam);
        logger.info("JSF: create: " + integerXbpResponse + " params: " + createParam);
        return integerXbpResponse;
    }


}
