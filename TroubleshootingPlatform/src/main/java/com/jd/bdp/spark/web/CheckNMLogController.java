package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.scheduler.Scheduler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@WebServlet("/check")
public class CheckNMLogController extends HttpServlet {

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        String applications = req.getParameter("applications");
        if(applications != null) {
            applications = applications.replaceAll("\n", ",");
        }
        String[] split = applications.split(",");
        CloseableHttpClient httpClient = HttpClients.createDefault();
        List<Future<JSONObject>> futures = new ArrayList<>();
        for(int j = 0; j< split.length; j++) {
            String app = split[j];
            app=app.trim();
            System.out.println("("+(j+1)+"/"+split.length+") app:" + app);
            Map<String, String> history = BDPUtils.getHistory(app);
            if(history.isEmpty()) {
                System.out.println("Url not found on history, app: " + app);
                continue;
            }
            String s = SimpleHttpClient.sendRequest(httpClient, history.get("history") + "/allexecutors");
            if(s == null || "".equals(s)) {
                System.out.println("Request rest ui failed. url: " + history.get("history") + "/allexecutors");
                continue;
            }

            JSONArray objects = JSON.parseArray(s);
            for (int i = 0; i < objects.size(); i++) {
                JSONObject jsonObject = objects.getJSONObject(i);
                JSONObject executorLogs = jsonObject.getJSONObject("executorLogs");
                String id = jsonObject.getString("id");
                if(executorLogs != null) {
                    String stdoutUrl = executorLogs.getString("stdout");
                    if(stdoutUrl !=null && !"".equals(stdoutUrl)) {
                        String finalApp = app;
                        futures.add(Scheduler.executorService.submit(() -> {
                            boolean hasLogs = false;
                            try {
                                String content = SimpleHttpClient.sendRequest(httpClient, stdoutUrl);
                                Document parse = Jsoup.parse(content);
                                String text = parse.select(".content").text();
                                hasLogs = text.contains("spark_stderr");
                            }catch (Exception e) {
                                log(e.getMessage()+" stdoutUrl: " + stdoutUrl,e);
                            }
                            JSONObject obj = new JSONObject();
                            obj.put("app", finalApp);
                            obj.put("stdout", stdoutUrl);
                            obj.put("id",id);
                            obj.put("hasLog", hasLogs);
                            return obj;
                        }));
                    }
                }
            }
        }
        PrintWriter writer = resp.getWriter();

        int exist = 0;
        for(Future<JSONObject> future:futures) {
            try {
                JSONObject jsonObject = future.get();
                if(jsonObject.getBooleanValue("hasLog")) {
                    exist +=1;
                }
                writer.write(jsonObject.toJSONString() + "\n");
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }
        if(futures.size() == 0) {
            String warningMsg = "No executor logs found. Please change the applications.";
            System.out.println("warningMsg = " + warningMsg);
            writer.write(warningMsg);
        } else {
            double ratio = exist * 100 / futures.size();
            String summary = "exist: " + exist + " ratio: " + ratio + "%\n";
            System.out.println("summary = " + summary);
            writer.write(summary);
        }
        writer.flush();
        writer.close();
    }
}