package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.EnumBuffaloTaskStatus;
import com.jd.bdp.common.Buffalo4Dao;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.scheduler.ForkBuffaloTaskService;
import com.jd.common.util.StringUtils;
import com.jd.common.web.LoginContext;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

import static com.jd.bdp.bean.Constants.SPARK_BUFFALO_TASK_INFO;

@WebServlet("/sqlController")
public class SQLController extends HttpServlet {

    /**
     * 重写doGet方法，处理GET请求
     * @param req HTTP请求对象
     * @param resp HTTP响应对象
     * @throws IOException 可能抛出IO异常
     * @return 无返回值
     */
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        String sql = req.getParameter("sql");
        String type = req.getParameter("type");
        resp.setContentType("text/html;charset=utf-8");
        PrintWriter writer = resp.getWriter();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", type);
        if ("query".equalsIgnoreCase(type)) {
            List<Map<String, Object>> spark = KongmingService.executeSql(sql, null);
            writer.write(spark.toString());
        } else if ("update".equalsIgnoreCase(type)) {
            int spark = KongmingService.executeUpdate(sql, null);
            writer.write(spark);
        } else if ("ProcessByFile".equals(type)) {
            String filePath = req.getParameter("filePath");
            BDPUtils.logAnalysisByFile(true, jsonObject, filePath);
            writer.write(jsonObject.toJSONString());
        } else {
            writer.write("UNKnown");
        }
        writer.flush();
        writer.close();
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        doGet(req, resp);
    }
}
