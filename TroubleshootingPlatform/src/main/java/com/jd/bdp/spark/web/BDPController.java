package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.SaveBuffaloBean;
import com.jd.bdp.common.Buffalo4TaskManager;
import com.jd.utils.beans.HttpResponse;
import org.apache.commons.lang3.StringUtils;

import javax.websocket.Session;
import java.io.IOException;
import java.util.Map;

public class BDPController {

    /**
     * 对给定的json数据进行解析和处理，并根据相关参数调用improve方法进行改进
     * @param json 要解析和处理的json数据
     * @param session 会话对象，用于记录操作日志等
     * @return 返回改进后的响应数据
     * @throws IOException 如果发生I/O异常
     */
    public JSONObject improve(String json, Session session) throws IOException {
        JSONObject data = JSON.parseObject(json);
        String gitProjectId = data.getString("gitProjectId");
        String applicationId = data.getString("applicationId");
        String projectSpaceId = data.getString("projectSpaceId");
        String site = data.getString("site");
        Long buffalo4TaskId = data.getLong("buffalo4TaskId");
        Long logId = data.getLong("logId");
        String latestConf = data.getString("conf");
        String taskNodeId = data.getString("taskNodeId");
        String taskNodeType = data.getString("taskNodeType");
        boolean useOriginNode = StringUtils.trimToEmpty(data.getString("useOriginNode")).equalsIgnoreCase("yes");
        JSONObject response = improve(gitProjectId, applicationId, site, projectSpaceId, buffalo4TaskId, logId,
                latestConf, taskNodeId, taskNodeType, useOriginNode, System.currentTimeMillis() + "", session);
        trackingLog(session, response.getLong("beforeDuration"), response.getString("forkedTask"));
        return response;
    }

    public JSONObject improve(String gitProjectId, String applicationId, String site,
                              String projectSpaceId, Long buffalo4TaskId, Long logId,
                              String latestConf, String taskNodeId, String taskNodeType,
                              boolean useOriginNode, String outputTableFlag,
                              Session session) throws IOException {
        JSONObject response = new JSONObject();

        WebSocket.send(session, "INFO: Authentication succeeded.");
        JSONObject latestLog = BDPUtils.getLatestLog(buffalo4TaskId, logId);

        if (latestLog == null) {
            WebSocket.send(session, "ERROR: No running log found.");
            response.put("errMsg", "No running log found");
            return response;
        }
        String runLogId = latestLog.getString("runLogId");
        response.put("originalLogId", runLogId);
        WebSocket.send(session, "INFO: Latest log id: " + runLogId +
                " duration: " + latestLog.getString("durationStr"));
        String url = "http://"+site+"/buffalo4/instance/log/downloadLogs.ajax?runLogId=" + runLogId;
        JSONObject trackingUrlObj = BDPUtils.logAnalysisByUrl(url, true, null, null);
        if (trackingUrlObj.getBooleanValue("isSuccess")) {
            WebSocket.send(session, "INFO: Tracking URL found for logId " + logId + ": " + trackingUrlObj.getString("url"));
        } else {
            WebSocket.send(session, "INFO: Tracking URL not found for logId " + logId + ": " + trackingUrlObj.getString("errMsg"));
        }
        if (trackingUrlObj.getIntValue("numOfSparkSubmit") > 1) {
            response.put("errMsg", "Has multiple spark-submit");
            return response;
        }
        // 新版实现调BDP接口
        JSONObject buffalo4Task = Buffalo4TaskManager.buffalo4GetTaskInfo(buffalo4TaskId).getJSONObject("obj");
        // 老版通过爬虫方式获取
//        JSONObject buffalo4Task1 = BDPUtils.getBuffalo4Task(buffalo4TaskId + "");
        if (buffalo4Task == null || buffalo4Task.isEmpty()) {
            WebSocket.send(session, "ERROR: No task found for taskId " + buffalo4TaskId + " on Buffalo4");
            response.put("errMsg", "No task found for taskId");
            return response;
        }
        response.put("managers", buffalo4Task.getString("managers"));
        if (buffalo4Task.getIntValue("taskNodeType") == 3) {
            WebSocket.send(session, "ERROR: Currently does not support containerized clients " + buffalo4TaskId + " on Buffalo4");
            response.put("errMsg", "Currently does not support containerized clients");
            return response;
        }
        WebSocket.send(session, "INFO: Get info about the origin task."
                + " useOriginNode: " + useOriginNode
                + " nodeType: " + buffalo4Task.getString("taskNodeType")
                + " taskNodeId: " + buffalo4Task.getString("taskNodeId")
                + " taskType: " + buffalo4Task.getString("taskType"));
        // single:单环节普通任务, wf:工作流任务
        // TODO: 当任务是工作流任务时，调用其它接口来获取当前环节的运行节点id
        if(useOriginNode && "wf".equalsIgnoreCase(buffalo4Task.getString("taskType"))) {
            WebSocket.send(session, "ERROR: Failed to get the node id of the current task.");
            response.put("errMsg", "Failed to get the node id of the current task");
            return response;
        }

        String taskName = buffalo4Task.getString("taskName");

        SaveBuffaloBean saveBuffaloBean = new SaveBuffaloBean();
        saveBuffaloBean.setMarket(buffalo4Task.getString("market"));
        saveBuffaloBean.setAccount(buffalo4Task.getString("account"));
        saveBuffaloBean.setQueue(buffalo4Task.getString("queue"));

        saveBuffaloBean.setAccountCode(buffalo4Task.getString("accountCode"));
        saveBuffaloBean.setClusterCode(buffalo4Task.getString("clusterCode"));
        saveBuffaloBean.setMarketCode(buffalo4Task.getString("marketCode"));
        saveBuffaloBean.setQueueCode(buffalo4Task.getString("queueCode"));
        saveBuffaloBean.setMarketCode(buffalo4Task.getString("marketCode"));
        saveBuffaloBean.setTaskNodeId(buffalo4Task.getString("taskNodeId"));
        saveBuffaloBean.setTaskNodeType(buffalo4Task.getString("taskNodeType"));

        String startStr = "INFO spark-sql";
        JSONObject script = BDPUtils.getBuffalo4ScriptInLog(site, runLogId, startStr);
        if (script.isEmpty()) {
            WebSocket.send(session, "ERROR: No log or script found.");
            response.put("errMsg", "No log or script found");
            return response;
        }
        String rewroteShellScript;
        String targetTableName;
        String fullSql = script.getString("fullsql");
        response.put("originalSQLCommand", fullSql);
        String env = latestLog.getString("env");
        Map<String, String> envMap = BDPUtils.parseEnv(env);

        String cluster = envMap.get("cluster");
        TestClusterConf testDB = getTestClusterConf(cluster);

        JSONObject rewroteShellObj = BDPUtils.rewriteSql(session, fullSql,
                latestConf + " " + testDB.hiveMetastoreUrl, outputTableFlag, testDB.newDBName);
        if (!rewroteShellObj.getBooleanValue("isSuccess")) {
            WebSocket.send(session, "ERROR: " + rewroteShellObj.getString("errMsg"));
            response.put("errMsg", rewroteShellObj.getString("errMsg"));
            return response;
        }
        rewroteShellScript = rewroteShellObj.getString("rewrote");
        targetTableName = rewroteShellObj.getString("targetTableName");
        response.put("targetTableName", targetTableName);
        long timeMil = System.currentTimeMillis();
        String fileName = "script_" + buffalo4TaskId + ".sh";
        String buffaloName = taskName + "_spark_team_" + buffalo4TaskId + "_" + runLogId + "_" + timeMil;
        JSONObject jsonObject = BDPUtils.forkBuffalo(fileName, gitProjectId, projectSpaceId, rewroteShellScript, applicationId,
                buffaloName, saveBuffaloBean);

        if (jsonObject.isEmpty() || !jsonObject.getBooleanValue("isSuccess")) {
            WebSocket.send(session, "ERROR: ForkBuffaloTask: Error.");
            response.put("errMsg", "ForkBuffaloTask: " + jsonObject.getString("errMsg"));
            return response;
        }
        String forkedTask = jsonObject.getJSONObject("data").getString("taskId");
        WebSocket.send(session, "INFO: Forked task " + jsonObject.getString("_msg") + " id: " + forkedTask);
        response.putAll(envMap);
        response.put("forkedTask", forkedTask);
        response.put("beforeDuration", latestLog.getLong("duration"));
        return response;
    }

    public static class TestClusterConf {
        public String newDBName;
        public String hiveMetastoreUrl;

    }
    public static TestClusterConf getTestClusterConf(String cluster) {
        TestClusterConf testClusterConf = new TestClusterConf();
        //        10k是wangriyu_test   hope是zmy_test
        if("rexxar".equals(cluster) || "hope".equals(cluster) ) {
            testClusterConf.newDBName= "zmy_test";
            testClusterConf.hiveMetastoreUrl = " --hiveconf hive.metastore.uris=thrift://172.21.213.165:10112 ";
        } else if("cairne".equals(cluster) || "10k".equals(cluster)) {
            testClusterConf.newDBName = "wangriyu_test";
            testClusterConf.hiveMetastoreUrl = " --hiveconf hive.metastore.uris=thrift://172.21.213.165:10113 ";
        } else {
            testClusterConf.newDBName = "dev";
            testClusterConf.hiveMetastoreUrl = " --hiveconf hive.metastore.uris=thrift://172.21.213.165:10112 ";
        }
        return testClusterConf;
    }

    private void trackingLog(Session session, Long beforeDuration, String forkedTask) {
        JSONObject latestExecute = null;
        boolean isFirst = true;
        for (int i = 0; i < 1440; i++) {
            JSONArray buffalo4Logs = BDPUtils.getBuffalo4Logs("dp.jd.com", forkedTask, null, "single", null, "current");
            if (buffalo4Logs.size() > 0) {
                latestExecute = buffalo4Logs.getJSONObject(0);
                if (isFirst) {
                    String url1 = "http://dp.jd.com/buffalo4/instance/log/downloadLogs.ajax?runLogId=" + latestExecute.getString("runLogId");
                    JSONObject trackingURL = BDPUtils.logAnalysisByUrl(url1, true, null, null);
                    if (trackingURL.getBooleanValue("isSuccess")) {
                        WebSocket.send(session, "Tracking URL: " + trackingURL.getString("url"));
                        isFirst = false;
                    }
                }
                String runStatus = latestExecute.getString("runStatus");
                WebSocket.send(session, "INFO: Tracking status of the task: " + forkedTask + " runLogId: " + latestExecute.getString("runLogId") + " runStatus: " + runStatus);
                if ("success".equals(runStatus) || "fail".equals(runStatus)) {
                    break;
                }
            } else {
                WebSocket.send(session, "WARNING: No logs found for task: " + forkedTask + ", please wait...");
            }
            try {
                Thread.sleep(1000 * 60);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        if(latestExecute != null) {
            Long afterDuration = latestExecute.getLong("duration");
            WebSocket.send(session, "INFO: Before duration: " + beforeDuration);
            WebSocket.send(session, "INFO: After duration: " + afterDuration);
            WebSocket.send(session, "INFO: ImproveRatio = " + (float) (beforeDuration - afterDuration) / beforeDuration);
        }
    }
}
