package com.jd.bdp.spark.web;

import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
//import com.jd.bdp.utils.MybatisUtils;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark 3.4 引擎升级任务入库(导入)
 */
@WebServlet("/spark/tasks")
public class SparkTaskController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(SparkTaskController.class.getName());
    
    private SparkUpgradeTaskMapper taskMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeTaskMapper.class);

    @SneakyThrows
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        ServletOutputStream outputStream = resp.getOutputStream();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("接口暂时停用！！！");
        writer.flush();
        writer.close();
    }


    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "application/json;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");
        logger.info("=========== 查询 Spark3.4 升级任务列表 ===========");
//        SparkUpgradeTaskMapper taskMapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);
        List<SparkUpgradeTaskBean> sparkUpgradeTaskBeans = taskMapper.selectList();
        // 获取PrintWriter对象
        PrintWriter out = resp.getWriter();
        out.print(sparkUpgradeTaskBeans);
        // 释放PrintWriter对象
        out.flush();
        out.close();
        logger.info("=========== 查询 Spark3.4 升级任务列表 ===========" + sparkUpgradeTaskBeans.toString());
    }
}
