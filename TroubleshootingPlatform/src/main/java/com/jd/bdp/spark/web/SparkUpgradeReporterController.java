package com.jd.bdp.spark.web;


import com.jd.bdp.bean.SparkUpgradeReporterBean;
import com.jd.bdp.bean.bo.*;
import com.jd.bdp.bean.vo.SparkUpgradeTaskSummaryVo;
import com.jd.bdp.mapper.spark.SparkUpgradeAssessmentMapper;
import com.jd.bdp.mapper.buffalo.SparkUpgradeBuffaloMapper;
import com.jd.bdp.utils.MapperManager;
//import com.jd.bdp.utils.MybatisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import static com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum.*;
import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark升级-buffalo4任务周报结果
 */
@WebServlet("/sparkUpgradeWeekReporter")
public class SparkUpgradeReporterController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeReporterController.class.getName());

    private SparkUpgradeBuffaloMapper buffaloMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeBuffaloMapper.class);
    
    private SparkUpgradeAssessmentMapper assessmentMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeAssessmentMapper.class);
    
    
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String startTime = req.getParameter("startTime");
        String endTime = req.getParameter("endTime");

        logger.info(String.format("=== 开始进行本周双跑任务汇总：时间范围 【%s, %s】", startTime, endTime));
        SparkUpgradeReporterBean bean = getRunTaskSummary(startTime, endTime);
        logger.info(String.format("=== 本周双跑任务汇总结果：时间范围【%s, %s】%s", startTime, endTime, MapperManager.writeValueAsString(bean)));
        logger.info(String.format("=== 本周双跑任务汇总信息：时间范围【%s, %s】%s", startTime, endTime, bean.toString()));
        
        req.setAttribute("msg",bean.toString());
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }
    
    /**
     * 自动同步
     * 本周共双跑1386个任务，成功322个，涉及1203个query，双跑性能提升24.68%，双跑成功的任务中，P95任务4个，核心基线任务42个
     * 预计节省核数
     * 双跑失败的任务中，双跑工具原因占比XX%，Spark 语法兼容问题 XX%，对数失败XX%
     * @param starTime
     * @param endTime
     */
    public SparkUpgradeReporterBean getRunTaskSummary(String starTime, String endTime){
//        SparkUpgradeBuffaloMapper buffaloMapper = MybatisUtils.getMapper(SparkUpgradeBuffaloMapper.class, "buffalo");
//        SparkUpgradeAssessmentMapper assessmentMapper = MybatisUtils.getMapper(SparkUpgradeAssessmentMapper.class);
        List<DoubleRunTaskSummaryBo> summaryList = buffaloMapper.selectDoubleRunSummary(starTime, endTime);
        logger.info(String.format("=== 所有最新双跑的最新双跑状态，数量: %s", summaryList.size()));
        Map<Integer, SparkUpgradeTaskSummaryVo> resultMap = new HashMap<>();
        List<SparkUpgradeTaskSummaryVo> resultList = new ArrayList<>();
        int i = 0;
        for(DoubleRunTaskSummaryBo summaryBo: summaryList){
            i++;
            if(i % 100 == 0){
                logger.info(String.format("=== 获取双跑任务最新状态进度: %s/%s", i, summaryList.size()));
            }
            Integer taskId = summaryBo.getTaskId();
            if(resultMap.containsKey(taskId)){
                SparkUpgradeTaskSummaryVo summaryVo = resultMap.get(taskId);
                // 计算累加属性
                summaryVo.addAccumulateVal(summaryBo.getRunStatus(), summaryBo.getTotalDuration24(), summaryBo.getTotalDuration34());
            }else{
                // 获取任务的累加属性
                SparkUpgradeTaskSummaryVo vo = new SparkUpgradeTaskSummaryVo();
                vo.setTaskId(taskId);
                vo.setRunStatus(summaryBo.getRunStatus());          // 运行状态
                vo.setDuration24(summaryBo.getTotalDuration24() ==  null ? 0 : summaryBo.getTotalDuration24() );   // spark2.4耗时
                vo.setDuration34(summaryBo.getTotalDuration34() == null ? 0: summaryBo.getTotalDuration34());   // spark3.4耗时
                
                // 获取任务的静态属性
                DoubleRunTaskStaticInfoBo staticInfoBo = assessmentMapper.selectTaskStaticInfoByTaskId(taskId);
                if(staticInfoBo != null){
                    vo.setJobNum(staticInfoBo.getJobNum() == null ? 0.0 : staticInfoBo.getJobNum() );                   // query数
                    vo.setIsBaseLine(staticInfoBo.getIsBaseLine() == null ? false: staticInfoBo.getIsBaseLine());       // 是否基线任务
                    vo.setIsP95(staticInfoBo.getIsP95() == null ? false: staticInfoBo.getIsP95() );                     // 是否p95任务
                    vo.setVCores(staticInfoBo.getReqVcoreDay() == null ? 0.0: staticInfoBo.getReqVcoreDay() );          // 核数
                    vo.setUpgradeStatus(staticInfoBo.getUpgradeStatus() == null ? UN_EXE.getCode(): staticInfoBo.getUpgradeStatus()); // 升级状态
                }else{
                    vo.setJobNum(0.0);                      // query数
                    vo.setIsBaseLine(false);                // 是否基线任务
                    vo.setIsP95(false);                     // 是否p95任务
                    vo.setVCores(0.0);                      // 核数
                    vo.setUpgradeStatus(UN_EXE.getCode()); // 升级状态
                }
                
                
                resultMap.put(taskId, vo);
                resultList.add(vo);
            }
        }
        logger.info(String.format("=== 双跑任务信息汇总完成： 【%s, %s】期间共双跑任务数：%s", starTime, endTime,resultMap.size()));
        
        // 信息汇总
        double totalVcoresNum = resultList.stream().mapToDouble(SparkUpgradeTaskSummaryVo::getVCores).sum();
        List<SparkUpgradeTaskSummaryVo> sucList = resultList.stream().filter(item -> "success".equalsIgnoreCase(item.getRunStatus()) && !UPGRADED.getCode().equalsIgnoreCase(item.getUpgradeStatus())).collect(Collectors.toList());
        long failTaskNum = resultList.stream().filter(item -> "fail".equalsIgnoreCase(item.getRunStatus())).count();
        long runningTaskNum = resultList.stream().filter(item -> "run".equalsIgnoreCase(item.getRunStatus())).count();
        double totalDuration24 = sucList.stream().mapToDouble(SparkUpgradeTaskSummaryVo::getDuration24).sum();
        double totalDuration34 = sucList.stream().mapToDouble(SparkUpgradeTaskSummaryVo::getDuration34).sum();
        double performanceRate = (totalDuration24 - totalDuration34) / totalDuration24;
        double totalSucVcores = sucList.stream().mapToDouble(SparkUpgradeTaskSummaryVo::getVCores).sum();
        
        SparkUpgradeReporterBean reporterBean = new SparkUpgradeReporterBean();
        reporterBean.setTotalTaskNum(resultList.size());
        reporterBean.setTotalVcoresNum(totalVcoresNum);
        reporterBean.setSuccessTaskNum(sucList.size());
        reporterBean.setFailTaskNum(failTaskNum);
        reporterBean.setRunningTaskNum(runningTaskNum);
        reporterBean.setSuccessQueryNum(sucList.stream().mapToDouble(SparkUpgradeTaskSummaryVo::getJobNum).sum());
        reporterBean.setPerformanceRatio(performanceRate);
        reporterBean.setSuccessP95Num(sucList.stream().filter(SparkUpgradeTaskSummaryVo::getIsP95).count());
        reporterBean.setSuccessBaselineNum(sucList.stream().filter(SparkUpgradeTaskSummaryVo::getIsBaseLine).count());
        reporterBean.setTotalSucVcores(totalSucVcores);
        reporterBean.setSaveVcores(totalSucVcores * performanceRate);
        return reporterBean;

    }
}
