package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.SparkUpgradeEngineVersionBean;
import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.bean.enums.SparkUpgradeOpTypeEnum;
import com.jd.bdp.bean.enums.SparkUpgradeStatusEnum;
import com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum;
import com.jd.bdp.common.Buffalo4TaskManager;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.filter.SSOFilterImpl;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
import com.jd.bdp.mapper.spark.SparkUpgradeVersionMapper;
import com.jd.bdp.utils.MapperManager;
//import com.jd.bdp.utils.MybatisUtils;
import com.jd.common.web.LoginContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * Buffalo4 引擎版本修改
 */
@WebServlet("/buffalo4TaskModify")
public class BuffaloTaskInfoModifyController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(BuffaloTaskInfoModifyController.class.getName());

    private static SparkUpgradeVersionMapper versionMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeVersionMapper.class);
    
    private static SparkUpgradeTaskMapper taskMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeTaskMapper.class);;
    
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");

        String oPType = req.getParameter("oPType");
        String taskIds1 = req.getParameter("taskIds");
        String actionId = StringUtils.trimToNull(req.getParameter("actionId"));

        String hadoopEngineType = req.getParameter("hadoopEngineType");
        String hadoopEngineVersion = req.getParameter("hadoopEngineVersion");
        String[] buffaloEnvs = new String[]{"buffalo4.bdp.jd.local", "buffalobasic0dev.dp.jd.com"};
        String pin = LoginContext.getLoginContext().getPin();
        StringBuilder msg = new StringBuilder();
        for (String buffaloEnv : buffaloEnvs) {
            msg.append(buffaloTaskInfoModify(oPType, taskIds1, hadoopEngineType, hadoopEngineVersion,
                    pin, buffaloEnv, actionId));
        }
        req.setAttribute("msg", msg);
        req.getRequestDispatcher("/dialog.jsp").forward(req, resp);
    }

    public static String buffaloTaskInfoModify(String oPType, String taskIds1, String hadoopEngineType,
                                               String hadoopEngineVersion, String pin, String buffaloEnv,
                                               String actionId2) throws IOException {
        StringBuilder builder = new StringBuilder();
        if("modify".equalsIgnoreCase(oPType)) {
            ArrayList<Integer> integers = new ArrayList<>();
            if (actionId2 != null) {
                integers.add(Integer.parseInt(actionId2));
            }
            updateEngineInfo(builder, taskIds1, hadoopEngineType, hadoopEngineVersion, pin,
                    SSOFilterImpl.holder.get().getTicket(), integers, buffaloEnv);
        } else if ("query".equalsIgnoreCase(oPType)) {
            Long taskIds = Long.parseLong(taskIds1);
            JSONObject jsonObject = Buffalo4TaskManager.buffalo4GetTaskInfo(taskIds);
            builder.append(jsonObject.toJSONString());
        } else {
            builder.append("未知操作类型");
        }
        return builder.toString();
    }

    /**
     *
     * @param builder
     * @param taskIdsStr  任务id，支持多任务，以逗号分隔
     * @param hadoopEngineType 引擎类型 spark或hive
     * @param hadoopEngineVersion 版本 3.4 、 3.0
     * @param pin  提交人的erp
     * @param ticket
     * @param actionIds  通过xbp传入的环节id
     * @param buffaloEnv  空值
     * @throws IOException
     */
    public static void updateEngineInfo(StringBuilder builder, String taskIdsStr, String hadoopEngineType,
                                        String hadoopEngineVersion, String pin, String ticket,
                                        List<Integer> actionIds, String buffaloEnv) throws IOException {
        builder.append("操作人: ").append(pin);
        String[] split = taskIdsStr.split("[,;]");
        for (String taskId : split) {
            taskId = StringUtils.trimToNull(taskId);
            Long taskIdInt = null;
            if (StringUtils.isNumeric(taskId)) {
                taskIdInt = Long.parseLong(taskId);
            }
            if (taskId == null) {
                continue;
            }
            JSONObject jsonObject = Buffalo4TaskManager.buffalo4GetTaskInfo(taskIdInt);
            logger.info("Received task info: " + jsonObject);
            if (jsonObject.getBooleanValue("success")) {
                try {
                    JSONObject taskInfo = jsonObject.getJSONObject("obj");
                    String taskType = StringUtils.trimToEmpty(taskInfo.getString("taskType"));
                    String managers = taskInfo.getString("managers");
                    List<String> managerList = Arrays.asList(managers.split(","));
                    if (managerList.contains(pin) || SSOFilterImpl.whiteSuperErps.contains(pin)) {
                        if (taskType.equalsIgnoreCase("single")) {
                            List<Integer> taskIdArray = new ArrayList<>();
                            taskIdArray.add(Integer.parseInt(taskId));
                            // 获取原始引擎版本信息
                            Map<Integer, String> currentVersionMap = getCurrentEngineVersion(Integer.parseInt(taskId), true);

                            JSONObject modifyTask = Buffalo4TaskManager.batchModifyMarketInfo(
                                    taskIdArray, null, hadoopEngineType, hadoopEngineVersion, ticket,
                                    buffaloEnv);
                            builder.append(" 任务:").append(taskId).append(" 环境:").append(buffaloEnv)
                                    .append(modifyTask.getString("message"));

                            // 保存升级记录
                            Integer code = modifyTask.getInteger("code");
                            if(code == 0 ){
                                logger.info(String.format("=== updateEngineInfo single task success, 调用buffalo接口修改引擎版本成功, taskId: %s,  message: %s", taskId, modifyTask.getString("message")));
                                String updateResult = updateTaskStatus(Integer.parseInt(taskId), hadoopEngineVersion, pin);
                                builder.append(updateResult);
                                recordSparkUpgradeInfo(Integer.parseInt(taskId), new ArrayList<>(), hadoopEngineType, hadoopEngineVersion, true, pin, currentVersionMap, SparkUpgradeStatusEnum.SUCCESSFUL);    
                            }else{
                                logger.info(String.format("=== updateEngineInfo single task failed, 调用buffalo接口修改引擎版本失败, taskId: %s,  reason: %s", taskId, modifyTask.getString("message")));
                                recordSparkUpgradeInfo(Integer.parseInt(taskId), new ArrayList<>(), hadoopEngineType, hadoopEngineVersion, true, pin, currentVersionMap, SparkUpgradeStatusEnum.FAILED);
                            }
                        } else if (taskType.equalsIgnoreCase("wf")) {
                            // 获取原始引擎版本信息
                            Map<Integer, String> currentVersionMap = getCurrentEngineVersion(Integer.parseInt(taskId), false);
                            
                            List<Integer> actions;
                            if(actionIds.isEmpty()) {
                                actions = getActions(taskId);
                            } else {
                                actions = new ArrayList<>(actionIds);
                            }
                            JSONObject modifyActionTask = Buffalo4TaskManager.batchModifyMarketInfo(
                                    new ArrayList<>(), actions, hadoopEngineType, hadoopEngineVersion, ticket, buffaloEnv);
                            builder.append(" 任务:").append(taskId)
                                    .append(" 环境:").append(buffaloEnv)
                                    .append(" (actionId:")
                                    .append(actions).append(") ")
                                    .append("修改引擎:").append(hadoopEngineType)
                                    .append("版本:").append(hadoopEngineVersion)
                                    .append(modifyActionTask.getString("message"));
                            
                            // 保存升级记录
                            Integer code = modifyActionTask.getInteger("code");
                            if(code == 0 ){
                                logger.info(String.format("=== updateEngineInfo wf task success, 调用buffalo接口修改引擎版本成功, taskId: %s,  message: %s", taskId, modifyActionTask.getString("message")));
                                String updateResult = updateTaskStatus(Integer.parseInt(taskId), hadoopEngineVersion, pin);
                                builder.append(updateResult);
                                recordSparkUpgradeInfo(Integer.parseInt(taskId), actions, hadoopEngineType, hadoopEngineVersion, false, pin, currentVersionMap, SparkUpgradeStatusEnum.SUCCESSFUL);
                            }else{
                                logger.info(String.format("=== updateEngineInfo wf task failed, 调用buffalo接口修改引擎版本失败, taskId: %s,  reason: %s", taskId, modifyActionTask.getString("message")));
                                recordSparkUpgradeInfo(Integer.parseInt(taskId), actions, hadoopEngineType, hadoopEngineVersion, false, pin, currentVersionMap, SparkUpgradeStatusEnum.FAILED);
                            }
                        } else {
                            builder.append(" 未知的任务类型，任务id是").append(taskId).append("类型是").append(taskType);
                        }
                    } else {
                        builder.append("当前登陆的用户没有权限操作该任务，该任务的负责人是").append(managers);
                    }
                } catch (Exception e) {
                    logger.info(String.format("=== updateEngineInfo taskId: %s, exception !!!", taskId, CommonUtil.exceptionToString(e)));
                    builder.append("Exception: ").append(CommonUtil.exceptionToString(e));
                }
            } else {
                builder.append("获取BDP的任务状态接口失败，请联系erp: wuguoxiao，异常信息是：")
                        .append(jsonObject.getString("message"));
            }
            builder.append("<br/>");
        }
    }

    public static  List<Integer> getActions(String taskId) throws IOException {
        List<Integer> actionIds = new ArrayList<>();
        JSONObject actionListByTaskId = Buffalo4TaskManager.getActionListByTaskId(taskId);
        if (actionListByTaskId.getBooleanValue("success")) {
            JSONArray list = actionListByTaskId.getJSONObject("obj").getJSONArray("actionList");
            for (int i = 0; i < list.size(); i++) {
                JSONObject actionObj = list.getJSONObject(i);
                // 仅处理`数据计算(py/sh)`和`数据计算(SQL)`类型的
                if ("pyscript".equalsIgnoreCase(actionObj.getString("actionType"))
                        || "datasql".equalsIgnoreCase(actionObj.getString("actionType"))) {
                    Integer actionId = actionObj.getInteger("actionId");
                    actionIds.add(actionId);
                }
            }
        }
        return actionIds;
    }


    /**
     * 获取buffalo任务各个环节原始的引擎版本
     * @param taskId 任务id
     * @param isSingle 是否标准任务
     * @return
     * @throws Exception
     */
    public static Map<Integer, String> getCurrentEngineVersion(Integer taskId, boolean isSingle) {
        Map<Integer, String> currentVersionMap = new HashMap<>();
        JSONObject actionEnvResult = null;
        try {
            actionEnvResult = Buffalo4TaskManager.getActionEnvByTaskId(taskId);
        } catch (IOException e) {
            logger.warning(String.format("=== 通过接口获取每个环节env信息成功: taskId:  %s", taskId));
            throw new RuntimeException(e);
        }
        Integer code = actionEnvResult.getInteger("code");
        if(code != 0){
            logger.info(String.format("=== 通过接口获取每个环节env信息成功失败：taskId: %s, message: %s", taskId, actionEnvResult.getString("message")));
            throw new RuntimeException(String.format("通过接口获取每个环节env信息成功失败：taskId: %s, message: %s", taskId, actionEnvResult.getString("message")));
        }else{
            logger.info(String.format("=== 通过接口获取每个环节env信息成功: taskId: %s, actions: %s", taskId, MapperManager.writeValueAsString(actionEnvResult)));
        }

        JSONArray list = actionEnvResult.getJSONObject("obj").getJSONArray("actions");

        // 获取每一个环节升级前原始引擎版本信息
        for(int i = 0; i < list.size(); i++){
            JSONObject actionObj = list.getJSONObject(i);
            Integer actionId = actionObj.getInteger("actionId");
            currentVersionMap.put(isSingle ? taskId : actionId, "default");  // 默认为spark2.4版本
            String env = actionObj.getString("env");
            if(StringUtils.isEmpty(env)){
                logger.info(String.format("=== 任务: %s, 环节: %s, env信息为空", taskId, actionId));
                continue;
            }
            for(String kv: env.split(",")){
                if(kv.contains("JDHXXXXX_ENGINE_VERSION") && kv.split("=").length > 1 ){  // TODO 是否需要判断下 BEE_HIVETASK_EXEC_ENGINE, 排除掉非spark引擎的环节
                    String version = kv.split("=")[1];
                    if(isSingle){
                        // 如果是标准任务
                        currentVersionMap.put(taskId, version);
                        break;
                    }else{
                        // 如果是工作流任务
                        currentVersionMap.put(actionId, version);
                    }
                }
            }
        }
        logger.info(String.format("=== 获取每一个环节当前最新引擎版本信息成功：%s", MapperManager.writeValueAsString(currentVersionMap)));
        return currentVersionMap;
    }
    
    /**
     * 记录每次升级、回滚任务引擎版本的信息
     * @param taskId 任务id
     * @param actionIds 环节id
     * @param hadoopEngineType 引擎类型
     * @param toVersion 调整引擎版本
     * @param isSingle 是否为标准任务
     * @param pin 操作人
     * @throws IOException
     */
    public static void recordSparkUpgradeInfo(Integer taskId, List<Integer> actionIds, String hadoopEngineType, String toVersion, boolean isSingle, String pin, Map<Integer, String> fromVersion, SparkUpgradeStatusEnum status) throws Exception {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<SparkUpgradeEngineVersionBean> beanList = new ArrayList<>();
            if(isSingle){
                // 如果是标准, 则环节id为空
                SparkUpgradeEngineVersionBean bean = new SparkUpgradeEngineVersionBean();
                bean.setTaskId(taskId);
                bean.setActionId(null);
                bean.setTaskType("single");
                bean.setEngine(hadoopEngineType);
                bean.setFromVersion(fromVersion.get(taskId));
                bean.setToVersion(toVersion);
                // 根据fromVersion、toVersion计算是升级还是回滚操作
                bean.setOpType(SparkUpgradeOpTypeEnum.calc(fromVersion.get(taskId), toVersion).getCode());
                bean.setStatus(status.getCode());
                bean.setCreateTime(sdf.format(new Date()));
                bean.setCreator(pin);
                beanList.add(bean);
            }else{
                // 如果是工作流任务
                String now = sdf.format(new Date());
                for(Integer actionId: actionIds){
                    SparkUpgradeEngineVersionBean bean = new SparkUpgradeEngineVersionBean();
                    bean.setTaskId(taskId);
                    bean.setActionId(actionId);
                    bean.setTaskType("wf");
                    bean.setEngine(hadoopEngineType);
                    bean.setFromVersion(fromVersion.get(actionId));
                    bean.setToVersion(toVersion);
                    // 根据fromVersion、toVersion计算是升级还是回滚操作
                    bean.setOpType(SparkUpgradeOpTypeEnum.calc(fromVersion.get(actionId), toVersion).getCode());
                    bean.setStatus(status.getCode());
                    bean.setCreateTime(now);
                    bean.setCreator(pin);
                    beanList.add(bean);
                }
            }
            logger.info(String.format("=== sparkUpgrade升级信息: %s", MapperManager.writeValueAsString(beanList)));

            // 开始保存数据库
//            SparkUpgradeVersionMapper mapper = MybatisUtils.getMapper(SparkUpgradeVersionMapper.class);
            int affectedSize = versionMapper.batchInsertSparkUpgradeVersions(beanList);
            if(affectedSize > 0){
                logger.info(String.format("=== 批量保存sparkUpgrade升级流水表信息成功: taskId: %s", taskId));
            }else{
                logger.info(String.format("=== 批量保存sparkUpgrade升级流水表信息失败: taskId: %s", taskId));
            }
        } catch (Exception e) {
            logger.log(Level.INFO, "=== 记录每次升级/回滚任务引擎版本的信息异常!!!", e);
        }
    }
    
    
    public static String updateTaskStatus(Integer taskId, String toVersion, String pin){
        try {
            logger.info(String.format("=== 开始更新任务状态: taskId: %s, toVersion: %s, pin: %s", taskId, toVersion, pin));
            String message = "";
            if(StringUtils.isBlank(toVersion)){
                message = String.format("更新任务状态失败, taskId: %s, 原因: toVersion为空", taskId);
                logger.info(message);
                return message;
            }

            String originVersion = SparkUpgradeTaskImportController.getTaskEngineOriginVersion(String.valueOf(taskId));
            if(StringUtils.isBlank(originVersion)){
                message = String.format("更新任务状态失败, taskId: %s, 原因: 无法获取任务原始引擎版本", taskId);
                logger.info(message);
                return message;
            }

//            SparkUpgradeTaskMapper mapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SparkUpgradeTaskBean task = new SparkUpgradeTaskBean();
            task.setTaskId(taskId);
            task.setCreator(pin);
            task.setCreateTime(sdf.format(new Date()));
            task.setUpdateTime(sdf.format(new Date()));
            task.setOriginVersion(originVersion);

            String status = "";
            if(toVersion.contains("3.4")){
                // 更新任务为升级状态
                status  = SparkUpgradeTaskStatusEnum.UPGRADED.getCode();
            }else if(toVersion.contains("default") || toVersion.contains("3.0")){
                // 更新任务为回滚状态
                status  = SparkUpgradeTaskStatusEnum.ROLLBACKED.getCode();
            }else{
                // 其他未知状态
                status  = SparkUpgradeTaskStatusEnum.UNKNOWN.getCode();
            }
            task.setStatus(status);

            int affectSize = taskMapper.insertSparkUpgradeTask(task);
            if(affectSize > 0){
                message = String.format("更新任务状态成功, taskId: %s, status: %s",taskId, status);
                logger.info(message);
                return message;
            }else{
                message = String.format("更新任务状态失败, taskId: %s, status: %s, 原因：mysql影响条数为0",taskId, status);
                logger.info(message);
                return message;
            }
        } catch (Exception e) {
            logger.info(String.format("=== 更新任务状态异常, taskId: %s, 原因: %s", taskId, CommonUtil.exceptionToString(e)));
            return String.format("更新任务状态异常, taskId: %s, 原因: %s", taskId, e.getMessage());
        }
    }
}
