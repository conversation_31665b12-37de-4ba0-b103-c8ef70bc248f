package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@WebServlet("/compare")
public class JSONCompareController extends HttpServlet {

    /**
     * 处理HTTP的GET请求，比较两个URL返回的JSON数据并将结果写回响应中
     * @param req HTTP请求对象
     * @param resp HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String leftUrl = req.getParameter("leftUrl");
        String rightUrl = req.getParameter("rightUrl");

        String left = SimpleHttpClient.sendRequest(httpclient, leftUrl);
        String right = SimpleHttpClient.sendRequest(httpclient, rightUrl);

        JSONObject jsonObject = JSONCompare.jsonCompare(JSONObject.parseObject(left).getJSONArray("sparkProperties"),
                JSONObject.parseObject(right).getJSONArray("sparkProperties"));
        PrintWriter writer = resp.getWriter();
        writer.write(jsonObject.toJSONString());
        writer.close();
    }
}
