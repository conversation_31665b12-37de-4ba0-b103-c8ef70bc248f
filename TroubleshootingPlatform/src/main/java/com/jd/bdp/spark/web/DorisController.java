package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.jd.bdp.common.DorisDao;
import com.jd.bdp.common.SDFThreadLocal;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Logger;

@WebServlet("/dorisController")
public class DorisController extends HttpServlet {
    private static final Logger logger = Logger.getLogger(DorisController.class.getName());

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        JSONArray data = new JSONArray();
        String taskId = req.getParameter("taskId");
        String logId = req.getParameter("logId");
        String dt = req.getParameter("dt");
        String returnPage = StringUtils.defaultIfEmpty(req.getParameter("returnPage"), "/presto.jsp");
        boolean returnJson = Boolean.parseBoolean(StringUtils.defaultIfEmpty(req.getParameter("returnJson"), "false"));
        String returnStr = "";
        if (logId != null && dt != null) {
            List<Map<String, Object>> durations = getDuration(taskId, "'"+dt+"'", logId);
            if(!durations.isEmpty()) {
                Map<String, Object> stringObjectMap = durations.get(0);
                List<Map<String, Object>> vCores = getVCores(taskId, "'"+dt+"'", logId);
                if(!vCores.isEmpty()) {
                    stringObjectMap.putAll(vCores.get(0));
                }
                List<Map<String, Object>> inputGB =getInputGB(taskId, "'"+dt+"'", logId);
                if(!inputGB.isEmpty()) {
                    stringObjectMap.putAll(inputGB.get(0));
                }
                data.add(stringObjectMap);
            }
        } else {
            String type = req.getParameter("static-type");
            String sql1 = req.getParameter("sql");
            int days = Integer.parseUnsignedInt(req.getParameter("days"));
            logger.info("sql = " + sql1);
            List<Map<String, Object>> datas = KongmingService.executeSql(sql1, "doris");
            if (!datas.isEmpty()) {
                int j  = 0;
                for (Map<String, Object> row : datas) {
                    logger.info("doris: " + (++j) + "/" + datas.size());
                    String taskid = (String) row.get("taskid");
                    dt = (String) row.get("dt");
                    if (dt != null && taskid != null) {
                        String join = getDts(dt, type, days, true);
                        List<Map<String, Object>> durationData = getDuration(taskid, join, null);
                        datas = durationData;
                        if(!durationData.isEmpty()) {
                            Map<String, Object> stringObjectMap = durationData.get(0);
                            List<Map<String, Object>> presto = getInputGB(taskid, join, null);
                            if(!presto.isEmpty()){
                                stringObjectMap.putAll(presto.get(0));
                            }

                            presto = getVCores(taskid, join, null);
                            if(!presto.isEmpty()){
                                stringObjectMap.putAll(presto.get(0));
                            }
                            logger.info(JSON.toJSONString(stringObjectMap));
                            data.add(stringObjectMap);
                        }
                    }
                }
            }
        }
        if (returnJson) {
            resp.setContentType("application/json");
            resp.setCharacterEncoding("UTF-8");
            PrintWriter writer = resp.getWriter();
            writer.write(returnStr);
            writer.flush();
            writer.close();
        } else {
            resp.setContentType("text/html;charset=utf-8");
            req.setAttribute("data", data);
            req.getRequestDispatcher(returnPage).forward(req, resp);
        }
    }

    public List<Map<String, Object>> getInputGB(String taskid, String dts, String logId) {
        CallerInfo callerInfo = Profiler.registerInfo("presto.getInputGB", "SparkMonitorApp", false, true);
        String sql;
        sql = "SELECT taskid,\n" +
                "    round(avg(inputGB)) AS inputGB, \n" +
                "    round(avg(shuffleReadGB)) AS shuffleReadGB \n" +
                "FROM (\n" +
                "    SELECT\n" +
                "      b.taskid,\n" +
                "      b.logid,\n" +
                "      sum(a.inputBytes / 1024) / 1024 / 1024 AS inputGB,\n" +
                "      sum(a.shuffleReadBytes / 1024) / 1024 / 1024 AS shuffleReadGB\n" +
                "    FROM\n" +
                "      fdm.fdm_spark_stageinfo_di as a\n" +
                "    INNER JOIN fdm.fdm_spark_appinfo_di as b on a.appid = b.appid and a.appattemptid = b.appattemptid \n" +
                "    WHERE\n" +
                "      a.dt in ( "+ dts +" ) and b.dt in ( "+ dts +" )\n" +
                (StringUtils.isNotEmpty(logId)? "  AND b.logId in ("+logId+") \n": "") +
                "      AND taskId = "+ taskid +"\n" +
                "    GROUP BY b.taskid, b.logId\n" +
                ")\n" +
                "GROUP BY taskid";
        List<Map<String, Object>> presto = DorisDao.executeSql(sql, "doris", null);
        logger.info("Query input shuffle sql: " + sql + " response: " + JSON.toJSONString(presto));
        Profiler.registerInfoEnd(callerInfo);
        return presto;
    }

    public static String getDts(String dt, String type, int days, boolean includeCur) {
        List<String> dts = new ArrayList<>();
        if(includeCur) {
            dts.add("'" + dt + "'");
        }
        try {
            Date date = SDFThreadLocal.getyyyyMMdd().parse(dt);
            for (int i = 1; i < (includeCur ? days: days + 1); i++) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DATE, type.equals("before") ? -i : i);
                Date time = calendar.getTime();
                dts.add("'" + SDFThreadLocal.getyyyyMMdd().format(time) + "'");
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return StringUtils.join(dts, ",");
    }

    public static List<Map<String, Object>> getVCores(String taskid, String join, String logId) {
        CallerInfo callerInfo = Profiler.registerInfo("presto.getVCores1_sparkmonitor", "sparkmonitor", false, true);
        List<Map<String, Object>> presto;
        String sql;
        sql = "SELECT\n" +
                "  task_id AS taskid,\n" +
                "  CAST(ROUND(AVG(sum_req_vcore_c_s)) AS bigint) AS req_vcore_c_s,\n" +
                "  CAST(ROUND(AVG(sum_req_mem_mbs_s)) AS bigint) AS req_mem_mbs_s\n" +
                "FROM\n" +
                "  (\n" +
                "    SELECT\n" +
                "      task_id,\n" +
                "      bee_businessid,\n" +
                "      SUM(req_vcore_c_s) AS sum_req_vcore_c_s,\n" +
                "      SUM(req_mem_mbs_s) AS sum_req_mem_mbs_s\n" +
                "  FROM\n" +
                "      gdm.gdm_m99_job_run_log_da\n" +
                "  WHERE\n" +
                "      dt IN("+ join +")\n" +
                "      AND task_id = "+ taskid +"\n" +
                (StringUtils.isNotEmpty(logId)? "  AND bee_sn in ("+logId+") \n": "") +
                "  GROUP BY\n" +
                "      task_id,\n" +
                "      bee_businessid\n" +
                "  )\n" +
                "GROUP BY\n" +
                "  task_id";
        presto = DorisDao.executeSql(sql, "doris", null);
        logger.info("Query req vcore sql: " + sql + " response: " + JSON.toJSONString(presto));
        Profiler.registerInfoEnd(callerInfo);
        return presto;
    }

    public static List<Map<String, Object>> getDuration(String taskid, String dts, String logId) {
        CallerInfo callerInfo = Profiler.registerInfo("presto.getDuration", "SparkMonitorApp", false, true);
        List<Map<String, Object>> datas;
        String sql =
                "SELECT\n" +
                        "  '"+ dts.replaceAll("'", "")+ "' AS dts, \n" +
                        "  task_id as taskid,\n" +
                        "  round(avg(exec_long)) as duration\n" +
                        "FROM\n" +
                        "  gdm.gdm_m99_task_run_log\n" +
                        "WHERE\n" +
                        "  dt in ( " + dts + " )\n" +
                        "  AND task_id = " + taskid + " \n" +
                        (StringUtils.isNotEmpty(logId) ? " AND log_id = " + logId + " \n" : "") +
                        (StringUtils.isNotEmpty(logId) ? " AND status != '失败' \n" : "") +
                        "GROUP BY task_id";
        datas = DorisDao.executeSql(sql, "doris", null);
        logger.info("Query avg duration sql: " + sql + " response: " + JSON.toJSONString(datas));
        Profiler.registerInfoEnd(callerInfo);
        return datas;
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }
}
