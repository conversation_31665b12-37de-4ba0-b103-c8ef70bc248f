/*
 * IRIS -- Intelligent Roadway Information System
 * Copyright (C) 2014-2015  AHMCT, University of California
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.LogBean;
import com.jd.bdp.bean.ProcessResult;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.common.FileUtil;
import com.jd.bdp.scheduler.Scheduler;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.core.util.IOUtils;

import java.io.*;
import java.lang.InterruptedException;
import java.lang.Process;
import java.lang.ProcessBuilder;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;


/**
 * Misc. OS utilities
 *
 * <AUTHOR> Swanston
 */
public class OSUtils {
    private static final Logger logger = Logger.getLogger(OSUtils.class.getName());

    /**
     * 生成新进程并执行命令
     * @param cmd 命令参数列表
     * @return processResult 进程执行结果
     * @throws InterruptedException 当进程被中断时抛出
     * @throws IOException 当IO操作发生异常时抛出
     */
    public static ProcessResult spawnProcess(final List<String> cmd) {
        ProcessResult processResult = new ProcessResult();
        if (cmd == null) {
            return null;
        }
        CallerInfo callerInfo = Profiler.registerInfo("bdp.spawnProcess", "SparkMonitorApp", false, true);
        try {
            Process proc = startProcess(cmd, true);
            processResult.setLogStr(consumeProcessOutput(proc.getInputStream()));
            processResult.setStatusCode(proc.waitFor());
            destroyProcess(proc);
            if(processResult.getStatusCode() != 0) {
                logger.warning("Cmd: " + cmd + " has error.");
                Profiler.functionError(callerInfo);
            }
        } catch (InterruptedException|IOException e) {
            processResult.setStatusCode(-1);
            logger.warning("Cmd: " + cmd + "\t" + CommonUtil.exceptionToString(e));
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return processResult;
    }
    /**
     * Start a new process.
     *
     * @param cmd   The cmd/arg list for execution
     * @param merge True to merge the process's error stream into its
     *              output stream
     * @return The new Process, or null upon failure
     */
    static private Process startProcess(List<String> cmd, boolean merge) throws IOException {
        if (cmd == null)
            return null;
        ProcessBuilder pb = new ProcessBuilder(cmd);
        if (merge)
            pb = pb.redirectErrorStream(true);
        Process proc = null;
        proc = pb.start();
        return proc;
    }

    public static String consumeProcessOutput(InputStream inputStream) {
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
        StringWriter writer = new StringWriter();
        try {
            int copy = IOUtils.copy(inputStreamReader, writer);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(),e);
        } finally {
            try {
                inputStreamReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        String theString = writer.toString();
        return theString;
    }
    /**
     * Consume a process's output stream until EOS is reached.
     * If this method is being used to prevent process blocking due to
     * full output buffers, then it is recommended that the process be
     * created with its error stream merged into its output stream,
     * otherwise blocking can still occur due to a full error stream
     * buffer.
     *
     * @param p The Process whose output stream to consume
     */
    static private void consumeProcessOutput(Process p, String fileName) {
        if (p == null) {
            return;
        }
        InputStream output = p.getInputStream();
        try {
            FileOutputStream outputStream = new FileOutputStream(fileName);
            byte[] bytes = new byte[1024];
            int len;
            while ((len = output.read(bytes)) != -1) {
                outputStream.write(bytes,0,len);
            }
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * Destroy a Process, first attempting to close its I/O streams.
     *
     * @param p The Process to destroy
     */
    static private void destroyProcess(Process p) {
        if (p == null)
            return;
        InputStream stdout = p.getInputStream();
        InputStream stderr = p.getErrorStream();
        OutputStream stdin = p.getOutputStream();
        try {
            if (stdout != null)
                stdout.close();
            if (stderr != null)
                stderr.close();
            if (stdin != null)
                stdin.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        p.destroy();
    }

    public static ProcessResult executeProcess(Map<String, String> envMap, String command, String dir) {
        ProcessResult processResult = new ProcessResult();
        CallerInfo callerInfo = Profiler.registerInfo("bdp.executeProcess", "SparkMonitorApp",
                false, true);
        List<String> envs = new ArrayList<>();

//        Map<String, String> getenv = new HashMap<>(System.getenv());
//        env.forEach((key, value) -> getenv.merge(key, value, (a, b) -> b + ":" + a));
//        getenv.forEach((key, value) -> envs.add(key + "=" + value));
        envMap.forEach((key, value) -> envs.add(key + "=" + value));
        Process proc = null;
        try {
            proc = Runtime.getRuntime().exec(
                    new String[]{"/bin/bash", "-c", command}, envs.toArray(new String[0]), new File(dir));
            processResult.setLogStr(consumeProcessOutput(proc.getInputStream()));
            processResult.setErrLog(consumeProcessOutput(proc.getErrorStream()));
            processResult.setStatusCode(proc.waitFor());
        } catch (IOException | InterruptedException e) {
            logger.warning("Cmd: " + command + "\t" + CommonUtil.exceptionToString(e));
            processResult.setLogStr(CommonUtil.exceptionToString(e));
            processResult.setStatusCode(-1);
            Profiler.functionError(callerInfo);
        } finally {
            destroyProcess(proc);
            Profiler.registerInfoEnd(callerInfo);
        }
        return processResult;
    }

    public static String downloadBuffaloLog(String url, JSONObject ret, String logType) {
        CallerInfo logAnalysisCaller = Profiler.registerInfo("bdp.bdpUtils.logAnalysis", "SparkMonitorApp", false, true);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        httpGet.addHeader("source", "guance");
        try (CloseableHttpResponse response = httpclient.execute(httpGet)) {
            if (response.getStatusLine().getStatusCode() == 200) {
                HttpEntity entity = response.getEntity();
                InputStream initialStream;
                if(StringUtils.isNotEmpty(logType) && logType.equalsIgnoreCase("idenew")){
                    String toStringJson = EntityUtils.toString(entity);
                    LogBean logBean = JSON.parseObject(toStringJson, LogBean.class);
                    List<String> logs = logBean.getObj().getLogs();
                    String logsStr = logs.stream().map((key)-> key + '\n').collect(Collectors.joining());
                    initialStream = new ByteArrayInputStream(logsStr.getBytes());
                }else{
                    initialStream = entity.getContent();
                }
                String logIdByUrl = BDPUtils.getLogIdByUrl(url);
                if (logIdByUrl != null) {
                    String filePath = Scheduler.commonBean.getDownloadBuffaloLogDir() + File.separator + "buffalo_logs" + File.separator + logIdByUrl + ".txt";
                    FileUtil.createDirectory(filePath);
                    File targetFile = new File(filePath);
                    java.nio.file.Files.copy(
                            initialStream,
                            targetFile.toPath(),
                            StandardCopyOption.REPLACE_EXISTING);
                    org.apache.commons.io.IOUtils.closeQuietly(initialStream);
                    return filePath;
                }
            }
        } catch (IOException e) {
            logger.warning("ERROR: " + CommonUtil.exceptionToString(e));
            ret.put("errMsg", e.getMessage());
            Profiler.functionError(logAnalysisCaller);
        } finally {
            Profiler.registerInfoEnd(logAnalysisCaller);
        }
        return null;
    }
}
