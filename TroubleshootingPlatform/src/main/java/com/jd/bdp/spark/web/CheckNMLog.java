package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import javax.websocket.Session;
import java.io.IOException;

public class CheckNMLog {

    public void check(String json, Session session) throws IOException {
        JSONObject data = JSON.parseObject(json);
        String url = data.getString("url");
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String s = SimpleHttpClient.sendRequest(httpclient, url);
        JSONArray objects = JSON.parseArray(s);
        for (int i = 0; i < objects.size(); i++) {
            JSONObject jsonObject = objects.getJSONObject(i);
            String id = jsonObject.getString("id");
            JSONObject executorLogs = jsonObject.getJSONObject("executorLogs");
            if(executorLogs != null) {
                String stdoutUrl = executorLogs.getString("stdout");
                if(stdoutUrl !=null && !"".equals(stdoutUrl)) {
                    String content = SimpleHttpClient.sendRequest(httpclient, stdoutUrl);
                    Document parse = Jsoup.parse(content);
                    String text = parse.select(".content").text();
                    boolean hasLogs = text.contains("spark_stderr");
                    WebSocket.send(session, "("+i+"/"+objects.size()+") id: "+id+
                            " "+stdoutUrl+" hasLogs: " + hasLogs);
                }
            }
        }
    }

}
