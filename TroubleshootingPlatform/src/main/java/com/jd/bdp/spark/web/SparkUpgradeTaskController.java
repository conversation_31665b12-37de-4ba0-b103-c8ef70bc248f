package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum;
import com.jd.bdp.mapper.spark.SparkUpgradeTaskMapper;
//import com.jd.bdp.utils.MybatisUtils;
import com.jd.bdp.utils.SparkUpgradeUtils;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.logging.Logger;

import static com.jd.bdp.filter.CommonSSOFilter.APPLICATION_CONTEXT;

/**
 * spark 3.4 引擎升级，任务版本回滚回调接口
 */
@WebServlet("/spark/task/notify")
public class SparkUpgradeTaskController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUpgradeTaskController.class.getName());

    private SparkUpgradeTaskMapper taskMapper = APPLICATION_CONTEXT.getBean(SparkUpgradeTaskMapper.class);
    
    @SneakyThrows
    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws IOException, ServletException {
        resp.setHeader("Content-Type", "text/html;charset=UTF-8");
        resp.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");
        String contentType = req.getContentType();
        if ("application/json".equals(contentType)) {
            BufferedReader reader = req.getReader();
            StringBuilder body = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }

            // 1 获取body
            String jsonBody = body.toString();
            logger.info("===========Spark 升级3.4 失败任务回调，请求body===========" + jsonBody);
            JSONObject taskInfo = JSONObject.parseObject(jsonBody);
            JSONArray alerts = taskInfo.getJSONArray("alerts");
            JSONObject alert = alerts.getJSONObject(0);
            String status = alert.getString("status");
            if ("firing".equals(status)) {
                JSONObject labels = alert.getJSONObject("labels");
                String taskIdStr = labels.getString("objId");
                int taskId = Integer.parseInt(taskIdStr);
                logger.info("===========Spark 升级3.4 失败任务回调，接收到任务ID===========" + taskIdStr);
                // 2 查询已录入升级任务
//                SparkUpgradeTaskMapper taskMapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);
                SparkUpgradeTaskBean task = taskMapper.selectOneByTaskId(taskIdStr);
                if (task != null && task.getStatus().equalsIgnoreCase(SparkUpgradeTaskStatusEnum.UPGRADED.getCode())) {
                    String originSparkVersion = task.getOriginVersion();
                    logger.info("===========Spark 升级3.4 失败任务回调，查询到的升级任务===========" + task);
                    // 3 将任务回滚至升级前的Spark引擎版本
                    if (StringUtils.isNoneEmpty(originSparkVersion)) {
                        boolean isRollbackSuc = SparkUpgradeUtils.rollbackSparkUpgradeTask(taskIdStr, originSparkVersion);
                        if(isRollbackSuc){
                            task.setStatus(SparkUpgradeTaskStatusEnum.ROLLBACKED.getCode());
                            taskMapper.updateTask(task);
                            logger.info("===========Spark 升级3.4 失败任务回滚，最终结果：回滚成功===========" + taskIdStr);
                        }else{
                            logger.info("===========Spark 升级3.4 失败任务回滚，最终结果：回滚失败===========" + taskIdStr);
                        }
                    } else {
                        logger.info("===========Spark 升级3.4 失败任务回调回滚跳过，查询到的升级前Spark引擎版本为空===========" + taskIdStr);
                    }
                } else {
                    logger.info("===========Spark 升级3.4 失败任务回调回滚跳过，根据任务ID查询到的任务为空、或任务状态不是UPGRADED===========" + taskIdStr);
                }
            }
        }
    }
}
