package com.jd.bdp.spark.web;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.logging.Logger;

@WebServlet("/sparkUI")
public class SparkUIController extends HttpServlet {

    private static final Logger logger = Logger.getLogger(SparkUIController.class.getName());

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String cluster = req.getParameter("cluster");
        String appId = req.getParameter("appId");
        String logTime = req.getParameter("logTime");
        logger.info(String.format("rebuild sparkui : cluster[%s],appId:[%s],logTime:[%s]",
                cluster, appId, logTime));
        ShellUtil shellUtil = new ShellUtil();
        int status = shellUtil.buildNewAppPath(logTime, appId, cluster);
        String url=String.format("/newSparkJH.jsp?appId=%s&status=%s",appId,status);
        resp.sendRedirect(url);
    }
}
