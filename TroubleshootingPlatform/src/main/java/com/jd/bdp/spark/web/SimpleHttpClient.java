package com.jd.bdp.spark.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.ResponseDTO;
import com.jd.bdp.common.CommonUtil;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.utils.Curl;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class SimpleHttpClient {

    private static final Logger logger = Logger.getLogger(SimpleHttpClient.class.getName());

    public static String sendRequest(CloseableHttpClient httpclient, String url) throws IOException {
        return sendRequest(httpclient,null,url, "SimpleHttpClient.sendRequest");
    }

    public static String sendRequest(CloseableHttpClient httpclient,
                                     RequestConfig requestConfig,
                                     String url, String key) {
        CallerInfo httpCaller = Profiler.registerInfo(key, "SparkMonitorApp", false, true);
        String result = "";
        long start1 = System.currentTimeMillis();
        HttpGet httpGet = new HttpGet(url);
        if(requestConfig != null) {
            httpGet.setConfig(requestConfig);
        }
        int code = -1;
        try (CloseableHttpResponse response1 = httpclient.execute(httpGet)) {
            code = response1.getStatusLine().getStatusCode();
            if (code == 200 || code == 500) {
                HttpEntity entity1 = response1.getEntity();
                result = EntityUtils.toString(entity1, "UTF-8");
                EntityUtils.consume(entity1);
                Profiler.functionError(httpCaller);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result = "调用孔明异常: url: " + url
                    + " error: "+ CommonUtil.exceptionToString(e);
            Profiler.functionError(httpCaller);
        } finally {
            Profiler.registerInfoEnd(httpCaller);
        }
        logger.info("Requested = [" + url + "] StatusCode = " + code + " taken "
                + (System.currentTimeMillis() - start1) + " ms");
        return result;
    }

    public static String sendJsonRequest(JSONObject object, String url, Map<String, String> headers) {
        HttpPost httpPost = new HttpPost(url);
        if (headers!= null) {
            headers.forEach(httpPost::addHeader);
        }
        httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
        StringEntity stringEntity = new StringEntity(object.toJSONString(), "UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        httpPost.setEntity(stringEntity);
        String ret = "";
        try {
            CloseableHttpResponse execute = Curl.httpClient.execute(httpPost);
            ret = EntityUtils.toString(execute.getEntity());
            EntityUtils.consume(execute.getEntity());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public static String sendPost(CloseableHttpClient httpclient,
                                  RequestConfig requestConfig,
                                  String url, Map<String, String> paramMap) {
        HttpPost httpPost = new HttpPost(url);
        if(requestConfig != null) {
            httpPost.setConfig(requestConfig);
        }
        String ret = "";
        try {
            List<NameValuePair> formParams = new ArrayList<>();
            paramMap.forEach((key, value) -> formParams.add(new BasicNameValuePair(key, value)));
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(formParams, "UTF-8");
            httpPost.setEntity(urlEncodedFormEntity);
            CloseableHttpResponse execute = httpclient.execute(httpPost);
            ret = EntityUtils.toString(execute.getEntity());
            EntityUtils.consume(execute.getEntity());
        } catch (IOException e) {
            e.printStackTrace();
            ret = "调用孔明异常: url: " + url
                    + " data: " + JSON.toJSONString(paramMap)
                    + " error: "+ CommonUtil.exceptionToString(e);
        }
        return ret;
    }

    /**
     * 发送POST请求
     * @param httpclient 可关闭的HttpClient
     * @param requestConfig 请求配置
     * @param url 请求的URL
     * @param formParams 表单参数
     * @return 响应DTO对象
     * @throws IOException IO异常
     */
    public static ResponseDTO sendPost(CloseableHttpClient httpclient,
                                       RequestConfig requestConfig,
                                       String url, List<NameValuePair> formParams) {
        ResponseDTO responseDTO = new ResponseDTO();
        HttpPost httpPost = new HttpPost(url);
        if(requestConfig != null) {
            httpPost.setConfig(requestConfig);
        }
        String ret = "";
        try {
            UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(formParams, "UTF-8");
            httpPost.setEntity(urlEncodedFormEntity);
            CloseableHttpResponse execute = httpclient.execute(httpPost);
            ret = EntityUtils.toString(execute.getEntity());
            EntityUtils.consume(execute.getEntity());
            responseDTO.setSuccess(true);
            responseDTO.setResult(ret);
        } catch (IOException e) {
            e.printStackTrace();
            ret = "调用孔明异常: url: " + url
                    + " data: " + JSON.toJSONString(formParams)
                    + " error: "+ CommonUtil.exceptionToString(e);
            responseDTO.setResult(ret);
        }
        return responseDTO;
    }
    public static ResponseDTO sendPost(CloseableHttpClient httpclient,
                                       RequestConfig requestConfig,
                                       String url, HttpEntity entity) {
        ResponseDTO responseDTO = new ResponseDTO();
        HttpPost httpPost = new HttpPost(url);
        if(requestConfig != null) {
            httpPost.setConfig(requestConfig);
        }
        String ret = "";
        try {
            httpPost.setEntity(entity);
            CloseableHttpResponse execute = httpclient.execute(httpPost);
            ret = EntityUtils.toString(execute.getEntity());
            EntityUtils.consume(execute.getEntity());
            responseDTO.setSuccess(true);
            responseDTO.setResult(ret);
        } catch (IOException e) {
            e.printStackTrace();
            ret = "调用孔明异常: url: " + url
                    + " data: " + JSON.toJSONString(entity)
                    + " error: "+ CommonUtil.exceptionToString(e);
            responseDTO.setResult(ret);
        }
        return responseDTO;
    }
}
