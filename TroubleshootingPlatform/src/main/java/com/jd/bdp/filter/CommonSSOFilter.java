package com.jd.bdp.filter;

import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.logging.Logger;

@WebFilter(filterName = "CommonSSOFilter", urlPatterns = {"/*"})
public class CommonSSOFilter implements Filter {

    private static final Logger logger = Logger.getLogger(CommonSSOFilter.class.getName());

    public static final Date currentDate = new Date();

    public static final ApplicationContext APPLICATION_CONTEXT = new ClassPathXmlApplicationContext("/spring-jsf.xml")  ;

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain filterChain) {
        logger.info("Initializing " + ((HttpServletRequest)servletRequest).getRequestURL().toString());
        APPLICATION_CONTEXT.getBean(SSOFilterImpl.class).filter(filterChain, servletRequest, servletResponse);
    }

    @Override
    public void destroy() {

    }
}
