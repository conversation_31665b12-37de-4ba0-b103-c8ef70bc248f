package com.jd.bdp.filter;

import com.alibaba.fastjson.JSONObject;
import com.jd.official.omdm.is.hr.HrUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.logging.Logger;

@Component
public class HrUserServiceImpl {

    private static final Logger logger = Logger.getLogger(HrUserServiceImpl.class.getName());

    @Autowired
    private HrUserService hrUserService;

    /**
     * https://cf.jd.com/pages/viewpage.action?pageId=80230163
     * http://omdmauth.jd.com/
     * 主数据接口文档
     * @param userName
     * @return
     * @throws UnsupportedEncodingException
     */
    public JSONObject getSuperiorBaseInfo(String userName) throws UnsupportedEncodingException {
        String appCode = "sparkmonitor_JDOS_LF";
        String businessId = UUID.randomUUID().toString();
        String safetyKey = "bd4628b4fc64c78b2bac";
        String responseFormat = "JSON";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String requestTimestamp = sdf.format(new Date());
        String sign = org.apache.commons.codec.digest.DigestUtils.md5Hex((appCode+businessId+requestTimestamp+safetyKey+userName).getBytes());
        String result = hrUserService.getSuperiorBaseInfo(appCode, businessId, requestTimestamp, sign, responseFormat, userName);
        String decode = URLDecoder.decode(result, "UTF-8");
        logger.info("Get superInfo: request userName: " + userName + " response: " + decode);
        JSONObject response = JSONObject.parseObject(decode);
        if (response.getIntValue("resStatus") == 200) {
            return response.getJSONObject("responsebody");
        }
        return new JSONObject();
    }

    public JSONObject getUserBaseInfoByUserName(String userName) throws UnsupportedEncodingException {
        String appCode = "sparkmonitor_JDOS_LF";
        String businessId = UUID.randomUUID().toString();
        String safetyKey = "bd4628b4fc64c78b2bac";
        String responseFormat = "JSON";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String requestTimestamp = sdf.format(new Date());
        String sign = org.apache.commons.codec.digest.DigestUtils.md5Hex((appCode+businessId+requestTimestamp+safetyKey+userName).getBytes());
        String result = hrUserService.getUserBaseInfoByUserName(appCode, businessId, requestTimestamp, sign, responseFormat, userName);
        String decode = URLDecoder.decode(result, "UTF-8");
        logger.info("Get superInfo: request userName: " + userName + " response: " + decode);
        JSONObject response = JSONObject.parseObject(decode);
        if (response.getIntValue("resStatus") == 200) {
            return response.getJSONObject("responsebody");
        }
        return new JSONObject();
    }
}
