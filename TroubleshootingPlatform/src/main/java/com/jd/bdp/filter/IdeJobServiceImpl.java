package com.jd.bdp.filter;

import com.alibaba.fastjson.JSON;
import com.jd.bdp.domain.dto.JsfAuthDTO;
import com.jd.jbdp.edc.api.extract.model.dto.JSFResultDTO;
import com.jd.jbdp.edc.api.extract.service.JobInfoInterface;
import com.jd.jbdp.edc.model.vo.extract.Job;
import com.jd.jbdp.edc.model.vo.extract.JobVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.logging.Logger;

@Component
public class IdeJobServiceImpl {
    private static final Logger logger = Logger.getLogger(IdeJobServiceImpl.class.getName());

    @Autowired
    private JobInfoInterface ideJobservice;

    /**
     * https://cf.jd.com/pages/viewpage.action?pageId=622410838
     * @param jobId
     * @param erp
     * @return
     */
    public JSFResultDTO<JobVO> getJobInfoById(Integer jobId, String erp) {
        JobVO jobVO = new JobVO();
        jobVO.setId(jobId);
        jobVO.setErp(erp);
        JSFResultDTO<JobVO> jobInfoById = ideJobservice.getJobInfoById(new JsfAuthDTO(), jobVO);
        return jobInfoById;
    }

    /**
     * https://cf.jd.com/pages/viewpage.action?pageId=*********
     * @param jobId
     * @param erp
     * @param jobName
     * @param description
     * @param targetFileName
     * @param targetType
     * @param sharedPersons
     * @param content
     * @param dbName
     * @param runMarketCode
     * @param queueCode
     * @param accountCode
     * @param runLogicClusterCode
     * @param noticeType
     * @param engineType
     * @return
     */
    public JSFResultDTO addOrModifyJobInfo(Integer jobId, String erp, String jobName, String description,
                                   String targetFileName, Integer targetType, String sharedPersons,
                                   String content, String dbName, String runMarketCode, String queueCode,
                                   String accountCode, String runLogicClusterCode, String noticeType,
                                   String engineType, Integer jobType, String groupName, String dispatchSystemJobIdNew) {
        Job job = new Job();
        job.setJobType(jobType);
        job.setGroupName(groupName);
        job.setDispatchSystemJobIdNew(dispatchSystemJobIdNew);
        job.setId(jobId);
        job.setErp(erp);
        job.setJobName(jobName);
        job.setDescription(description);
        job.setTargetFileName(targetFileName);
        job.setTargetType(targetType);
        job.setSharedPersons(sharedPersons);
        job.setContent(content);
        job.setDbName(dbName);
        job.setRunMarketCode(runMarketCode);
        job.setQueueCode(queueCode);
        job.setAccountCode(accountCode);
        job.setRunLogicClusterCode(runLogicClusterCode);
        job.setNoticeType(noticeType);
        job.setEngineType(engineType);
        JSFResultDTO save = ideJobservice.save(new JsfAuthDTO(), job);
        logger.info("Save ide job: request: " + JSON.toJSONString(job) + " response: " + JSON.toJSONString(save));
        return save;
    }

    public boolean updateVersion(Integer jobId, String engine){
        JSFResultDTO<JobVO> jobInfoById = getJobInfoById(jobId, "liutong24");
        JobVO job = jobInfoById.getObj();
        JSFResultDTO jsfResultDTO = addOrModifyJobInfo(
                job.getId(), job.getOwner(), job.getJobName(), job.getDescribe(), job.getTargetFileName(),
                job.getTargetType(), job.getSharedPersons(), job.getContent(), job.getDbName(),
                job.getRunMarketCode(), job.getQueueCode(), job.getAccountCode(), job.getRunLogicClusterCode(),
                job.getNoticeType(), engine, job.getJobType(), job.getGroupName(), job.getDispatchSystemJobIdNew());
        return jsfResultDTO.getCode() <= 0;
    }
}
