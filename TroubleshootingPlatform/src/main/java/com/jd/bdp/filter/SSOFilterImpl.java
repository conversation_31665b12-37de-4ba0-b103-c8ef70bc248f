package com.jd.bdp.filter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jd.bdp.bean.AllowList;
import com.jd.bdp.bean.LoginBean;
import com.jd.bdp.common.CommonUtil;
import com.jd.bdp.common.JimDBUtils;
import com.jd.bdp.common.SSOFilter;
import com.jd.bdp.common.YamlUtil;
import com.jd.common.springmvc.interceptor.SpringSSOInterceptor;
import com.jd.common.web.LoginContext;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.logging.Logger;

import static com.jd.bdp.bean.Constants.SSO_COOKIE_KEY;

@Component
public class SSOFilterImpl {
    private static final Logger logger = Logger.getLogger(SSOFilterImpl.class.getName());

    @Value("${APP_DOMAIN}")
    private String appDomain;
    @Autowired
    private SpringSSOInterceptor springSSOInterceptor;

    public static final ThreadLocal<LoginBean> holder = new ThreadLocal<LoginBean>();

    public static Set<String> whiteSuperErps = new HashSet<>(YamlUtil.loadYaml("allowListSuper.yaml", AllowList.class).getAllowList());
    private Set<String> whiteErps = new HashSet<>(YamlUtil.loadYaml("allowList.yaml", AllowList.class).getAllowList());

    private Map<String, Set<String>> urlWriteListMapping = new HashMap<>();

    public static boolean isSuper() {
        return whiteSuperErps.contains(LoginContext.getLoginContext().getPin());
    }

    public static String getPin() {
        return StringUtils.trimToEmpty(LoginContext.getLoginContext().getPin());
    }

    @PostConstruct
    public void initUrlWriteListMapping() {
        urlWriteListMapping.put("/kongmingForm.jsp", whiteErps);
        urlWriteListMapping.put("/kongming.jsp", whiteErps);
        urlWriteListMapping.put("/kongming-rss.jsp", whiteErps);
        urlWriteListMapping.put("/improves.jsp", whiteErps);
        urlWriteListMapping.put("/improve.jsp", whiteErps);
        urlWriteListMapping.put("/launchTask", whiteErps);
        urlWriteListMapping.put("/sqlController", whiteSuperErps);
        urlWriteListMapping.put("/buffaloLogs", whiteErps);
    }

    public void filter(FilterChain filterChain, ServletRequest servletRequest, ServletResponse servletResponse) {
        HttpServletRequest servletRequest1 = (HttpServletRequest) servletRequest;
        HttpServletResponse servletResponse1 = (HttpServletResponse) servletResponse;
        logger.info("domain: " + springSSOInterceptor.getAppHomeUrl()
                + " remote: " + servletRequest.getRemoteAddr() + "host: " + servletRequest.getRemoteHost()
                + " user: " + ((HttpServletRequest) servletRequest).getRemoteUser());
        try {
            String requestUrl = servletRequest1.getRequestURL().toString();
            if (springSSOInterceptor.isExclude(servletRequest1)) {
                filterChain.doFilter(servletRequest1, servletResponse1);
            } else {
                boolean ticketIsValid = springSSOInterceptor.preHandle(servletRequest1, servletResponse1, null);
                String ticket = springSSOInterceptor.getCookieValue(servletRequest1, SSO_COOKIE_KEY);
                holder.set(new LoginBean(ticket));
                LoginContext loginContext = LoginContext.getLoginContext();
                Enumeration<String> headerNames = servletRequest1.getHeaderNames();
                JSONObject header = new JSONObject();
                while (headerNames.hasMoreElements()) {
                    String s = headerNames.nextElement();
                    header.put(s, servletRequest1.getHeader(s));
                }
                String remoteHost = servletRequest1.getRemoteHost();
                logger.info("RequestUrl: " + requestUrl + " RemoteHost: " + remoteHost +
                        " CurrentPin: " + (loginContext != null ? loginContext.getPin() : "")
                        + " HasAuth: " + ticketIsValid + " Header: " + header.toJSONString());
                if (ticketIsValid) {
                    JimDBUtils.incrUV(loginContext == null ? "null" : loginContext.getPin());
                    Profiler.registerInfoEnd(Profiler.registerInfo("troubleshooting.PV", "SparkMonitorApp", false, true));
                    JimDBUtils.incrPV();
                    Set<Map.Entry<String, Set<String>>> entries = urlWriteListMapping.entrySet();
                    boolean hitUrl = false;
                    for(Map.Entry<String, Set<String>> entry: entries) {
                        if(requestUrl.contains(entry.getKey())) {
                            Set<String> value = entry.getValue();
                            if (loginContext != null && value.contains(loginContext.getPin())) {
                                filterChain.doFilter(servletRequest, servletResponse);
                            } else {
                                servletRequest.getRequestDispatcher("/error.jsp").forward(servletRequest, servletResponse);
                            }
                            hitUrl=true;
                            break;
                        }
                    }
                    if(!hitUrl) {
                        filterChain.doFilter(servletRequest, servletResponse);
                    }
                } else {
                    servletResponse1.sendRedirect("https://ssa.jd.com/sso/login?ReturnUrl=http://" + appDomain);
                }
            }
        } catch (IllegalArgumentException ex) {
            try {
                logger.info("Failed to login with invalid cookie: " + CommonUtil.exceptionToString(ex));
                servletResponse1.sendRedirect("https://ssa.jd.com/sso/login?ReturnUrl=http://" + appDomain);
            } catch (IOException e) {
                logger.warning("Failed to login: " + CommonUtil.exceptionToString(ex));
            }
        } catch (Exception ex) {
            Profiler.registerInfo("troubleshooting.exception", "SparkMonitorApp", false, true);
            logger.warning("Failed to get SpringSSOInterceptor from spring context. Full stacktrace is " + CommonUtil.exceptionToString(ex));
        }
    }
}
