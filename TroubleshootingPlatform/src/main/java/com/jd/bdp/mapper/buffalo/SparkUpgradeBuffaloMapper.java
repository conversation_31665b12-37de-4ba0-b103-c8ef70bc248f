package com.jd.bdp.mapper.buffalo;

import com.jd.bdp.bean.BuffaloTaskInfoBean;
import com.jd.bdp.bean.BuffaloTaskRunLogBean;
import com.jd.bdp.bean.SparkUpgradeBuffaloLogBean;
import com.jd.bdp.bean.bo.DoubleRunTaskBaselineBo;
import com.jd.bdp.bean.bo.DoubleRunTaskStatusSizeBo;
import com.jd.bdp.bean.bo.DoubleRunTaskDurationBo;
import com.jd.bdp.bean.bo.DoubleRunTaskSummaryBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: xiaowei79
 * description: Spark升级任务Buffalo数据源mapper
 * date: 2024-09-25 01:59:00
 */
public interface SparkUpgradeBuffaloMapper {
    
    /**
     * 根据原始任务id获取最新的双跑任务id集合
     * 一个原始任务id，对应多批双跑任务id，每批代表双跑某一天的sql， 每批中的每个任务代表原始任务的一个环节
     * @param originTaskId
     * @return
     */
    String selectDoubleTaskIdByOriginTaskId(@Param("originTaskId") Integer originTaskId);

    
    /**
     * 获取双跑任务的最新状态(从今日0点~此刻)
     * 获取每个环节的双跑任务的状态,只有都成功，原始的任务才算双跑成功
     * @param doubleRunTaskIds
     * @return
     */
    List<String> selectLatestInstRunStatusByDoubleTaskIdFromToday(@Param("doubleRunTaskIds") List<String> doubleRunTaskIds);

    /**
     * 获取双跑任务的最新状态(从昨日0点~此刻)
     * 获取每个环节的双跑任务的状态,只有都成功，原始的任务才算双跑成功
     * @param doubleRunTaskIds
     * @return
     */
    List<String> selectLatestInstRunStatusByDoubleTaskIdFromYesterday(@Param("doubleRunTaskIds") List<String> doubleRunTaskIds);


    /**
     * 根据双跑任务id获取最新失败的双跑运行记录
     * @param doubleRunTaskIds
     * @return
     */
    List<SparkUpgradeBuffaloLogBean> selectFailedDoubleRunTaskLogByDoubleRunTaskIds(@Param("doubleRunTaskIds") List<String> doubleRunTaskIds);


    /**
     * 获取所有最新双跑任务的最新状态、spark2.4耗时、spark3.4耗时、最新task实例、运行时间
     * @param startTime
     * @param endTime
     * @return
     */
    List<DoubleRunTaskSummaryBo> selectDoubleRunSummary(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 获取执行日期范围内双跑的原始任务集合
     * @param startTime
     * @param endTime
     * @return
     */
    List<Integer> selectOriginTaskInInterval(@Param("startTime")String startTime, @Param("endTime") String endTime);
    
    
    Integer selectRunningTaskInstancesCount();


    /**
     * 获取buffalo中各实例的状态和数量
     * @return
     */
    List<DoubleRunTaskStatusSizeBo> selectBuffaloInstanceStatusAndSize();
    
}
