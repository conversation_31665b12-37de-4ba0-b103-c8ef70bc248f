package com.jd.bdp.mapper.spark;

import com.jd.bdp.bean.SparkUpgradeExplainBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: xiaowei79
 * description: Spark逻辑计划执行结果mapper
 * date: 2024-09-25 01:59:00
 */
public interface SparkUpgradeExplainMapper {

    List<SparkUpgradeExplainBean> selectListByTaskId(@Param("taskId") Integer taskId);

    List<SparkUpgradeExplainBean> selectListByTaskIdAndStatus(@Param("taskId") Integer taskId, @Param("status") List<String> status);

    List<SparkUpgradeExplainBean> selectLatestListByTaskId(@Param("taskId") Integer taskId);
    

}
