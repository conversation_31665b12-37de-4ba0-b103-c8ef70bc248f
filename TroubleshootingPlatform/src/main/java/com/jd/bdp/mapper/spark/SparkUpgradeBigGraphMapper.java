package com.jd.bdp.mapper.spark;

import com.jd.bdp.bean.SparkUpgradeAssessmentBean;
import com.jd.bdp.bean.bo.DoubleRunErrorCodeAndSizeBo;
import com.jd.bdp.bean.bo.DoubleRunTaskStatusSizeBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: xiaowei79
 * description: Spark升级任务摸底全景mapper类
 * date: 2024-09-25 01:59:00
 */
public interface SparkUpgradeBigGraphMapper {

    List<SparkUpgradeAssessmentBean> selectListByPage(@Param("offset") Integer offset, @Param("length") Integer length, @Param("taskIds") List<Integer> taskIds, @Param("condition") String condition);
    
    int updateImportStatusByTaskId(@Param("taskId") Integer taskId, @Param("isImport") String isImport);
    
    List<DoubleRunErrorCodeAndSizeBo> selectErrorCodeForBigGraph();

    List<DoubleRunTaskStatusSizeBo> selectTaskStatusAndSizeIncr();

    List<DoubleRunErrorCodeAndSizeBo> selectAllErrorCodeFromCurrent();
    
    Integer selectSumErrorCodeCountFromCurrent(@Param("list") List<String> errorCodeList);
}
