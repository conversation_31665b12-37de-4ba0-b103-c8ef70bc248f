package com.jd.bdp.mapper.spark;

import com.jd.bdp.bean.SparkUpgradeTasKErrorRltBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: xiaowei79
 * description: Spark升级任务跟ErrorCode错误码关联关系
 * date: 2024-09-25 01:59:00
 */
public interface SparkUpgradeTaskErrorCodeRltMapper {

    int deleteRltsByTaskId(@Param("originTaskId") Integer originTaskId);
    
    int batchInsertTaskErrorRlts(@Param("list") List<SparkUpgradeTasKErrorRltBean> list);

    List<SparkUpgradeTasKErrorRltBean> selectErrorRltListByTaskId(@Param("originTaskId") Integer originTaskId);

    int batchInsertTaskErrorRltsHistory(@Param("list") List<SparkUpgradeTasKErrorRltBean> list);

}
