package com.jd.bdp.mapper.spark;

import com.jd.bdp.bean.SparkUpgradeEngineVersionBean;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * author: xiaowei79
 * description: buffalo任务spark升级版本记录
 * date: 2024-09-25 01:59:00
 */
public interface SparkUpgradeVersionMapper {
    List<SparkUpgradeEngineVersionBean> selectSparkUpgradeVersionList();
    
    int batchInsertSparkUpgradeVersions(@Param("list") List<SparkUpgradeEngineVersionBean> list);
}
