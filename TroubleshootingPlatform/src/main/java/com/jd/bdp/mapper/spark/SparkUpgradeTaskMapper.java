package com.jd.bdp.mapper.spark;

import com.jd.bdp.bean.SparkUpgradeTaskBean;
import com.jd.bdp.bean.bo.DoubleRunTaskStatusSizeBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: xiaowei79
 * description: buffalo任务spark升级版本记录
 * date: 2024-09-25 01:59:00
 */
public interface SparkUpgradeTaskMapper {
    List<SparkUpgradeTaskBean> selectList();

    List<SparkUpgradeTaskBean> selectListByPage(@Param("list") List<Integer> list, @Param("status") String status,  @Param("offset") Integer offset,  @Param("length") Integer length);

    List<SparkUpgradeTaskBean> selectListByStatus(@Param("status") String status, @Param("length") Integer length);

    Long selectTotalCount(@Param("list") List<Integer> list, @Param("status") String status);

    SparkUpgradeTaskBean selectOneByTaskId(String taskId);

    SparkUpgradeTaskBean selectUpgradedOneByTaskId(String taskId);

    int insertSparkUpgradeTask(SparkUpgradeTaskBean task);

    int insertSparkUpgradeTaskV2(SparkUpgradeTaskBean task);

    int insertSparkUpgradeTasks(@Param("list") List<SparkUpgradeTaskBean> tasks);

    int updateTask(SparkUpgradeTaskBean task);

    int updateTasks(@Param("list") List<SparkUpgradeTaskBean> tasks);
    
    int updateStatusByCondition(@Param("taskId") String taskId, @Param("preStatus") List<String> preStatus, @Param("targetStatus") String targetStatus);

    int updateStatusByList(@Param("taskIds") List<String> taskIds, @Param("preStatus") List<String> preStatus, @Param("targetStatus") String targetStatus);

    int updateDoubleRunTaskIdByOriginTaskId(@Param("doubleRunTaskId") String doubleRunTaskId, @Param("originTaskId") Integer originTaskId);

    List<SparkUpgradeTaskBean> selectTargetTaskList(@Param("selectTargetTaskSql")String selectTargetTaskSql);
    
}
