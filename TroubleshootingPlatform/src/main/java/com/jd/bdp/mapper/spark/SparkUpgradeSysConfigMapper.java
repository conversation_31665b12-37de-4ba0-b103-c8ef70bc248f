package com.jd.bdp.mapper.spark;


import com.jd.bdp.bean.domain.SparkUpgradeSysConfigBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author: xiaowei79
 * description: Spark升级任务摸底全景mapper类
 * date: 2024-09-25 01:59:00
 */
public interface SparkUpgradeSysConfigMapper {

    String getConfigValue(@Param("configKey") String configKey);

    List<String> getConfigValueLike(@Param("configKey") String configKey);
    
    int setConfigValue(@Param("configKey") String configKey, @Param("configValue") String configValue);
    
    List<SparkUpgradeSysConfigBean> selectAllConfigs();
    
    int addConfig(@Param("configKey") String configKey, @Param("configValue") String configValue, @Param("description") String description, @Param("erp")String erp);
}
