package com.jd.bdp.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.bdp.bean.enums.SparkUpgradeStatusEnum;
import com.jd.bdp.common.Buffalo4TaskManager;
import com.jd.bdp.filter.SSOFilterImpl;
import com.jd.bdp.spark.web.BuffaloTaskInfoModifyController;
import com.jd.common.web.LoginContext;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import static com.jd.bdp.scheduler.Scheduler.commonBean;
/**
 * author: xiaowei79
 * description: spark任务升级的工具类
 * date: 2024-09-27 17:20:00
 */
public class SparkUpgradeUtils {

    private static final Logger logger = Logger.getLogger(SparkUpgradeUtils.class.getName());

    /**
     * 升级版本： 从当前版本升级至3.4_preview
     * @param taskId 任务id
     * @throws Exception
     */
    public static boolean upgradeSparkUpgradeTask(String taskId) throws Exception {
        List<Integer> taskIdArray = new ArrayList<>();
        taskIdArray.add(Integer.parseInt(taskId));
        
        JSONObject taskResult = Buffalo4TaskManager.buffalo4GetTaskInfo(Long.parseLong(taskId));
        logger.info(String.format("==== upgradeSparkUpgradeTask, taskId: %s, taskResult: %s", taskId, MapperManager.writeValueAsString(taskResult)));
        Integer code = taskResult.getInteger("code");
        
        if(code == 0){
            // 获取所有的环节id
            JSONObject taskInfo = taskResult.getJSONObject("obj");
            String taskType = StringUtils.trimToEmpty(taskInfo.getString("taskType"));
            boolean isSinge = "single".equalsIgnoreCase(taskType);
            List<Integer> actions = new ArrayList<>();
            if(!isSinge){
                actions =  getActions(taskResult); // 工作流任务
            }
            logger.info(String.format("=== upgradeSparkUpgradeTask, taskId: %s, taskType: %s,  actionIds: %s", taskId, taskType, MapperManager.writeValueAsString(actions)));

            // 当前版本
            Map<Integer, String> currentVersionMap = BuffaloTaskInfoModifyController.getCurrentEngineVersion(Integer.parseInt(taskId), isSinge);
            logger.info(String.format("==== upgradeSparkUpgradeTask: taskId: %s, currentVersionMap: %s", taskId, MapperManager.writeValueAsString(currentVersionMap)));
            
            String toVersion = "3.4";

            // 升级版本
            String ticket = SSOFilterImpl.holder.get() != null && StringUtils.isNotBlank(SSOFilterImpl.holder.get().getTicket()) ? SSOFilterImpl.holder.get().getTicket() : commonBean.getBuffaloApiUserToken();
            logger.info(String.format("=== rollbackSparkUpgradeTask, taskId: %s, ticket: %s", taskId, ticket));
            JSONObject upgradeResult = Buffalo4TaskManager.batchModifyMarketInfo(isSinge ? taskIdArray: null, actions, "spark", toVersion, ticket, "buffalo4.bdp.jd.local");
            logger.info(String.format("=== upgradeSparkUpgradeTask: taskId: %s, upgrade result: %s", taskId, MapperManager.writeValueAsString(upgradeResult)));

            // 记流水表
            Integer updateCode = upgradeResult.getInteger("code");
            String pin = LoginContext.getLoginContext() != null ? LoginContext.getLoginContext().getPin() : "xiaowei12";
            if(updateCode == 0){
                logger.info(String.format("=== upgrade task success, 调用buffalo接口修改引擎版本成功, taskId: %s, message: %s", taskId, upgradeResult.get("message")));
                BuffaloTaskInfoModifyController.recordSparkUpgradeInfo(Integer.parseInt(taskId), actions, "spark", toVersion, isSinge, pin, currentVersionMap, SparkUpgradeStatusEnum.SUCCESSFUL);
                return true;
            }else{
                logger.info(String.format("=== upgrade task failed, 调用buffalo接口修改引擎版本失败, taskId: %s, reason: %s", taskId, upgradeResult.get("message")));
                BuffaloTaskInfoModifyController.recordSparkUpgradeInfo(Integer.parseInt(taskId), actions, "spark", toVersion, true, pin, currentVersionMap, SparkUpgradeStatusEnum.FAILED);
                return false;
            }
        }else{
            logger.info(String.format("=== upgradeSparkUpgradeTask, taskId: %s, 获取通过buffalo接口获取任务信息失败, 原因: %s", taskId, taskResult.getString("message")));
           return false; 
        }
    }

    public static  List<Integer> getActions(JSONObject taskResult) throws IOException {
        List<Integer> actionIds = new ArrayList<>();
        JSONArray list = taskResult.getJSONObject("obj").getJSONArray("actionList");
        for (int i = 0; i < list.size(); i++) {
            JSONObject actionObj = list.getJSONObject(i);
            // 仅处理`数据计算(py/sh)`和`数据计算(SQL)`类型的
            if ("pyscript".equalsIgnoreCase(actionObj.getString("actionType"))
                    || "datasql".equalsIgnoreCase(actionObj.getString("actionType"))) {
                Integer actionId = actionObj.getInteger("actionId");
                actionIds.add(actionId);
            }
        }
        
        return actionIds;
    }


    /**
     * 回滚版本：从当前版本回滚至origin版本, 当前版本必须为3.4才能回滚
     * @param taskId 任务id
     * @param originVersion
     * @throws Exception
     */
    public static boolean rollbackSparkUpgradeTask(String taskId, String originVersion) throws Exception {
        List<Integer> taskIdArray = new ArrayList<>();
        taskIdArray.add(Integer.parseInt(taskId));
        
        JSONObject taskResult = Buffalo4TaskManager.buffalo4GetTaskInfo(Long.parseLong(taskId));
        logger.info(String.format("==== rollbackSparkUpgradeTask, taskId: %s, taskResult: %s", taskId, MapperManager.writeValueAsString(taskResult)));
        Integer code = taskResult.getInteger("code");
        if(code == 0){
            // 获取所有的环节id
            JSONObject taskInfo = taskResult.getJSONObject("obj");
            String taskType = StringUtils.trimToEmpty(taskInfo.getString("taskType"));
            boolean isSinge = "single".equalsIgnoreCase(taskType);
            List<Integer> actions = new ArrayList<>();
            if(!isSinge){
                actions =  getActions(taskResult);  // 如果是工作流任务，需要获取所有的环节id
            }
            logger.info(String.format("=== rollbackSparkUpgradeTask, taskId: %s, taskType: %s,  actionIds: %s", taskId, taskType, MapperManager.writeValueAsString(actions)));
            

            // 当前版本
            Map<Integer, String> currentVersionMap = BuffaloTaskInfoModifyController.getCurrentEngineVersion(Integer.parseInt(taskId), isSinge);
            logger.info(String.format("=== rollbackSparkUpgradeTask, taskId: %s, currentVersion: %s", taskId, MapperManager.writeValueAsString(currentVersionMap)));
            
            
            // 当前版本必须为3.4、3.4_preview才能回滚
            String fromVersion = null;
            String toVersion = originVersion;
            String pin = LoginContext.getLoginContext() != null ? LoginContext.getLoginContext().getPin() : "xiaowei12";
            if(isSinge){
                fromVersion = currentVersionMap.get(Integer.parseInt(taskId));
                if(fromVersion == null || !fromVersion.contains("3.4")){  // 如果不是当前版本不是3.4，则直接退出，无需回滚
                    logger.info(String.format("=== rollback task failed, taskId: %s, currentVersion: %s, reason: 当前任务spark版本非3.4，不能回滚, ", taskId, fromVersion));
                    BuffaloTaskInfoModifyController.recordSparkUpgradeInfo(Integer.parseInt(taskId), new ArrayList<>(), "spark", toVersion, true, pin, currentVersionMap, SparkUpgradeStatusEnum.FAILED);
                    return false;
                }
            }else{
                for(Integer actionId: actions){
                    fromVersion = currentVersionMap.get(actionId);
                    if(fromVersion == null || !fromVersion.contains("3.4")){
                        logger.info(String.format("=== rollback task failed, taskId: %s, actionId: %s, currentVersion: %s, reason: 当前任务spark版本非3.4，不能回滚, ", taskId, actionId, fromVersion));
                        BuffaloTaskInfoModifyController.recordSparkUpgradeInfo(Integer.parseInt(taskId), new ArrayList<>(), "spark", toVersion, true, pin, currentVersionMap, SparkUpgradeStatusEnum.FAILED);
                        return false;
                    }
                }
            }

            // 回滚版本
            String ticket = SSOFilterImpl.holder.get() != null && StringUtils.isNotBlank(SSOFilterImpl.holder.get().getTicket()) ? SSOFilterImpl.holder.get().getTicket() : commonBean.getBuffaloApiUserToken();
            logger.info(String.format("=== rollbackSparkUpgradeTask, taskId: %s, ticket: %s", taskId, ticket));
            JSONObject rollbackResult = Buffalo4TaskManager.batchModifyMarketInfo(isSinge ? taskIdArray: null, actions, "spark", toVersion, ticket, "buffalo4.bdp.jd.local");
            logger.info(String.format("=== rollbackSparkUpgradeTask: taskId: %s, rollback result: %s", taskId, MapperManager.writeValueAsString(rollbackResult)));

            // 记流水表
            Integer rollbackCode = rollbackResult.getInteger("code");
            if(rollbackCode == 0){
                logger.info(String.format("=== rollback task success, 调用buffalo接口修改引擎版本成功, taskId: %s, message: %s", taskId, rollbackResult.get("message")));
                BuffaloTaskInfoModifyController.recordSparkUpgradeInfo(Integer.parseInt(taskId), actions, "spark", toVersion, isSinge, pin, currentVersionMap, SparkUpgradeStatusEnum.SUCCESSFUL);
                return true;
            }else{
                logger.info(String.format("=== rollback task failed, 调用buffalo接口修改引擎版本失败, taskId: %s, reason: %s", taskId, rollbackResult.get("message")));
                BuffaloTaskInfoModifyController.recordSparkUpgradeInfo(Integer.parseInt(taskId), actions, "spark", toVersion, isSinge, pin, currentVersionMap, SparkUpgradeStatusEnum.FAILED);
                return false;
            }
        }else{
            logger.info(String.format("=== rollbackSparkUpgradeTask, taskId: %s, 获取通过buffalo接口获取任务信息失败, 原因: %s", taskId, taskResult.getString("message")));
            return false;
        }
    }


    public static void main(String[] args) throws Exception {
         boolean b = upgradeSparkUpgradeTask("1383912");
//        boolean b = rollbackSparkUpgradeTask("1587706", "default");
        System.out.println("====== b: "+ b);
    }
}
