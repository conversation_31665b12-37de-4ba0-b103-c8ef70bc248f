package com.jd.bdp.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Package: com.bdpops.newt.common.json
 * User: xiaowei79
 * Email: <EMAIL>
 * Date: 2021/9/6
 * Time: 4:31 下午
 * Description:
 */
public class MapperManager {
    private static final Logger logger = LoggerFactory.getLogger(MapperManager.class);

    private MapperManager() {
    }

    enum MapperManagerEnum {
        INSTANCE;
        private ObjectMapper objectMapper;

        private MapperManagerEnum() {
            objectMapper = new ObjectMapper();
            //在反序列化时忽略在 json 中存在但 Java 对象不存在的属性
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                    false);
        }

        public ObjectMapper getInstnce() {
            return objectMapper;
        }
    }

    public static final ObjectMapper getInstance() {
        return MapperManagerEnum.INSTANCE.getInstnce();
    }

    public static final String writeValueAsString(Object object) {
        try {
            return getInstance().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.error("writeValueAsString failed", e);
        }
        return null;
    }

    public static final <T> T readValue(String content, Class<T> valueType) {
        try {
            return getInstance().readValue(content, valueType);
        } catch (Exception e) {
            logger.error("readValue failed", e);
        }
        return null;
    }
}
