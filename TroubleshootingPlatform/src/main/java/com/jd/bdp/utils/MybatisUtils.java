//package com.jd.bdp.utils;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.jd.bdp.bean.*;
//import com.jd.bdp.bean.bo.DoubleRunTaskStaticInfoBo;
//import com.jd.bdp.bean.bo.DoubleRunTestBo;
//import com.jd.bdp.bean.enums.SparkUpgradeExplainEnum;
//import com.jd.bdp.bean.enums.SparkUpgradeOpTypeEnum;
//import com.jd.bdp.bean.enums.SparkUpgradeTaskStatusEnum;
//import com.jd.bdp.mapper.spark.*;
//import org.apache.ibatis.io.Resources;
//import org.apache.ibatis.session.SqlSession;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.apache.ibatis.session.SqlSessionFactoryBuilder;
//import org.apache.ibatis.session.defaults.DefaultSqlSession;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.logging.Level;
//import java.util.logging.Logger;
//
///**
// * author: xiaowei79
// * description: mybatis工具类
// * date: 2024-09-25 02:12:00
// */
//public class MybatisUtils {
//    private static final Logger logger = Logger.getLogger(MybatisUtils.class.getName());
//
//    private static ThreadLocal<Map<String, SqlSession>> sessionThreadLocal = new ThreadLocal<>();
//
//    private static volatile Map<String, SqlSessionFactory> allFactories;
//
//    static {
//        try {
//            if (allFactories == null) {
//                synchronized (MybatisUtils.class) {
//                    if (allFactories == null) {
//                        allFactories = new HashMap<>();
//                        InputStream in = Resources.getResourceAsStream("mybatis-config.xml");
//                        SqlSessionFactory defaultFactory = new SqlSessionFactoryBuilder().build(in, "default");
//                        in = Resources.getResourceAsStream("mybatis-config.xml");
//                        SqlSessionFactory buffaloFactory = new SqlSessionFactoryBuilder().build(in, "buffalo");
//                        allFactories.put("default", defaultFactory);
//                        allFactories.put("buffalo", buffaloFactory);
//                        
//                        in = Resources.getResourceAsStream("mybatis-config.xml");
//                        SqlSessionFactory localFactory = new SqlSessionFactoryBuilder().build(in, "jmr_dev");
//                        in = Resources.getResourceAsStream("mybatis-config.xml");
//                        SqlSessionFactory suqianFactory = new SqlSessionFactoryBuilder().build(in, "suqian");
//                        allFactories.put("jmr_dev", localFactory);
//                        allFactories.put("suqian", suqianFactory);
//                    }
//                }
//            }
//        } catch (IOException e) {
//            logger.log(Level.INFO, "初始化mysql数据库连接异常", e);
//            throw new RuntimeException(e);
//        }
//    }
//
//
//    public static synchronized <T> T getMapper(Class<T> mapperClass) {
//        try {
//            if (sessionThreadLocal.get() == null || sessionThreadLocal.get().get("default") == null) {
//                Map<String, SqlSession> sessionMap = sessionThreadLocal.get() == null ? new HashMap<>() : sessionThreadLocal.get();
//                SqlSession session = allFactories.get("default").openSession(true);
//                sessionMap.put("default", session);
//                sessionThreadLocal.set(sessionMap);
//                return session.getMapper(mapperClass);
//            } else {
//                SqlSession session = sessionThreadLocal.get().get("default");
//                return session.getMapper(mapperClass);
//            }
//        } catch (Exception e) {
//            logger.log(Level.INFO, "创建mysql会话异常", e);
//            throw new RuntimeException(e);
//        }
//    }
//
//    public static synchronized <T> T getMapper(Class<T> mapperClass, String dataSource) {
//        try {
//            if (sessionThreadLocal.get() == null || sessionThreadLocal.get().get(dataSource) == null) {
//                Map<String, SqlSession> sessionMap = sessionThreadLocal.get() == null ? new HashMap<>() : sessionThreadLocal.get();
//                SqlSession session = allFactories.get(dataSource).openSession(true);
//                sessionMap.put(dataSource, session);
//
//                DefaultSqlSession defaultSqlSession = (DefaultSqlSession) session;
//                defaultSqlSession.close();
//                sessionThreadLocal.set(sessionMap);
//                return session.getMapper(mapperClass);
//            } else {
//                SqlSession session = sessionThreadLocal.get().get(dataSource);
//                return session.getMapper(mapperClass);
//            }
//        } catch (Exception e) {
//            logger.log(Level.INFO, "创建mysql会话异常", e);
//            throw new RuntimeException(e);
//        }
//    }
//
//    public static synchronized <T> T getMapperOneSession(Class<T> mapperClass, String dataSource) {
//        try (SqlSession session = allFactories.get(dataSource).openSession(true)) {
//            return session.getMapper(mapperClass);
//        } catch (Exception e) {
//            logger.log(Level.INFO, "创建mysql会话异常", e);
//            throw new RuntimeException(e);
//        }
//    }
//
//
//    public static void main(String[] args) throws Exception {
//        test14();
//    }
//
//    public static void test01() throws JsonProcessingException {
//        ObjectMapper objectMapper = new ObjectMapper();
//        SparkUpgradeVersionMapper mapper = MybatisUtils.getMapper(SparkUpgradeVersionMapper.class);
//        List<SparkUpgradeEngineVersionBean> beanList = new ArrayList<>();
//
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        SparkUpgradeEngineVersionBean bean_1 = new SparkUpgradeEngineVersionBean();
//        bean_1.setTaskId(1);
//        bean_1.setActionId(111);
//        bean_1.setEngine("spark");
//        bean_1.setFromVersion("3.4");
//        bean_1.setToVersion("default");
//        bean_1.setOpType(SparkUpgradeOpTypeEnum.calc("3.4", "default").getCode());
//        bean_1.setStatus("SUCCESSFUL");
//        bean_1.setCreateTime(sdf.format(new Date()));
//        bean_1.setCreator("xiaowei12");
//        beanList.add(bean_1);
//
//
//        SparkUpgradeEngineVersionBean bean_2 = new SparkUpgradeEngineVersionBean();
//        bean_2.setTaskId(1);
//        bean_2.setActionId(112);
//        bean_2.setEngine("hive");
//        bean_2.setFromVersion("3.0");
//        bean_2.setToVersion("3.4");
//        bean_2.setOpType(SparkUpgradeOpTypeEnum.calc("3.0", "3.4").getCode());
//        bean_2.setStatus("SUCCESSFUL");
//        bean_2.setCreateTime(sdf.format(new Date()));
//        bean_2.setCreator("xiaowei12");
//        beanList.add(bean_2);
//
//        int size = mapper.batchInsertSparkUpgradeVersions(beanList);
//        if (size > 0) {
//            System.out.println("升级记录保存成功");
//        }
//
//        List<SparkUpgradeEngineVersionBean> list = mapper.selectSparkUpgradeVersionList();
//        System.out.println(objectMapper.writeValueAsString(list));
//    }
//
//    private static void test02() {
//        SparkUpgradeExplainMapper mapper = MybatisUtils.getMapper(SparkUpgradeExplainMapper.class);
//        List<SparkUpgradeExplainBean> explainBeanList = mapper.selectListByTaskId(1111);
//        for (SparkUpgradeExplainBean bean : explainBeanList) {
//            System.out.println(bean);
//        }
//
//        System.out.println("======= selectListByTaskIdAndStatus ========");
//        List<SparkUpgradeExplainBean> list = mapper.selectListByTaskIdAndStatus(2222, Arrays.asList(SparkUpgradeExplainEnum.UN_EXE.getCode(), SparkUpgradeExplainEnum.FAILED.getCode()));
//        for (SparkUpgradeExplainBean bean : list) {
//            System.out.println(bean);
//        }
//    }
//
//    private static void test03() {
//        SparkUpgradeTaskMapper mapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class);
//        int i = mapper.updateStatusByCondition("1120", Arrays.asList(SparkUpgradeTaskStatusEnum.ROLLBACKED.getCode()), SparkUpgradeTaskStatusEnum.EXPLAINING.getCode());
//        System.out.println("=== updateStatusByCondition: " + i);
//    }
//
//    private static void test04() {
//        SparkUpgradeErrorCodeMapper buffaloMapper = MybatisUtils.getMapper(SparkUpgradeErrorCodeMapper.class, "buffalo");
//        List<SparkUpgradeErrorCodeBean> list1 = buffaloMapper.selectAll();
//        for (SparkUpgradeErrorCodeBean bean : list1) {
//            System.out.println(bean);
//        }
//
//        System.out.println("================= buffalo ==============");
//
//        SparkUpgradeExplainMapper explainMapper = MybatisUtils.getMapper(SparkUpgradeExplainMapper.class);
//        List<SparkUpgradeExplainBean> list2 = explainMapper.selectListByTaskId(1111);
//        for (SparkUpgradeExplainBean bean : list2) {
//            System.out.println(bean);
//        }
//    }
//
//    private static void test05() {
//        for (int i = 0; i < 100; i++) {
//            Thread t1 = new Thread(() -> {
//                SparkUpgradeErrorCodeMapper buffaloMapper = MybatisUtils.getMapper(SparkUpgradeErrorCodeMapper.class, "buffalo");
//                List<SparkUpgradeErrorCodeBean> list1 = buffaloMapper.selectAll();
//                for (SparkUpgradeErrorCodeBean bean : list1) {
//                    System.out.println("=== suqian " + Thread.currentThread().getName() + " " + bean);
//                }
//            });
//
//            t1.start();
//        }
//
//
//        for (int i = 0; i < 100; i++) {
//            Thread t2 = new Thread(() -> {
//                SparkUpgradeExplainMapper explainMapper = MybatisUtils.getMapper(SparkUpgradeExplainMapper.class);
//                List<SparkUpgradeExplainBean> list2 = explainMapper.selectListByTaskId(1111);
//                for (SparkUpgradeExplainBean bean : list2) {
//                    System.out.println("=== local " + Thread.currentThread().getName() + " " + bean);
//                }
//            });
//
//            t2.start();
//        }
//    }
//
//    private static void test06() {
//        SparkUpgradeAssessmentMapper mapper = MybatisUtils.getMapper(SparkUpgradeAssessmentMapper.class, "jmr_dev");
//        List<SparkUpgradeAssessmentBean> list = mapper.selectListByPage(0, 10, Arrays.asList(11757), null);
//        for (SparkUpgradeAssessmentBean bean : list) {
//            System.out.println(bean);
//        }
//        System.out.println("======== SparkUpgradeAssessmentMapper 2 =========");
//
//        List<SparkUpgradeAssessmentBean> list2 = mapper.selectListByPage(0, 10, null, null);
//        for (SparkUpgradeAssessmentBean bean : list2) {
//            System.out.println(bean);
//        }
//    }
//
//    private static void test07() {
//        SparkUpgradeAssessmentMapper mapper = MybatisUtils.getMapper(SparkUpgradeAssessmentMapper.class, "jmr_dev");
//        List<SparkUpgradeAssessmentBean> list = mapper.selectListByPage(0, 10, null, "is_double_run_task_created = 'Y' and inst_all_success = 0");
//        System.out.println(String.format("=== list size: %s", list.size()));
//        for (SparkUpgradeAssessmentBean bean : list) {
//            System.out.println(bean);
//        }
//    }
//
//    private static void test09() {
//        SparkUpgradeSysConfigMapper mapper = MybatisUtils.getMapper(SparkUpgradeSysConfigMapper.class, "jmr_dev");
//        String all_schedule_enable = mapper.getConfigValue("all_schedule_enable");
//        System.out.println(String.format("== all_schedule_enable: %s", all_schedule_enable));
//
//        int i = mapper.setConfigValue("all_schedule_enable", "true");
//        System.out.println(String.format("=== affected size: %s", i));
//
//        String all_schedule_enable2 = mapper.getConfigValue("all_schedule_enable");
//        System.out.println(String.format("== all_schedule_enable: %s", all_schedule_enable2));
//    }
//
//    private static void test10() {
//        SparkUpgradeTaskMapper mapper = MybatisUtils.getMapper(SparkUpgradeTaskMapper.class, "jmr_dev");
//        List<SparkUpgradeTaskBean> list = mapper.selectList();
//        for (SparkUpgradeTaskBean bean : list) {
//            System.out.println(bean);
//        }
//    }
//
//    private static void test11() {
//        SparkUpgradeAssessmentMapper mapper = MybatisUtils.getMapper(SparkUpgradeAssessmentMapper.class, "jmr_dev");
//        DoubleRunTaskStaticInfoBo staticInfoBo = mapper.selectTaskStaticInfoByTaskId(11757);
//        System.out.println(staticInfoBo);
//    }
//
//    private static void test12() {
//        SparkUpgradeSysConfigMapper mapper = MybatisUtils.getMapper(SparkUpgradeSysConfigMapper.class, "jmr_dev");
//        List<String> list = mapper.getConfigValueLike("double_run");
//        for (String value : list) {
//            System.out.println(value);
//        }
//    }
//
//
//    private static void test13() {
//        for (int i = 0; i < 20; i++) {
//            Thread t2 = new Thread(() -> {
//                SparkUpgradeExplainMapper explainMapper = MybatisUtils.getMapper(SparkUpgradeExplainMapper.class, "jmr_dev");
//                List<SparkUpgradeExplainBean> list2 = explainMapper.selectListByTaskId(1111);
//                for (SparkUpgradeExplainBean bean : list2) {
//                    System.out.println("=== local " + Thread.currentThread().getName() + " " + bean);
//                }
//
//                try {
//                    System.out.println("==== sleep =====");
//                    Thread.sleep(10 * 60 * 1000);
//                    System.out.println("==== sleep end =====");
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//
//                SparkUpgradeTestMapper testMapper = MybatisUtils.getMapper(SparkUpgradeTestMapper.class, "suqian");
//                List<DoubleRunTestBo> list3 = testMapper.selectDoubleRunTestList();
//                for (DoubleRunTestBo bean : list3) {
//                    System.out.println("=== suqian " + Thread.currentThread().getName() + " " + bean);
//                }
//            });
//
//            t2.start();
//        }
//    }
//
//
//    private static void test14() {
//        SparkUpgradeExplainMapper explainMapper = MybatisUtils.getMapper(SparkUpgradeExplainMapper.class, "jmr_dev");
//        List<SparkUpgradeExplainBean> list2 = explainMapper.selectListByTaskId(1111);
//        for (SparkUpgradeExplainBean bean : list2) {
//            System.out.println("=== local " + Thread.currentThread().getName() + " " + bean);
//        }
//
//        try {
//            System.out.println("==== sleep =====");
//            Thread.sleep(10 * 60 * 1000);
//            System.out.println("==== sleep end =====");
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        SparkUpgradeTestMapper testMapper = MybatisUtils.getMapper(SparkUpgradeTestMapper.class, "suqian");
//        List<DoubleRunTestBo> list3 = testMapper.selectDoubleRunTestList();
//        for (DoubleRunTestBo bean : list3) {
//            System.out.println("=== suqian " + Thread.currentThread().getName() + " " + bean);
//        }
//    }
//
//
//}
//
