<%@ page import="static com.jd.bdp.bean.Constants.TASK_TYPE_BUFFALO_HIVETASK" %>
<%@ page import="static com.jd.bdp.bean.Constants.TASK_TYPE_BUFFALO_PY_SPARK" %>
<%@ page import="static com.jd.bdp.bean.Constants.*" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Spark统一工单平台</title>
</head>
<body>
<jsp:include page="/include/jqueryui.html"/>
<style>
    fieldset {
        border: 0;
    }
    label {
        display: inline-block; width: 8em;
    }
    .tag {
        width: 4em;
    }
    fieldset div {
        margin-bottom: 2em;
    }
    fieldset .help {
        display: inline-block;
    }
    .ui-tooltip {
        width: 210px;
    }
    .ui-selectmenu-button.ui-button {
        width: 4em;
    }
</style>

<form method="get" action="metrics" accept-charset="utf-8">
    <fieldset>
        <div>
            <label for="app">ApplicationId:</label>
            <input id="app" name="ApplicationId" title="" style="line-height: 25px; width: 500px;">
        </div>
        <div>
            <input class="ui-button ui-widget ui-corner-all" style="width:500px;" type="submit" value="Submit">
        </div>
    </fieldset>
</form>
<script>
    var tooltips = $("[title]").tooltip({
        position: {
            my: "left top",
            at: "right+5 top-5",
            collision: "none"
        }
    });
</script>
</body>
</html>
