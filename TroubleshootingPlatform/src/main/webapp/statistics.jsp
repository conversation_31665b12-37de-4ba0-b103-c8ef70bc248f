<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="org.apache.http.impl.client.HttpClients" %>
<%@ page import="org.apache.http.impl.client.CloseableHttpClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="com.alibaba.fastjson.JSONArray" %>
<%@ page import="com.jd.bdp.common.JimDBUtils" %>
<%@ page import="com.jd.bdp.common.CalendarUtils" %>
<%@ page import="com.jd.bdp.common.SDFThreadLocal" %>
<%@ page import="com.jd.bdp.spark.web.DateTimeUtils" %>
<%@ page import="com.jd.bdp.spark.web.SimpleHttpClient" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.Collections" %>
<%@ page import="java.util.Comparator" %>
<%@ page import="java.util.List" %>
<%@ page import="java.util.Map" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>Statistics</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <%
        for(int i = 0 ; i > -3; i--) {
            String jimdbKey = "uv." + SDFThreadLocal.getyyyyMM().format(CalendarUtils.getYYYYMMWithSpecifiedAmount(i));
            out.println(jimdbKey + ": " + JimDBUtils.getUV(jimdbKey) + "<br/>");
        }
        %>
        <table data-role="table" id="table-column-toggle" data-mode="columntoggle" class="ui-responsive table-stroke">
        <thead>
            <tr>
            <th data-priority="2">Pin</th>
            <th data-priority="2">Views</th>
            </tr>
        </thead>
        <tbody>
            <%
            for(Map.Entry<String, String> entry: JimDBUtils.getUVDetails().entrySet()){
            %>
            <tr>
            <td><%=entry.getKey()%></td>
            <td><%=entry.getValue()%></td>
            </tr>
            <%
            }
            %>
        </tbody>
        </table>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>