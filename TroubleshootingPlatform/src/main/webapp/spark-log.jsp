<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.spark.web.BDPUtils" %>
<%@ page import="java.util.Map" %>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
        String site = request.getParameter("site");
        String logId = request.getParameter("logId");
        String logType = request.getParameter("logType");
        Map<String, String> logData = BDPUtils.analysisLogByLogId(null, null, logId, logType, site, true);
        BDPUtils.syncToJimDB(null, logId, logData);
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>数据可观测-任务诊断</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div>
    <div role="main" class="ui-content jqm-content">
        <table data-role="table" id="movie-table" data-mode="reflow" class="ui-body-d ui-shadow table-stripe ui-responsive">
            <thead>
            <tr class="ui-bar-d">
                <th>日志id</th>
                <th>
                    <%=logId%>
                </th>
            </tr>
            </thead>
            <tbody>
            <%
                for(Map.Entry<String, String> entry: logData.entrySet()) {
            %>
            <tr>
                <td><%=entry.getKey()%></td>
                <td><%=entry.getValue()%></td>
            </tr>
            <%
                }
            %>
            </tbody>
        </table>
    </div>

</div>
</body>
</html>
