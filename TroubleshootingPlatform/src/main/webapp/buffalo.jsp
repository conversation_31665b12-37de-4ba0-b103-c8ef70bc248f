<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    String host = "";
    String appId = request.getParameter("appId");
    String startTime = request.getParameter("startTime");
    String radio_cluster = request.getParameter("radio_cluster");
    String sortField = request.getParameter("sortField");
    String sortType = request.getParameter("sortType");
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>数据可观测-任务诊断</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <form action="/buffaloLogs">
          <fieldset data-role="controlgroup" data-type="horizontal">
            <legend>站点:</legend>
            <input type="radio" name="site" id="radio-choice-h-2a" value="dp.jd.com" checked="checked">
            <label for="radio-choice-h-2a">主站</label>
            <input type="radio" name="site" id="radio-choice-h-2b" value="bdp.jd.co.th">
            <label for="radio-choice-h-2b">泰国站</label>
            <input type="radio" name="site" id="radio-choice-h-2c" value="bdp.jd.id">
            <label for="radio-choice-h-2c">印尼站</label>
          </fieldset>
          <input name="buffaloVersion" type="hidden" value="BUFFALO4" />
          <label for="number-pattern">Buffalo任务ID:</label>
          <input type="number" name="taskId" pattern="[0-9]*" id="number-pattern" value="">
          <input type="submit" value="查询">
        </form>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>