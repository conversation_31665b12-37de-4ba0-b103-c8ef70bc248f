<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Spark Task 分析测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Spark Task 分析功能测试</h1>
    
    <div class="test-section">
        <h3>功能说明</h3>
        <p>此页面用于测试 Spark Application Task 分析功能：</p>
        <ul>
            <li>输入 Spark Application API 地址</li>
            <li>分析应用的所有 task 数量（忽略 SKIPPED 状态的 stage）</li>
            <li>统计推测执行的 task 数量</li>
            <li>统计重试的 task 数量</li>
            <li>计算相关比例</li>
            <li><strong>显示推测执行 task 的详细信息（Task ID、Stage ID、Attempt ID、Index、Status、Duration）</strong></li>
            <li><strong>显示重试 task 的详细信息（Task ID、Stage ID、Attempt ID、Attempt、Index、Status、Duration）</strong></li>
            <li><strong>支持大量 task 的完整分析（通过分页查询获取所有 task 信息，每页 100 个）</strong></li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>访问主页面</h3>
        <p>点击下面的按钮访问 Spark Task 分析主页面：</p>
        <button class="test-button" onclick="window.open('sparkTaskAnalysis.jsp', '_blank')">打开分析页面</button>
    </div>
    
    <div class="test-section">
        <h3>API 测试</h3>
        <p>测试 Servlet API 是否正常工作：</p>
        <button class="test-button" onclick="testAPI()">测试 API</button>
        <div id="apiResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>使用说明</h3>
        <ol>
            <li>确保 Spark History Server 正在运行</li>
            <li>获取 Spark Application 的 API URL</li>
            <li>在分析页面输入 URL 并点击"开始分析"</li>
            <li>查看分析结果</li>
        </ol>
        
        <h4>URL 格式示例：</h4>
        <code>http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855</code>
    </div>

    <script>
        function testAPI() {
            var resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试 API...';
            
            // 发送一个空的请求来测试 API 是否响应
            fetch('sparkTaskAnalysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'historyUrl='
            })
            .then(response => response.json())
            .then(data => {
                if (data.success === false && data.message === 'Application URL 不能为空') {
                    resultDiv.innerHTML = '<strong style="color: green;">✓ API 正常工作</strong><br>错误处理正确：' + data.message;
                } else {
                    resultDiv.innerHTML = '<strong style="color: orange;">⚠ API 响应异常</strong><br>响应：' + JSON.stringify(data);
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<strong style="color: red;">✗ API 测试失败</strong><br>错误：' + error.message;
            });
        }
    </script>
</body>
</html> 