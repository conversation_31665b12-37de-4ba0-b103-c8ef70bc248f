<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.bean.KongMingRecordBean" %>
<%@ page import="com.jd.common.util.StringUtils" %>
<%@ page import="java.util.Map" %>
<%@ page import="com.jd.bdp.spark.web.KongmingService" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <style>
        .controlgroup-textinput{
            padding-top:.22em;
            padding-bottom:.22em;
        }
        .padding_em {
            padding: 0.2em 1em;
        }
    </style>
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>Spark 3.x 复制表</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <div data-role="navbar">
            <ul>
                <li><a href="spark3-summary.jsp">Summary</a></li>
                <li><a href="spark3-tasks.jsp" class="ui-btn-active">Spark3 Tasks</a></li>
                <li><a href="spark3-details.jsp">Spark3 Logs</a></li>
                <li><a href="spark3-vs.jsp">Spark3 vs Spark2</a></li>
            </ul>
        </div><!-- /navbar -->
        <%
        String pageNo = StringUtils.trimToNull(request.getParameter("pageNo"));
        String pageSize = StringUtils.trimToNull(request.getParameter("pageSize"));
        String task_id = StringUtils.trimToNull(request.getParameter("task_id"));
        String fork_task_id = StringUtils.trimToNull(request.getParameter("fork_task_id"));
        String status_code = StringUtils.trimToNull(request.getParameter("status_code"));
        String status = StringUtils.trimToNull(request.getParameter("status"));
        KongMingRecordBean kongMingRecordBean = KongmingService.queryBuffaloTask(
            pageNo, pageSize, task_id, fork_task_id, status_code, status);
        %>
        <form action="/spark3-tasks.jsp" method="post">
        <div data-role="controlgroup" data-type="horizontal">
            <a href="spark3-tasks.jsp?pageNo=<%=kongMingRecordBean.getPreviousPageNo()%>" class="ui-shadow ui-btn ui-corner-all ui-icon-arrow-l ui-btn-icon-left">上一页</a>
            <a href="spark3-tasks.jsp?pageNo=<%=kongMingRecordBean.getNextPageNo()%>" class="ui-shadow ui-btn ui-corner-all ui-icon-arrow-r ui-btn-icon-right">下一页</a>
            <input type="text" name="task_id" value="<%=StringUtils.trimToEmpty(task_id)%>" id="search-control-group-1" placeholder="task_id" data-wrapper-class="controlgroup-textinput ui-btn">
            <input type="text" name="fork_task_id" value="<%=StringUtils.trimToEmpty(fork_task_id)%>" id="search-control-group-2" placeholder="fork_task_id" data-wrapper-class="controlgroup-textinput ui-btn">
            <label for="select-v-1e">Select</label>
            <select name="status_code" id="select-v-1e">
            	<option value="">status_code</option>
            	<option value="0" <%="0".equals(status_code) ? "selected=\"selected\"" : ""%>>0</option>
            	<option value="1" <%="1".equals(status_code) ? "selected=\"selected\"" : ""%>>1</option>
            	<option value="2" <%="2".equals(status_code) ? "selected=\"selected\"" : ""%>>2</option>
            	<option value="4" <%="4".equals(status_code) ? "selected=\"selected\"" : ""%>>4</option>
            </select>
            <input type="text" name="status" value="<%=StringUtils.trimToEmpty(status)%>" id="search-control-group-4" placeholder="status" data-wrapper-class="controlgroup-textinput ui-btn">
            <button>Submit</button>
        </div>
        </form>
        <table data-role="table" id="movie-table" data-mode="columntoggle" class="ui-responsive table-stripe">
          <thead>
            <tr class="ui-bar-d">
                <th></th>
                <th>buffalo_version</th>
                <th>task_id</th>
                <th>managers</th>
                <th>status_code</th>
                <th>status</th>
                <th>fork_task_id</th>
                <th data-priority="5">OriginalSQL</th>
                <th data-priority="5">create_time</th>
                <th data-priority="5">update_time</th>
                <th data-priority="5">checkData</th>
                <th data-priority="5">message</th>
                <th>Operate</th>
            </tr>
          </thead>
          <tbody>
            <%
            int i = 1;
            for (Map<String, Object> row: kongMingRecordBean.getRows()) {
            %>
            <tr>
                <td><%=i++%></td>
                <td><%=row.get("buffalo_version")%></td>
                <td><a target="_blank" href="/buffaloLogs?site=dp.jd.com&buffaloVersion=<%=row.get("buffalo_version")%>&taskId=<%=row.get("task_id")%>"><%=row.get("task_id")%></a></td>
                <td><%=row.get("managers")%></td>
                <td><%=row.get("status_code")%></td>
                <td><%=row.get("status")%></td>
                <td><a target="_blank" href="/buffaloLogs?site=dp.jd.com&buffaloVersion=<%=row.get("buffalo_version")%>&taskId=<%=row.get("fork_task_id")%>"><%=row.get("fork_task_id")%></a></td>
                <td><%=StringUtils.abbreviate((String)row.get("originalSQLCommand"), 25)%></td>
                <td><%=row.get("create_time")%></td>
                <td><%=row.get("update_time")%></td>
                <td><%=row.get("checkData")%></td>
                <td><%=row.get("message")%></td>
                <td>
                    <fieldset data-role="controlgroup" data-type="horizontal" data-mini="true" style="margin: 0;">
                        <a target="_blank" href="/sqlController?type=deleteTask&taskId=<%=row.get("task_id")%>" class="ui-shadow ui-btn ui-corner-all padding_em">Delete</a>
                    </fieldset>
                </td>
            </tr>
            <%
            }
            %>
          </tbody>
        </table>
	</div>
</div>
</body>
</html>