<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.bean.XbpForm" %>
<%@ page import="com.jd.bdp.bean.KongMingRecordBean" %>
<%@ page import="com.jd.bdp.filter.SSOFilterImpl" %>
<%@ page import="com.jd.bdp.spark.web.XBPController" %>
<%@ page import="com.jd.common.util.StringUtils" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.DecimalFormat" %>

<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <style>
        .controlgroup-textinput{
            padding-top:.22em;
            padding-bottom:.22em;
        }
        .padding_em {
            padding: 0.2em 1em;
        }
    </style>
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>XBP流程</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <%
            String pageNo = StringUtils.defaultIfEmpty(request.getParameter("pageNo"), "1");
            String pageSize = StringUtils.defaultIfEmpty(request.getParameter("pageSize"), "20");
            String paramProcessID = StringUtils.trimToEmpty(request.getParameter("paramProcessID"));
            String flowType = StringUtils.trimToEmpty(request.getParameter("paramFlowType"));
            String flowStatus = StringUtils.trimToEmpty(request.getParameter("flowStatus"));
            String inspectionItem = StringUtils.trimToEmpty(request.getParameter("inspectionItem"));
            String taskIds = StringUtils.trimToEmpty(request.getParameter("taskIds"));
            String ticketIds = StringUtils.trimToEmpty(request.getParameter("ticketIds"));
            String startApprove = StringUtils.trimToEmpty(request.getParameter("startApprove"));
            String startCreate = StringUtils.trimToEmpty(request.getParameter("startCreate"));
            String endCreate = StringUtils.trimToEmpty(request.getParameter("endCreate"));
            String username = StringUtils.trimToEmpty(request.getParameter("username"));
            if (request.getParameter("username") == null) {
                username = SSOFilterImpl.getPin();
            }
            KongMingRecordBean kongMingRecordBean = XBPController.getXbpList(pageNo, pageSize, paramProcessID,
                inspectionItem, flowStatus, taskIds, startApprove, startCreate, endCreate, username, flowType,
                ticketIds);
            List<Map<String, Object>> inspectionItems = XBPController.getInspectionItem();
            List<Map<String, Object>> flowStatusItems = XBPController.getFlowStatus();
            List<Map<String, Object>> flowTypeItems = XBPController.getFlowType();
        %>
        <form id="xbp_form" action="/xbp-list.jsp" method="post">
            <div data-role="controlgroup" data-type="horizontal">
                <a href="xbp-list.jsp?pageNo=<%=kongMingRecordBean.getPreviousPageNo()%>&paramProcessID=<%=paramProcessID%>&pageSize=<%=pageSize%>&inspectionItem=<%=inspectionItem%>&flowStatus=<%=flowStatus%>&startCreate=<%=startCreate%>&startApprove=<%=startApprove%>&username=<%=StringUtils.trimToEmpty(username)%>" class="ui-shadow ui-btn ui-corner-all ui-icon-arrow-l ui-btn-icon-left">上一页</a>
                <a href="xbp-list.jsp?pageNo=<%=kongMingRecordBean.getNextPageNo()%>&paramProcessID=<%=paramProcessID%>&pageSize=<%=pageSize%>&inspectionItem=<%=inspectionItem%>&flowStatus=<%=flowStatus%>&startCreate=<%=startCreate%>&startApprove=<%=startApprove%>&username=<%=StringUtils.trimToEmpty(username)%>" class="ui-shadow ui-btn ui-corner-all ui-icon-arrow-r ui-btn-icon-right">下一页</a>
                <input type="text" name="taskIds" value="<%=StringUtils.trimToEmpty(taskIds)%>" id="search-control-group-1" placeholder="taskIds" data-wrapper-class="controlgroup-textinput ui-btn">
                <input type="text" name="ticketIds" value="<%=StringUtils.trimToEmpty(ticketIds)%>" id="search-control-group-1" placeholder="ticketIds" data-wrapper-class="controlgroup-textinput ui-btn">
                <input type="text" name="username" value="<%=StringUtils.trimToEmpty(username)%>" id="search-control-group-3" placeholder="用户ERP" data-wrapper-class="controlgroup-textinput ui-btn">
                <label for="select-v-2e">Select</label>
                <select name="pageSize" id="select-v-2e">
                    <option value="">分页</option>
                    <option value="20" <%="20".equals(pageSize) ? "selected=\"selected\"" : ""%>>20</option>
                    <option value="50" <%="50".equals(pageSize) ? "selected=\"selected\"" : ""%>>50</option>
                    <option value="100" <%="100".equals(pageSize) ? "selected=\"selected\"" : ""%>>100</option>
                    <option value="200" <%="200".equals(pageSize) ? "selected=\"selected\"" : ""%>>200</option>
                    <option value="500" <%="500".equals(pageSize) ? "selected=\"selected\"" : ""%>>500</option>
                    <option value="800" <%="800".equals(pageSize) ? "selected=\"selected\"" : ""%>>800</option>
                    <option value="1000" <%="1000".equals(pageSize) ? "selected=\"selected\"" : ""%>>1000</option>
                </select>
                <label for="select-v-1e">Select</label>
                <select name="paramProcessID" id="select-v-1e">
                    <option value="">流程ID</option>
                    <option value="8720" <%="8720".equals(paramProcessID) ? "selected=\"selected\"" : ""%>>普通流程</option>
                    <option value="10800" <%="10800".equals(paramProcessID) ? "selected=\"selected\"" : ""%>>巡检流程</option>
                </select>
                <label for="select-v-4e">Select</label>
                <select name="paramFlowType" id="select-v-4e">
                    <option value="" <%=flowType.equals("") ? "selected=\"selected\"" : ""%>>流程类型</option>
                    <%
                    for (Map<String, Object> row: flowTypeItems) {
                    %>
                    <option value="<%=row.get("flow_type")%>" <%=flowType.equals(row.get("flow_type") + "") ? "selected=\"selected\"" : ""%>><%=row.get("flow_type")%></option>
                    <%
                    }
                    %>
                </select>
                <label for="select-v-3e">Select</label>
                <select name="flowStatus" id="select-v-3e">
                    <option value="" <%=flowStatus.equals("") ? "selected=\"selected\"" : ""%>>审批状态</option>
                    <%
                    for (Map<String, Object> row: flowStatusItems) {
                    %>
                    <option value="<%=row.get("flow_status")%>" <%=flowStatus.equals(row.get("flow_status") + "") ? "selected=\"selected\"" : ""%>><%=row.get("flow_status_msg")%></option>
                    <%
                    }
                    %>
                </select>
                <label for="select-v-2e">Select</label>
                <select name="inspectionItem" id="select-v-2e">
                    <option value="" <%=inspectionItem.equals("") ? "selected=\"selected\"" : ""%>>巡检项</option>
                    <%
                    for (Map<String, Object> row: inspectionItems) {
                    %>
                    <option value="<%=row.get("inspection_item")%>" <%=inspectionItem.equals(row.get("inspection_item")) ? "selected=\"selected\"" : ""%>><%=row.get("inspection_item")%></option>
                    <%
                    }
                    %>
                </select>
                <input type="text" name="startCreate" value="<%=StringUtils.trimToEmpty(startCreate)%>" placeholder="创建时间开始" data-wrapper-class="controlgroup-textinput ui-btn">
                <input type="text" name="endCreate" value="<%=StringUtils.trimToEmpty(endCreate)%>" placeholder="创建时间结束" data-wrapper-class="controlgroup-textinput ui-btn">
                <input type="text" name="startApprove" value="<%=StringUtils.trimToEmpty(startApprove)%>" placeholder="审批时间" data-wrapper-class="controlgroup-textinput ui-btn">
                <button>查询</button>
            </div>
        </form>
        <div style="overflow:scroll;">
            <table data-role="table" id="table-column-toggle" data-mode="columntoggle" class="ui-body-d ui-shadow table-stripe ui-responsive">
                <thead>
                    <tr class="ui-bar-d">
                        <th></th>
                        <th>ID</th>
                        <th>流程ID</th>
                        <th>任务来源</th>
                        <th>流程类型</th>
                        <th>审批状态</th>
                        <th>巡检项</th>
                        <th>集群</th>
                        <th>集市</th>
                        <th>任务id</th>
                        <th>环节id</th>
                        <th>任务负责人</th>
                        <th>Xbp</th>
                        <th>修改引擎为</th>
                        <th>修改版本为</th>
                        <th>创建时间</th>
                        <th>审批时间</th>
                        <th>之前耗时(分钟)</th>
                        <th>之后耗时(分钟)</th>
                        <th>时间对比</th>
                        <th>之前VCore</th>
                        <th>之后VCore</th>
                        <th>VCore对比</th>
                        <th>优化天数</th>
                        <th>累计VCore节省</th>
                        <th>年</th>
                        <th>月</th>
                        <th>周</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                <%
                int i = 1;
                for (Map<String, Object> row: kongMingRecordBean.getRows()) {
                %>
                    <tr>
                        <td><%=i++%></td>
                        <td><%=row.get("id")%></td>
                        <td><%=row.get("process_id")%></td>
                        <td><%=row.get("source")%></td>
                        <td><%=row.get("flow_type")%></td>
                        <td><%=row.get("flow_status_msg")%></td>
                        <td><%=StringUtils.trimToEmpty((String)row.get("inspection_item"))%></td>
                        <td><%=row.get("clusterCode")%></td>
                        <td><%=row.get("marketCode")%></td>
                        <td><a target="_blank" href="buffaloLogs?site=dp.jd.com&buffaloVersion=BUFFALO4&taskId=<%=row.get("taskids")%>"><%=row.get("taskids")%></a></td>
                        <td><%=StringUtils.trimToEmpty((String)row.get("actionids"))%></td>
                        <td><a target="_blank" href="timline://chat/?topin=<%=row.get("username")%>"><%=row.get("username")%></a></td>
                        <td><a target="_blank" href="http://xbp.jd.com/ticket/<%=row.get("ticket_id")%>"><%=row.get("ticket_id")%></a></td>
                        <td><%=row.get("engine")%></td>
                        <td><%=row.get("version")%></td>
                        <td><%=row.get("create_time")%></td>
                        <td><%=row.get("approve_or_reject")%></td>
                        <td><%=row.get("beforeElapsedMin")%></td>
                        <td><%=row.get("afterElapsedMin")%></td>
                        <td><%=row.get("elapsedRate")%></td>
                        <td><%=row.get("beforeVcore")%></td>
                        <td><%=row.get("afterVcore") %></td>
                        <td><%=row.get("vcoreRate") %></td>
                        <td><%=row.get("approveDays") %></td>
                        <td><%=row.get("amountVCores") %></td>
                        <td><%=row.get("create_year") %></td>
                        <td><%=row.get("create_month") %></td>
                        <td><%=row.get("create_week") %></td>
                        <td>
                            <fieldset data-role="controlgroup" data-type="horizontal" data-mini="true" style="margin: 0;">
                                <a target="_blank" href="/xbpController?ticketIds=<%=row.get("ticket_id")%>&type=before" class="ui-shadow ui-btn ui-corner-all padding_em">Before</a>
                                <a target="_blank" href="/xbpController?ticketIds=<%=row.get("ticket_id")%>&type=after" class="ui-shadow ui-btn ui-corner-all padding_em">After</a>
                                <%if(SSOFilterImpl.isSuper()) {%><a target="_blank" href="/xbpController?processType=callback&ticketId=<%=row.get("ticket_id")%>" class="ui-shadow ui-btn ui-corner-all padding_em">用户</a><%} %>
                                <%if(SSOFilterImpl.isSuper()) {%><a target="_blank" href="/xbpController?processType=callback_platform_recheck&ticketId=<%=row.get("ticket_id")%>" class="ui-shadow ui-btn ui-corner-all padding_em">平台</a><%} %>
                                <%if(SSOFilterImpl.isSuper()) {%><a target="_blank" href="/xbpController?deleteIds=<%=row.get("id")%>" class="ui-shadow ui-btn ui-corner-all padding_em">Del</a><%} %>
                                <%if(SSOFilterImpl.isSuper()) {%><a target="_blank" href="/xbpController?statusTickets=<%=row.get("ticket_id")%>" class="ui-shadow ui-btn ui-corner-all padding_em">Status</a><%} %>
                                <%if(SSOFilterImpl.isSuper()) {%><a target="_blank" href="/xbpController?revokeTickets=<%=row.get("ticket_id")%>" class="ui-shadow ui-btn ui-corner-all padding_em">Revoke</a><%} %>
                                <%if(SSOFilterImpl.isSuper()) {%><a target="_blank" href="/xbpController?remindTickets=<%=row.get("ticket_id")%>" class="ui-shadow ui-btn ui-corner-all padding_em">Remind</a><%} %>
                                <%if(SSOFilterImpl.isSuper()) {%><a target="_blank" href="/xbpController?recreateTickets=<%=row.get("ticket_id")%>&processId=<%=row.get("process_id")%>" class="ui-shadow ui-btn ui-corner-all padding_em">ReCreate</a><%} %>
                            </fieldset>
                        </td>
                    </tr>
                <%
                }
                %>
                </tbody>
            </table>
        </div>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>