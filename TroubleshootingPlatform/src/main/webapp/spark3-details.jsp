<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.common.util.StringUtils" %>
<%@ page import="com.jd.bdp.bean.KongMingRecordBean" %>
<%@ page import="java.util.Map" %>
<%@ page import="com.jd.bdp.spark.web.KongmingService" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <style>
        .controlgroup-textinput{
            padding-top:.22em;
            padding-bottom:.22em;
        }
        .padding_em {
            padding: 0.2em 1em;
        }
    </style>
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>Spark 3.x 双跑明细</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <div data-role="navbar">
            <ul>
                <li><a href="spark3-summary.jsp">Summary</a></li>
                <li><a href="spark3-tasks.jsp">Spark3 Tasks</a></li>
                <li><a href="spark3-details.jsp" class="ui-btn-active">Spark3 Logs</a></li>
                <li><a href="spark3-vs.jsp">Spark3 vs Spark2</a></li>
            </ul>
        </div><!-- /navbar -->
        <%
        String pageNo = StringUtils.trimToNull(request.getParameter("pageNo"));
        String pageSize = StringUtils.trimToNull(request.getParameter("pageSize"));
        String task_id = StringUtils.trimToNull(request.getParameter("task_id"));
        String fork_task_id = StringUtils.trimToNull(request.getParameter("fork_task_id"));
        String runStatus = StringUtils.trimToNull(request.getParameter("runStatus"));
        String failureRegression = StringUtils.trimToNull(request.getParameter("failureRegression"));
        KongMingRecordBean kongMingRecordBean = KongmingService.queryBuffaloLog(
            pageNo, pageSize, task_id, fork_task_id, runStatus, failureRegression);
        %>
        <form action="/spark3-details.jsp" method="post">
        <div data-role="controlgroup" data-type="horizontal">
            <a href="spark3-details.jsp?pageNo=<%=kongMingRecordBean.getPreviousPageNo()%>" class="ui-shadow ui-btn ui-corner-all ui-icon-arrow-l ui-btn-icon-left">上一页</a>
            <a href="spark3-details.jsp?pageNo=<%=kongMingRecordBean.getNextPageNo()%>" class="ui-shadow ui-btn ui-corner-all ui-icon-arrow-r ui-btn-icon-right">下一页</a>
            <input type="text" name="task_id" value="<%=StringUtils.trimToEmpty(task_id)%>" id="search-control-group-1" placeholder="task_id" data-wrapper-class="controlgroup-textinput ui-btn">
            <input type="text" name="fork_task_id" value="<%=StringUtils.trimToEmpty(fork_task_id)%>" id="search-control-group-2" placeholder="fork_task_id" data-wrapper-class="controlgroup-textinput ui-btn">
            <label for="select-v-1e">Select</label>
            <select name="runStatus" id="select-v-1e">
            	<option value="">runStatus</option>
            	<option value="run" <%="run".equals(runStatus) ? "selected=\"selected\"" : ""%>>run</option>
            	<option value="success" <%="success".equals(runStatus) ? "selected=\"selected\"" : ""%>>success</option>
            	<option value="fail" <%="fail".equals(runStatus) ? "selected=\"selected\"" : ""%>>fail</option>
            </select>
            <label for="select-v-2e">Select</label>
            <select name="failureRegression" id="select-v-2e">
            	<option value="">FailureRegression</option>
            	<option value="true" <%="true".equals(failureRegression) ? "selected=\"selected\"" : ""%>>true</option>
            	<option value="false" <%="false".equals(failureRegression) ? "selected=\"selected\"" : ""%>>false</option>
            </select>
            <button>Submit</button>
        </div>
        </form>
        <table data-role="table" id="movie-table" data-mode="columntoggle" class="ui-responsive table-stripe">
          <thead>
            <tr class="ui-bar-d">
                <th></th>
                <th data-priority="5">Buffalo Version</th>
                <th data-priority="6">Origin Task Id</th>
                <th data-priority="6">Fork Task Id</th>
                <th data-priority="6">RunLogId</th>
                <th data-priority="6">InstanceId</th>
                <th data-priority="6">RunStatus</th>
                <th data-priority="6">Duration</th>
                <th data-priority="6">WaitAMDuration</th>
                <th data-priority="6">DurationActual</th>
                <th data-priority="6">Spark Version</th>
                <th data-priority="6">ApplicationId</th>
                <th data-priority="6">OutputTable</th>
                <th data-priority="6">Status Code</th>
                <th data-priority="5">Create Time</th>
                <th data-priority="5">Update Time</th>
                <th data-priority="1">Op</th>
            </tr>
          </thead>
          <tbody>
            <%
            int i = 1;
            for (Map<String, Object> row: kongMingRecordBean.getRows()) {
            %>
            <tr>
                <td><%=i++%></td>
                <td><%=row.get("buffalo_version")%></td>
                <td><a target="_blank" href="buffaloLogs?site=dp.jd.com&buffaloVersion=<%=row.get("buffalo_version")%>&taskId=<%=row.get("origin_task_id")%>"><%=row.get("origin_task_id")%></a></td>
                <td><a target="_blank" href="buffaloLogs?site=dp.jd.com&buffaloVersion=<%=row.get("buffalo_version")%>&taskId=<%=row.get("fork_task_id")%>"><%=row.get("fork_task_id")%></a></td>
                <td><a target="_blank" href="buffalo-log.jsp?site=dp.jd.com&taskId=<%=row.get("fork_task_id")%>&buffaloVersion=buffalo4&runLogId=<%=row.get("runLogId")%>&details=true"><%=row.get("runLogId")%></a></td>
                <td><a target="_blank" href="http://dp.jd.com/buffalo4/instance/detail.html?taskInstanceId=<%=row.get("instanceId")%>&tab=instanceRunLog"><%=row.get("instanceId")%></a></td>
                <td><%=row.get("runStatus")%></td>
                <td><%=row.get("duration")%></td>
                <td><%=row.get("waitAMDuration")%></td>
                <td><%=row.get("duration_actual")%></td>
                <td><%=row.get("spark_version")%></td>
                <td><%=row.get("applicationId") + ""%></td>
                <td><%=row.get("targetTableName") + ""%></td>
                <td><%=row.get("status_code")%></td>
                <td><%=row.get("create_time")%></td>
                <td><%=row.get("update_time")%></td>
                <td>
                    <fieldset data-role="controlgroup" data-type="horizontal" data-mini="true" style="margin: 0;">
                        <a target="_blank" href="/sqlController?type=runSpark&originTaskId=<%=row.get("origin_task_id")%>&forkTaskId=<%=row.get("fork_task_id")%>&hadoopEngineVersion=default&random=2_4" class="ui-shadow ui-btn ui-corner-all padding_em">Run_2</a>
                        <a target="_blank" href="/sqlController?type=runSpark&originTaskId=<%=row.get("origin_task_id")%>&forkTaskId=<%=row.get("fork_task_id")%>&hadoopEngineVersion=3.0&random=3_0" class="ui-shadow ui-btn ui-corner-all padding_em">Run_3</a>
                    </fieldset>
                </td>
            </tr>
            <%
            }
            %>
          </tbody>
        </table>
	</div>
</div>
</body>
</html>