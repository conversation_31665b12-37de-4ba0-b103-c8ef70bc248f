<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    String appId = request.getParameter("appId");
    String status = request.getParameter("status");
    String msg= "重构校验成功";
    String displayState = "";
    if(status.equals("-1")){
       msg="重构校验失败，请检查集群和时间是否正确";
       displayState="display:none;";
    }
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>数据可观测-SparkUI重新加载</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        重构校验状态：<%=msg%>
        <br>
        Spark UI重构中，Spark JH为异步加载，请耐心等待2~3分钟后尝试:<br/>
        <a style="<%=displayState%>" href="http://spark-history-3.jd.com/history/<%=appId%>">http://spark-history-3.jd.com/history/<%=appId%></a>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>