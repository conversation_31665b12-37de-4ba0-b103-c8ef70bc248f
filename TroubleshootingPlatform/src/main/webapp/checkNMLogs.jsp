<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Improve</title>
    <jsp:include page="/include/jqueryui.html"/>
    <style>
        * {
            font-family: monospace;font-size: 14px;
        }
    </style>
</head>
<body>
<table>
    <tr>
        <td>Example</td>
        <td>http://************:18080/api/v1/applications/application_1571127790599_7751844/executors/</td>
    </tr>
    <tr>
        <td>URL</td>
        <td><input type="text" id="url" value="" style="width: 900px;"></td>
    </tr>
</table>

<button id="query" class="ui-button ui-widget ui-corner-all" style="width: 600px; height: 40px;" >
    Check
</button>

<pre id="console">
</pre>
<script>
    var isLoaded = false;
    var ws = null;
    function websocket(data, outputSelector) {
        if(isLoaded) {
            ws.send(data);
        } else {
            var host = window.location.host;
            var url = "ws://"+host+"/websocket";
            if ('WebSocket' in window) {
                ws = new WebSocket(url);
                console.log("WebSocket");
            } else if ('MozWebSocket' in window) {
                ws = new MozWebSocket(url);
                console.log("MozWebSocket");
            } else {
                alert('WebSocket is not supported by this browser.');
                return;
            }

            ws.onopen = function (evt) {
                ws.send(data);
            };

            ws.onmessage = function (evt) {
                $(outputSelector).append(evt.data + "<br/>")
            };

            ws.onclose = function (e) {
                console.log('WebSocket发生错误: ' + e.code)
                console.log(e)
            };
            window.onbeforeunload = function () {
                // ws.close()
            }
        }
    }

    $(function () {
        $("#query").click(function () {
            $("#console").html("");
            var json = {
                "url":  $("#url").val()
            };
            var data = '{"class":"com.jd.bdp.spark.web.CheckNMLog",' +
                '"method":"check","json":"' + encodeURIComponent(JSON.stringify(json)) + '"}';
            console.log("json: "+JSON.stringify(json))
            console.log("data: " + data);
            websocket(data, "#console");
        });
    });
</script>
</body>
</html>
