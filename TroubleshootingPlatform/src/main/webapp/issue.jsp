<%@ page import="static com.jd.bdp.bean.Constants.TASK_TYPE_BUFFALO_HIVETASK" %>
<%@ page import="static com.jd.bdp.bean.Constants.TASK_TYPE_BUFFALO_PY_SPARK" %>
<%@ page import="static com.jd.bdp.bean.Constants.*" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Spark统一工单平台</title>
</head>
<body>
<jsp:include page="/include/jqueryui.html"/>
<style>
    fieldset {
        border: 0;
    }
    label {
        display: inline-block; width: 8em;
    }
    .tag {
        width: 4em;
    }
    fieldset div {
        margin-bottom: 2em;
    }
    fieldset .help {
        display: inline-block;
    }
    .ui-tooltip {
        width: 210px;
    }
    .ui-selectmenu-button.ui-button {
        width: 4em;
    }
</style>

<form method="post" action="issues" accept-charset="utf-8">
    <fieldset>
        <div><h1 style="text-align: center;">Spark统一工单平台</h1></div>
        <div <%
            if(!"true".equals(request.getParameter("showIssueUrl"))) {
                out.print("hidden");
            }
        %>>
            <label>Issue URL:</label>
            <label style="width: 11em;" for="issueUrlFeedback">devops/Feedback</label>
            <input class="checkbox" type="radio" checked name="issueUrl" value="https://git.jd.com/devops/Feedback/issues" id="issueUrlFeedback">
            <label style="width: 11em;" for="issueUrlSpark">wuguoxiao/spark</label>
            <input class="checkbox" type="radio" name="issueUrl" value="https://git.jd.com/wuguoxiao/spark/issues" id="issueUrlSpark">
        </div>
        <div>
            <label>站点:</label>
            <label style="width: 11em;" for="siteChina">中国站</label>
            <input class="checkbox" type="radio" name="site" value="中国站,<%=SITE_DOMAIN_CHINA%>" id="siteChina">
            <label style="width: 11em;" for="siteThailand">泰国站</label>
            <input class="checkbox" type="radio" name="site" value="泰国站,<%=SITE_DOMAIN_THAILAND%>" id="siteThailand">
            <label style="width: 11em;" for="siteIndonesia">印尼站</label>
            <input class="checkbox" type="radio" name="site" value="印尼站,<%=SITE_DOMAIN_INDONESIA%>" id="siteIndonesia">
        </div>
        <div>
            <label>任务类型:</label>
            <label style="width: 11em;" for="taskTypeBuffaloHiveTask"><%=TASK_TYPE_BUFFALO_HIVETASK%></label>
            <input class="checkbox" type="radio" name="taskType" value="<%=TASK_TYPE_BUFFALO_HIVETASK%>" id="taskTypeBuffaloHiveTask">
            <label style="width: 11em;" for="taskTypeBuffaloPySpark"><%=TASK_TYPE_BUFFALO_PY_SPARK%></label>
            <input class="checkbox" type="radio" name="taskType" value="<%=TASK_TYPE_BUFFALO_PY_SPARK%>" id="taskTypeBuffaloPySpark">
            <label style="width: 11em;" for="taskTypeBuffaloShell"><%=TASK_TYPE_BUFFALO_SHELL%></label>
            <input class="checkbox" type="radio" name="taskType" value="<%=TASK_TYPE_BUFFALO_SHELL%>" id="taskTypeBuffaloShell">
            <label for="taskTypeIDE"><%=TASK_TYPE_DEV_IDE%></label>
            <input class="checkbox" type="radio" name="taskType" value="<%=TASK_TYPE_DEV_IDE%>" id="taskTypeIDE">
            <label for="taskTypeGateone"><%=TASK_TYPE_GATEONE%></label>
            <input class="checkbox" type="radio" name="taskType" value="<%=TASK_TYPE_GATEONE%>" id="taskTypeGateone">
        </div>
        <div>
            <label>客诉:</label>
            <label for="radio-1"><%=ISSUE_REACH_NONE%></label>
            <input class="checkbox" type="radio" name="issuesReach" value="<%=ISSUE_REACH_NONE%>" id="radio-1">
            <label for="radio-2"><%=ISSUE_REACH_AFFECTED_FINAL_USER%></label>
            <input class="checkbox" type="radio" name="issuesReach" value="<%=ISSUE_REACH_AFFECTED_FINAL_USER%>" id="radio-2">
            <label for="radio-3"><%=ISSUE_REACH_MAYBE_FINAL_USER%></label>
            <input class="checkbox" type="radio" name="issuesReach" value="<%=ISSUE_REACH_MAYBE_FINAL_USER%>" id="radio-3">
        </div>
        <div>
            <label>问题类型:</label>
            <label for="radio-4">任务异常</label>
            <input class="checkbox" type="radio" name="issuesType" value="任务异常" id="radio-4">
            <label for="radio-5">任务运行慢</label>
            <input class="checkbox" type="radio" name="issuesType" value="任务运行慢" id="radio-5">
        </div>
        <div>
            <label>任务标签:</label>
            <label class="tag" for="tagNone">无</label>
            <input class="checkbox" type="checkbox" name="taskTag" value="无" id="tagNone">
            <label class="tag" for="tagL1">L1</label>
            <input class="checkbox" type="checkbox" name="taskTag" value="L1" id="tagL1">
            <label class="tag" for="tagL2">L2</label>
            <input class="checkbox" type="checkbox" name="taskTag" value="L2" id="tagL2">
            <label class="tag" for="tagL3">L3</label>
            <input class="checkbox" type="checkbox" name="taskTag" value="L3" id="tagL3">
            <label style="width: 5em;" class="tag" for="tagGold">黄金眼</label>
            <input class="checkbox" type="checkbox" name="taskTag" value="黄金眼" id="tagGold">
            <label class="tag" for="taggdm">GDM</label>
            <input class="checkbox" type="checkbox" name="taskTag" value="GDM" id="taggdm">
            <label class="tag" for="tagadm">ADM</label>
            <input class="checkbox" type="checkbox" name="taskTag" value="ADM" id="tagadm">
        </div>
        <div>
            <label for="hour">SLA时效要求：</label>
            <select class="select" name="slaHour" id="hour">
                <option selected="selected" value="">无</option>
                <option value="0">0时</option>
                <option value="1">1时</option>
                <option value="2">2时</option>
                <option value="3">3时</option>
                <option value="4">4时</option>
                <option value="5">5时</option>
                <option value="6">6时</option>
                <option value="7">7时</option>
                <option value="8">8时</option>
                <option value="9">9时</option>
                <option value="10">10时</option>
                <option value="11">11时</option>
                <option value="12">12时</option>
                <option value="13">13时</option>
                <option value="14">14时</option>
                <option value="15">15时</option>
                <option value="16">16时</option>
                <option value="17">17时</option>
                <option value="18">18时</option>
                <option value="19">19时</option>
                <option value="20">20时</option>
                <option value="21">21时</option>
                <option value="22">22时</option>
                <option value="23">23时</option>
            </select>
            <select class="select" name="slaMinute" id="minute">
                <option selected="selected" value="">无</option>
                <option value="0">00分</option>
                <option value="5">05分</option>
                <option value="15">15分</option>
                <option value="20">20分</option>
                <option value="25">25分</option>
                <option value="30">30分</option>
                <option value="35">35分</option>
                <option value="40">40分</option>
                <option value="45">45分</option>
                <option value="50">50分</option>
                <option value="55">55分</option>
            </select>
            可选,不填写代表无时效要求,如果选择09:00,代表9点前的SLA
        </div>
        <div>
            <label for="buffaloTaskId">任务ID:</label>
            <input id="buffaloTaskId" name="buffaloTaskId" title="Buffalo平台的任务ID，请输入一个任务id" style="line-height: 25px;">
        </div>
        <div>
            <label for="buffaloLogId">日志ID:</label>
            <input id="buffaloLogId" name="buffaloLogId" title="Buffalo平台的日志ID，请输入一个日志ID，请注意，这是日志ID，不是实例ID" style="line-height: 25px;">
        </div>
        <div>
            <label for="feedbackErp">反馈人ERP:</label>
            <input id="feedbackErp" name="feedbackErp" title="反馈人ERP" style="line-height: 25px;">
        </div>
        <div>
            <label for="issueTitle">问题简述:</label>
            <input id="issueTitle" name="issueTitle" title="问题简述" style="width: 500px; line-height: 25px;">
        </div>
        <div>
            <label>问题描述:</label>
            <textarea style="width: 700px; height: 200px;" name="issueDescription"></textarea>* 提供第一次的stacktrace
        </div>
        <div style="text-align: center;"><input class="ui-button ui-widget ui-corner-all" style="width:500px;" type="submit" value="提交工单"></div>
    </fieldset>
</form>
<script>
    $(function() {
        $("#speed").selectmenu();
    });
    var tooltips = $("[title]").tooltip({
        position: {
            my: "left top",
            at: "right+5 top-5",
            collision: "none"
        }
    });
    $("#hour")
        .selectmenu()
        .selectmenu("menuWidget")
        .addClass("overflow");
    $("#minute")
        .selectmenu()
        .selectmenu("menuWidget")
        .addClass("overflow");
    $(".checkbox").checkboxradio();
</script>
</body>
</html>
