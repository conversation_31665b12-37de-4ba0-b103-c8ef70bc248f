<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="org.apache.commons.lang3.StringUtils" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    String taskIds = StringUtils.trimToEmpty(request.getParameter("taskIds"));
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <a href="index.jsp" class="ui-btn ui-icon-back ui-btn-icon-left">Back</a>
        <h1>Spark升级-任务导入</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div>
    <div role="main" class="ui-content jqm-content">
        <form action="/sparkUpgradeTaskImport" method="post">
            <label for="taskIds">Buffalo4任务ID(支持多个任务id，用逗号或分号分隔):</label>
            <input type="text" name="taskIds" id="taskIds" value="<%=taskIds%>">
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>操作类型:</legend>
                <input type="radio" name="opType" id="radio-choice-h-2a" value="UPGRADE" checked="checked">
                <label for="radio-choice-h-2a">批量升级</label>
                <input type="radio" name="opType" id="radio-choice-h-2b" value="ROLLBACK">
                <label for="radio-choice-h-2b">批量回滚</label>
                <input type="radio" name="opType" id="radio-choice-h-2c" value="RESET">
                <label for="radio-choice-h-2c">批量重置</label>
            </fieldset>
            <input type="checkbox" name="checkbox-mini-0" id="checkbox-mini-0" data-mini="true">
            <label for="checkbox-mini-0">我已阅读引擎间的兼容性问题，并已完成验证。</label>
            <input type="submit" value="提交">
            <h1>注意事项</h1>
            <h3 class="ui-title">对于重复任务，导入会失败</h3>
            <h3 class="ui-title">多个任务ID使用逗号分隔，最好分批导入，每批任务不超过数量100个</h3>
        </form>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>