<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Spark Application Task 分析</title>
    <link rel="stylesheet" href="css/jquery-ui.css">
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="css/css.css">
    <script src="js/jquery-3.1.1.js"></script>
    <script src="js/jquery-ui.js"></script>
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result-container {
            margin-top: 30px;
            display: none;
        }
        .result-card {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .result-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .result-label {
            font-weight: bold;
            color: #555;
        }
        .result-value {
            color: #007bff;
            font-weight: bold;
        }
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        .error-message {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            display: none;
        }
        .success-message {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            display: none;
        }
        .example-url {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        .table-container {
            overflow-x: auto;
            margin-top: 15px;
        }
        .task-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        .task-table th, .task-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .task-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .task-table tr:hover {
            background-color: #f8f9fa;
        }
        .task-table td {
            color: #6c757d;
        }
        .no-data {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Spark Application Task 分析工具</h1>
        <p>输入 Spark Application API 地址，分析应用的 task 执行情况</p>
        
        <form id="analysisForm">
            <div class="form-group">
                <label for="historyUrl">Spark Application API URL:</label>
                <input type="text" id="historyUrl" name="historyUrl" 
                       placeholder="例如: http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855"
                       required>
                <div class="example-url">
                    示例: http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855
                </div>
            </div>
            
            <button type="submit" class="btn" id="analyzeBtn">开始分析</button>
        </form>
        
        <div class="loading" id="loading">
            <p>正在分析 Spark Application，请稍候...</p>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
        
        <div class="result-container" id="resultContainer">
            <div class="result-card">
                <div class="result-title">Application 基本信息</div>
                <div class="result-item">
                    <span class="result-label">Application ID:</span>
                    <span class="result-value" id="appId"></span>
                </div>
                <div class="result-item">
                    <span class="result-label">Application Name:</span>
                    <span class="result-value" id="appName"></span>
                </div>
                <div class="result-item">
                    <span class="result-label">History URL:</span>
                    <span class="result-value" id="historyUrlResult"></span>
                </div>
                <div class="result-item">
                    <span class="result-label">处理 Stages:</span>
                    <span class="result-value" id="processedStages"></span>
                </div>
                <div class="result-item">
                    <span class="result-label">总 Stages:</span>
                    <span class="result-value" id="totalStages"></span>
                </div>
            </div>
            
            <div class="result-card">
                <div class="result-title">Task 统计信息</div>
                <div class="result-item">
                    <span class="result-label">总 Task 数量:</span>
                    <span class="result-value" id="totalTasks"></span>
                </div>
                <div class="result-item">
                    <span class="result-label">推测执行 Task 数量:</span>
                    <span class="result-value" id="speculativeTasks"></span>
                </div>
                <div class="result-item">
                    <span class="result-label">重试 Task 数量:</span>
                    <span class="result-value" id="retryTasks"></span>
                </div>
                <div class="result-item">
                    <span class="result-label">推测执行比例:</span>
                    <span class="result-value" id="speculativeRatio"></span>
                </div>
                <div class="result-item">
                    <span class="result-label">重试比例:</span>
                    <span class="result-value" id="retryRatio"></span>
                </div>
            </div>
            
            <!-- 推测执行 Task 详细信息 -->
            <div class="result-card" id="speculativeDetailsCard" style="display: none;">
                <div class="result-title">推测执行 Task 详细信息</div>
                <div class="table-container">
                    <table class="task-table" id="speculativeTable">
                        <thead>
                            <tr>
                                <th>Task ID</th>
                                <th>Stage ID</th>
                                <th>Attempt ID</th>
                                <th>Index</th>
                                <th>Status</th>
                                <th>Duration (ms)</th>
                            </tr>
                        </thead>
                        <tbody id="speculativeTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 重试 Task 详细信息 -->
            <div class="result-card" id="retryDetailsCard" style="display: none;">
                <div class="result-title">重试 Task 详细信息</div>
                <div class="table-container">
                    <table class="task-table" id="retryTable">
                        <thead>
                            <tr>
                                <th>Task ID</th>
                                <th>Stage ID</th>
                                <th>Attempt ID</th>
                                <th>Attempt</th>
                                <th>Index</th>
                                <th>Status</th>
                                <th>Duration (ms)</th>
                            </tr>
                        </thead>
                        <tbody id="retryTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('#analysisForm').on('submit', function(e) {
                e.preventDefault();
                
                var historyUrl = $('#historyUrl').val().trim();
                if (!historyUrl) {
                    showError('请输入 Spark Application History URL');
                    return;
                }
                
                // 显示加载状态
                $('#loading').show();
                $('#resultContainer').hide();
                $('#errorMessage').hide();
                $('#successMessage').hide();
                $('#analyzeBtn').prop('disabled', true);
                
                // 发送 AJAX 请求
                $.ajax({
                    url: 'sparkTaskAnalysis',
                    type: 'POST',
                    data: {
                        historyUrl: historyUrl
                    },
                    dataType: 'json',
                    success: function(response) {
                        $('#loading').hide();
                        $('#analyzeBtn').prop('disabled', false);
                        
                        if (response.success) {
                            showSuccess(response.message);
                            displayResults(response.data);
                        } else {
                            showError(response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#loading').hide();
                        $('#analyzeBtn').prop('disabled', false);
                        showError('请求失败: ' + error);
                    }
                });
            });
            
            function showError(message) {
                $('#errorMessage').text(message).show();
            }
            
            function showSuccess(message) {
                $('#successMessage').text(message).show();
            }
            
            function displayResults(data) {
                // 显示基本信息
                $('#appId').text(data.appId || 'N/A');
                $('#appName').text(data.appName || 'N/A');
                $('#historyUrlResult').text(data.historyUrl || 'N/A');
                $('#processedStages').text(data.processedStages || 0);
                $('#totalStages').text(data.totalStages || 0);
                
                // 显示统计信息
                $('#totalTasks').text(data.totalTasks || 0);
                $('#speculativeTasks').text(data.speculativeTasks || 0);
                $('#retryTasks').text(data.retryTasks || 0);
                
                // 计算比例
                var totalTasks = data.totalTasks || 0;
                var speculativeRatio = totalTasks > 0 ? ((data.speculativeTasks || 0) / totalTasks * 100).toFixed(2) + '%' : '0%';
                var retryRatio = totalTasks > 0 ? ((data.retryTasks || 0) / totalTasks * 100).toFixed(2) + '%' : '0%';
                
                $('#speculativeRatio').text(speculativeRatio);
                $('#retryRatio').text(retryRatio);
                
                // 显示推测执行 Task 详细信息
                displaySpeculativeTaskDetails(data.speculativeTaskDetails || []);
                
                // 显示重试 Task 详细信息
                displayRetryTaskDetails(data.retryTaskDetails || []);
                
                // 显示结果容器
                $('#resultContainer').show();
            }
            
            function displaySpeculativeTaskDetails(speculativeTasks) {
                var tableBody = $('#speculativeTableBody');
                tableBody.empty();
                
                if (speculativeTasks.length > 0) {
                    speculativeTasks.forEach(function(task) {
                        var row = '<tr>' +
                            '<td>' + (task.taskId || 'N/A') + '</td>' +
                            '<td>' + (task.stageId || 'N/A') + '</td>' +
                            '<td>' + (task.attemptId || 'N/A') + '</td>' +
                            '<td>' + (task.index || 'N/A') + '</td>' +
                            '<td>' + (task.status || 'N/A') + '</td>' +
                            '<td>' + (task.duration || 'N/A') + '</td>' +
                            '</tr>';
                        tableBody.append(row);
                    });
                    $('#speculativeDetailsCard').show();
                } else {
                    $('#speculativeDetailsCard').hide();
                }
            }
            
            function displayRetryTaskDetails(retryTasks) {
                var tableBody = $('#retryTableBody');
                tableBody.empty();
                
                if (retryTasks.length > 0) {
                    retryTasks.forEach(function(task) {
                        var row = '<tr>' +
                            '<td>' + (task.taskId || 'N/A') + '</td>' +
                            '<td>' + (task.stageId || 'N/A') + '</td>' +
                            '<td>' + (task.attemptId || 'N/A') + '</td>' +
                            '<td>' + (task.attempt || 'N/A') + '</td>' +
                            '<td>' + (task.index || 'N/A') + '</td>' +
                            '<td>' + (task.status || 'N/A') + '</td>' +
                            '<td>' + (task.duration || 'N/A') + '</td>' +
                            '</tr>';
                        tableBody.append(row);
                    });
                    $('#retryDetailsCard').show();
                } else {
                    $('#retryDetailsCard').hide();
                }
            }
        });
    </script>
</body>
</html> 