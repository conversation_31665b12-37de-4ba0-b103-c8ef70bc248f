<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.common.YamlUtil" %>
<html>
<head>
    <title>Improve</title>
    <jsp:include page="/include/jqueryui.html"/>
    <style>
        * {
            font-family: monospace;font-size: 14px;
        }
    </style>
    <%
        String buffalo4TaskId = request.getParameter("buffalo4TaskId");
        String logId = request.getParameter("logId");
        String conf = request.getParameter("conf");
        String taskNodeId = request.getParameter("taskNodeId");
        String taskNodeType = request.getParameter("taskNodeType");
        String immediate = request.getParameter("immediate");
        if(buffalo4TaskId==null){
            buffalo4TaskId="";
        }
        if(logId==null){
            logId="";
        }
        if(conf==null){
            conf="";
        }
        if(taskNodeId == null) {
            taskNodeId = YamlUtil.sqlTemplate.get("taskNodeId");
        }
        if(taskNodeType ==null) {
            taskNodeType = YamlUtil.sqlTemplate.get("taskNodeType");
        }
    %>
</head>
<body>
<table border="1" style="border-collapse:collapse;">
    <tr>
        <td>ProjectId</td>
        <td width="200"><input type="text" id="gitProjectId" value="45268"></td>
        <td>required</td>
    </tr>
    <tr>
        <td>ApplicationId</td>
        <td><input type="text" id="applicationId" value="13459"></td>
        <td>required</td>
    </tr>
    <tr>
        <td>projectSpaceId</td>
        <td><input type="text" id="projectSpaceId" value="15077"></td>
        <td>required</td>
    </tr>
    <tr>
        <td>Site</td>
        <td><input type="text" id="site" value="bdp.jd.com"></td>
        <td>required</td>
    </tr>
    <tr>
        <td>Buffalo4TaskId</td>
        <td><input type="text" id="buffalo4TaskId" value="<%=buffalo4TaskId%>"></td>
        <td>required</td>
    </tr>
    <tr>
        <td>LogId</td>
        <td><input type="text" id="logId" value="<%=logId%>"></td>
        <td>optional</td>
    </tr>
    <tr>
        <td>使用原任务的节点：</td>
        <td>
            <input type="radio" name="useOriginNode" value="yes" checked>是</a>
            <input type="radio" name="useOriginNode" value="no">否</a>
        </td>
        <td>required</td>
    </tr>
    <tr>
        <td>Node</td>
        <td width="450">ID<input type="text" id="taskNodeId" value="<%=taskNodeId%>">
            Type<input type="text" id="taskNodeType" value="<%=taskNodeType%>" style="width: 50px;"></td>
        <td>
<pre>
虚拟节点 ID: 278  Type: 2 虚拟节点名称：vip_jdh_client_spark_huide
虚拟节点 ID: 356  Type: 2 虚拟节点名称：vip_jdh_client_spark_3
物理节点 ID: 3577 Type: 1 物理节点名称：client-*************
</pre>
        </td>
    </tr>
    <tr>
        <td>Conf</td>
        <td colspan="2"><textarea id="conf" style="line-height: 20px;width: 906px;height: 200px;margin: 0px;"><%=conf%></textarea></td>
        <td>
<pre>eg.
--conf spark.executor.cores=4
--conf spark.executor.memory=12g
--conf spark.executor.memoryOverhead=2560
--conf spark.yarn.executor.memoryOverhead=2560
--conf spark.executor.instances=2
--conf spark.dynamicAllocation.initialExecutors=2
--conf spark.dynamicAllocation.maxExecutors=1013
--conf spark.sql.adaptive.maxNumPostShufflePartitions=10000
--class org.apache.spark.sql.hive.thriftserver.SparkSqlCluster
--deploy-mode=cluster
--jars hdfs://*************:9000/jvm-profiler-1.0.1.jar
--files hdfs://*************:9000/influxdb.yaml
--files hdfs://*************:9000/metrics.properties
--conf spark.yarn.am.extraJavaOptions="-javaagent:jvm-profiler-1.0.1.jar=reporter=com.uber.profiling.reporters.InfluxDBOutputReporter,configProvider=com.uber.profiling.YamlConfigProvider,configFile=influxdb.yaml,metricInterval=1000,sampleInterval=1000 -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:G1HeapRegionSize=32m -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4 -XX:+UseG1GC -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:-PrintCommandLineFlags -XX:+PrintTenuringDistribution -XX:+PrintReferenceGC"
--conf spark.driver.extraJavaOptions="-javaagent:jvm-profiler-1.0.1.jar=reporter=com.uber.profiling.reporters.InfluxDBOutputReporter,configProvider=com.uber.profiling.YamlConfigProvider,configFile=influxdb.yaml,metricInterval=1000,sampleInterval=1000 -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:G1HeapRegionSize=32m -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4 -XX:+UseG1GC -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:-PrintCommandLineFlags -XX:+PrintTenuringDistribution -XX:+PrintReferenceGC"
--conf spark.executor.extraJavaOptions="-javaagent:jvm-profiler-1.0.1.jar=reporter=com.uber.profiling.reporters.InfluxDBOutputReporter,configProvider=com.uber.profiling.YamlConfigProvider,configFile=influxdb.yaml,metricInterval=1000,sampleInterval=1000 -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:G1HeapRegionSize=32m -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4 -XX:+UseG1GC -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:-PrintCommandLineFlags -XX:+PrintTenuringDistribution -XX:+PrintReferenceGC"
--conf spark.yarn.am.extraJavaOptions="-javaagent:jvm-profiler-1.0.1.jar=reporter=com.uber.profiling.reporters.ConsoleOutputReporter,metricInterval=1000,sampleInterval=1000 -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:G1HeapRegionSize=32m -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4 -XX:+UseG1GC -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:-PrintCommandLineFlags -XX:+PrintTenuringDistribution -XX:+PrintReferenceGC"
--conf spark.driver.extraJavaOptions="-javaagent:jvm-profiler-1.0.1.jar=reporter=com.uber.profiling.reporters.ConsoleOutputReporter,metricInterval=1000,sampleInterval=1000 -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:G1HeapRegionSize=32m -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4 -XX:+UseG1GC -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:-PrintCommandLineFlags -XX:+PrintTenuringDistribution -XX:+PrintReferenceGC"
--conf spark.executor.extraJavaOptions="-javaagent:jvm-profiler-1.0.1.jar=reporter=com.uber.profiling.reporters.ConsoleOutputReporter,metricInterval=1000,sampleInterval=1000 -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:G1HeapRegionSize=32m -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4 -XX:+UseG1GC -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:-PrintCommandLineFlags -XX:+PrintTenuringDistribution -XX:+PrintReferenceGC"
--conf spark.driver.extraJavaOptions="-agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=5005 -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:G1HeapRegionSize=32m -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4 -XX:+UseG1GC -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:-PrintCommandLineFlags -XX:+PrintTenuringDistribution -XX:+PrintReferenceGC"
</pre></td>
    </tr>
</table>

<button id="query" class="ui-button ui-widget ui-corner-all" style="width: 600px; height: 40px;" >
    Submit
</button>

<pre id="console">
</pre>
<script>
    var isLoaded = false;
    var ws = null;
    function websocket(data, outputSelector) {
        if(isLoaded) {
            ws.send(data);
        } else {
            var host = window.location.host;
            var url = "ws://"+host+"/websocket";
            if ('WebSocket' in window) {
                ws = new WebSocket(url);
                console.log("WebSocket");
            } else if ('MozWebSocket' in window) {
                ws = new MozWebSocket(url);
                console.log("MozWebSocket");
            } else {
                alert('WebSocket is not supported by this browser.');
                return;
            }

            ws.onopen = function (evt) {
                ws.send(data);
            };

            ws.onmessage = function (evt) {
                $(outputSelector).append(evt.data + "<br/>")
            };

            ws.onclose = function (e) {
                console.log('WebSocket发生错误: ' + e.code)
                console.log(e)
            };
            window.onbeforeunload = function () {
                // ws.close()
            }
        }
    }

    $(function () {
        $("#query").click(function () {
            $("#console").html("");
            var json = {
                "gitProjectId":  $("#gitProjectId").val(),
                "projectSpaceId":  $("#projectSpaceId").val(),
                "applicationId": $("#applicationId").val(),
                "site": $("#site").val(),
                "buffalo4TaskId": $("#buffalo4TaskId").val(),
                "logId": $("#logId").val(),
                "taskNodeId": $("#taskNodeId").val(),
                "taskNodeType": $("#taskNodeType").val(),
                "conf": $("#conf").val(),
                "useOriginNode": $("input[name=useOriginNode]").val()
            };
            var data = '{"class":"com.jd.bdp.spark.web.BDPController",' +
                '"method":"improve","json":"' + encodeURIComponent(JSON.stringify(json)) + '"}';
            console.log("json: "+JSON.stringify(json));
            console.log("data: " + data);
            websocket(data, "#console");
        });
        if(<%=immediate%>) {
            $("#query").click()
        }
    });
</script>
</body>
</html>
