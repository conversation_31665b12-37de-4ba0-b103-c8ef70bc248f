<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="org.apache.http.impl.client.HttpClients" %>
<%@ page import="org.apache.http.impl.client.CloseableHttpClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="com.alibaba.fastjson.JSONArray" %>
<%@ page import="com.jd.bdp.spark.web.DateTimeUtils" %>
<%@ page import="com.jd.bdp.spark.web.SimpleHttpClient" %>
<%@ page import="com.jd.common.util.StringUtils" %>
<%@ page import="com.jd.bdp.common.JimDBUtils" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.Collections" %>
<%@ page import="java.util.Comparator" %>
<%@ page import="java.util.Date" %>
<%@ page import="java.util.List" %>
<%@ page import="java.lang.Long" %>
<%@ page import="java.lang.NumberFormatException" %>
<%@ page import="java.text.DecimalFormat" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    JSONArray data = (JSONArray)request.getAttribute("data");
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <a href="prestoForm.jsp" class="ui-btn ui-icon-back ui-btn-icon-left">Back</a>
        <h1>Easy</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <table data-role="table" id="movie-table" data-mode="columntoggle" class="ui-responsive table-stroke">
          <thead>
            <tr class="ui-bar-d">
              <th data-priority="1">任务id</th>
              <th data-priority="1">平均耗时（秒）</th>
              <th data-priority="1">时间范围</th>
              <th data-priority="1">Input（GB）</th>
              <th data-priority="1">ShuffleRead（GB）</th>
              <th data-priority="1">ReqVCore</th>
              <th data-priority="1">ReqMem（MB）</th>
            </tr>
          </thead>
          <tbody>
            <%
            if(data != null) {
            for(int i = 0; i < data.size(); i++) {
                JSONObject log = data.getJSONObject(i);
            %>
            <tr>
              <td><a target="_blank" href="/buffaloLogs?site=dp.jd.com&buffaloVersion=BUFFALO4&taskId=<%=log.getString("taskid")%>"><%=log.getString("taskid")%></a></td>
              <td><%=StringUtils.trimToEmpty(log.getString("duration"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("dts"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("inputGB"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("shuffleReadGB"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("req_vcore_c_s"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("req_mem_mbs_s"))%></td>
            </tr>
            <%
            }
            }
            %>
          </tbody>
        </table>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>