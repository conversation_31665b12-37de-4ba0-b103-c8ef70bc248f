<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="org.apache.http.impl.client.HttpClients" %>
<%@ page import="org.apache.http.impl.client.CloseableHttpClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="com.alibaba.fastjson.JSONArray" %>
<%@ page import="com.jd.bdp.spark.web.DateTimeUtils" %>
<%@ page import="com.jd.bdp.spark.web.SimpleHttpClient" %>
<%@ page import="com.jd.common.util.StringUtils" %>
<%@ page import="com.jd.bdp.common.JimDBUtils" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.Collections" %>
<%@ page import="java.util.Comparator" %>
<%@ page import="java.util.Date" %>
<%@ page import="java.util.List" %>
<%@ page import="java.lang.Long" %>
<%@ page import="java.lang.NumberFormatException" %>
<%@ page import="java.text.DecimalFormat" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    JSONArray data = (JSONArray)request.getAttribute("data");
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <a href="similarmodelForm.jsp" class="ui-btn ui-icon-back ui-btn-icon-left">Back</a>
        <h1>Easy</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <table data-role="table" id="movie-table" data-mode="columntoggle" class="ui-responsive table-stroke">
          <thead>
            <tr class="ui-bar-d">
              <th data-priority="1">db_name_a</th>
              <th data-priority="1">tbl_name_a</th>
              <th data-priority="1">db_name_b</th>
              <th data-priority="1">tbl_name_b</th>
              <th data-priority="1">a表分区</th>
              <th data-priority="1">b表分区</th>
              <th data-priority="1">column_a</th>
              <th data-priority="1">column_b</th>
              <th data-priority="1">相同血缘字段</th>
            </tr>
          </thead>
          <tbody>
            <%
            if(data != null) {
            for(int i = 0; i < data.size(); i++) {
                JSONObject log = data.getJSONObject(i);
            %>
            <tr>
              <td><%=StringUtils.trimToEmpty(log.getString("db_name_a"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("tbl_name_a"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("db_name_b"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("tbl_name_b"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("part_name_a"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("part_name_b"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("column_a"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("column_b"))%></td>
              <td><%=StringUtils.trimToEmpty(log.getString("common_field"))%></td>
            </tr>
            <%
            }
            }
            %>
          </tbody>
        </table>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>