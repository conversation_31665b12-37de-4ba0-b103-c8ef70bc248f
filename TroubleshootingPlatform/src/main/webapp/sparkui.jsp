<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>数据可观测-SparkUI重新加载</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <form action="/sparkUI">
          <fieldset data-role="controlgroup" data-type="horizontal">
            <legend>集群:</legend>
            <input type="radio" name="cluster" id="radio-choice-h-2a" value="10k" checked="checked">
            <label for="radio-choice-h-2a">10k</label>
            <input type="radio" name="cluster" id="radio-choice-h-2b" value="hope">
            <label for="radio-choice-h-2b">hope</label>
            <input type="radio" name="cluster" id="radio-choice-h-2c" value="tyrande">
            <label for="radio-choice-h-2c">tyrande</label>
            <input type="radio" name="cluster" id="radio-choice-h-2d" value="dcjdtrcc">
            <label for="radio-choice-h-2d">科技</label>
            <input type="radio" name="cluster" id="radio-choice-h-2e" value="ysera">
            <label for="radio-choice-h-2e">大同</label>
            <input type="radio" name="cluster" id="radio-choice-h-2f" value="move">
            <label for="radio-choice-h-2f">飘移</label>
            <input type="radio" name="cluster" id="radio-choice-h-2g" value="jdcfc">
            <label for="radio-choice-h-2g">jdcfc</label>
          </fieldset>
          <div>
              <label for="datepicker">Date:</label>
              <input id="datepicker" name="logTime" type="date" data-format="YYYY-MM-DD" style="line-height: 25px; width: 400px;">
          </div>
          <label for="number-pattern">AppId:</label>
          <input type="text" name="appId" id="number-pattern" value="">
          <input type="submit" value="重新构建SparkUI">
        </form>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>