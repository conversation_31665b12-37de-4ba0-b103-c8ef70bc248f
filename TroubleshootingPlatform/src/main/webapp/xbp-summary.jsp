<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.bean.XbpForm" %>
<%@ page import="com.jd.bdp.bean.KongMingRecordBean" %>
<%@ page import="com.jd.bdp.filter.SSOFilterImpl" %>
<%@ page import="com.jd.bdp.spark.web.XBPController" %>
<%@ page import="com.jd.common.util.StringUtils" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.DecimalFormat" %>

<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <style>
        .controlgroup-textinput{
            padding-top:.22em;
            padding-bottom:.22em;
        }
        .padding_em {
            padding: 0.2em 1em;
        }
    </style>
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>XBP流程（Hive升Spark类型）（vcores/86400/1.8/1.6/48）</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <%
            List<Map<String, Object>> bill = XBPController.getBill(false, false, false, false);
            List<Map<String, Object>> bill2 = XBPController.getBill(true, false, false, false);
            List<Map<String, Object>> bill3 = XBPController.getBill(false, true, false, false);
            List<Map<String, Object>> bill4 = XBPController.getBill(false, true, true, false);
            List<Map<String, Object>> bill5 = XBPController.getBill(false, true, false, true);
        %>
        <div style="overflow:scroll;">
            <table data-role="table" id="table-column-toggle" data-mode="columntoggle" class="ui-body-d ui-shadow table-stripe ui-responsive">
                <thead>
                    <tr class="ui-bar-d">
                        <th>审批单数量</th>
                        <th>优化前耗时（秒）</th>
                        <th>优化后耗时（秒）</th>
                        <th>耗时降低（%）</th>
                        <th>优化前vcore</th>
                        <th>优化后vcore</th>
                        <th>vcore节省</th>
                        <th>vcore节省（%）</th>
                        <th>vcore节省（每天）</th>
                    </tr>
                </thead>
                <tbody>
                <%
                for (Map<String, Object> row: bill) {
                %>
                    <tr>
                        <td><%=row.get("cnt_tickets")%></td>
                        <td><%=row.get("sum_before_elapsed_sec")%></td>
                        <td><%=row.get("sum_after_elapsed_sec")%></td>
                        <td><%=row.get("elapsed_rate")%></td>
                        <td><%=row.get("sum_before_vcore")%></td>
                        <td><%=row.get("sum_after_vcore")%></td>
                        <td><%=row.get("vcore_diff")%></td>
                        <td><%=row.get("vcore_rate")%></td>
                        <td><%=row.get("vcore_day")%></td>
                    </tr>
                <%
                }
                %>
                </tbody>
            </table>
            <table data-role="table" id="table-column-toggle" data-mode="columntoggle" class="ui-body-d ui-shadow table-stripe ui-responsive">
                <thead>
                    <tr class="ui-bar-d">
                        <th>集群</th>
                        <th>审批单数量</th>
                        <th>优化前耗时（秒）</th>
                        <th>优化后耗时（秒）</th>
                        <th>耗时降低（%）</th>
                        <th>优化前vcore</th>
                        <th>优化后vcore</th>
                        <th>vcore节省</th>
                        <th>vcore节省（%）</th>
                        <th>vcore节省（每天）</th>
                    </tr>
                </thead>
                <tbody>
                <%
                for (Map<String, Object> row: bill2) {
                %>
                    <tr>
                        <td><%=row.get("clusterCode")%></td>
                        <td><%=row.get("cnt_tickets")%></td>
                        <td><%=row.get("sum_before_elapsed_sec")%></td>
                        <td><%=row.get("sum_after_elapsed_sec")%></td>
                        <td><%=row.get("elapsed_rate")%></td>
                        <td><%=row.get("sum_before_vcore")%></td>
                        <td><%=row.get("sum_after_vcore")%></td>
                        <td><%=row.get("vcore_diff")%></td>
                        <td><%=row.get("vcore_rate")%></td>
                        <td><%=row.get("vcore_day")%></td>
                    </tr>
                <%
                }
                %>
                </tbody>
            </table>
            <table data-role="table" id="table-column-toggle" data-mode="columntoggle" class="ui-body-d ui-shadow table-stripe ui-responsive">
                <thead>
                    <tr class="ui-bar-d">
                        <th>年</th>
                        <th>审批单数量</th>
                        <th>优化前耗时（秒）</th>
                        <th>优化后耗时（秒）</th>
                        <th>耗时降低（%）</th>
                        <th>优化前vcore</th>
                        <th>优化后vcore</th>
                        <th>vcore节省</th>
                        <th>vcore节省（%）</th>
                        <th>vcore节省（每天）</th>
                    </tr>
                </thead>
                <tbody>
                <%
                for (Map<String, Object> row: bill3) {
                %>
                    <tr>
                        <td><%=row.get("create_year")%></td>
                        <td><%=row.get("cnt_tickets")%></td>
                        <td><%=row.get("sum_before_elapsed_sec")%></td>
                        <td><%=row.get("sum_after_elapsed_sec")%></td>
                        <td><%=row.get("elapsed_rate")%></td>
                        <td><%=row.get("sum_before_vcore")%></td>
                        <td><%=row.get("sum_after_vcore")%></td>
                        <td><%=row.get("vcore_diff")%></td>
                        <td><%=row.get("vcore_rate")%></td>
                        <td><%=row.get("vcore_day")%></td>
                    </tr>
                <%
                }
                %>
                </tbody>
            </table>
            <table data-role="table" id="table-column-toggle" data-mode="columntoggle" class="ui-body-d ui-shadow table-stripe ui-responsive">
                <thead>
                    <tr class="ui-bar-d">
                        <th>年</th>
                        <th>月</th>
                        <th>审批单数量</th>
                        <th>优化前耗时（秒）</th>
                        <th>优化后耗时（秒）</th>
                        <th>耗时降低（%）</th>
                        <th>优化前vcore</th>
                        <th>优化后vcore</th>
                        <th>vcore节省</th>
                        <th>vcore节省（%）</th>
                        <th>vcore节省（每天）</th>
                    </tr>
                </thead>
                <tbody>
                <%
                for (Map<String, Object> row: bill4) {
                %>
                    <tr>
                        <td><%=row.get("create_year")%></td>
                        <td><%=row.get("create_month")%></td>
                        <td><%=row.get("cnt_tickets")%></td>
                        <td><%=row.get("sum_before_elapsed_sec")%></td>
                        <td><%=row.get("sum_after_elapsed_sec")%></td>
                        <td><%=row.get("elapsed_rate")%></td>
                        <td><%=row.get("sum_before_vcore")%></td>
                        <td><%=row.get("sum_after_vcore")%></td>
                        <td><%=row.get("vcore_diff")%></td>
                        <td><%=row.get("vcore_rate")%></td>
                        <td><%=row.get("vcore_day")%></td>
                    </tr>
                <%
                }
                %>
                </tbody>
            </table>
            <table data-role="table" id="table-column-toggle" data-mode="columntoggle" class="ui-body-d ui-shadow table-stripe ui-responsive">
                <thead>
                    <tr class="ui-bar-d">
                        <th>年</th>
                        <th>周</th>
                        <th>审批单数量</th>
                        <th>优化前耗时（秒）</th>
                        <th>优化后耗时（秒）</th>
                        <th>耗时降低（%）</th>
                        <th>优化前vcore</th>
                        <th>优化后vcore</th>
                        <th>vcore节省</th>
                        <th>vcore节省（%）</th>
                        <th>vcore节省（每天）</th>
                    </tr>
                </thead>
                <tbody>
                <%
                for (Map<String, Object> row: bill5) {
                %>
                    <tr>
                        <td><%=row.get("create_year")%></td>
                        <td><%=row.get("create_week")%></td>
                        <td><%=row.get("cnt_tickets")%></td>
                        <td><%=row.get("sum_before_elapsed_sec")%></td>
                        <td><%=row.get("sum_after_elapsed_sec")%></td>
                        <td><%=row.get("elapsed_rate")%></td>
                        <td><%=row.get("sum_before_vcore")%></td>
                        <td><%=row.get("sum_after_vcore")%></td>
                        <td><%=row.get("vcore_diff")%></td>
                        <td><%=row.get("vcore_rate")%></td>
                        <td><%=row.get("vcore_day")%></td>
                    </tr>
                <%
                }
                %>
                </tbody>
            </table>
        </div>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>