<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="com.alibaba.fastjson.JSONArray" %>
<%@ page import="com.jd.bdp.common.CommonUtil" %>
<%@ page import="com.jd.common.util.StringUtils" %>
<%@ page import="com.jd.bdp.common.JimDBUtils" %>

<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    JSONArray buffalo4Logs = (JSONArray)request.getAttribute("buffalo4Logs");
    String site = request.getAttribute("site").toString();
    String taskId = request.getAttribute("taskId").toString();
    String slaFlag = request.getAttribute("slaFlag") + "";
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <a href="buffalo.jsp" class="ui-btn ui-icon-back ui-btn-icon-left">Back</a>
        <h1>数据可观测-任务诊断</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <a target="_blank" href="buffaloTaskModify.jsp?hidden=1&taskIds=<%=taskId%>" class="ui-btn ui-btn-inline ui-shadow">Buffalo4修改引擎</a>
        <a target="_blank" href="launchTask?buffaloId=<%=taskId%>&buffaloVersion=buffalo4" class="ui-btn ui-btn-inline ui-shadow">Buffalo4任务重跑</a>
        <a target="_blank" href="/sqlController?type=getTaskInfo&taskId=<%=taskId%>" class="ui-btn ui-btn-inline ui-shadow">显示任务属性</a>
        <a target="_blank" href="" class="ui-btn ui-btn-inline ui-shadow">SLA标识：<%=slaFlag%></a>
        <a target="_blank" href="/criticalPath?taskid=<%=taskId%>" class="ui-btn ui-btn-inline ui-shadow">关键链路</a>
        <a target="_blank" href="/runDualTasks.jsp?taskId=<%=taskId%>&immediate=true" class="ui-btn ui-btn-inline ui-shadow">双跑</a>
        <a target="_blank" href="/xbp.jsp?taskId=<%=taskId%>" class="ui-btn ui-btn-inline ui-shadow">发送XBP</a>
        <table data-role="table" id="movie-table" data-filter="true" data-mode="columntoggle" class="ui-responsive table-stroke">
          <thead>
            <tr class="ui-bar-d">
              <th data-priority="1">任务id</th>
              <th data-priority="1">日志id</th>
              <th data-priority="1">实例id</th>
              <th data-priority="1">引擎</th>
              <th data-priority="1">运行节点</th>
              <th data-priority="1">开始运行时间</th>
              <th data-priority="1">运行结束时间</th>
              <th data-priority="1">脚本版本</th>
              <!--
              <th data-priority="1">运行参数</th>
              -->
              <th data-priority="1">运行状态</th>
              <th data-priority="1">运行信息</th>
              <th data-priority="1">耗时(分钟)</th>
              <th data-priority="1">Spark版本</th>
              <th data-priority="1">AppId</th>
              <th data-priority="1">RSS上下线状态</th>
              <th data-priority="1">YARN标签</th>
              <th data-priority="1">YARN级别</th>
              <th data-priority="1">资源隔离</th>
              <th data-priority="1">重保</th>
              <th data-priority="1">HiveTask版本</th>
              <th data-priority="1">HTRes</th>
              <th data-priority="1">ExeCores</th>
              <th data-priority="1">ExeMem</th>
              <th data-priority="1">MaxExe</th>
              <th data-priority="1">Parts</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <%
            if(buffalo4Logs != null) {
            for(int i = 0; i < buffalo4Logs.size(); i++) {
                JSONObject log = buffalo4Logs.getJSONObject(i);
                String runLogId = log.getString("id");
                String runTime = log.getString("run_time");
                String scriptVersion = log.getString("script_version");
            %>
            <tr>
              <td><a target="_blank" href="http://<%=site%>/buffalo4/task/detail.html?taskId=<%=taskId%>"><%=taskId%></a></td>
              <td><a target="_blank" href="http://<%=site%>/buffalo4/instance/log/run_info.html?runLogId=<%=runLogId%>&taskId=<%=taskId%>&type=1"><%=runLogId%></a></td>
              <td><a target="_blank" href="http://<%=site%>/buffalo4/instance/detail.html?taskInstanceId=<%=log.getString("instance_id")%>&tab=instanceRunLog"><%=log.getString("instance_id")%></a></td>
              <td><%=log.getString("engine")%></td>
              <td><%=log.getString("task_node_run_name")%></td>
              <td><%=runTime%></td>
              <td><%=log.getString("end_time")%></td>
              <td><a target="_blank" href="http://<%=site%>/buffalo4/script/download.ajax?fileId=<%=log.getString("script")%>&version=<%=scriptVersion%>"><%=scriptVersion%></a></td>
              <!--
              <td><%=StringUtils.abbreviate(log.getString("args"), 60)%></td>
              -->
              <td style="<%=log.getString("runStatusStyle")%>">
                <%=log.getString("runStatusCN")%>
                <% if("true".equals(log.getString("withRootCause"))) { %>
                <a target="_blank" href="/buffalo-log.jsp?site=<%=site%>&taskId=<%=taskId%>&buffaloVersion=buffalo4&runLogId=<%=runLogId%>&details=true">根因</a>
                <% } %>
              </td>
              <td><%=log.getString("msg") == null ? "" : log.getString("msg")%></td>
              <td><%=log.getString("elapsed_time_dbl")%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "Running Spark version"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "appId"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "RSS上下线状态"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "YARN标签"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "YARN级别"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "资源隔离"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "重保"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "HiveTask版本"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "HTRes"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "ExeCores"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "ExeMem"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "MaxExe"))%></td>
              <td><%=JimDBUtils.getFirstEle(JimDBUtils.getHashVal(taskId, runLogId, "Parts"))%></td>
              <td>
                <fieldset data-role="controlgroup" data-type="horizontal" data-mini="true">
                    <a target="_blank" class="ui-shadow ui-btn ui-corner-all" href="/buffalo-log.jsp?site=<%=site%>&taskId=<%=taskId%>&buffaloVersion=buffalo4&runLogId=<%=runLogId%>&details=true">详情</a>
                    <a target="_blank" class="ui-shadow ui-btn ui-corner-all" href="/buffaloLogs?type=getEnv&taskId=<%=taskId%>&runLogId=<%=runLogId%>">环境</a>
                    <a target="_blank" class="ui-shadow ui-btn ui-corner-all" href="http://dp.jd.com/auroradaa/task_diagnose_serves/task_diagnosis/task_diagnosis_detail?taskLogId=<%=runLogId%>&beeSource=BUFFALO4">诊断</a>
                    <a target="_blank" class="ui-shadow ui-btn ui-corner-all" href="/dorisController?taskId=<%=taskId%>&logId=<%=runLogId%>&dt=<%=CommonUtil.getDt(runTime)%>">资源</a>
                    <a target="_blank" class="ui-shadow ui-btn ui-corner-all" href="/buffaloLogs?type=getBuffaloLog&logId=<%=runLogId%>">日志</a>
                </fieldset>
              </td>
            </tr>
            <%
            }
            }
            %>
          </tbody>
        </table>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>