<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.bean.XbpForm" %>
<%@ page import="com.jd.common.util.StringUtils" %>

<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    String taskId = StringUtils.trimToEmpty(request.getParameter("taskId"));
    String actionId = StringUtils.trimToEmpty(request.getParameter("actionId"));
    if(StringUtils.isNotEmpty(actionId)) {
        actionId = ":"+actionId;
    }
    String flowType = StringUtils.defaultIfEmpty(request.getParameter("flow"), "default");
    String engineType = StringUtils.defaultIfEmpty(request.getParameter("engine"), "default");
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
<style>
.ui-datepicker { z-index:9999 !important}
</style>
<script>
$(function(){
  $("input[name='processId']").bind("click", tapHandler);
  $("#inspection_lastday").datepicker();
  function tapHandler(event) {
    if($(event.target).val() == 10800) {
      $(".inspection").show();
    } else {
      $(".inspection").hide();
    }
  }
  $("input[name='processId']:checked").trigger("click");
});
</script>
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>XBP流程发起工具</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <form action="/xbpController" method="post">
            <input type="hidden" name="processType" value="createFlow">

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>XBP流程ID:</legend>
                <input type="radio" name="processId" id="radio-choice-h-1a" value="8720" checked="checked">
                <label for="radio-choice-h-1a">普通流程</label>
                <input type="radio" name="processId" id="radio-choice-h-1b" value="10800">
                <label for="radio-choice-h-1b">巡检流程</label>
            </fieldset>

            <label for="textarea">任务id:</label>
            <textarea cols="40" rows="8" name="taskids" id="textarea" placeholder="输入多个任务id(以英文逗号分隔，任务ID可指定环节ID，如：101,102:10201#10202,103)"><%=taskId%><%=actionId%></textarea>

            <label class="inspection" for="inspection_item">巡检项:</label>
            <textarea class="inspection" cols="40" rows="8" name="inspection_item" id="inspection_item" placeholder="巡检项"></textarea>

            <label class="inspection" for="inspection_desc">问题描述:</label>
            <textarea class="inspection" cols="40" rows="8" name="inspection_desc" id="inspection_desc" placeholder="问题描述"></textarea>

            <label class="inspection" for="inspection_propose">治理建议:</label>
            <textarea class="inspection" cols="40" rows="8" name="inspection_propose" id="inspection_desc" placeholder="问题描述"></textarea>

            <label class="inspection" for="inspection_7day">近7天出现次数:</label>
            <textarea class="inspection" cols="40" rows="8" name="inspection_7day" id="inspection_7day" placeholder="近7天出现次数"></textarea>

            <fieldset class="inspection" data-role="controlgroup" data-type="horizontal">
                <legend>风险等级:</legend>
                <input type="radio" name="inspection_level" id="radio-choice-h-7a" value="高危">
                <label for="radio-choice-h-7a">高危</label>
                <input type="radio" name="inspection_level" id="radio-choice-h-7b" value="中危">
                <label for="radio-choice-h-7b">中危</label>
                <input type="radio" name="inspection_level" id="radio-choice-h-7c" value="低危">
                <label for="radio-choice-h-7c">低危</label>
            </fieldset>

            <label class="inspection" for="inspection_lastday">截止日期:</label>
            <input class="inspection" type="text" name="inspection_lastday" id="inspection_lastday" data-role="date">

            <fieldset class="inspection" data-role="controlgroup" data-type="horizontal">
                <legend>平台修改能力:</legend>
                <input type="radio" name="inspection_plat" id="radio-choice-h-10a" value="无" checked="checked">
                <label for="radio-choice-h-10a">无</label>
                <input type="radio" name="inspection_plat" id="radio-choice-h-10b" value="有">
                <label for="radio-choice-h-10b">有</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>邮件抄上级:</legend>
                <input type="radio" name="ccSuper" id="radio-choice-h-11b" value="No" checked="checked">
                <label for="radio-choice-h-11b">否</label>
                <input type="radio" name="ccSuper" id="radio-choice-h-11a" value="Yes">
                <label for="radio-choice-h-11a">是</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>流程类型:</legend>
                <input type="radio" name="flowType" id="radio-choice-b-2a" value="" <% if ("default".equals(flowType)) { %> checked="checked" <% } %> >
                <label for="radio-choice-b-2a">无</label>
                <input type="radio" name="flowType" id="radio-choice-b-2d" value="Hive2Spark">
                <label for="radio-choice-b-2d">Hive2Spark v3</label>
                <input type="radio" name="flowType" id="radio-choice-b-2g" value="Hive2SparkV2">
                <label for="radio-choice-b-2g">Hive2Spark v2</label>
                <input type="radio" name="flowType" id="radio-choice-b-2b" value="Spark2To3">
                <label for="radio-choice-b-2b">升级至Spark3.4</label>
                <input type="radio" name="flowType" id="radio-choice-b-2c" value="RSS" <% if ("rss".equals(flowType)) { %> checked="checked" <% } %> >
                <label for="radio-choice-b-2c">升级至RSS</label>
                <input type="radio" name="flowType" id="radio-choice-b-2e" value="Emergency">
                <label for="radio-choice-b-2e">紧急情况</label>
                <input type="radio" name="flowType" id="radio-choice-b-2f" value="RateLimit">
                <label for="radio-choice-b-2f">限流</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>任务类型:</legend>
                <input type="radio" name="taskType" id="radio-choice-h-2a" value="数据计算" checked="checked">
                <label for="radio-choice-h-2a">数据计算</label>
                <input type="radio" name="taskType" id="radio-choice-h-2b" value="数据出库">
                <label for="radio-choice-h-2b">数据出库</label>
                <input type="radio" name="taskType" id="radio-choice-h-2c" value="数据拉链">
                <label for="radio-choice-h-2c">数据拉链</label>
                <input type="radio" name="taskType" id="radio-choice-h-2d" value="无">
                <label for="radio-choice-h-2d">无</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>任务来源:</legend>
                <input type="radio" name="source" id="radio-choice-h-3a" value="调度中心" checked="checked">
                <label for="radio-choice-h-3a">调度中心</label>
                <input type="radio" name="source" id="radio-choice-h-3c" value="9N_BUFFALO4">
                <label for="radio-choice-h-3c">9N_BUFFALO4</label>
                <input type="radio" name="source" id="radio-choice-h-3b" value="自助提数">
                <label for="radio-choice-h-3b">自助提数</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>当前引擎类型:</legend>
                <input type="radio" name="currentEngine" id="radio-choice-i-4a" value="spark" checked="checked">
                <label for="radio-choice-i-4a">spark</label>
                <input type="radio" name="currentEngine" id="radio-choice-i-4b" value="hive">
                <label for="radio-choice-i-4b">hive</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>当前引擎版本:</legend>
                <input type="radio" name="currentEngineVersion" id="radio-choice-j-4a" value="default" checked="checked">
                <label for="radio-choice-j-4a">default</label>
                <input type="radio" name="currentEngineVersion" id="radio-choice-j-4b" value="3.0">
                <label for="radio-choice-j-4b">3.0</label>
                <input type="radio" name="currentEngineVersion" id="radio-choice-j-4c" value="3.2">
                <label for="radio-choice-j-4c">3.2</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend><%=XbpForm.engineUpdated[0] %>:</legend>
                <input type="radio" name="engine" id="radio-choice-h-4c" value="" <% if("default".equals(engineType)) { %> checked="checked" <% } %> >
                <label for="radio-choice-h-4c">不修改引擎</label>
                <input type="radio" name="engine" id="radio-choice-h-4a" value="hive" <% if("hive".equals(engineType)) { %> checked="checked" <% } %> >
                <label for="radio-choice-h-4a">hive</label>
                <input type="radio" name="engine" id="radio-choice-h-4b" value="spark" <% if("spark".equals(engineType)) { %> checked="checked" <% } %> >
                <label for="radio-choice-h-4b">spark</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend><%=XbpForm.engineVersionUpdated[0] %>:</legend>
                <input type="radio" name="version" id="radio-choice-h-5a" value="default" checked="checked">
                <label for="radio-choice-h-5a">default(Spark默认是2.4版本, 或Hive默认版本)</label>
                <input type="radio" name="version" id="radio-choice-h-5b" value="3.0">
                <label for="radio-choice-h-5b">Spark 3.0版本</label>
                <input type="radio" name="version" id="radio-choice-h-5c" value="3.2">
                <label for="radio-choice-h-5c">Spark 3.2版本</label>
                <input type="radio" name="version" id="radio-choice-h-5f" value="3.4">
                <label for="radio-choice-h-5f">Spark 3.4版本</label>
                <input type="radio" name="version" id="radio-choice-h-5d" value="rss">
                <label for="radio-choice-h-5d">Spark RSS版本（非平台人员请勿选择）</label>
                <input type="radio" name="version" id="radio-choice-h-5e" value="rss_l3">
                <label for="radio-choice-h-5e">Spark RSS L3版本（非平台人员请勿选择）</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>孔明RSS套餐:</legend>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6a" value="" checked="checked">
                <label for="radio-choice-h-6a">不修改</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6p" value="SPARK_RSS_DROP_ALL">
                <label for="radio-choice-h-6p">清除</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6b" value="SPARK|SPARK_10K_RSS_L0_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6b">10K_L0</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6c" value="SPARK|SPARK_10K_RSS_L0_SECOND_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6c">10K_L0_2</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6d" value="SPARK|SPARK_10K_RSS_L3_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6d">10K_L3</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6e" value="SPARK|SPARK_10K_RSS_L3_SECOND,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6e">10K_L3_2</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6f" value="SPARK|SPARK_10K_RSS_STANDBY_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6f">10K_STANDBY</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6g" value="SPARK|SPARK_HOPE_RSS_L0_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6g">HOPE_L0</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6h" value="SPARK|SPARK_HOPE_RSS_L0_SECOND_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6h">HOPE_L0_2</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6i" value="SPARK|SPARK_HOPE_RSS_L3_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6i">HOPE_L3</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6j" value="SPARK|SPARK_HOPE_RSS_L3_SECOND,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6j">HOPE_L3_2</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6k" value="SPARK|SPARK_HOPE_RSS_L3_STANDBY_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6k">HOPE_STANDBY</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6v" value="SPARK|SPARK_YSERA_RSS_L0_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6v">大同_L0</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6o" value="SPARK|SPARK_YSERA_RSS_L0_SECOND_PG,SPARK|SPARK_MEMORY_RSS_PG">
                <label for="radio-choice-h-6o">大同_STANDBY</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6w" value="SPARK|SPARK_DCJDTROS_RSS_L0_PG">
                <label for="radio-choice-h-6w">KEJI</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6l" value="SPARK_HOPE_RSS_HUIDU_PG">
                <label for="radio-choice-h-6l">HOPE_HUIDU</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6m" value="SPARK_SHELL|SPARK34_SHELL_10K_RSS_L3_PG">
                <label for="radio-choice-h-6m">10K_RSS2.0</label>
                <input type="radio" name="kongMingTemplate" id="radio-choice-h-6n" value="SPARK_SHELL|SPARK34_SHELL_ysera_RSS_L3_PG">
                <label for="radio-choice-h-6n">大同_RSS2.0</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>孔明倾斜套餐:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-7b" value="SPARK_SKEWED_2_PG">
                <label for="checkbox-choice-h-7b">SPARK_2（暂不生效）</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-7c" value="SPARK_SKEWED_3_PG">
                <label for="checkbox-choice-h-7c">SPARK_3（暂不生效）</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-7a" value="SPARK_SKEWED_3_DETECT_PG">
                <label for="checkbox-choice-h-7a">SPARK_3_DETECT</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>孔明巡检套餐:</legend>
                <input type="radio" name="unKnowHostTemplate" id="radio-choice-h-8a" value="" checked="checked">
                <label for="radio-choice-h-8a">无</label>
                <input type="radio" name="unKnowHostTemplate" id="radio-choice-h-8b" value="XUNJIAN_PACKAGE_HIVETASK">
                <label for="radio-choice-h-8b">XUNJIAN_PACKAGE_HIVETASK</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>孔明巡检套餐:</legend>
                <input type="radio" name="runFastTemplate" id="radio-choice-h-9a" value="" checked="checked">
                <label for="radio-choice-h-9a">无</label>
                <input type="radio" name="runFastTemplate" id="radio-choice-h-9b" value="XUNJIAN_PACKAGE_SPARK">
                <label for="radio-choice-h-9b">XUNJIAN_PACKAGE_SPARK</label>
                <input type="radio" name="runFastTemplate" id="radio-choice-h-9c" value="XUNJIAN_PACKAGE_HIVE">
                <label for="radio-choice-h-9c">XUNJIAN_PACKAGE_HIVE</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>孔明套餐(C2002):</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-i-10d" value="SPARK|ZB_PACKAGE_SPARK_3">
                <label for="checkbox-choice-i-10d">SPARK</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-i-10e" value="SPARK|ZB_PACKAGE_SPARK_3_NO_C2002">
                <label for="checkbox-choice-i-10e">SPARK_NO_C2002</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-i-10f" value="HIVE|ZB_PACKAGE_HIVE">
                <label for="checkbox-choice-i-10f">HIVE</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-i-10g" value="HIVE|ZB_PACKAGE_HIVE_NO_C2002">
                <label for="checkbox-choice-i-10g">HIVE_NO_C2002</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-10h" value="MAPREDUCE|ZB_PACKAGE_MAPREDUCE">
                <label for="checkbox-choice-h-10h">MR</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-10i" value="MAPREDUCE|ZB_PACKAGE_MAPREDUCE_NO_C2002">
                <label for="checkbox-choice-h-10i">MR_NO_C2002</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>孔明套餐(VIP):</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-j-11b" value="SPARK|ZB_PACKAGE_SPARK_VIP_10K">
                <label for="checkbox-choice-j-11b">SPARK_10K</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-j-11c" value="SPARK|ZB_PACKAGE_SPARK_VIP_HOPE">
                <label for="checkbox-choice-j-11c">SPARK_HOPE</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-j-11d" value="HIVE|ZB_PACKAGE_HIVE_VIP_10K">
                <label for="checkbox-choice-j-11d">HIVE_10K</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-j-11e" value="HIVE|ZB_PACKAGE_HIVE_VIP_HOPE">
                <label for="checkbox-choice-j-11e">HIVE_HOPE</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-j-11f" value="MAPREDUCE|ZB_PACKAGE_MAPREDUCE_VIP_10K">
                <label for="checkbox-choice-j-11f">MR_10K</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-j-11g" value="MAPREDUCE|ZB_PACKAGE_MAPREDUCE_VIP_HOPE">
                <label for="checkbox-choice-j-11g">MR_HOPE</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>调度L3降级套餐:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-16e" value="REMOVE_ALL_LIMIT">
                <label for="checkbox-choice-h-16e">解除限流</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-16a" value="MAPREDUCE|MAPREDUCE_TASK_CORE_TIME_LIMIT_NORMAL_L3">
                <label for="checkbox-choice-h-16a">MAPREDUCE_TASK_CORE_TIME</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-16b" value="SPARK|SPARK_TASK_CORE_TIME_LIMIT_NORMAL_L3">
                <label for="checkbox-choice-h-16b">SPARK_TASK_CORE_TIME</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-16c" value="MAPREDUCE|MAPREDUCE_TASK_DAILY_TIME_LIMIT_NORMAL_L3">
                <label for="checkbox-choice-h-16c">MAPREDUCE_TASK_DAILY_TIME</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-16d" value="SPARK|SPARK_TASK_DAILY_TIME_LIMIT_NORMAL_L3">
                <label for="checkbox-choice-h-16d">SPARK_TASK_DAILY_TIME</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>HiveTask资源高级别:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-17a" value="HIVE_TASK|HIVETASK_LOW_RESCOURCE_PG">
                <label for="checkbox-choice-h-17a">LOW</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-17b" value="HIVE_TASK|HIVETASK_MEDIUM_RESCOURCE_PG">
                <label for="checkbox-choice-h-17b">MEDIUM</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-17c" value="HIVE_TASK|HIVETASK_HIGH_RESCOURCE_PG">
                <label for="checkbox-choice-h-17c">HIGH</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>参数调整partitions:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-18a" value="HIVE_TASK|SPARK_TASK_L0_NORAML_PG">
                <label for="checkbox-choice-h-18a">5000</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>Hive升Spark参数优化:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-19a" value="HIVE_TASK|HIVETASK_HIVE_TO_SPARK_L0_NORAML_PG">
                <label for="checkbox-choice-h-19a">是</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>发送逻辑计划到JDQ:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-20b" value="SPARK|SPARK_PLAIN_JDQ_PG">
                <label for="checkbox-choice-h-20b">否</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>节能模式套餐:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-21a" value="HIVE|HIVE_ENERGY_SAVING_MODE">
                <label for="checkbox-choice-h-21a">是</label>
            </fieldset>

            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>hive2spark科技独立套餐</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-21b" value="HIVE_TASK|HIVE_TASK_KEJI_PROMOTE_PG">
                <label for="checkbox-choice-h-21b">是</label>
            </fieldset>

           <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>合并小文件优化套餐:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-22a" value="MERGEFILES|MERGEFILES_TASK_NORMAL_PG">
                <label for="checkbox-choice-h-22a">MAPREDUCE_NORMAL</label>

                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-22b" value="SHELL|SHELL_TASK_MERGEFILE_SPARK_PG">
                <label for="checkbox-choice-h-22b">SHELL_SPARK_NORMAL</label>

                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-22c" value="SPARK|SPARK_TASK_MERGEFILES_NORAML_PG">
                <label for="checkbox-choice-h-22c">SPARK_NORMAL</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                 <legend>spark运行模式优化套餐:</legend>
                 <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-23a" value="HIVE_TASK|HIVE_TASK_SQL_TYPE_PG">
                 <label for="checkbox-choice-h-23a">SPARK_SQL</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                 <legend>Hive AM GC优化套餐 (10w map] = 8g (10w,20w] = 12g  (20w] = 16g）:</legend>
                 <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-24a" value="MAPREDUCE|MAPREDUCE_AM_GC_OPTIMIZE_CONF">
                 <label for="checkbox-choice-h-24a">Hive AM GC</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                 <legend>HiveTask任务关闭使用Hive重试:</legend>
                 <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-25a" value="HIVE_TASK|HIVETASK_TASK_RETRY_CLOSE_PACKAGE">
                 <label for="checkbox-choice-h-25a">HiveTask关闭重试</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                 <legend>HiveTask Spark任务使用Hive重试:</legend>
                 <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-26a" value="HIVE_TASK|HIVETASK_ALLOW_RETRY_WITH_HIVE_PG">
                 <label for="checkbox-choice-h-26a">HiveTask Spark失败使用Hive重试</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>Spark任务开启数据虚拟化功能:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-27a" value="SPARK|SPARK_DATA_VIRTUAL_ENABLE_PG">
                <label for="checkbox-choice-h-27a">Spark任务开启数据虚拟化功能</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>Hive任务开启数据虚拟化功能:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-28a" value="HIVE|HIVE_DATA_VIRTUAL_ENABLE_PG">
                <label for="checkbox-choice-h-28a">Hive任务开启数据虚拟化功能</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>HiveTask任务修改为灰度的Spark引擎:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-29a" value="HIVE_TASK|HIVETASK_NORMAL_SPARK_CONF">
                <label for="checkbox-choice-h-29a">修改为灰度的Spark引擎</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>开启虚拟化(开启EC，不开启Zlib):</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-30a" value="HIVE_TASK|MULTI_COMPRESS_SPARK_CONF">
                <label for="checkbox-choice-h-30a">开启虚拟化</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>Spark 3.4 通用优化:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-31a" value="SPARK|SPARK_3_4_UPGRADE_GENERAL_CLUSTER_PG">
                <label for="checkbox-choice-h-31a">Spark 3.4 通用优化（coalesce）</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>3.4 MaxPartitions:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-31b" value="SPARK|SPARK_3_4_MAX_PARTITION_BYTES_8M_PG">
                <label for="checkbox-choice-h-31b">8M</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-31c" value="SPARK|SPARK_3_4_MAX_PARTITION_BYTES_16M_PG">
                <label for="checkbox-choice-h-31c">16M</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-31d" value="SPARK|SPARK_3_4_MAX_PARTITION_BYTES_32M_PG">
                <label for="checkbox-choice-h-31d">32M</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-31e" value="SPARK|SPARK_3_4_MAX_PARTITION_BYTES_64M_PG">
                <label for="checkbox-choice-h-31e">64M</label>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-31f" value="SPARK|SPARK_3_4_MAX_PARTITION_BYTES_128M_PG">
                <label for="checkbox-choice-h-31f">128M</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>PIG漂移1:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-32a" value="MAPREDUCE|MAPREDUCE_CRC_EXT_LFRZ_PG,PIG|MAPREDUCE_CRC_EXT_LFRZ_PG">
                <label for="checkbox-choice-h-32a">PIG漂移</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>PIG漂移2:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-33a" value="MAPREDUCE|MAPREDUCE_CRC_EXT_LFRZ_SINGLE_PG,PIG|MAPREDUCE_CRC_EXT_LFRZ_SINGLE_PG,">
                <label for="checkbox-choice-h-33a">PIG漂移</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>下发hudi_pure=false:</legend>
                <input type="checkbox" name="CORE_TIME_LIMIT_NORMAL_L3_Template" id="checkbox-choice-h-34a" value="SPARK_SHELL|SPARK_SHELL_DISABLED_HUDI_PG">
                <label for="checkbox-choice-h-34a">下发hudi_pure=false</label>
            </fieldset>
            <label for="textarea">升级背景:</label>
            <textarea cols="40" rows="8" name="rtfMsg" id="rtfMsg" placeholder=""></textarea>
            <input type="submit" value="提交XBP审批流">
        </form>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>