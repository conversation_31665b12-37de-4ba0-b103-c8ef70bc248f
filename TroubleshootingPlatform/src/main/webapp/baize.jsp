<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>模拟白泽回调</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <form id="dataForm">
          <textarea cols="40" rows="10" name="jsonData" id="jsonData" placeholder="">{"receiver":"baize","status":"firing","alerts":[{"status":"firing","labels":{"alert_id":"996e1916-8428-4e1b-96e8-dfbe32657c37","alertname":"996e1916-8428-4e1b-96e8-dfbe32657c37","cycle_type":"custom","department":"none","endTime":"2025-01-14 17:25:53","execTime":"2025-01-14 17:15:00","instanceId":"3642429645090979846","instance_type":"normal","job":"pushgateway","managers":"niemingjun","monitor_id":"d795664a-62b1-4f1a-baf4-c5939493cbd1","priority":"L1","queueTime":"2025-01-14 17:15:02","runTime":"2025-01-14 17:15:07","serviceId":"diagnose-312c-4ad3-bb73-093c7d19f02c","service_id":"b4e95386-9f4a-41ba-97e6-8d91f6f889b6","taskId":"1391840","taskName":"none","taskType":"single","task_import":"0"},"annotations":{"act_875335e8_4c93_4d1e_a29f_e294292bdeb6":"Buffalo任务ID 825631 等级 L1 实例ID 3642429645090979846 实例类型 normal 任务负责人 niemingjun 任务周期 custom 运行失败","act_e69ad157_b1a3_41e6_a2e7_828d4ca61dff":"Buffalo任务ID 825631 等级 L1 实例ID 3642429645090979846 实例类型 normal 任务负责人 niemingjun 任务周期 custom 运行失败","alert_director":"wuguoxiao","alert_env_key":"hadoop","alert_id":"996e1916-8428-4e1b-96e8-dfbe32657c37","alert_name":"针对默认支持Hudi的调度L0L1任务运行失败监控","alert_type":"","business_title":"【白泽】监控","cnf_time":"","cycle_type":"","cycle_value":"","description":"Buffalo任务ID 825631 等级 L1 实例ID 3642429645090979846 实例类型 normal 任务负责人 niemingjun 任务周期 custom 运行失败","expression":"(buffalo_task_instance_fail{priority=~\"L0|L1\"}) >= 0.000000","for":"1m","is_px_template":"","is_tmp":"0","last_time":"0","metric_measures_id":"301","metric_type_id":"3","monitor_id":"d795664a-62b1-4f1a-baf4-c5939493cbd1","monitor_name":"系统服务_Spark支持Hudi","operator":">=","prod_line_code":"","px_fa_id":"0","repeat_interval":"1h","server_code":"","service_id":"b4e95386-9f4a-41ba-97e6-8d91f6f889b6","service_name":"hadoop_monitor","severity":"warning","summary":"","threshold":"0.000000","time_window_lower":"00:00","time_window_upper":"00:00","value":"646000"},"startsAt":"2025-01-14T09:27:27Z","endsAt":"0001-01-01T00:00:00Z","generatorURL":"/graph?g0.expr=%28buffalo_task_instance_fail%7Bpriority%3D~%22L0%7CL1%22%7D%29+%3E%3D+0&g0.tab=1","fingerprint":"0f8b5acbeffb586d","sentAt":"2025-01-14T17:27:27.692488271+08:00"}],"groupLabels":{"alertname":"996e1916-8428-4e1b-96e8-dfbe32657c37"},"commonLabels":null,"commonAnnotations":{"service_id":"b4e95386-9f4a-41ba-97e6-8d91f6f889b6"},"externalURL":"http://host-11-173-176-183:8089","version":"4","groupKey":""}</textarea>
          <button type="submit">提交</button>
        </form>
        <p id="response"></p>
    <script>
        document.getElementById('dataForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const jsonData = document.getElementById('jsonData').value;
            fetch('TaskFailCallback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: jsonData
            })
            .then(response => response.text())
            .then(text => {
                document.getElementById('response').innerText = text;
            })
            .catch(error => {
                console.error('错误:', error);
            });
        });
    </script>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>