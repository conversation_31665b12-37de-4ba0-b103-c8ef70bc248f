<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="org.apache.commons.lang3.StringUtils" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    String taskIds = StringUtils.trimToEmpty(request.getParameter("taskIds"));
    String actionId = StringUtils.trimToEmpty(request.getParameter("actionId"));
    String engine = StringUtils.trimToEmpty(request.getParameter("engine"));
    String version = StringUtils.trimToEmpty(request.getParameter("version"));
    String hidden = StringUtils.trimToNull(request.getParameter("hidden"));
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>Buffalo4引擎版本修改</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <form action="/buffalo4TaskModify">
            <input type="hidden" name="oPType" value="modify" />
            <label for="taskIds">Buffalo4任务ID(支持多个任务id，用逗号或分号分隔):</label>
            <input type="text" name="taskIds" id="taskIds" value="<%=taskIds%>">
            <label for="actionId">环节ID(可为空，仅支持一个环节id，为空代表所有环节):</label>
            <input type="text" name="actionId" id="actionId" value="<%=actionId%>">
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>引擎类型:</legend>
                <input type="radio" name="hadoopEngineType" id="radio-choice-h-2a" <% if("default".equals(engine)) {%> checked="checked"<%}%> value="default">
                <label for="radio-choice-h-2a">default</label>
                <input type="radio" name="hadoopEngineType" id="radio-choice-h-2b" <% if("spark".equals(engine)) {%> checked="checked"<%}%> value="spark">
                <label for="radio-choice-h-2b">spark</label>
                <input type="radio" name="hadoopEngineType" id="radio-choice-h-2c" <% if("hive".equals(engine)) {%> checked="checked"<%}%> value="hive">
                <label for="radio-choice-h-2c">hive</label>
            </fieldset>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>引擎版本:</legend>
                <input type="radio" name="hadoopEngineVersion" id="radio-choice-h-3a" <% if("default".equals(version)) {%> checked="checked"<%}%> value="default">
                <label for="radio-choice-h-3a">default</label>
                <input type="radio" name="hadoopEngineVersion" id="radio-choice-h-3b" <% if("3.0".equals(version)) {%> checked="checked"<%}%> value="3.0">
                <label for="radio-choice-h-3b">Spark 3.0</label>
                <input type="radio" name="hadoopEngineVersion" id="radio-choice-h-3f" <% if("3.4".equals(version)) {%> checked="checked"<%}%> value="3.4">
                <label for="radio-choice-h-3f">Spark 3.4</label>
                <% if (hidden != null) { %>
                <input type="radio" name="hadoopEngineVersion" id="radio-choice-h-3d" <% if("rss".equals(version)) {%> checked="checked"<%}%> value="rss">
                <label for="radio-choice-h-3d">Spark RSS版本</label>
                <input type="radio" name="hadoopEngineVersion" id="radio-choice-h-3e" <% if("rss_l3".equals(version)) {%> checked="checked"<%}%> value="rss_l3">
                <label for="radio-choice-h-3e">Spark RSS版本 L3</label>
                <input type="radio" name="hadoopEngineVersion" id="radio-choice-h-3c" <% if("3.2".equals(version)) {%> checked="checked"<%}%> value="3.2">
                <label for="radio-choice-h-3c">Spark 3.2</label>
                <input type="radio" name="hadoopEngineVersion" id="radio-choice-h-3g" <% if("3.4_preview".equals(version)) {%> checked="checked"<%}%> value="3.4_preview">
                <label for="radio-choice-h-3g">Spark 3.4 preview</label>
                <% } %>
            </fieldset>
            <input type="checkbox" name="checkbox-mini-0" id="checkbox-mini-0" data-mini="true">
            <label for="checkbox-mini-0">我已阅读引擎间的兼容性问题，并已完成验证。</label>
            <div style="border: 2px solid red; padding: 10px; margin: 10px 0; background-color: #ffe6e6;">
                <strong>特别提醒：</strong>请注意此次修改对开发生产隔离的任务会同时修改开发环境和正式环境。
            </div>
            <input type="submit" value="更新">
            <h1>注意事项</h1>
            <h3 class="ui-title">相同的程序/脚本在不同的引擎间（Hive引擎、Spark 2.x、Spark 3.x版本）运行，可能存在不兼容的问题。以下梳理了常见的不兼容问题和相应的解决方案。更多的兼容性问题请查阅文档。</h3>
            <h4>Spark与Hive的兼容</h4>
            <p>1、<a target="_blank" href="https://cf.jd.com/pages/viewpage.action?pageId=155425038">Grouping__id函数兼容</a></p>
            <p>2、<a target="_blank" href="https://cf.jd.com/pages/viewpage.action?pageId=674986231">add months函数兼容</a></p>
            <p>3、<a target="_blank" href="https://cf.jd.com/pages/viewpage.action?pageId=715080789">标准差函数（stddev）兼容</a></p>
            <p>4、<a target="_blank" href="https://cf.jd.com/pages/viewpage.action?pageId=715080983">get_json_object 函数解析json串使用手册兼容</a></p>
            <p><a target="_blank" href="https://cf.jd.com/pages/viewpage.action?pageId=180370681">查看更多</a></p>
            <h4>Spark 2.x与Spark 3.x的兼容</h4>
            <p><a target="_blank" href="https://cf.jd.com/pages/viewpage.action?pageId=330786857">查看更多</a></p>
        </form>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>