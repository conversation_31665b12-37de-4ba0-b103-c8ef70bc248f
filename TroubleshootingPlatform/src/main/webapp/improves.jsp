<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Improve</title>
    <jsp:include page="/include/jqueryui.html"/>
</head>
<body>
<table>
    <tr>
        <td>Buffalo4TaskIds</td>
        <td><textarea id="taskIds" style="width: 500px;height: 100px;"></textarea></td>
    </tr>
    <tr>
        <td>conf</td>
        <td><textarea id="conf" style="width: 500px;height: 100px;"></textarea></td>
    </tr>
</table>
<button id="query" class="ui-button ui-widget ui-corner-all" style="width: 600px; height: 40px;" >
    Submit
</button>
<script>
    $(function () {
        $("#query").click(function () {
            $.each($("#taskIds").val().replace(/\n/g,",").split(","),function(i,n){
                window.open("improve.jsp?immediate=true&buffalo4TaskId=" + n +"&conf=" + $("#conf").val(), n)
            })
        })
    });
</script>
</body>
</html>
