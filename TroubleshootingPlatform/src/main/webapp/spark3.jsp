<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.scheduler.Scheduler" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>Spark3双跑后台</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <h1>增加双跑任务：</h1>
        <form method="post" action="/sqlController" target="_blank">
            <input type="hidden" name="type" value="insert"/>
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>Buffalo版本:</legend>
                <input type="radio" name="taskversion" id="radio-choice-i-2a" value="BUFFALO4" checked="checked">
                <label for="radio-choice-i-2a">BUFFALO4</label>
                <input type="radio" name="taskversion" id="radio-choice-i-2b" value="BUFFALO3">
                <label for="radio-choice-i-2b">BUFFALO3</label>
            </fieldset>
            <label for="taskIds">Buffalo任务ID(支持多个任务id，用逗号或分号分隔):</label>
            <input type="text" name="taskIds" id="taskIds" value="">
            <input type="submit" value="Insert">
        </form>
        <a target="_blank" href="spark3-summary.jsp">Spark 3.x 双跑概要</a><br/>
        <a target="_blank" href="spark3-tasks.jsp">Spark 3.x 复制表</a><br/>
        <a target="_blank" href="spark3-details.jsp">Spark 3.x 双跑明细</a><br/>

        <div class="ui-field-contain">
            <label for="textinput-1"><a target="_blank" href="http://origin.jd.com/globalSearch/ipSearch?key=bdp.spark3.scheduler1.">ForkTask Queue:</a></label>
            <input type="text" name="textinput-1" id="textinput-1" value="<%=Scheduler.forkBuffaloTaskExecutorService.getQueue().size()%>">
        </div>
        <div class="ui-field-contain">
            <label for="textinput-2"><a target="_blank" href="http://origin.jd.com/globalSearch/ipSearch?key=bdp.spark3.scheduler2.">FillBuffaloLog Queue:</a></label>
            <input type="text" name="textinput-2" id="textinput-2" value="<%=Scheduler.monitorAndFillLogExecutorService.getQueue().size()%>">
        </div>
        <div class="ui-field-contain">
            <label for="textinput-3"><a target="_blank" href="http://origin.jd.com/globalSearch/ipSearch?key=bdp.spark3.scheduler3.">FillSparkVersion Queue:</a></label>
            <input type="text" name="textinput-3" id="textinput-3" value="<%=Scheduler.fillSparkVersionExecutorService.getQueue().size()%>">
        </div>
        <div class="ui-field-contain">
            <label for="textinput-4"><a target="_blank" href="http://origin.jd.com/globalSearch/ipSearch?key=bdp.spark3.scheduler4.">RunSpark3 Queue:</a></label>
            <input type="text" name="textinput-4" id="textinput-4" value="<%=Scheduler.runSpark3ExecutorService.getQueue().size()%>">
        </div>
        <div class="ui-field-contain">
            <label for="textinput-5"><a target="_blank" href="http://origin.jd.com/console/middleware/ump/monitor/perfomance?appId=33610&appName=SparkMonitorApp&platform=j-one&endPointKey=bdp.spark3.scheduler5.compareTool&thresholdOn=0&isSecond=0&frequency=1">CheckData Queue:</a></label>
            <input type="text" name="textinput-5" id="textinput-5" value="<%=Scheduler.checkDataExecutorService.getQueue().size()%>">
        </div>
        <div data-role="controlgroup" data-type="horizontal">
            <a target="_blank" href="/scheduler?type=stop" class="ui-btn ui-corner-all">Shutdown Scheduler</a>
            <a target="_blank" href="/scheduler?type=start" class="ui-btn ui-corner-all">Start Scheduler</a>
            <a target="_blank" href="/scheduler?type=stopForkTask" class="ui-btn ui-corner-all">Shutdown ForkTask Scheduler</a>
            <a target="_blank" href="/scheduler?type=startForkTask" class="ui-btn ui-corner-all">Start ForkTask Scheduler</a>
            <a target="_blank" href="/scheduler?type=stopFillBuffaloLog" class="ui-btn ui-corner-all">Shutdown FillBuffaloLog Scheduler</a>
            <a target="_blank" href="/scheduler?type=stopFillSparkVersion" class="ui-btn ui-corner-all">Shutdown FillSparkVersion Scheduler</a>
            <a target="_blank" href="/scheduler?type=startFillSparkVersion" class="ui-btn ui-corner-all">Start FillSparkVersion Scheduler</a>
            <a target="_blank" href="/scheduler?type=backup" class="ui-btn ui-corner-all">BackUp Mysql</a>
            <a target="_blank" href="/scheduler?type=test" class="ui-btn ui-corner-all">Test Mysql</a>
            <a target="_blank" href="/scheduler?type=truncate" class="ui-btn ui-corner-all">Truncate Mysql</a>
            <a target="_blank" href="/scheduler?type=cleanInvalidData" class="ui-btn ui-corner-all">CleanInvalidData Mysql</a>
        </div>
        <div data-role="controlgroup" data-type="horizontal">
        </div>
	</div>
</div>
</body>
</html>