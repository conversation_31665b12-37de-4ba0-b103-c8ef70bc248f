<%--
  Created by IntelliJ IDEA.
  User: wuguoxiao
  Date: 2019-12-21
  Time: 13:23
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Spark Tuning</title>
    <jsp:include page="/include/jqueryui.html"/>
    <style>
        fieldset {
            border: 0;
        }
        label {
            display: inline-block; width: 8em;
        }
        .tag {
            width: 4em;
        }
        fieldset div {
            margin-bottom: 2em;
        }
        fieldset .help {
            display: inline-block;
        }
        .ui-tooltip {
            width: 210px;
        }
        .ui-selectmenu-button.ui-button {
            width: 4em;
        }
    </style>
</head>
<body>
<form method="post" action="performanceTuning" accept-charset="utf-8">
    <fieldset>
        <div><h1 style="text-align: center;">Spark Auto Tuning</h1></div>
        <div>
            <label for="buffaloTaskId">Market:</label>
            <input id="buffaloTaskId" name="market" value="119" title="Market" style="line-height: 25px;">
            119:数据仓库_JDW_NEW（任务管理）_10k
            280:零售子集团集市
        </div>
        <div>
            <label for="buffaloLogId">Queue:</label>
            <input id="buffaloLogId" name="queue" value="634" title="Queue" style="line-height: 25px;">
            634:数据仓库JDW_NEW_公共数据_10k
            1038:移动上海分析队列_hope
            2147:商家生态核心队列_core_10k
        </div>
        <div>
            <label for="feedbackErp">Account:</label>
            <input id="feedbackErp" name="account" value="1969" title="Account" style="line-height: 25px;">
            1969:adm模型开发
            2215:移动超级生产账号_mobile_sc
            985:商家生态超级生产账号_eco_10k
        </div>
        <div>
            <label>BuffaloNameType:</label>
            <label for="radio-4">TableNameType</label>
            <input class="checkbox" type="radio" name="buffaloNameType" checked value="TableName" id="radio-4">
            <label for="radio-5">Random</label>
            <input class="checkbox" type="radio" name="buffaloNameType" value="Random" id="radio-5">
        </div>
        <div>
            <label>TaskType:</label>
            <label for="radio-1">HiveTask</label>
            <input class="checkbox" type="radio" name="taskType" checked value="HiveTask" id="radio-1">
            <label for="radio-2">SparkShell</label>
            <input class="checkbox" type="radio" name="taskType" value="SparkShell" id="radio-2">
            <label for="radio-3">Native</label>
            <input class="checkbox" type="radio" name="taskType" value="Native" id="radio-3">
        </div>
        <div>
            <label>SQL:</label>
            <textarea style="width: 700px; height: 200px;" name="sql">
use adm;
set spark.sql.hive.mergeFiles=true;

insert overwrite table adm.adm_s14_traffic_plat_item_sum_test1 partition (dt)
select web_site_id
      ,max(chan_type) as chan_type
      ,model as sale_mode
      ,chan_first_cate_cd
      ,max(case when substr(lpad(conv(grouping__id,10,2),8,0),5,1)='0' then chan_first_cate_name else NULL end) as chan_first_cate_name
      ,chan_second_cate_cd
      ,max(case when substr(lpad(conv(grouping__id,10,2),8,0),6,1)='0' then chan_second_cate_name else NULL end) as chan_second_cate_name
      ,chan_third_cate_cd
      ,max(case when substr(lpad(conv(grouping__id,10,2),8,0),7,1)='0' then chan_third_cate_name else NULL end) as chan_third_cate_name
      ,chan_fourth_cate_cd
      ,max(case when substr(lpad(conv(grouping__id,10,2),8,0),8,1)='0' then chan_fourth_cate_name else NULL end) as chan_fourth_cate_name
      ,count(1) as item_pv
      ,count(distinct browser_uniq_id) as item_uv
      ,count(distinct session_id) as item_visits
      ,sum(case when first_request_flag=1 and last_request_flag=1 then 1 else 0 end) as item_bounces
      ,sum(case when first_request_flag=1 then 1 else 0 end) as item_exits
      ,count(distinct case when coalesce(user_log_acct,'')<>'' then user_log_acct end) as item_login_users
      ,sum(stm_rt) as item_rt
      ,regexp_replace('"""+ ht.data_day_str+ """', '-', '') stat_tm
      ,bs
      ,case when lpad(bin(cast(GROUPING__ID AS bigint)),8,'0')  = '00011111' then '67108865'  --平台,站点
            when lpad(bin(cast(GROUPING__ID AS bigint)),8,'0')  = '00001111' then '67108867'  --平台,站点,销售模式
            when lpad(bin(cast(GROUPING__ID AS bigint)),8,'0')  = '00010111' then '335544321'  --平台,站点,一级渠道
            when lpad(bin(cast(GROUPING__ID AS bigint)),8,'0')  = '00010011' then '872415233'  --平台,站点,一级渠道,二级渠道
            when lpad(bin(cast(GROUPING__ID AS bigint)),8,'0')  = '00010001' then '1946157057'  --平台,站点,一级渠道,二级渠道,三级渠道
            when lpad(bin(cast(GROUPING__ID AS bigint)),8,'0')  = '00010000' then '4093640705'  --平台,站点,一级渠道,二级渠道,三级渠道,四级渠道
        end as lvl
      ,blacklist_flag
      ,'day' as tp
      ,'plat_item' as dp
      ,'""" + ht.data_day_str + """' as dt
from  adm.adm_d14_traffic_plat_item_di
where dt = '""" + ht.data_day_str + """'
group by
      blacklist_flag
      ,bs
      ,web_site_id
      ,model
      ,chan_first_cate_cd
      ,chan_second_cate_cd
      ,chan_third_cate_cd
      ,chan_fourth_cate_cd
grouping sets
(
     (blacklist_flag,bs,web_site_id)
    ,(blacklist_flag,bs,web_site_id,model)
    ,(blacklist_flag,bs,web_site_id,chan_first_cate_cd)
    ,(blacklist_flag,bs,web_site_id,chan_first_cate_cd,chan_second_cate_cd)
    ,(blacklist_flag,bs,web_site_id,chan_first_cate_cd,chan_second_cate_cd,chan_third_cate_cd)
    ,(blacklist_flag,bs,web_site_id,chan_first_cate_cd,chan_second_cate_cd,chan_third_cate_cd,chan_fourth_cate_cd)
);

            </textarea>
        </div>
        <div>
<pre>
export SPARK_HOME=/home/<USER>/weixiuli/work12/spark-2.4.3-bin-internal-online-localSort-201912231537

/bin/sh_bak spark-sql --master yarn           \
    --conf spark.submit.deployMode=client     \
    --conf spark.executor.instances=2         \
    --conf spark.executor.cores=4             \
    --conf spark.executor.memory=12g          \
    --conf spark.driver.memory=8g             \
    --conf spark.driver.cores=4               \
    --conf spark.sql.shuffle.partitions=900   \
    --conf spark.dynamicAllocation.enabled=True   \
    --conf spark.shuffle.service.enabled=True     \
    --conf spark.speculation=true                 \
    --conf spark.isLoadHivercFile=true            \
    --conf spark.sql.tempudf.ignoreIfExists=true  \
    --conf spark.sql.parser.quotedRegexColumnNames=true  \
    --conf spark.sql.crossJoin.enabled=true              \
    --conf spark.resource.level=low                      \
    --conf spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version=2   \
    --conf spark.sql.retry.with.hive=True                                   \
    --hiveconf hive.exec.orc.split.strategy=BI                              \
    --conf spark.sql.source=HiveTask                                        \
    -e "

    set hive.exec.dynamic.partition=true;
    set hive.exec.dynamic.partition.mode=nonstrict;

"
</pre>
        </div>
        <div style="text-align: center;"><input class="ui-button ui-widget ui-corner-all" style="width:500px;" type="submit" value="Tuning"></div>
    </fieldset>
</form>
<script>
    $(".checkbox").checkboxradio();
</script>
</body>
</html>
