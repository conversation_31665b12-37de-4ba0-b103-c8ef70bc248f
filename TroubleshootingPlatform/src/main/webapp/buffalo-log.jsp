<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.spark.web.BDPUtils" %>
<%@ page import="java.util.Map" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    String site = request.getParameter("site");
    String taskId = request.getParameter("taskId");
    String runLogId = request.getParameter("runLogId");
    String buffaloVersion = request.getParameter("buffaloVersion");
    String runTime = request.getParameter("runTime");
    String endTime = request.getParameter("endTime");
    String duration = request.getParameter("duration");
    boolean hasDetail = request.getParameter("details").equals("true");

    Map<String, String> logData = BDPUtils.analysisLogByLogId(taskId, buffaloVersion, runLogId, null, site, hasDetail);
    BDPUtils.syncToJimDB(taskId, runLogId, logData);
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <a href="buffaloLogs?site=<%=site%>&taskId=<%=taskId%>&buffaloVersion=<%=buffaloVersion%>" class="ui-btn ui-icon-back ui-btn-icon-left">Back</a>
        <h1>数据可观测-任务诊断</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
    <table data-role="table" id="movie-table" data-mode="reflow" class="ui-body-d ui-shadow table-stripe ui-responsive">
      <thead>
        <tr class="ui-bar-d">
          <th>任务id</th>
          <th><%=buffaloVersion%> -
          <%=("buffalo4").equalsIgnoreCase(buffaloVersion) ?
            "<a target=\"_blank\" href=\"http://"+site+"/buffalo4/task/detail.html?taskId="+taskId+"\">" + taskId + "</a>"
            : "<a target=\"_blank\" href=\"http://"+site+"/buffalo/task/detail.html?id="+taskId+"\">" + taskId + "</a>"
          %></th>
        </tr>
        <tr class="ui-bar-d">
            <th>日志id</th>
            <th>
              <%=runLogId%>
            </th>
        </tr>
      </thead>
      <tbody>
        <%
        for(Map.Entry<String, String> entry: logData.entrySet()) {
        %>
        <tr>
          <td><%=entry.getKey()%></td>
          <td><%=entry.getValue()%></td>
        </tr>
        <%
        }
        %>
      </tbody>
    </table>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>