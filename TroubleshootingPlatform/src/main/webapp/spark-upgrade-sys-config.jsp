<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="org.apache.commons.lang3.StringUtils" %>
<%@ page import="org.apache.http.impl.client.HttpClients" %>
<%@ page import="org.apache.http.impl.client.CloseableHttpClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="com.alibaba.fastjson.JSONArray" %>
<%@ page import="com.jd.bdp.spark.web.DateTimeUtils" %>
<%@ page import="com.jd.bdp.spark.web.SimpleHttpClient" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.Collections" %>
<%@ page import="java.util.Comparator" %>
<%@ page import="java.util.List" %>
<%@ page import="java.lang.NumberFormatException" %>
<%@ page import="java.text.DecimalFormat" %>
<%@ page import="com.jd.bdp.bean.domain.SparkUpgradeSysConfigBean" %>
<%@ page import="com.jd.bdp.bean.PageInfoList" %>
<%@ page import="com.jd.bdp.bean.SparkUpgradeTaskBean" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
        PageInfoList<SparkUpgradeSysConfigBean> result = (PageInfoList<SparkUpgradeSysConfigBean>) request.getAttribute("result");
        List<SparkUpgradeSysConfigBean> configList = result.getList();
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <a href="index.jsp" class="ui-btn ui-icon-back ui-btn-icon-left">Back</a>
        <h1>Spark升级-系统配置类</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div>
    <div role="main" class="ui-content jqm-content">
        <form action="/sparkUpgradeSysConfig" method="post">
            <label for="configKey">系统配置key(支持多个key，用逗号或分号分隔):</label>
            <input type="text" name="configKey" id="configKey" value="">
            <label for="configKey">系统配置value(只支持单个value): key有多个值，value只有一个值时，则所有key都会使用同一个value</label>
            <input type="text" name="configValue" id="configValue" value="">
            <label for="description">系统配置描述(简单描述配置key/value的作用)</label>
            <input type="text" name="description" id="description" value="">
            <input type="submit" value="提交">
            <h2>注意事项</h2>
            <h3 class="ui-title">数据库中不存在的key会自动新增</h3>
        </form>
        
        <div>
            <table data-role="table" id="movie-table" data-filter="false" data-mode="columntoggle" class="ui-responsive table-stroke">
                <thead>
                <tr class="ui-bar-d">
                    <th data-priority="1">配置id</th>
                    <th data-priority="1">配置描述</th>
                    <th data-priority="1">配置Key</th>
                    <th data-priority="1">配置Value</th>
                    <th data-priority="1">创建时间</th>
                    <th data-priority="1">操作人</th>
                </tr>
                </thead>
                <tbody>
                <%
                    if(configList != null) {
                        for(int i = 0; i < configList.size(); i++) {
                            SparkUpgradeSysConfigBean item = configList.get(i);
                            Long id = item.getId();
                            String description = item.getDescription();
                            String key = item.getConfigKey();
                            String value = item.getConfigValue();
                            String createTime = item.getCreateTime();
                            String erp = item.getErp();
                %>
                <tr>
                    <td><%=id%></td>
                    <td><%=description%></td>
                    <td><%=key%></td>
                    <td><%=value%></td>
                    <td><%=createTime%></td>
                    <td><%=erp%></td>
                </tr>
                <%
                        }
                    }
                %>
                </tbody>
            </table>
        </div>

    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>