<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="org.apache.http.impl.client.HttpClients" %>
<%@ page import="org.apache.http.impl.client.CloseableHttpClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="com.alibaba.fastjson.JSONArray" %>
<%@ page import="com.jd.bdp.spark.web.DateTimeUtils" %>
<%@ page import="com.jd.bdp.spark.web.SimpleHttpClient" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.Collections" %>
<%@ page import="java.util.Comparator" %>
<%@ page import="java.util.List" %>
<%@ page import="java.lang.NumberFormatException" %>
<%@ page import="java.text.DecimalFormat" %>
<%@ page import="com.jd.bdp.common.JimDBUtils" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
    String host = "";
    String appId = request.getParameter("appId");
    String startTime = request.getParameter("startTime");
    String radio_cluster = request.getParameter("radio_cluster");
    String sortField = request.getParameter("sortField");
    String sortType = request.getParameter("sortType");
    %>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>数据可观测-任务诊断</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <form action="/dorisController" method="post">
          <label for="textarea">SQL:</label>
          <textarea cols="40" rows="8" name="sql" id="textarea"></textarea>
          <label for="number-pattern">对比天数:</label>
          <input type="number" name="days" pattern="[0-9]*" id="number-pattern" value="1">
          <fieldset data-role="controlgroup" data-type="horizontal">
            <legend>统计之前之后:</legend>
            <input type="radio" name="static-type" id="radio-choice-h-2a" value="before" checked="checked">
            <label for="radio-choice-h-2a">之前</label>
            <input type="radio" name="static-type" id="radio-choice-h-2b" value="after">
            <label for="radio-choice-h-2b">之后</label>
          </fieldset>
          <input type="submit" value="查询">
        </form>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>