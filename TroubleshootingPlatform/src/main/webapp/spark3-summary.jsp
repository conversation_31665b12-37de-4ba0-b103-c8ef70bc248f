<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.jd.bdp.spark.web.KongmingService" %>
<%@ page import="com.jd.bdp.scheduler.ForkBuffaloTaskService" %>
<%@ page import="java.util.List" %>
<%@ page import="java.util.Map" %>
<%@ page import="static com.jd.bdp.bean.Constants.SPARK_BUFFALO_TASK_INFO" %>
<%@ page import="static com.jd.bdp.bean.Constants.SPARK_BUFFALO_LOG_INFO" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
</head>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <h1>Spark3双跑统计</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div><!-- header -->
    <div role="main" class="ui-content jqm-content">
        <div data-role="navbar">
            <ul>
                <li><a href="spark3-summary.jsp" class="ui-btn-active">Summary</a></li>
                <li><a href="spark3-tasks.jsp">Spark3 Tasks</a></li>
                <li><a href="spark3-details.jsp">Spark3 Logs</a></li>
                <li><a href="spark3-vs.jsp">Spark3 vs Spark2</a></li>
            </ul>
        </div><!-- /navbar -->
        <%
        List<Map<String, Object>> taskStatusCntMap = KongmingService.executeSql("select status_code, status, count(1) as cnt from "+SPARK_BUFFALO_TASK_INFO+" group by status_code, status", null);
        List<Map<String, Object>> statusCntMap = KongmingService.executeSql("select runStatus,count(1) as cnt from "+SPARK_BUFFALO_LOG_INFO+" group by runStatus", null);
        List<Map<String, Object>> sparkVersionCntMap = KongmingService.executeSql("select spark_version,count(1) as cnt from "+SPARK_BUFFALO_LOG_INFO+" group by spark_version", null);
        List<Map<String, Object>> compare3and2Summary = ForkBuffaloTaskService.compare3and2ByTemplate("compare2And3Summary");
        List<Map<String, Object>> compare3and2Regression = ForkBuffaloTaskService.compare3and2ByTemplate("compare2And3Regression");
        List<Map<String, Object>> checkDataSummary = ForkBuffaloTaskService.compare3and2ByTemplate("checkDataSummary");

        Object o = KongmingService.executeSql("select count(1) as count from "
                + SPARK_BUFFALO_TASK_INFO, null).get(0).get("count");
        int taskCount = o == null ? 0 : Integer.parseInt(o.toString());
        %>
        <h3>Task Status Summary</h3>
        <table data-role="table" id="table-4" data-mode="reflow" class="ui-body-d ui-shadow table-stripe ui-responsive">
          <thead>
            <tr class="ui-bar-d">
                <th>status_code</th>
                <th>status</th>
                <th>count</th>
                <th>percent</th>
            </tr>
          </thead>
          <tbody>
            <%
            for (Map<String, Object> row: taskStatusCntMap) {
            %>
            <tr>
                <td><%=row.get("status_code")%></td>
                <td><%=row.get("status")%></td>
                <td><%=row.get("cnt")%></td>
                <td><%=taskCount == 0 ? "0%" : ((Integer.parseInt(row.get("cnt").toString()) * 100 / taskCount) + "%") %></td>
            </tr>
            <%
            }
            %>
          </tbody>
        </table>

        <h3>Log Status Summary</h3>
        <table data-role="table" id="table-2" data-mode="reflow" class="ui-body-d ui-shadow table-stripe ui-responsive">
          <thead>
            <tr class="ui-bar-d">
                <th>run_status</th>
                <th>count</th>
            </tr>
          </thead>
          <tbody>
            <%
            for (Map<String, Object> row: statusCntMap) {
            %>
            <tr>
                <td><%=row.get("runStatus")%></td>
                <td><%=row.get("cnt")%></td>
            </tr>
            <%
            }
            %>
          </tbody>
        </table>

        <h3>Log Version Summary</h3>
        <table data-role="table" id="table-3" data-mode="reflow" class="ui-body-d ui-shadow table-stripe ui-responsive">
          <thead>
            <tr class="ui-bar-d">
                <th>Spark Version</th>
                <th>count</th>
            </tr>
          </thead>
          <tbody>
            <%
            for (Map<String, Object> row: sparkVersionCntMap) {
            %>
            <tr>
                <td><%=row.get("spark_version")%></td>
                <td><%=row.get("cnt")%></td>
            </tr>
            <%
            }
            %>
          </tbody>
        </table>

        <h3>Compare 2 And 3 Summary</h3>
        <table data-role="table" id="table-4" data-mode="reflow" class="ui-body-d ui-shadow table-stripe ui-responsive">
          <thead>
            <tr class="ui-bar-d">
                <th>Spark 2 Duration</th>
                <th>Spark 3 Duration</th>
                <th>Duration Ratio(Spark3/Spark2)</th>
            </tr>
          </thead>
          <tbody>
            <%
            for (Map<String, Object> row: compare3and2Summary) {
            %>
            <tr>
                <td><%=row.get("sum_duration_2")%></td>
                <td><%=row.get("sum_duration_3")%></td>
                <td><%=row.get("duration_rate")%>%</td>
            </tr>
            <%
            }
            %>
          </tbody>
        </table>
        <h3>Compare 2 And 3 Regression Ratio</h3>
        <table data-role="table" id="table-5" data-mode="reflow" class="ui-body-d ui-shadow table-stripe ui-responsive">
          <thead>
            <tr class="ui-bar-d">
                <th>Total Records</th>
                <th>Slow records with Spark 3.x</th>
                <th>Slow Ratio(Spark3/Total)</th>
            </tr>
          </thead>
          <tbody>
            <%
            for (Map<String, Object> row: compare3and2Regression) {
            %>
            <tr>
                <td><%=row.get("total")%></td>
                <td><%=row.get("slow")%></td>
                <td><%=row.get("ratio")%>%</td>
            </tr>
            <%
            }
            %>
          </tbody>
        </table>
        <h3>CheckData Summary</h3>
        <table data-role="table" id="table-6" data-mode="reflow" class="ui-body-d ui-shadow table-stripe ui-responsive">
          <thead>
            <tr class="ui-bar-d">
                <th>CheckDataCode</th>
                <th>count</th>
            </tr>
          </thead>
          <tbody>
            <%
            for (Map<String, Object> row: checkDataSummary) {
            %>
            <tr>
                <td><%=row.get("checkData")%></td>
                <td><%=row.get("checkCnt")%></td>
            </tr>
            <%
            }
            %>
          </tbody>
        </table>
	</div>
</div>
</body>
</html>