<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="org.apache.http.impl.client.HttpClients" %>
<%@ page import="org.apache.http.impl.client.CloseableHttpClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="com.alibaba.fastjson.JSONArray" %>
<%@ page import="com.jd.bdp.common.CommonUtil" %>
<%@ page import="com.jd.bdp.spark.web.DateTimeUtils" %>
<%@ page import="com.jd.bdp.spark.web.SimpleHttpClient" %>
<%@ page import="com.jd.common.util.StringUtils" %>
<%@ page import="com.jd.bdp.common.JimDBUtils" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="java.util.Collections" %>
<%@ page import="java.util.Comparator" %>
<%@ page import="java.util.Date" %>
<%@ page import="java.util.List" %>
<%@ page import="java.lang.Long" %>
<%@ page import="java.lang.NumberFormatException" %>
<%@ page import="java.text.DecimalFormat" %>
<%@ page import="com.jd.bdp.filter.SSOFilterImpl" %>
<%@ page import="com.jd.bdp.bean.SparkUpgradeTaskBean" %>
<%@ page import="com.jd.bdp.bean.PageInfoList" %>

<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/include/jqueryMobile.html"/>
    <%
        String site = request.getAttribute("site") == null ? "dp.jd.com" : request.getAttribute("site").toString();
        String taskIds = (String)request.getAttribute("taskIds") != null ? (String)request.getAttribute("taskIds") : "";
        String status =(String) request.getAttribute("status") != null ? (String) request.getAttribute("status") : "" ;
        System.out.println("====status:"+status);
        PageInfoList<SparkUpgradeTaskBean> result = (PageInfoList<SparkUpgradeTaskBean>) request.getAttribute("result");
        Integer currentPage = result.getCurrentPage();
        Integer pageSize = result.getPageSize();
        Long totalPage = result.getTotalPage();
        Long totalSize = result.getTotalSize();
        List<SparkUpgradeTaskBean> taskList = result.getList();
    %>
</head>
<script>
    function selectPageSize() {
        var selectBox = document.getElementById("pageSize");
        var newPageSize = selectBox.options[selectBox.selectedIndex].value;
        if (newPageSize != "") {
            var status = "<%=status%>";
            var currentPage = "<%=currentPage%>";
            var taskIds = "<%=taskIds%>";
            console.log(status);
            console.log(currentPage);
            console.log(taskIds);
            console.log(newPageSize);
            // window.location.href = "/sparkUpgradeTaskList?currentPage="+ currentPage + "&taskIds="+ taskIds+ "&status="+ status+ "&pageSize=" + newPageSize;
        }
    }
</script>
<body>
<div data-role="page" data-quicklinks="true">
    <div data-role="header" class="jqm-header" data-position="fixed">
        <a href="index.jsp" class="ui-btn ui-icon-back ui-btn-icon-left">Back</a>
        <h1>Spark升级-任务信息汇总</h1>
        <jsp:include page="/include/header_statistics.jsp"/>
    </div>
    <div role="main" class="ui-content jqm-content">
        <form action="/sparkUpgradeTaskList" method="get">
            <fieldset data-role="controlgroup" data-type="horizontal">
                <legend>任务状态:</legend>
                <input type="radio" name="status" id="radio-choice-h-2a" value="UPGRADED" <% if("UPGRADED".equals(status)) {%> checked="checked"<%}%> >
                <label for="radio-choice-h-2a">已升级</label>
                <input type="radio" name="status" id="radio-choice-h-2b" value="ROLLBACKED" <% if("ROLLBACKED".equals(status)) {%> checked="checked"<%}%> >
                <label for="radio-choice-h-2b">已回滚</label>
                <input type="radio" name="status" id="radio-choice-h-2c" value="" <% if("".equals(status)) {%> checked="checked"<%}%> >
                <label for="radio-choice-h-2c">ALL</label>
            </fieldset>
            <label for="taskIds">Buffalo4任务ID(支持多个任务id，用逗号或分号分隔):</label>
            <input type="text" name="taskIds" id="taskIds" value="<%=taskIds%>">
            <label for="pageSize">选择每页大小</label>
            <select name="pageSize" id="pageSize" onchange="selectPageSize();">
                <option value="20" <%= "20".equals(pageSize.toString()) ? "selected" : "" %>>20</option>
                <option value="50" <%= "50".equals(pageSize.toString()) ? "selected" : "" %>>50</option>
                <option value="100" <%= "100".equals(pageSize.toString()) ? "selected" : "" %>>100</option>
                <option value="200" <%= "200".equals(pageSize.toString()) ? "selected" : "" %>>200</option>
                <option value="500" <%= "500".equals(pageSize.toString()) ? "selected" : "" %>>500</option>
            </select>
            <input type="hidden" name="currentPage" value="<%=currentPage%>"/>
            <input type="submit" value="查询">
        </form>
        
        <div align="right">
            <a href="/sparkUpgradeTaskList?currentPage=1&pageSize=<%=pageSize%>&taskIds=<%=taskIds%>&status=<%=status%>" class="ui-btn ui-btn-inline ui-shadow">首页</a>
            <a href="/sparkUpgradeTaskList?currentPage=<%=currentPage - 1%>&pageSize=<%=pageSize%>&taskIds=<%=taskIds%>&status=<%=status%>" class="ui-btn ui-btn-inline ui-shadow">上一页</a>
            当前<%=currentPage%>页&nbsp;
            每页<%=pageSize%>条&nbsp;
            共<%=totalPage%>页数&nbsp;
            共<%=totalSize%>条数
            <a href="/sparkUpgradeTaskList?currentPage=<%=currentPage + 1%>&pageSize=<%=pageSize%>&taskIds=<%=taskIds%>&status=<%=status%>" class="ui-btn ui-btn-inline ui-shadow">下一页</a>
            <a href="/sparkUpgradeTaskList?currentPage=<%=totalPage%>&pageSize=<%=pageSize%>&taskIds=<%=taskIds%>&status=<%=status%>" class="ui-btn ui-btn-inline ui-shadow">最后一页</a>
        </div>
        
        <div>
            <table data-role="table" id="movie-table" data-filter="false" data-mode="columntoggle" class="ui-responsive table-stroke">
                <thead>
                <tr class="ui-bar-d">
                    <th data-priority="1">任务id</th>
                    <th data-priority="1">引擎原始版</th>
                    <th data-priority="1">升级状态</th>
                    <th data-priority="1">创建时间</th>
                    <th data-priority="1">创建人</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <%
                    if(taskList != null) {
                        for(int i = 0; i < taskList.size(); i++) {
                            SparkUpgradeTaskBean task = taskList.get(i);
                            Integer taskId = task.getTaskId();
                            String originVersion = task.getOriginVersion();
                            String taskStatus = task.getStatus();
                            String createTime = task.getCreateTime();
                            String creator = task.getCreator();
                %>
                <tr>
                    <td><a target="_blank" href="http://<%=site%>/buffalo4/task/detail.html?taskId=<%=taskId%>"><%=taskId%></a></td>
                    <td><%=originVersion%></td>
                    <td><%=taskStatus%></td>
                    <td><%=createTime%></td>
                    <td><%=creator%></td>
                    <td>
                        <fieldset data-role="controlgroup" data-type="horizontal" data-mini="true">
                            <a target="_blank" class="ui-shadow ui-btn ui-corner-all" href="/sparkUpgradeTaskOperate?taskId=<%=taskId%>&opType=upgrade">升级</a>
                            <a target="_blank" class="ui-shadow ui-btn ui-corner-all" href="/sparkUpgradeTaskOperate?taskId=<%=taskId%>&opType=rollback">回滚</a>
                        </fieldset>
                    </td>
                </tr>
                <%
                        }
                    }
                %>
                </tbody>
            </table>    
        </div>

        <div align="right">
            <a href="/sparkUpgradeTaskList?currentPage=1&pageSize=<%=pageSize%>&taskIds=<%=taskIds%>&status=<%=status%>" class="ui-btn ui-btn-inline ui-shadow">首页</a>
            <a href="/sparkUpgradeTaskList?currentPage=<%=currentPage - 1%>&pageSize=<%=pageSize%>&taskIds=<%=taskIds%>&status=<%=status%>" class="ui-btn ui-btn-inline ui-shadow">上一页</a>
            当前为<%=currentPage%>页&nbsp;
            每页<%=pageSize%>条&nbsp;
            共<%=totalPage%>页数&nbsp;
            共<%=totalSize%>条数
            <a href="/sparkUpgradeTaskList?currentPage=<%=currentPage + 1%>&pageSize=<%=pageSize%>&taskIds=<%=taskIds%>&status=<%=status%>" class="ui-btn ui-btn-inline ui-shadow">下一页</a>
            <a href="/sparkUpgradeTaskList?currentPage=<%=totalPage%>&pageSize=<%=pageSize%>&taskIds=<%=taskIds%>&status=<%=status%>" class="ui-btn ui-btn-inline ui-shadow">最后一页</a>
        </div>
    </div><!-- main -->
    <div data-role="footer" data-position="fixed">
        <h1>Power By SparkTeam!</h1>
    </div>
</div>
</body>
</html>