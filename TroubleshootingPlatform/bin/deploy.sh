#!/bin/sh
set -x

rz

export JDHXXXXX_USER=dd_edw
export JDHXXXXX_CLUSTER_NAME=10k
export JDHXXXXX_QUEUE=bdp_jdw_dd_edw_union.bdp_jdw_dd_edw_rir
source /software/servers/env/env.sh

export JRE_HOME=/software/servers/jdk1.8.0_121/
export PATH=$JAVA_HOME/bin:$PATH

./bin/shutdown.sh

sleep 5

\rm -rf webapps/ROOT/*
\rm -rf logs/*

mv SparkTroubleshooting.war webapps/ROOT/

cd webapps/ROOT/

jar xvf SparkTroubleshooting.war

\rm -rf SparkTroubleshooting.war

cd ../../

cp authenticate.yaml webapps/ROOT/WEB-INF/classes/authenticate.yaml

./bin/startup.sh

tail -f logs/*