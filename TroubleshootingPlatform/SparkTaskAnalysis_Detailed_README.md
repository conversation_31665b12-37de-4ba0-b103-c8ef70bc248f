# Spark Application Task 分析工具 (详细版)

## 功能概述

这个工具用于分析 Spark Application 的 task 执行情况，支持直接输入具体的 Spark Application API 地址，并提供详细的 task 信息展示。

## 主要功能

### 基础统计
- 统计应用的总 task 数量
- 统计推测执行的 task 数量
- 统计重试的 task 数量
- 计算相关比例

### 详细信息展示 ⭐ 新增功能
- **推测执行 Task 详细信息**：显示每个推测执行 task 的 Task ID、Stage ID、Attempt ID、Index、Status、Duration
- **重试 Task 详细信息**：显示每个重试 task 的 Task ID、Stage ID、Attempt ID、Attempt、Index、Status、Duration

## 支持格式

工具支持以下 URL 格式：

1. **API 格式**：
   ```
   http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855
   ```

2. **History 格式**：
   ```
   http://host:port/history/application_xxx_xxx
   ```

## 使用方法

### 1. 访问分析页面

```
http://your-server:port/TroubleshootingPlatform/sparkTaskAnalysis.jsp
```

### 2. 输入 Spark Application API URL

直接输入完整的 Application API 地址，例如：
```
http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855
```

### 3. 点击"开始分析"

系统会自动：
- 从 URL 中提取 Application ID
- 获取 Application 基本信息
- 获取所有 Stages 信息
- **过滤掉 SKIPPED 状态的 Stage**
- 分析每个有效 Stage 的 Task 详情
- 统计推测执行和重试的 Task 数量
- **收集详细的 Task 信息**

### 4. 查看分析结果

结果包括：
- **Application 基本信息**：ID、名称、API URL、处理的 Stages 数量
- **Task 统计信息**：总数量、推测执行数量、重试数量、相关比例
- **推测执行 Task 详细信息表格**：包含 Task ID、Stage ID、Attempt ID、Index、Status、Duration
- **重试 Task 详细信息表格**：包含 Task ID、Stage ID、Attempt ID、Attempt、Index、Status、Duration

## API 接口

### POST /sparkTaskAnalysis

**请求参数：**
- `historyUrl`: Spark Application API URL

**响应格式：**
```json
{
  "success": true,
  "message": "分析完成",
  "data": {
    "appId": "application_1745827167801_22878855",
    "appName": "My Spark App",
    "historyUrl": "http://10k-zd.sparkhs-3.jd.com/api/v1/applications/application_1745827167801_22878855",
    "totalTasks": 1000,
    "speculativeTasks": 50,
    "retryTasks": 10,
    "processedStages": 5,
    "totalStages": 5,
    "speculativeTaskDetails": [
      {
        "taskId": 12345,
        "stageId": 0,
        "attemptId": 0,
        "index": 0,
        "status": "SUCCESS",
        "duration": 1500
      }
    ],
    "retryTaskDetails": [
      {
        "taskId": 12346,
        "stageId": 1,
        "attemptId": 0,
        "attempt": 1,
        "index": 1,
        "status": "SUCCESS",
        "duration": 2000
      }
    ]
  }
}
```

## 详细信息字段说明

### 推测执行 Task 详细信息
- **Task ID**: 任务的唯一标识符
- **Stage ID**: 任务所属的 Stage ID
- **Attempt ID**: Stage 的尝试 ID
- **Index**: 任务在 Stage 中的索引
- **Status**: 任务执行状态（SUCCESS、FAILED、RUNNING 等）
- **Duration**: 任务执行时长（毫秒）

### 重试 Task 详细信息
- **Task ID**: 任务的唯一标识符
- **Stage ID**: 任务所属的 Stage ID
- **Attempt ID**: Stage 的尝试 ID
- **Attempt**: 任务的尝试次数
- **Index**: 任务在 Stage 中的索引
- **Status**: 任务执行状态
- **Duration**: 任务执行时长（毫秒）

## 技术实现

### 核心改进

1. **详细信息收集**：在分析过程中收集每个推测执行和重试 task 的详细信息
2. **表格展示**：使用响应式表格展示详细信息
3. **条件显示**：只有当存在推测执行或重试 task 时才显示相应的详细信息表格
4. **SKIPPED Stage 过滤**：自动忽略状态为 "SKIPPED" 的 Stage，只分析有效的 Stage

### 数据收集逻辑

```java
// 遍历所有 stages，过滤掉 SKIPPED 状态的 stage
for (int i = 0; i < stages.size(); i++) {
    JSONObject stage = stages.getJSONObject(i);
    
    // 忽略 SKIPPED 状态的 stage
    String stageStatus = stage.getString("status");
    if ("SKIPPED".equals(stageStatus)) {
        logger.info("跳过 SKIPPED 状态的 Stage: " + stage.getIntValue("stageId"));
        continue;
    }
    
    // 获取每个 stage 的详细 task 信息
    int stageId = stage.getIntValue("stageId");
    int attemptId = stage.getIntValue("attemptId");
    
    // 分页获取所有 task 信息
    List<JSONObject> allTasks = new ArrayList<>();
    int offset = 0;
    int pageSize = 100; // Spark History Server 每页返回 100 个 task
    
    while (true) {
        String tasksUrl = applicationUrl + "/stages/" + stageId + "/" + attemptId + "/taskList?offset=" + offset + "&length=" + pageSize;
        
        try {
            String tasksResponse = SimpleHttpClient.sendRequest(httpClient, requestConfig, tasksUrl, "SparkTaskAnalysis.getTasks");
            JSONArray tasks = JSON.parseArray(tasksResponse);
            
            if (tasks == null || tasks.isEmpty()) {
                break; // 没有更多 task
            }
            
            // 收集当前页的 task
            for (int j = 0; j < tasks.size(); j++) {
                allTasks.add(tasks.getJSONObject(j));
            }
            
            if (tasks.size() < pageSize) {
                break; // 最后一页
            }
            
            offset += pageSize; // 下一页
        } catch (Exception e) {
            logger.error("获取 Stage " + stageId + " 的 Task 信息失败", e);
            break;
        }
    }
    
    // 处理收集到的所有 task
    for (JSONObject task : allTasks) {
        // 分析推测执行和重试 task...
    }
}

// 收集推测执行 task 的详细信息
if (task.getBooleanValue("speculative")) {
    Map<String, Object> taskDetail = new HashMap<>();
    taskDetail.put("taskId", task.getLongValue("taskId"));
    taskDetail.put("stageId", stageId);
    taskDetail.put("attemptId", attemptId);
    taskDetail.put("index", task.getIntValue("index"));
    taskDetail.put("status", task.getString("status"));
    taskDetail.put("duration", task.getLongValue("duration"));
    speculativeTaskDetails.add(taskDetail);
}

// 收集重试 task 的详细信息
if (task.getIntValue("attempt") > 0) {
    Map<String, Object> taskDetail = new HashMap<>();
    taskDetail.put("taskId", task.getLongValue("taskId"));
    taskDetail.put("stageId", stageId);
    taskDetail.put("attemptId", attemptId);
    taskDetail.put("attempt", task.getIntValue("attempt"));
    taskDetail.put("index", task.getIntValue("index"));
    taskDetail.put("status", task.getString("status"));
    taskDetail.put("duration", task.getLongValue("duration"));
    retryTaskDetails.add(taskDetail);
}
```

## 界面特性

### 表格功能
- **响应式设计**：支持水平滚动，适应不同屏幕尺寸
- **悬停效果**：鼠标悬停时高亮显示行
- **条件显示**：只有当存在相应数据时才显示表格
- **清晰布局**：使用不同颜色区分表头和内容

### 用户体验
- **加载状态**：分析过程中显示加载提示
- **错误处理**：完善的错误信息展示
- **数据验证**：输入验证和格式检查

## 测试

### 测试页面

访问测试页面验证功能：
```
http://your-server:port/TroubleshootingPlatform/sparkTaskAnalysisTest.jsp
```

### 测试用例

1. **有推测执行 task 的应用**：
   - 验证推测执行详细信息表格是否正确显示
   - 检查表格数据是否准确

2. **有重试 task 的应用**：
   - 验证重试详细信息表格是否正确显示
   - 检查 Attempt 字段是否正确

3. **无异常 task 的应用**：
   - 验证详细信息表格是否隐藏

4. **包含 SKIPPED stage 的应用**：
   - 验证 SKIPPED stage 是否被正确过滤
   - 检查日志中是否记录了被跳过的 Stage ID
   - 确认统计结果只包含有效 Stage 的 Task

5. **包含大量 task 的 stage**：
   - 验证是否能够通过分页查询获取到所有 task 信息（每页 100 个）
   - 检查分页查询的日志记录是否正确
   - 检查推测执行和重试 task 的统计是否完整
   - 确认详细信息表格包含所有相关 task

## 部署说明

1. 确保项目依赖正确配置
2. 部署到 Web 容器（如 Tomcat）
3. 确保网络可以访问 Spark History Server
4. 配置适当的防火墙规则

## API 调用优化

### Task 信息获取优化

在获取 Stage 的 Task 详细信息时，发现了一个重要的优化点：

**问题**：
- 使用 `/stages/{stageId}/{attemptId}/taskList` 只能返回前 20 个 task 信息
- 对于包含大量 task 的 stage，会丢失大部分 task 数据

**解决方案**：
- 使用分页查询，通过 `offset` 和 `length` 参数获取所有 task 信息
- 循环查询直到没有更多 task 返回为止

**分页查询实现**：
```java
// 分页获取所有 task 信息
List<JSONObject> allTasks = new ArrayList<>();
int offset = 0;
int pageSize = 100; // Spark History Server 每页返回 100 个 task

while (true) {
    String tasksUrl = applicationUrl + "/stages/" + stageId + "/" + attemptId + "/taskList?offset=" + offset + "&length=" + pageSize;
    
    // 获取当前页的 task 信息
    JSONArray tasks = JSON.parseArray(tasksResponse);
    
    if (tasks == null || tasks.isEmpty()) {
        // 没有更多 task，退出循环
        break;
    }
    
    // 将当前页的 task 添加到总列表中
    for (int j = 0; j < tasks.size(); j++) {
        allTasks.add(tasks.getJSONObject(j));
    }
    
    // 如果返回的 task 数量少于 pageSize，说明已经是最后一页
    if (tasks.size() < pageSize) {
        break;
    }
    
    // 继续下一页
    offset += pageSize;
}
```

**分页参数说明**：
- `offset`: 起始位置，从 0 开始
- `length`: 每页返回的 task 数量，设置为 100 以提高查询效率

**pageSize 优化说明**：
- 原始默认值：20 个 task/页
- 优化后：100 个 task/页
- 优化效果：减少分页查询次数，提高数据获取效率
- 适用场景：特别适合包含大量 task 的 Spark 应用

**分页查询流程**：
1. 从 offset=0 开始查询第一页
2. 检查返回的 task 数量
3. 如果返回数量等于 pageSize，继续查询下一页
4. 如果返回数量小于 pageSize 或为空，说明已到最后一页
5. 重复步骤 2-4 直到获取所有 task

**错误处理**：
- 如果某次分页查询失败，记录警告日志并停止该 stage 的查询
- 继续处理其他 stage，不中断整个分析过程
- 确保已获取的 task 数据不会丢失

**影响**：
- **数据完整性**：现在可以获取到所有 task 的详细信息
- **统计准确性**：推测执行和重试 task 的统计更加准确
- **分析全面性**：能够分析所有 task 的执行情况
- **性能优化**：通过分页减少单次请求的数据量

## SKIPPED Stage 处理

### 为什么过滤 SKIPPED Stage？

在 Spark 应用中，某些 Stage 可能因为以下原因被标记为 "SKIPPED" 状态：

1. **缓存命中**：Stage 的计算结果已经被缓存，无需重新计算
2. **优化策略**：Spark 优化器决定跳过某些 Stage
3. **依赖关系**：某些 Stage 由于依赖关系被跳过

### 过滤逻辑

工具会自动检查每个 Stage 的状态，并跳过 "SKIPPED" 状态的 Stage：

```java
// 忽略 SKIPPED 状态的 stage
String stageStatus = stage.getString("status");
if ("SKIPPED".equals(stageStatus)) {
    logger.info("跳过 SKIPPED 状态的 Stage: " + stage.getIntValue("stageId"));
    continue;
}
```

### 影响

- **统计准确性**：只统计实际执行的 Task，提供更准确的性能分析
- **日志记录**：在日志中记录被跳过的 Stage ID，便于调试
- **性能优化**：避免分析无意义的 Stage，提高分析效率

## 注意事项

1. **网络访问**：确保应用服务器可以访问 Spark History Server
2. **URL 格式**：确保输入的 URL 包含完整的 Application ID
3. **权限要求**：History Server 需要允许外部访问
4. **性能考虑**：大量 Task 的应用可能需要较长分析时间
5. **数据量**：详细信息表格可能包含大量数据，建议在浏览器中启用滚动

## 错误处理

### 常见错误

1. **URL 格式错误**
   - 错误信息：请输入有效的 HTTP/HTTPS URL
   - 解决方案：检查 URL 格式是否正确

2. **无法提取 Application ID**
   - 错误信息：无法从 URL 中提取 Application ID
   - 解决方案：确保 URL 包含 application_xxx_xxx 格式的 ID

3. **无法访问 API**
   - 错误信息：无法获取 Application 信息
   - 解决方案：检查网络连接和 API 地址

4. **API 响应异常**
   - 错误信息：无法获取 Stages 信息
   - 解决方案：检查 Spark 版本兼容性

## 扩展功能

可以考虑添加的功能：

1. **排序功能**：支持按不同字段排序表格
2. **搜索功能**：支持在详细信息中搜索特定 task
3. **导出功能**：支持将详细信息导出为 CSV 或 Excel
4. **分页功能**：支持大量数据的分页显示
5. **图表展示**：使用图表可视化 task 分布情况

### Spark History Server API

工具使用以下 Spark History Server REST API：

- `GET /api/v1/applications/{appId}` - 获取应用信息
- `GET /api/v1/applications/{appId}/stages` - 获取 Stages 信息
- `GET /api/v1/applications/{appId}/stages/{stageId}/{attemptId}/taskList?offset={offset}&length={length}` - 分页获取 Task 详情 