#!/bin/bash

#set -x

date

rss_conf_dir='/home/<USER>/rss/spark-defaults.conf'
nm_conf_dir='/software/servers/yarn-2.7.1/etc/hadoop/yarn-site.xml'

baizepg='http://baizepg.jd.local:2000/api/put?service_id=55600fc1-5749-4db3-b5d9-0e39ce9472e2&data_fmt=opentsdb'

# for test (prometheus: http://t.bz-pr.jd.local)
baizepgTest='http://t.bz-pg.jd.local/api/put?service_id=test_opentsdb'

host="$(hostname --ip-address)"
timestamp="$(date +%s%3N)"

backlog_recvq_dropped="$(netstat -as | fgrep 'sockets dropped' | awk '{print $1}')"
backlog_sendq_overflowed="$(netstat -as | fgrep 'socket overflowed' | awk '{print $1}')"

function sendToBaize() {
	local key="$1"
	local value="$2"
	local cluster="$3"
	local serviceType="$4"
	local namespace="$5"
	local port="$6"

	curl -H "Content-Type: application/json" -X POST \
	-d "[
	    {
	        \"metric\": \"${key}\",
	        \"timestamp\": \"${timestamp}\",
	        \"value\": \"${value}\",
	        \"tags\":
	        {
	            \"host\": \"${host}\",
	            \"serviceId\": \"55600fc1-5749-4db3-b5d9-0e39ce9472e2\",
	            \"cluster\": \"${cluster}\",
	            \"serviceType\": \"${serviceType}\",
	            \"namespace\": \"${namespace}\",
	            \"port\": \"${port}\"
	        }
	    }
	]" "$baizepg"
}

function execute() {
  local cluster="$1"
  local port="$2"
  local serviceType="$3"
  local namespace="$4"

  backlog_port_recvq="$(ss -ln src :$port | grep -v 'Netid' | awk '{print $3}')"
  backlog_port_sendq="$(ss -ln src :$port | grep -v 'Netid' | awk '{print $4}')"

  sendToBaize 'backlog_port_recvq' "$backlog_port_recvq" "$cluster" "$serviceType" "$namespace" "$port"
  sendToBaize 'backlog_port_sendq' "$backlog_port_sendq" "$cluster" "$serviceType" "$namespace" "$port"
  sendToBaize 'backlog_recvq_dropped' "$backlog_recvq_dropped" "$cluster" "$serviceType" "$namespace" "$port"
  sendToBaize 'backlog_sendq_overflowed' "$backlog_sendq_overflowed" "$cluster" "$serviceType" "$namespace" "$port"
}


if [ -f $rss_conf_dir ];then
	namespace="$(cat $rss_conf_dir | fgrep 'spark.shuffle.rss.etcd.namespace' | awk -F = '{print $2}')"
  cluster="$(cat $rss_conf_dir | fgrep 'spark.JDHXXXXX_CLUSTER_NAME' | awk -F = '{print $2}')"
  port="$(cat $rss_conf_dir | fgrep 'spark.shuffle.service.port' | awk -F = '{print $2}')"

  execute "$cluster" "$port" 'rss' "$namespace"
fi

if [ -f $nm_conf_dir ];then
  cluster="$(cat $nm_conf_dir | grep -A1 "yarn.resourcemanager.cluster-id" | sed -n '2p' | awk -v FS='<value>' '{print $2}' | awk -v FS='</value>' '{print $1}')"

  execute "$cluster" '7337' 'ess' "$cluster-ess"
  execute "$cluster" '13562' 'mrShuffle' "$cluster-mrShuffle"
fi
