package com.jd.bdp.engine

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 *
 */
object DateTimeConverter {
  def convert(dateTimeString :String): String = {
    if(Option(dateTimeString).isEmpty){
      ""
    } else {
      val dateTimeFormatter = DateTimeFormatter.ofPattern(if(dateTimeString.length > 19) "yyyy-MM-dd HH:mm:ss.S" else "yyyy-MM-dd HH:mm:ss")
      val localDateTime = LocalDateTime.parse(dateTimeString, dateTimeFormatter)
      val localDate = localDateTime.toLocalDate
      val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
      localDate.format(dateFormatter)
    }
  }
}
