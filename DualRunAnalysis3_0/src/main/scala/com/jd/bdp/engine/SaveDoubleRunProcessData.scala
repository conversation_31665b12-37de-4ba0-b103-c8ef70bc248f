package com.jd.bdp.engine

import org.apache.spark.sql.{SaveMode, SparkSession}
import org.apache.spark.sql.functions.lit

import java.time.LocalDate
import java.time.format.DateTimeFormatter

object SaveDoubleRunProcessData {

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "5g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.shuffle.targetPostShuffleInputSize", "41943040")
      .appName("DeleteDualRunTables App")
      .enableHiveSupport()
      .getOrCreate()
    val data = spark.read.jdbc(StrConstant.tempJdbcUrl, s"(select * from spark_upgrade_engine_task_tbl) as tmp", StrConstant.tempConnectionProperties)
    data.withColumn("dt",lit(getYesterday())).write.partitionBy("dt").mode(SaveMode.Append).saveAsTable("tmp.spark_upgrade_engine_task_tbl_proc")
    spark.close()
  }

  def getYesterday(): String = {
    val today = LocalDate.now()
    val yesterday = today.minusDays(1)
    yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
  }
}
