package com.jd.bdp.engine

import java.util.Properties

object StrConstant extends Serializable {
  val jdbcUrl = "*****************************************************************************"
  val jdbcUser = "dispatch_1_0_ro"
  val jdbcPassword = "pXRirVcfdZ7SbXQdiFnV4diFdF9OVUCj"
  val connectionProperties: Properties = {
    val props = new Properties()
    props.put("driver", "com.mysql.jdbc.Driver")
    props.put("user", "dispatch_1_0_ro")
    props.put("password", "pXRirVcfdZ7SbXQdiFnV4diFdF9OVUCj")
    props
  }

  val tempJdbcUrl = "***************************************************************************"
  val tempConnectionProperties: Properties = {
    val props = new Properties()
    props.put("driver", "com.mysql.jdbc.Driver")
    props.put("user", "salt")
    props.put("password", "salt")
    props
  }

  val randomFunctionSQL =
    """
      |SELECT
      |	task_id,
      |	group_concat(random_function, ',') AS random_functions
      |FROM
      |	spark_upgrade_engine_task_tbl
      |GROUP BY
      |	task_id
      |HAVING
      |	random_functions IS NOT NULL
      |""".stripMargin

  val errorCodeSQL =
    """
      |SELECT
      |	a.*,
      |	b.error_description,
      |	b.already_fix
      |FROM
      |	spark_upgrade_task_error_rlt AS a
      |LEFT JOIN spark_upgrade_error_code_dict AS b
      |ON
      |	a.error_code = b.error_code
      |""".stripMargin

  val errorMessagesMap: Map[String, String] = Map(
    "nondeterministic expressions are only allowed" -> "nondeterministic",
    "SCHEMA_NOT_FOUND" -> "schema not found",
    "WriteNsWhiteListProxyWrapper" -> "white ns",
    "UNRESOLVED_ROUTINE" -> "unresolved routine",
    "org.apache.spark.SparkFileNotFoundException: File does not exist" -> "file does not exist",
    "requires that the data to be inserted have the same number of columns as the target table" -> "columns num mismatch",
    "TABLE_OR_VIEW_NOT_FOUND" -> "table not found",
    "Operation not allowed: ROW FORMAT DELIMITED is only compatible with" -> "row format",
    "PARSE_SYNTAX_ERROR" -> "syntax error",
    "mismatched input 'wangriyu_test' expecting" -> "multi table",
    "com.jd.compute.compare.Result: (2," -> "code 2",
    "run_spark3.sh: line 128:   479 Killed" -> "cgroup exceed",
    "fields than the actual ORC physical schema" -> "schema check error",
    "java.lang.ExceptionInInitializerError" -> "create temp function error",
    "BDP系统账号(bdp_sys)】 ,超时失败" -> "running timeout",
    "Could not execute broadcast in 600 secs" -> "broadcast timeout"
  )
  val buffaloDualTask =
    """
      |SELECT
      |   *
      |FROM b_def_task AS a
      |WHERE
      |   a.app_group_id = 106020
      |   AND a.status = 3
      |   AND a.disabled = 0
      |   AND a.deleted = 0
      |   AND a.name LIKE 'BUFFALO_%'
      |   AND LENGTH(SUBSTRING_INDEX(a.name, '_', - 1)) <= 3
      |""".stripMargin

  val latestInsId =
    """
      |SELECT
      |    a.id AS dual_run_id,
      |    a.id AS task_def_id,
      |    a.name,
      |    substring_index(substring_index(a.name, '_', 2), '_', - 1) AS origin_buffalo_id,
      |    substring_index(substring_index(a.name, '_', 3), '_', - 1) AS origin_log_id,
      |    a.created,
      |    MAX(e.instance_id) AS latest_ins_id
      |FROM
      |    b_def_task AS a
      |LEFT JOIN b_run_log AS e
      |ON
      |    a.id = e.task_def_id
      |    AND e.instance_type = 1
      |WHERE
      |    a.app_group_id = 106020
      |    AND a.status = 3
      |    AND a.disabled = 0
      |    AND a.deleted = 0
      |    AND a.name LIKE 'BUFFALO_%'
      |    AND LENGTH(SUBSTRING_INDEX(a.name, '_', - 1)) > 5
      |    AND date(e.created) >= date(now())
      |    /*__PLACEHOLD_INSTANCE_NULL_*/
      |GROUP BY
      |    a.id,
      |    a.name,
      |    a.created
      |""".stripMargin

  val dualRunResultCheckSQL =
    """
      |SELECT * FROM dual_run_result_checklist_3
      |""".stripMargin

  val sql =
    """
      |  SELECT
      |    f.name,
      |    f.origin_buffalo_id,
      |    f.origin_log_id,
      |    dual_run_id,
      |    run_2_4,
      |    run_3_4,
      |    CASE
      |    WHEN data_check IS NULL
      |    THEN 'skipped_or_waiting'
      |    ELSE data_check
      |    END AS data_check,
      |    duration_2_4,
      |    duration_3_4,
      |    duration_2_4 - duration_3_4 AS time_saved_3_vs_2,
      |    1 - duration_3_4 / duration_2_4 AS ratio,
      |    duration_check,
      |    f.run_2_4_logid,
      |    f.run_3_4_logid,
      |    CASE WHEN f.run_check_logid IS NULL
      |      THEN '-1'
      |      ELSE f.run_check_logid
      |    END AS run_check_logid
      |  FROM
      |  (
      |  SELECT
      |  e.name,
      |  e.dual_run_id,
      |  e.origin_buffalo_id,
      |  e.origin_log_id,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = '2.4'
      |  THEN log_id
      |  END) AS run_2_4_logid,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = '3.4'
      |  THEN log_id
      |  END) AS run_3_4_logid,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = 'check'
      |  THEN log_id
      |  END) AS run_check_logid,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = '2.4'
      |  THEN run_status
      |  END) AS run_2_4,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = '3.4'
      |  THEN run_status
      |  END) AS run_3_4,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = 'check'
      |  THEN run_status
      |  END) AS data_check,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = '2.4'
      |  THEN e.duration
      |  END) AS duration_2_4,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = '3.4'
      |  THEN e.duration
      |  END) AS duration_3_4,
      |  MAX(
      |  CASE
      |  WHEN e.inst_type = 'check'
      |  THEN e.duration
      |  END) AS duration_check
      |  FROM
      |  (
      |    /*__PLACEHOLDER_LAST_RUN_INFO__*/
      |  ) AS e
      |  GROUP BY
      |  task_def_id,
      |  e.origin_buffalo_id,
      |  e.origin_log_id
      |  ) AS f
      |  WHERE 1 = 1 AND f.run_2_4_logid IS NOT NULL
      |  AND (run_2_4 = 'fail' or run_3_4 = 'fail' or data_check = 'fail')
      |""".stripMargin;
  val lastRunInfo =
    """
      |  SELECT
      |  a.name,
      |  a.origin_buffalo_id,
      |  a.origin_log_id,
      |  a.task_def_id AS dual_run_id,
      |  CASE
      |  WHEN args LIKE '2\_4%%'
      |  THEN '2.4'
      |  WHEN args LIKE '3\_4%%'
      |  THEN '3.4'
      |  WHEN script_path = 'data_compare3.sh'
      |  THEN 'check'
      |  ELSE 'UNKNOW'
      |  END AS inst_type,
      |  f.id AS log_id,
      |  now() - run_time as elapsedSecond,
      |  f.*
      |  FROM
      |  (
      |  /*__PLACEHOLD_MAX_INSTANCE_*/
      |  ) AS a
      |  LEFT JOIN b_run_log AS f
      |  ON
      |  a.task_def_id = f.task_def_id
      |  AND a.latest_ins_id = f.task_ins_id
      |  AND f.instance_type = 2
      |""".stripMargin
  val placehold = "\\/\\*__PLACEHOLD_INSTANCE_NULL_\\*\\/"
  val unexecutedTasks = latestInsId.replaceAll(placehold, " AND e.instance_id IS NULL")
  val executedTasks = latestInsId.replaceAll(placehold, " AND e.instance_id IS NOT NULL")
  val last =
    """
      |SELECT
      |	a.name,
      |	a.origin_buffalo_id,
      |	a.origin_log_id,
      |	a.task_def_id AS dual_run_id,
      |	CASE
      |		WHEN args LIKE '2\_4%%'
      |		THEN '2.4'
      |		WHEN args LIKE '3\_4%%'
      |		THEN '3.4'
      |		WHEN script_path = 'data_compare3.sh'
      |		THEN 'check'
      |		ELSE 'UNKNOW'
      |	END AS inst_type,
      |	f.id AS log_id,
      |	now() - run_time AS elapsedSecond,
      |	f.*
      |FROM
      |	(
      |		SELECT
      |			a.id AS task_def_id,
      |			a.name,
      |			substring_index(substring_index(a.name, '_', 2), '_', - 1) AS origin_buffalo_id,
      |			substring_index(substring_index(a.name, '_', 3), '_', - 1) AS origin_log_id,
      |			a.created,
      |			MAX(e.instance_id) AS latest_ins_id
      |		FROM
      |			b_def_task AS a
      |		LEFT JOIN b_run_log AS e
      |		ON
      |			a.id = e.task_def_id
      |			AND e.instance_type = 1
      |		WHERE
      |			a.app_group_id = 106020
      |			AND a.status = 3
      |			AND a.disabled = 0
      |			AND a.deleted = 0
      |			AND a.name LIKE 'BUFFALO_%'
      |			AND LENGTH(SUBSTRING_INDEX(a.name, '_', - 1)) > 5
      |			AND e.instance_id IS NOT NULL
      |		GROUP BY
      |			a.id,
      |			a.name,
      |			a.created
      |	) AS a
      |LEFT JOIN b_run_log AS f
      |ON
      |	a.task_def_id = f.task_def_id
      |	AND a.latest_ins_id = f.task_ins_id
      |	AND f.instance_type = 2
      |""".stripMargin
  val sql2 =
    """
      |SELECT
      |	f.name,
      |	f.origin_buffalo_id,
      |	f.origin_log_id,
      |	dual_run_id,
      |	run_2_4,
      |	run_3_4,
      |	CASE
      |		WHEN data_check IS NULL
      |		THEN 'skipped_or_waiting'
      |		ELSE data_check
      |	END AS data_check,
      |	duration_2_4,
      |	duration_3_4,
      |	duration_2_4 - duration_3_4 AS time_saved_3_vs_2,
      |	1 - duration_3_4 / duration_2_4 AS ratio,
      |	duration_check,
      |	f.run_2_4_logid,
      |	f.run_3_4_logid,
      |	CASE
      |		WHEN f.run_check_logid IS NULL
      |		THEN '-1'
      |		ELSE f.run_check_logid
      |	END AS run_check_logid
      |FROM
      |	(
      |		SELECT
      |			e.name,
      |			e.dual_run_id,
      |			e.origin_buffalo_id,
      |			e.origin_log_id,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = '2.4'
      |					THEN log_id
      |				END) AS run_2_4_logid,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = '3.4'
      |					THEN log_id
      |				END) AS run_3_4_logid,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = 'check'
      |					THEN log_id
      |				END) AS run_check_logid,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = '2.4'
      |					THEN run_status
      |				END) AS run_2_4,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = '3.4'
      |					THEN run_status
      |				END) AS run_3_4,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = 'check'
      |					THEN run_status
      |				END) AS data_check,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = '2.4'
      |					THEN e.duration
      |				END) AS duration_2_4,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = '3.4'
      |					THEN e.duration
      |				END) AS duration_3_4,
      |			MAX(
      |				CASE
      |					WHEN e.inst_type = 'check'
      |					THEN e.duration
      |				END) AS duration_check
      |		FROM
      |			(
      |				SELECT
      |					a.name,
      |					a.origin_buffalo_id,
      |					a.origin_log_id,
      |					a.task_def_id AS dual_run_id,
      |					CASE
      |						WHEN args LIKE '2_4%%'
      |						THEN '2.4'
      |						WHEN args LIKE '3_4%%'
      |						THEN '3.4'
      |						WHEN script_path = 'data_compare3.sh'
      |						THEN 'check'
      |						ELSE 'UNKNOW'
      |					END AS inst_type,
      |					f.id AS log_id,
      |					now() - run_time AS elapsedSecond,
      |					f.*
      |				FROM
      |					(
      |						SELECT
      |							a.id AS task_def_id,
      |							a.name,
      |							substring_index(substring_index(a.name, '_', 2), '_', - 1) AS origin_buffalo_id,
      |							substring_index(substring_index(a.name, '_', 3), '_', - 1) AS origin_log_id,
      |							a.created,
      |							MAX(e.instance_id) AS latest_ins_id
      |						FROM
      |							b_def_task AS a
      |						LEFT JOIN b_run_log AS e
      |						ON
      |							a.id = e.task_def_id
      |							AND e.instance_type = 1
      |						WHERE
      |							a.app_group_id = 106020
      |							AND a.status = 3
      |							AND a.disabled = 0
      |							AND a.deleted = 0
      |							AND a.name LIKE 'BUFFALO_%'
      |							AND LENGTH(SUBSTRING_INDEX(a.name, '_', - 1)) > 5
      |							AND e.instance_id IS NOT NULL
      |						GROUP BY
      |							a.id,
      |							a.name,
      |							a.created
      |					) AS a
      |				LEFT JOIN b_run_log AS f
      |				ON
      |					a.task_def_id = f.task_def_id
      |					AND a.latest_ins_id = f.task_ins_id
      |					AND f.instance_type = 2
      |			) AS e
      |		GROUP BY
      |			task_def_id,
      |			e.origin_buffalo_id,
      |			e.origin_log_id
      |	) AS f
      |WHERE
      |	1 = 1
      |	AND f.run_2_4_logid IS NOT NULL
      |	AND
      |	(
      |		run_2_4 = 'fail'
      |		OR run_3_4 = 'fail'
      |		OR data_check = 'fail'
      |	)
      |""".stripMargin

  val upgradedAndRollbackTasksSQL =
    """
      |SELECT *, task_id AS origin_task_id FROM spark_upgrade_engine_task_tbl
      |""".stripMargin

  val completedOriginTasks =
    """
      |SELECT
      |	buffalo_id AS origin_task_id
      |FROM
      |	(
      |		SELECT
      |			aa.run_status,
      |			bb.buffalo_id
      |		FROM
      |			b_run_log AS aa
      |		INNER JOIN
      |			(
      |				SELECT
      |					a.id AS task_def_id,
      |					substring_index(substring_index(name, '_', 2), '_', - 1) AS buffalo_id,
      |					MAX(e.instance_id) AS latest_ins_id
      |				FROM
      |					b_def_task a
      |				JOIN b_run_log e
      |				ON
      |					a.id = e.task_def_id
      |					AND e.instance_type = 1
      |				WHERE
      |					a.app_group_id = 106020
      |					AND status = 3
      |					AND disabled = 0
      |					AND a.deleted = 0
      |					AND a.name REGEXP 'BUFFALO_[0-9]+_[0-9]+'
      |				GROUP BY
      |					a.id,
      |					substring_index(substring_index(name, '_', 2), '_', - 1)
      |			) AS bb ON aa.task_def_id = bb.task_def_id
      |			AND aa.instance_id = bb.latest_ins_id
      |	) AS cc
      |GROUP BY
      |	buffalo_id
      |HAVING
      |	COUNT(
      |		CASE
      |			WHEN run_status != 'success'
      |			THEN 1
      |		END) = 0
      |""".stripMargin

  val compareDualRunTimeSql =
    """
      |SELECT
      |	origin_buffalo_id,
      |	COUNT(1) AS dual_run_cnt_action,
      |	SUM(run_2_4_success + run_3_4_success + data_check_success) AS dual_run_all_success,
      |	CASE
      |		WHEN SUM(run_2_4_success + run_3_4_success + data_check_success) = 0
      |		THEN 'all_success'
      |		ELSE 'has_failed'
      |	END AS dual_run_status,
      |	SUM(duration_2_4) AS dual_run_duration_2_4,
      |	SUM(duration_3_4) AS dual_run_duration_3_4,
      |	SUM(duration_check) AS dual_run_duration_check
      |FROM
      |	(
      |		SELECT
      |			CASE
      |				WHEN run_2_4 = 'success'
      |				THEN 0
      |				ELSE 1
      |			END AS run_2_4_success,
      |			CASE
      |				WHEN run_3_4 = 'success'
      |				THEN 0
      |				ELSE 1
      |			END AS run_3_4_success,
      |			CASE
      |				WHEN data_check = 'success'
      |				THEN 0
      |				ELSE 1
      |			END AS data_check_success,
      |			h.*
      |		FROM
      |			(
      |				SELECT
      |					f.name,
      |					f.origin_buffalo_id,
      |					f.origin_log_id,
      |					task_def_id,
      |					run_2_4,
      |					run_3_4,
      |					CASE
      |						WHEN data_check IS NULL
      |						THEN 'skipped_or_waiting'
      |						ELSE data_check
      |					END AS data_check,
      |					duration_2_4,
      |					duration_3_4,
      |					duration_2_4 - duration_3_4 AS time_saved_3_vs_2,
      |					1 - duration_3_4 / duration_2_4 AS ratio,
      |					duration_check
      |				FROM
      |					(
      |						SELECT
      |							e.name,
      |							e.task_def_id,
      |							e.origin_buffalo_id,
      |							e.origin_log_id,
      |							MAX(
      |								CASE
      |									WHEN e.inst_type = '2.4'
      |									THEN run_status
      |								END) AS run_2_4,
      |							MAX(
      |								CASE
      |									WHEN e.inst_type = '3.4'
      |									THEN run_status
      |								END) AS run_3_4,
      |							MAX(
      |								CASE
      |									WHEN e.inst_type = 'check'
      |									THEN run_status
      |								END) AS data_check,
      |							MAX(
      |								CASE
      |									WHEN e.inst_type = '2.4'
      |									THEN e.duration
      |								END) AS duration_2_4,
      |							MAX(
      |								CASE
      |									WHEN e.inst_type = '3.4'
      |									THEN e.duration
      |								END) AS duration_3_4,
      |							MAX(
      |								CASE
      |									WHEN e.inst_type = 'check'
      |									THEN e.duration
      |								END) AS duration_check
      |						FROM
      |							(
      |								SELECT
      |									a.name,
      |									a.origin_buffalo_id,
      |									a.origin_log_id,
      |									CASE
      |										WHEN args LIKE '2\_4%'
      |										THEN '2.4'
      |										WHEN args LIKE '3\_4%'
      |										THEN '3.4'
      |										WHEN script_path = 'data_compare3.sh'
      |										THEN 'check'
      |										ELSE 'UNKNOW'
      |									END AS inst_type,
      |									f.*,
      |									f.id AS log_id
      |								FROM
      |									(
      |										SELECT
      |											a.id AS task_def_id,
      |											a.name,
      |											substring_index(substring_index(name, '_', 2), '_', - 1) AS origin_buffalo_id,
      |											substring_index(substring_index(name, '_', 3), '_', - 1) AS origin_log_id,
      |											MAX(e.instance_id) AS latest_ins_id
      |										FROM
      |											b_def_task AS a
      |										LEFT JOIN b_run_log AS e
      |										ON
      |											a.id = e.task_def_id
      |											AND e.instance_type = 1
      |										WHERE
      |											a.app_group_id = 106020
      |											AND status = 3
      |											AND disabled = 0
      |											AND a.deleted = 0
      |											AND a.name LIKE 'BUFFALO_%'
      |											AND LENGTH(SUBSTRING_INDEX(name, '_', - 1)) > 5
      |											/*  AND a.id = 1502384
      |											AND a.name LIKE '%1006775%' */
      |										GROUP BY
      |											a.id,
      |											a.name
      |									) AS a
      |								LEFT JOIN b_run_log AS f
      |								ON
      |									a.task_def_id = f.task_def_id
      |									AND f.instance_type = 2
      |									AND a.latest_ins_id = f.task_ins_id
      |							) AS e
      |						GROUP BY
      |							task_def_id,
      |							e.origin_buffalo_id,
      |							e.origin_log_id
      |					) AS f
      |			) AS h
      |	) AS i
      |GROUP BY
      |	origin_buffalo_id
      |""".stripMargin

  val explainCheckSql =
    """
      |SELECT
      |	buffalo_id,
      |	SUM(
      |		CASE
      |			WHEN status != 'SUCCESSFUL'
      |			THEN 1
      |			ELSE 0
      |		END) * 1 AS explain_is_failed
      |FROM
      |	(
      |		SELECT
      |			a.*
      |		FROM
      |			spark_upgrade_sql_rewrite_tbl AS a
      |		LEFT JOIN
      |			(
      |				SELECT
      |					buffalo_id,
      |					MAX(create_time) AS max_create_time
      |				FROM
      |					spark_upgrade_sql_rewrite_tbl
      |			) AS b
      |		ON
      |			a.buffalo_id = b.buffalo_id
      |			AND a.create_time = b.max_create_time
      |	) AS b
      |GROUP BY
      |	buffalo_id
      |""".stripMargin

  val dualRunTasksSQL =
    """
      |SELECT
      |	a.id AS dual_run_id,
      |	a.name,
      |	substring_index(substring_index(name, '_', 2), '_', - 1) AS origin_buffalo_id,
      | DATE(modified) as created
      |FROM
      |	b_def_task AS a
      |WHERE
      |	a.app_group_id = 106020
      |	AND status = 3
      |	AND disabled = 0
      |	AND a.deleted = 0
      |	AND a.name REGEXP 'BUFFALO_[0-9]+_[0-9]+'
      |""".stripMargin

  val latestTasksSql =
    s"""
       |SELECT
       |	substring_index(substring_index(name, '_', 2), '_', - 1) AS origin_buffalo_id,
       |	MAX(DATE(modified)) AS created
       |FROM
       |	b_def_task
       |WHERE
       |	app_group_id = 106020
       |	AND status = 3
       |	AND disabled = 0
       |	AND deleted = 0
       |	AND name REGEXP 'BUFFALO_[0-9]+_[0-9]+'
       |GROUP BY
       |	origin_buffalo_id
       |""".stripMargin

  val taskCriticalPath =
    """
      |select * from critical_path_1
      |""".stripMargin

  val redisKey = "isDualRun1004"
  val redisInstanceKey = "dualRunInstance1004"

  val buffaloInstanceSQL =
    """
      |SELECT
      |	run_status, task_ins_id
      |FROM
      |	b_run_log
      |WHERE
      |	instance_type = 1
      |AND deleted = 'false'
      |AND queue_time >= '2024-10-04'
      |""".stripMargin

  val shuffleHeavyTasksSQL =
    """
      |SELECT taskid as task_id,
      |       max(max_shuffle_TB) AS max_shuffle_TB,
      |       max(sum_shuffle_TB) AS sum_shuffle_TB
      |FROM
      |  (SELECT b.erp,
      |          b.taskid,
      |          b.logid,
      |          b.jobsource,
      |          b.cluster,
      |          b.queue,
      |          CAST(SUBSTR(b.starttime_date, 12, 2) AS INT) AS start_hour,
      |          CAST((CASE WHEN SUBSTR(b.endtime_date, 12, 2) = '' THEN '-1' ELSE SUBSTR(b.endtime_date, 12, 2) END) AS INT) AS end_hour,
      |          b.duration / 1000 AS durationSecond,
      |          (instr(c.spark_config_all, 'spark.shuffle.manager -> remote') > 0
      |           AND instr(c.spark_config_all, 'spark.shuffle.rss.enabled -> true') > 0) AS isRSS,
      |          b.appSparkVersion,
      |          a.ns,
      |          a.appid,
      |          a.appattemptid,
      |          SUM(inputBytes / 1024 / 1024) / 1024 AS sum_input_GB,
      |          MAX(inputBytes / 1024 / 1024) / 1024 AS max_input_GB,
      |          SUM(shuffleWriteBytes / 1024 / 1024) / 1024 / 1024 AS sum_shuffle_TB,
      |          MAX(shuffleWriteBytes / 1024 / 1024) / 1024 / 1024 AS max_shuffle_TB,
      |          a.dt
      |   FROM fdm.fdm_spark_stageinfo_di AS a
      |   INNER JOIN fdm.fdm_spark_appinfo_di AS b ON a.appid = b.appid
      |   AND a.appattemptid = b.appattemptid
      |   AND a.dt = b.dt
      |   INNER JOIN fdm.fdm_spark_environmentinfo_di AS c ON a.appid = c.appid
      |   AND a.appattemptid = c.appattemptid
      |   AND a.dt = c.dt
      |   WHERE a.dt = sysdate(- 1)
      |     AND b.dt = sysdate(- 1)
      |     AND c.dt = sysdate(- 1)
      |   GROUP BY b.erp, a.dt, b.taskid, b.logid, b.jobsource, b.cluster, b.queue, CAST(SUBSTR(b.starttime_date, 12, 2) AS INT), CAST((CASE WHEN SUBSTR(b.endtime_date, 12, 2) = '' THEN '-1' ELSE SUBSTR(b.endtime_date, 12, 2) END) AS INT), b.duration, (instr(c.spark_config_all, 'spark.shuffle.manager -> remote') > 0
      |AND instr(c.spark_config_all, 'spark.shuffle.rss.enabled -> true') > 0), b.appSparkVersion,
      |                                                                          a.ns,
      |                                                                          a.appid,
      |                                                                          a.appattemptid) AS a
      |WHERE isRSS = 'false'
      |GROUP BY taskid
      |""".stripMargin

  val stageCheckSQL =
    """
      |SELECT
      |	task_id,
      |	b.id,
      |	b.name
      |FROM
      |	b_def_action AS b
      |INNER JOIN
      |	(
      |		SELECT
      |			id
      |		FROM
      |			b_def_task AS a
      |		WHERE
      |			a.app_group_id = 106020
      |			AND a.status = 3
      |			AND a.disabled = 0
      |			AND a.deleted = 0
      |			AND a.name REGEXP 'BUFFALO_[0-9]+_[0-9]+'
      |	) AS a
      |ON
      |	a.id = b.task_id
      |""".stripMargin
}
