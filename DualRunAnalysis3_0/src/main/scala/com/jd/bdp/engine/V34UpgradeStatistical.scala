package com.jd.bdp.engine

import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession
import java.sql.{Connection, DriverManager}

import org.apache.spark.sql.functions.col
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
<h3>1 environment</h3>
<pre>
export JDHXXXXX_CLUSTER_NAME=cairne;
export JDHXXXXX_USER=dd_edw;
export JDHXXXXX_QUEUE=bdp_jdw_dd_edw_bdp_spark;
export TEAM_USER=dd_edw_system_optimization;
source /software/servers/env/env.sh
unset HADOOP_USER_CERTIFICATE
export SPARK_HOME=/software/servers/cairne/dd_edw/spark_3.4
</pre>

<h3>2 submit</h3>
<pre>
spark-submit --conf spark.sql.files.maxPartitionBytes=5242880 \
--queue root.bdp_jdw_dd_edw_test \
--conf spark.isLoadHivercFile=true \
--conf spark.sql.tempudf.ignoreIfExists=true \
--class com.jd.bdp.engine.V34UpgradeStatistical \
DualRunAnalysis3_0-1.0-SNAPSHOT.jar
</pre>

<h3>3 Publish</h3>
每天定时定时任务会从以下地址获取最新版本的包，<b>请注意修改脚本后一定要发布至以下地址</b><br/>
http://storage.jd.local/moneta/Dual-Run/DualRunAnalysis3_0-1.0-SNAPSHOT.jar

 */
object V34UpgradeStatistical {
  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .appName("V34UpgradeStatistical App")
      .enableHiveSupport()
      .getOrCreate()

    spark.conf.set("hive.exec.dynamic.partition.mode", "nonstrict")

    Class.forName("com.mysql.jdbc.Driver")
    val url = "***************************************************************************"
    val username = "salt"
    val password = "salt"

    val today = LocalDate.now()
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    val formattedDate = today.format(formatter)
    val tMinus1: LocalDate = today.minusDays(1)
    val tMinus14: LocalDate = today.minusDays(14)

//    val shuzi1 = spark.read.jdbc(tempJdbcUrl,
//      s"(select * from spark_3_4_shuzi_upgrade_2) AS tmp", tempConnectionProperties)
//      .withColumn("ratio_tasks", col("buffalo_spark34_taskids") / col("buffalo_all_taskids"))
//    shuzi1
//      .write.format("jdbc")
//      .option("url", "***************************************************************************")
//      .option("dbtable", "spark_3_4_shuzi_upgrade_3")
//      .option("driver", "com.mysql.jdbc.Driver")
//      .option("user", "salt")
//      .option("password", "salt")
//      .mode("overwrite")
//      .save()

    val spark_3_4_query_ratio = "spark_3_4_upgrade_2"
    val spark_3_4_shuzi_query = "spark_3_4_shuzi_upgrade_3"
    val cur_dt = "sysdate(0)"
    val saveMode = "append"

    /**
     * CREATE TABLE `spark_3_4_upgrade` (
     * `task_num` bigint(20) NOT NULL,
     * `job_num` double not null,
     * `stable_job_num` bigint(20) not null,
     * `no_stable_job_num` bigint(20) not null,
     * `run_promotion` double not null,
     * `resource_promotion` double not null,
     * `cur_dt` varchar(20),
     * UNIQUE INDEX idx_upgrade (cur_dt)
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
     */
    val progress = spark.sql(
      """
      |SELECT
      |	COUNT(1) AS task_num,
      |	SUM(job_num) AS job_num,
      |	SUM(
      |		CASE
      |			WHEN is_stable = 1
      |			THEN 1
      |			ELSE 0
      |		END) AS stable_job_num,
      |	SUM(
      |		CASE
      |			WHEN is_stable = 0
      |			THEN 1
      |			ELSE 0
      |		END) AS no_stable_job_num,
      |	1 -(SUM(current_sum_exe_min) / SUM(sum_exe_min)) AS run_promotion,
      |	SUM(req_vcore_day) - SUM(recent_vcore_day) AS resource_promotion,
      |	sysdate(0) as cur_dt
      |FROM
      |	tmp.lvfulong_spark_upgrade_big_graph
      |WHERE
      |	upgrade_status = 'UPGRADED'
      |	AND
      |	(
      |		current_sum_exe_min <= 60
      |		OR
      |		(
      |			current_sum_exe_min > 60
      |			AND duration_promotion >( - 1.5)
      |		)
      |	)
      |""".stripMargin)
    progress.cache()
    progress.collect().foreach(row => {
      val curDt = row.getAs[String]("cur_dt")
      val connection: Connection = DriverManager.getConnection(url, username, password)
      try {
        val sql = "DELETE FROM spark_3_4_upgrade WHERE cur_dt = ?"
        val preparedStatement = connection.prepareStatement(sql)
        preparedStatement.setString(1, curDt)
        preparedStatement.executeUpdate()
      } catch {
        case e: Exception =>
          e.printStackTrace()
      } finally {
        if (connection != null) connection.close()
      }
    })
    progress.repartition(1)
      .write.format("jdbc")
      .option("url", "***************************************************************************")
      .option("dbtable", "spark_3_4_upgrade")
      .option("driver", "com.mysql.jdbc.Driver")
      .option("user", "salt")
      .option("password", "salt")
      .mode("append")
      .save()

    // below is re-export detail to hive table, with same logic as above.
    // ensure consistency when modifying.
    val progressDetail = spark.sql(
      """
        |SELECT
        |	bee_source AS bee_source,
        |	task_id AS task_id,
        |	COUNT(1) AS task_num,
        |	SUM(job_num) AS job_num,
        |	SUM(current_sum_exe_min) AS sum_current_sum_exe_min,
        |	SUM(sum_exe_min) AS sum_base_sum_exe_min,
        |	AVG(current_sum_exe_min) AS avg_current_sum_exe_min,
        |	AVG(sum_exe_min) AS avg_base_sum_exe_min,
        |	SUM(req_vcore_day) AS sum_req_vcore_day,
        |	SUM(recent_vcore_day) AS sum_recent_vcore_day,
        |	SUM(
        |		CASE
        |			WHEN is_stable = 1
        |			THEN 1
        |			ELSE 0
        |		END) AS stable_job_num,
        |	SUM(
        |		CASE
        |			WHEN is_stable = 0
        |			THEN 1
        |			ELSE 0
        |		END) AS no_stable_job_num,
        |	1 -(SUM(current_sum_exe_min) / SUM(sum_exe_min)) AS run_promotion,
        |	SUM(req_vcore_day) - SUM(recent_vcore_day) AS resource_promotion,
        |	sysdate(0) as dt
        |FROM
        |	tmp.lvfulong_spark_upgrade_big_graph
        |WHERE
        |	upgrade_status = 'UPGRADED'
        |	AND
        |	(
        |		current_sum_exe_min <= 60
        |		OR
        |		(
        |			current_sum_exe_min > 60
        |			AND duration_promotion >( - 1.5)
        |		)
        |	)
        |GROUP BY
        |	bee_source,
        |	task_id
        |""".stripMargin)

    val scriptVersionDF = V34UpgradeAssessment.getScriptVersionDF(spark, tMinus14, tMinus1)
    val scriptDF = progressDetail.join(scriptVersionDF, progressDetail("task_id") === scriptVersionDF("task_id"), "left")
      .drop(scriptVersionDF("task_id"))

    scriptDF.write.mode("overwrite").format("hive").partitionBy("dt")
      .saveAsTable("tmp.wuguoxiao_spark_upgrade_big_graph_detail")

    /**
create table spark_3_4_query_ratio (
spark_34_buffalo_tasks double not null,
spark_buffalo_tasks double not null,
num_spark34 double not null,
num_other double not null,
app_queries double not null,
ratio_tasks double not null,
ratio_spark34_queries double not null,
ratio_other_queries double not null,
cur_dt varchar(20),
UNIQUE INDEX idx_qr_cur_dt (cur_dt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
     */
    val spark34queryDF = spark.sql(
      s"""
        |SELECT
        |	COUNT(DISTINCT(
        |		CASE
        |			WHEN jobsource IN('BUFFALO4', '9N_BUFFALO4')
        |				AND appsparkversion LIKE '3.4%'
        |			THEN taskid
        |		END)) AS spark_34_buffalo_tasks,
        |	COUNT(DISTINCT(
        |		CASE
        |			WHEN jobsource IN('BUFFALO4', '9N_BUFFALO4')
        |			THEN taskid
        |		END)) AS spark_buffalo_tasks,
        |	SUM(
        |		CASE
        |			WHEN is_spark34 = 1
        |			THEN 1
        |			ELSE 0
        |		END) / 7.0 AS num_spark34,
        |	SUM(
        |		CASE
        |			WHEN is_spark34 = 0
        |			THEN 1
        |			ELSE 0
        |		END) / 7.0 AS num_other,
        |	COUNT(1) / 7.0 AS num_all,
        |	${cur_dt} as cur_dt
        |FROM
        |	(
        |		SELECT
        |			dt,
        |			taskid,
        |			jobsource,
        |			appsparkversion,
        |			CASE
        |				WHEN appsparkversion LIKE '3.4%'
        |				THEN 1
        |				ELSE 0
        |			END AS is_spark34
        |		FROM
        |			fdm.fdm_spark_appinfo_di
        |		WHERE
        |			dt >= sysdate( - 7)
        |			AND dt <= sysdate( - 1)
        |	)
        |	a
        |""".stripMargin)
      .withColumn("ratio_tasks", col("spark_34_buffalo_tasks") / col("spark_buffalo_tasks"))
      .withColumn("ratio_spark34_queries", col("num_spark34") / col("num_all"))
      .withColumn("ratio_other_queries", col("num_other") / col("num_all"))
    spark34queryDF.cache()
    spark34queryDF.collect().foreach(row => {
      val connection: Connection = DriverManager.getConnection(url, username, password)
      try {
        val sql = s"DELETE FROM ${spark_3_4_query_ratio} WHERE cur_dt = ?"
        val preparedStatement = connection.prepareStatement(sql)
        preparedStatement.setString(1, formattedDate)
        preparedStatement.executeUpdate()
      } catch {
        case e: Exception =>
          e.printStackTrace()
      } finally {
        if (connection != null) connection.close()
      }
    })
    spark34queryDF.repartition(1)
      .write.format("jdbc")
      .option("url", "***************************************************************************")
      .option("dbtable", spark_3_4_query_ratio)
      .option("driver", "com.mysql.jdbc.Driver")
      .option("user", "salt")
      .option("password", "salt")
      .mode(saveMode)
      .save()

    /**
     * create table spark_3_4_shuzi_query (
     * num_spark34 double not null,
     * num_other double not null,
     * num_all double not null,
     * ratio_spark34 double not null,
     * ratio_other double not null,
     * cur_dt varchar(20),
     * UNIQUE INDEX idx_qr_cur_dt (cur_dt)
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
     */
    val shuzi = spark.sql(
      s"""
        |SELECT
        |	SUM(
        |		CASE
        |			WHEN is_spark34 = 1
        |			THEN 1
        |			ELSE 0
        |		END) / 7.0 AS num_spark34,
        |	SUM(
        |		CASE
        |			WHEN is_spark34 = 0
        |			THEN 1
        |			ELSE 0
        |		END) / 7.0 AS num_other,
        |	COUNT(1) / 7.0 AS num_all,
        |	SUM(
        |		CASE
        |			WHEN is_spark34 = 1
        |			THEN 1
        |			ELSE 0
        |		END) / 7.00 /(COUNT(1) / 7.00) AS ratio_spark34,
        |	SUM(
        |		CASE
        |			WHEN is_spark34 = 0
        |			THEN 1
        |			ELSE 0
        |		END) / 7.00 /(COUNT(1) / 7.00) AS ratio_other,
        |		count(DISTINCT buffalo_spark34_taskids) AS buffalo_spark34_taskids,
        |		count(DISTINCT buffalo_all_taskids) AS buffalo_all_taskids,
        |		${cur_dt} as cur_dt
        |FROM
        |	(
        |		SELECT
        |			c.dept_name_2,
        |			b.is_spark34,
        |			b.buffalo_spark34_taskids,
        |			b.buffalo_all_taskids
        |		FROM
        |			(
        |				SELECT
        |					job_id,
        |					erp,
        |					dt
        |				FROM
        |					gdm.gdm_m99_job_run_log_da
        |				WHERE
        |					dt >= sysdate( - 7)
        |					AND dt <= sysdate( - 1)
        |			)
        |			a
        |		JOIN
        |			(
        |				SELECT
        |					dt,
        |					CASE
        |						WHEN appsparkversion LIKE '3.4%'
        |						THEN 1
        |						ELSE 0
        |					END AS is_spark34,
        |					CASE
        |						WHEN jobsource IN('BUFFALO4', '9N_BUFFALO4')
        |							AND appsparkversion LIKE '3.4%'
        |						THEN taskid
        |					END buffalo_spark34_taskids,
        |					CASE
        |						WHEN jobsource IN('BUFFALO4', '9N_BUFFALO4')
        |						THEN taskid
        |					END buffalo_all_taskids,
        |					appid
        |				FROM
        |					fdm.fdm_spark_appinfo_di
        |				WHERE
        |					dt >= sysdate( - 7)
        |					AND dt <= sysdate( - 1)
        |			)
        |			b
        |		ON
        |			a.dt = b.dt
        |			AND a.job_id = b.appid
        |		JOIN
        |			(
        |				SELECT
        |					u_account,
        |					dept_name_2
        |				FROM
        |					adm.adm_d02_org_hr_dept_da
        |				WHERE
        |					dt = sysdate( - 2)
        |			)
        |			c
        |		ON
        |			a.erp = c.u_account
        |	)
        |	d
        |WHERE
        |	dept_name_2 = '数据资产与应用部'
        |""".stripMargin)
      .withColumn("ratio_tasks", col("buffalo_spark34_taskids") / col("buffalo_all_taskids"))
    // Run after 24, but upstream data not ready, use data from two days prior.
    shuzi.show()

    shuzi.cache()
    shuzi.collect().foreach(row => {
      val connection: Connection = DriverManager.getConnection(url, username, password)
      try {
        val sql = s"DELETE FROM ${spark_3_4_shuzi_query} WHERE cur_dt = ?"
        val preparedStatement = connection.prepareStatement(sql)
        preparedStatement.setString(1, formattedDate)
        preparedStatement.executeUpdate()
      } catch {
        case e: Exception =>
          e.printStackTrace()
      } finally {
        if (connection != null) connection.close()
      }
    })
    shuzi.repartition(1)
      .write.format("jdbc")
      .option("url", "***************************************************************************")
      .option("dbtable", spark_3_4_shuzi_query)
      .option("driver", "com.mysql.jdbc.Driver")
      .option("user", "salt")
      .option("password", "salt")
      .mode(saveMode)
      .save()

    spark.stop()
  }
}
