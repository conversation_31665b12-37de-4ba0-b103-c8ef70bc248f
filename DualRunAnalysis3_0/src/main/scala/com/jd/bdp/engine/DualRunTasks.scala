package com.jd.bdp.engine

import java.net.{HttpURLConnection, URL}
import scala.io.Source
import com.jd.bdp.engine.StrConstant.{completedOriginTasks, connectionProperties, dualRunTasksSQL, jdbcPassword, jdbcUrl, jdbcUser, latestInsId, latestTasksSql, unexecutedTasks}
import org.apache.log4j.Logger

import java.sql.{Connection, DriverManager, ResultSet}
import java.time.format.DateTimeFormatter
import java.time.LocalDateTime
import com.jd.bdp.engine.V34UpgradeAssessment

import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{LongType, StringType, StructField, StructType}

/**
export JDHXXXXX_CLUSTER_NAME=cairne;
export JDHXXXXX_USER=dd_edw;
export JDHXXXXX_QUEUE=bdp_jdw_dd_edw_bdp_spark;
export TEAM_USER=dd_edw_system_optimization;
source /software/servers/env/env.sh

spark-submit --master local[4] --class com.jd.bdp.engine.DualRunTasks DualRunAnalysis-1.0-SNAPSHOT.jar
 *
 */
object DualRunTasks {

  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .appName("DualRunTasks App")
      .enableHiveSupport()
      .getOrCreate()

    logger.info(s"=== args: ${args.mkString(",")}")
    val defaultVal = Seq(false, "file:///root/wuguoxiao/wuguoxiao/doubleRun/run_dual_run/task_ids.txt", 5, true)
    val args2 = args.padTo(defaultVal.length, "")
    logger.info(s"=== args2: ${args2.mkString(",")}")
    val handleDefaultArgs = args2.zip(defaultVal).map { case (param, default) =>
      if (param == null || param.isEmpty) default else param
    }
    val showProcess = handleDefaultArgs(0).toString.toBoolean
    val filePath = handleDefaultArgs(1).toString
    val sleepTime = handleDefaultArgs(2).toString.toInt
    val ignoreRepeatTasks = handleDefaultArgs(3).toString.toBoolean
    val taskDF = spark.read.text(filePath)

    val taskIdDF = taskDF.withColumn("origin_task_id", col("value").cast("long")).drop("value")
    val addFollower = V34UpgradeAssessment.addFollower(spark, taskIdDF, "origin_task_id")                   // origin_task_id、dev_follower

    // buffalo双跑任务id
    val dualRunTasks = spark.read.jdbc(jdbcUrl, s"(${dualRunTasksSQL}) AS tmp", connectionProperties)            // dual_run_id、name、origin_buffalo_id、created
    val latestRunTasks = spark.read.jdbc(jdbcUrl, s"(${latestTasksSql}) as tmp", connectionProperties)           // origin_buffalo_id、created
    val lastRunRecord = dualRunTasks.join(latestRunTasks,Seq("origin_buffalo_id","created")).drop("created")  // dual_run_id、name、origin_buffalo_id

    var dualRunId = addFollower.join(lastRunRecord, lastRunRecord("origin_buffalo_id") === addFollower("origin_task_id"), "left")  // origin_task_id、dev_follower、dual_run_id、name、origin_buffalo_id
    logger.info(s"Origin task count: ${addFollower.count()}, Dual-Run task count: ${dualRunId.select("origin_buffalo_id").distinct().count()}")
    dualRunId = dualRunId.drop(dualRunId("origin_buffalo_id"))                                                          // origin_task_id、dev_follower、dual_run_id、name

    val lastInstanceTasks = spark.read.jdbc(jdbcUrl, s"(${latestInsId}) AS tmp", connectionProperties)          // dual_run_id、task_def_id、name、origin_buffalo_id、origin_log_id、created、latest_ins_id
    val lastInstDF = dualRunId.join(lastInstanceTasks, dualRunId("dual_run_id") === lastInstanceTasks("dual_run_id"), "left")
      .drop(lastInstanceTasks("dual_run_id")).drop(lastInstanceTasks("task_def_id")).drop(lastInstanceTasks("name"))
      .drop(lastInstanceTasks("origin_buffalo_id")).drop(lastInstanceTasks("origin_log_id")).drop(lastInstanceTasks("created")) // origin_task_id、dev_follower、dual_run_id、name、latest_ins_id

    val lastInstRDD = lastInstDF.rdd.mapPartitions {
      partition =>
        var connection: Connection = null
        try {
          connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
          val statement = connection.createStatement()
          partition.map {
            row =>
              val lastInstId = row.getAs[Long]("latest_ins_id")
              val originTaskId = row.getAs[Long]("origin_task_id")
              val doubleRunTaskId = row.getAs[Int]("dual_run_id")
              if(lastInstId == 0){
                logger.info(s"=== originTaskId: $originTaskId, 双跑任务id: $doubleRunTaskId， 当天最新双跑实例id为空, 可以创建新的双跑实例")
                Row.fromSeq(row.toSeq :+ null :+ null :+ null)
              }else{
                val query = s"SELECT run_status FROM b_run_log WHERE instance_id = $lastInstId"
                val resultSet = statement.executeQuery(query)
                val runStatus = if (resultSet.next()) resultSet.getString(1) else null
                logger.info(s"=== originTaskId: $originTaskId, 双跑任务id: $doubleRunTaskId, 当天最新双跑实例id为: $lastInstId, 最新双跑实例状态: $runStatus")

                val durationQuery = s"SELECT sum(if(args like '2_4%', duration, 0)) as duration_24, " +
                  s"sum(if(args like '3_4%', duration, 0)) as duration_34 " +
                  s"FROM b_run_log WHERE task_ins_id = ${lastInstId} and instance_type = 2"
                val durationResultSet = statement.executeQuery(durationQuery)
                val (duration24, duration34) = if(durationResultSet.next())
                  (durationResultSet.getLong(1), durationResultSet.getLong(2))
                else (null, null)
                Row.fromSeq(row.toSeq :+ runStatus :+ duration24 :+ duration34)
              }
          }
        } finally {
        }
    }
    val newSchema = StructType(lastInstDF.schema.fields :+
      StructField("run_status", StringType, nullable = true) :+
      StructField("duration24", LongType, nullable = true) :+
      StructField("duration34", LongType, nullable = true))
    var runStatusDF = spark.createDataFrame(lastInstRDD, newSchema)  // origin_task_id、dev_follower、dual_run_id、name、latest_ins_id、run_status、duration24、duration34
    
    logger.info(s"=== newSchema:${newSchema}")
    saveToExcel(runStatusDF)

    if(!showProcess) {
      if (ignoreRepeatTasks) {
        runStatusDF = runStatusDF.filter("run_status is null or run_status = 'fail' ")
      }
      Console.withErr(System.err) {
        runStatusDF.show(false)
      }
      runStatusDF.collect().foreach(row => {
        val originTaskId = row.getAs[Long]("origin_task_id")
        val dualRunId = row.getAs[Integer]("dual_run_id")
        val name = row.getAs[String]("name")
        val devFollower = row.getAs[String]("dev_follower")
        val response = fetchFromHttp(dualRunId, devFollower, originTaskId)
        logger.info(s"dualRunId[$dualRunId] name[$name] setStatus = ${response}")
        Thread.sleep(sleepTime * 1000)
      })
      runStatusDF.show()
    }

    spark.close()
  }

  def saveToExcel(runStatusDF: DataFrame): Unit = {
    val defaultPath = s"file:///home/<USER>/dual-run/dual-run/output_${LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))}"
    logger.info("Result in csv file at " + defaultPath)
    runStatusDF.repartition(1).write.format("csv").option("header", "true").save(defaultPath)
  }

  def fetchFromHttp(dualRunTaskId: Integer, devFollower: String, originTaskId: Long): String = {
    if (dualRunTaskId != null) {
      val str = s"http://troubleshooting.jd.com/sparkUpgradeCreateInstance?responseType=json&taskid=${dualRunTaskId}&follower=${devFollower}&originTaskId=${originTaskId}"
      val connection = new URL(str).openConnection().asInstanceOf[HttpURLConnection]
      connection.setRequestMethod("GET")
      try {
        val response = Source.fromInputStream(connection.getInputStream).mkString
        logger.info(s"response = ${response}")
        response
      } finally {
        connection.disconnect();
      }
    } else "Error: Dual run id is null"
  }
}
