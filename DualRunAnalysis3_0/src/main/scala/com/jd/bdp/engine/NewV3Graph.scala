package com.jd.bdp.engine

import org.apache.spark.sql.{DataFrame, SaveMode, SparkSession}

import java.text.SimpleDateFormat
import java.util.{Calendar, Date}
import StrConstant._

object NewV3Graph {

  def main(args: Array[String]): Unit = {
    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.shuffle.targetPostShuffleInputSize", "41943040")
      .config("spark.dynamicAllocation.initialExecutors", "200")
      .config("spark.dynamicAllocation.minExecutors", "200")
      .config("spark.dynamicAllocation.maxExecutors", "2000")
      .config("spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version", 2)
      .appName(this.getClass.getSimpleName.split("\\.").last)
      .enableHiveSupport()
      .getOrCreate()

    val baseDataTable = "tmp.spark_double_run_base_data"
    val startDt = "2024-07-16"
    val endDt = getYesterday
    val baseData = getAllBasedData(startDt, endDt,spark)
    baseData.write.mode(SaveMode.Overwrite).format("orc").saveAsTable(baseDataTable)
    val taskInfoTable = "task_info_table"
    getTaskInfoData(baseDataTable, spark).createOrReplaceTempView(taskInfoTable)
    val finalGraphTable="tmp.spark_double_run_graph"
    val finalGraph = getFinalGraphData(taskInfoTable, spark)
    finalGraph.write.mode(SaveMode.Overwrite).format("orc").saveAsTable(finalGraphTable)
    saveFinalGraphToMySql(finalGraphTable, spark)
    spark.close()
  }

  def getYesterday: String = {
    val today = new Date()
    val calendar = Calendar.getInstance
    calendar.setTime(today)
    calendar.add(Calendar.DAY_OF_YEAR, -1)
    new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime)
  }

  def saveFinalGraphToMySql(finalGraphTable:String,spark:SparkSession):Unit={
    val tableName = "lvfulong_temp_v34_data"
    spark.read.table(finalGraphTable).repartition(10).write.mode(SaveMode.Overwrite).format("jdbc")
      .option("url",tempJdbcUrl)
      .option("dbtable",tableName)
      .option("user",tempConnectionProperties.getProperty("user"))
      .option("password",tempConnectionProperties.getProperty("password"))
      .option("driver",tempConnectionProperties.getProperty("driver"))
      .save()
  }

  def getFinalGraphData(taskInfoTable:String,spark:SparkSession):DataFrame={
    val sql=
      s"""
         |SELECT
         |	a.task_id,
         |	a.bee_source,
         |	b.task_first_category,
         |	b.task_2nd_category,
         |	b.bgname,
         |	b.task_owner,
         |	b.dept_1_name,
         |	b.dept_2_name,
         |	b.dept_3_name,
         |	b.status,
         |	b.priority,
         |	a.req_vcore_day,
         |	a.sum_exe_min,
         |	a.job_num,
         |	a.isHive,
         |	a.isSpark,
         |	b.only_spark_sql,
         |	b.all_hivetask,
         |	a.old_version,
         |	b.new_version
         |FROM
         |	(
         |		SELECT
         |			bee_source,
         |			task_id,
         |			ROUND(SUM(
         |				CASE
         |					WHEN row_num >= 1
         |						AND row_num <= 14
         |					THEN task_req_vcore
         |					ELSE 0
         |				END) / SUM(
         |				CASE
         |					WHEN row_num >= 1
         |						AND row_num <= 14
         |					THEN 1
         |					ELSE 0
         |				END), 2) AS req_vcore_day,
         |			ROUND(SUM(
         |				CASE
         |					WHEN row_num >= 1
         |						AND row_num <= 14
         |					THEN task_run_min
         |					ELSE 0
         |				END) / SUM(
         |				CASE
         |					WHEN row_num >= 1
         |						AND row_num <= 14
         |					THEN 1
         |					ELSE 0
         |				END), 2) AS sum_exe_min,
         |			ROUND(SUM(
         |				CASE
         |					WHEN row_num >= 1
         |						AND row_num <= 14
         |					THEN job_num
         |					ELSE 0
         |				END) / SUM(
         |				CASE
         |					WHEN row_num >= 1
         |						AND row_num <= 14
         |					THEN 1
         |					ELSE 0
         |				END), 2) AS job_num,
         |			0 AS isHive,
         |			1 AS isSpark,
         |			MIN(version_num) AS old_version
         |		FROM
         |			${taskInfoTable}
         |		GROUP BY
         |			task_id,
         |			bee_source
         |	)
         |	a
         |JOIN
         |	(
         |		SELECT
         |			bee_source,
         |			task_first_category,
         |			task_2nd_category,
         |			bgname,
         |			task_owner,
         |			dept_1_name,
         |			dept_2_name,
         |			dept_3_name,
         |			status,
         |			priority,
         |			task_id,
         |			MAX(version_num) AS new_version,
         |			CASE
         |				WHEN SUM(
         |						CASE
         |							WHEN all_hivetask = 'N'
         |							THEN 1
         |							ELSE 0
         |						END) > 0
         |				THEN 'N'
         |				ELSE 'Y'
         |			END AS all_hivetask,
         |			CASE
         |				WHEN SUM(
         |						CASE
         |							WHEN spark_app_types NOT IN('SPARK-SqlShell,SPARK-Unknown', 'SPARK-Unknown,SPARK-SqlShell','SPARK-Unknown','SPARK-SqlShell')
         |							THEN 1
         |							ELSE 0
         |						END) > 0
         |				THEN 0
         |				ELSE 1
         |			END AS only_spark_sql
         |		FROM
         |			${taskInfoTable} b1
         |		WHERE
         |			exists (select 1 from (select bee_source,task_id,max(row_num) as row_num
         |      from ${taskInfoTable} group by bee_source,task_id) b2 where b1.bee_source=b2.bee_source
         |      and b1.task_id=b2.task_id and b1.row_num = b2.row_num)
         |		GROUP BY
         |			bee_source,
         |			task_first_category,
         |			task_2nd_category,
         |			bgname,
         |			task_owner,
         |			dept_1_name,
         |			dept_2_name,
         |			dept_3_name,
         |			status,
         |			priority,
         |			task_id
         |	)
         |	b
         |ON
         |	a.bee_source = b.bee_source
         |	AND a.task_id = b.task_id
         | """.stripMargin
    spark.sql(sql).createOrReplaceTempView("tmp_final_graph")
    val filterSql =
      s"""
         |SELECT
         |	a.*
         |FROM
         |	tmp_final_graph a
         |WHERE
         |	NOT EXISTS
         |	(
         |		SELECT
         |			1
         |		FROM
         |			adm.adm_m99_task_detail_info_da b
         |		WHERE
         |			b.dt = sysdate( - 1)
         |			AND
         |			(
         |				b.is_delete = 1
         |				OR status = '禁用'
         |			)
         |			AND a.task_id = b.task_id
         |	)
         |""".stripMargin
    spark.sql(filterSql)
  }

  def getTaskInfoData(baseDataTable:String, spark:SparkSession):DataFrame={
    val sql=
      s"""
         |select * from (SELECT
         |	dt,
         |	bee_source,
         |	task_first_category,
         |	task_2nd_category,
         |	bgname,
         |	task_owner,
         |	task_id,
         |	dept_1_name,
         |	dept_2_name,
         |	dept_3_name,
         |	dept_4_name,
         |	market_name,
         |	status,
         |	priority,
         |	COUNT(1) AS job_num,
         |	MAX(full_exec_long) AS full_exec_long,
         |	SUM(job_req_vcore / 3600 / 24.0) AS task_req_vcore,
         |	SUM(job_run_time / 60.0) AS task_run_min,
         |  concat_ws(',', collect_set(is_spark34)) as is_spark34,
         |  concat_ws(',', collect_set(job_type)) as job_type,
         |  concat_ws(',', collect_set(spark_app_type)) AS spark_app_types,
         |	CASE
         |		WHEN SUM(
         |				CASE
         |					WHEN spark_sql_source = 'HiveTask'
         |					THEN 1
         |					ELSE 0
         |				END) = COUNT(1)
         |		THEN 'Y'
         |		ELSE 'N'
         |	END AS all_hivetask,
         |	MAX(current_version_num) AS version_num,
         |	row_number() over(partition BY task_id, bee_source, task_first_category, task_2nd_category, bgname, task_owner, dept_1_name, dept_2_name, dept_3_name, dept_4_name, market_name, status, priority order by dt) AS row_num
         |FROM
         |	${baseDataTable}
         |GROUP BY
         |	dt,
         |	task_id,
         |	bee_source,
         |	task_first_category,
         |	task_2nd_category,
         |	bgname,
         |	task_owner,
         |	dept_1_name,
         |	dept_2_name,
         |	dept_3_name,
         |	dept_4_name,
         |	market_name,
         |	status,
         |	priority
         |)a
         |where
         |	is_spark34 = '0' and
         |	job_type = 'spark'
         | """.stripMargin
    spark.sql(sql)
  }

  def getAllBasedData(startDt:String,endDt:String,spark:SparkSession):DataFrame= {
    val sql=
      s"""
         |SELECT
         |	a.bee_source,
         |	a.job_id,
         |	a.is_spark34,
         |	a.spark_app_type,
         |	a.job_type,
         |	a.dt AS dt,
         |	b.task_id,
         |	b.task_owner,
         |	b.bgname,
         |	b.dept_1_name,
         |	b.dept_2_name,
         |	b.dept_3_name,
         |	b.dept_4_name,
         |	b.datatype,
         |	b.task_cate AS task_first_category,
         |	b.task_type_name AS task_2nd_category,
         |	b.market_name,
         |	a.status,
         |	b.priority,
         |	a.full_exec_long,
         |	a.final_status,
         |	a.req_mem,
         |	a.req_vcore,
         |	a.job_num,
         |	a.is_ht,
         |	a.ht_engine,
         |	a.job_has_sql,
         |	a.action_name,
         |	a.script_name,
         |	a.current_version_num,
         |	a.script_path,
         |	a.script_type,
         |	a.job_req_vcore,
         |	a.job_run_time,
         |  a.spark_sql_source
         |FROM
         |	(
         |		SELECT
         |			a1.dt,
         |			a1.task_id,
         |			a1.bee_source,
         |			a1.job_id,
         |			a1.job_type,
         |			a1.total_long_s AS job_run_time,
         |			a1.req_vcore_c_s AS job_req_vcore,
         |			a2.is_spark34,
         |			a3.spark_app_type,
         |      a3.spark_sql_source, --是否HiveTask来判断是否HiveTask提交的job
         |			CASE
         |				WHEN a4.engine IS NULL
         |				THEN 0
         |				ELSE 1
         |			END AS is_ht,
         |			a4.engine AS ht_engine,
         |			a5.full_exec_long,
         |			a5.req_mem,
         |			a5.req_vcore,
         |			a5.status,
         |			a5.final_status,
         |			a5.job_num,
         |			a1.job_has_sql,
         |			a6.action_name,
         |			a6.script_name,
         |			a6.current_version_num,
         |			a6.script_path,
         |			a6.script_type
         |		FROM
         |			(
         |				SELECT
         |					dt,
         |					task_id,
         |					action_id,
         |					bee_source,
         |					job_id,
         |					job_type,
         |					total_long_s,
         |					req_vcore_c_s,
         |					CASE
         |						WHEN hql IS NULL
         |							OR hql = ''
         |						THEN 0
         |						ELSE 1
         |					END AS job_has_sql
         |				FROM
         |					gdm.gdm_m99_job_run_log_da
         |				WHERE
         |					dt >= '${startDt}'
         |					AND dt <= '${endDt}'
         |					AND bee_source IN('BUFFALO4', '9N_BUFFALO4')
         |			)
         |			a1
         |		JOIN
         |			(
         |				SELECT
         |					dt,
         |					task_id,
         |					MAX(full_exec_long) AS full_exec_long,
         |					MAX(req_mem['latest']) AS req_mem,
         |					MAX(req_vcore['latest']) AS req_vcore,
         |					MAX(job_num['latest']) AS job_num,
         |					MAX(final_status) AS final_status,
         |					MAX(status) AS status
         |				FROM
         |					adm.adm_m99_task_detail_info_da
         |				WHERE
         |					dt >= '${startDt}'
         |					AND dt <= '${endDt}'
         |					AND datatype = 'BUFFALO4'
         |					AND task_cate IN('工作流任务', 'oozie任务', '标准任务')
         |					AND full_exec_long > 0 --如果是测试实例，即使运行成功，时间也会是负值，表字段存在口径不一致的问题
         |					AND req_vcore['latest'] > 0
         |					AND job_num['latest'] > 0 --过滤实际未申请集群资源的任务
         |					AND final_status = '成功' --运行成功的记录
         |					AND is_delete = 0 --未删除
         |				GROUP BY
         |					dt,
         |					task_id
         |			)
         |			a5
         |		ON
         |			a1.dt = a5.dt
         |			AND a1.task_id = a5.task_id
         |		JOIN
         |			(
         |				SELECT
         |					dt,
         |					task_id,
         |					action_id,
         |					MAX(action_name) AS action_name,
         |					MAX(script_name) AS script_name,
         |					MAX(current_version_num) AS current_version_num,
         |					MAX(script_path) AS script_path,
         |					MAX(script_type) AS script_type
         |				FROM
         |					adm.adm_m99_task_detail_info_da
         |				WHERE
         |					dt >= '${startDt}'
         |					AND dt <= '${endDt}'
         |					AND datatype = 'BUFFALO4'
         |					AND task_cate IN('工作流环节', '标准任务')
         |					AND status = '正常'
         |					AND final_status = '成功'
         |					AND is_delete = 0
         |				GROUP BY
         |					dt,
         |					task_id,
         |					action_id
         |			)
         |			a6
         |		ON
         |			a1.dt = a6.dt
         |			AND a1.task_id = a6.task_id
         |			AND a1.action_id = a6.action_id
         |		LEFT JOIN
         |			(
         |				SELECT
         |					dt,
         |					bufflo_id AS task_id,
         |					concat_ws(',', collect_set(engine)) AS engine
         |				FROM
         |					app.app_hivetask_log
         |				WHERE
         |					dt >= '${startDt}'
         |					AND dt <= '${endDt}'
         |				GROUP BY
         |					dt,
         |					bufflo_id
         |			)
         |			a4
         |		ON
         |			a1.task_id = a4.task_id
         |			AND a1.dt = a4.dt
         |		LEFT JOIN
         |			(
         |				SELECT
         |					dt,
         |					appid,
         |					jobsource,
         |					taskid,
         |					CASE
         |						WHEN appsparkversion LIKE '3.4%'
         |						THEN 1
         |						ELSE 0
         |					END AS is_spark34
         |				FROM
         |					fdm.fdm_spark_appinfo_di
         |				WHERE
         |					dt >= '${startDt}'
         |					AND dt <= '${endDt}'
         |			)
         |			a2
         |		ON
         |			a1.dt = a2.dt
         |			AND a1.job_id = a2.appid
         |			AND a1.bee_source = a2.jobsource
         |		LEFT JOIN
         |			(
         |				SELECT
         |					dt,
         |					appid,
         |					spark_app_type,
         |					beesource,
         |                    spark_sql_source
         |				FROM
         |					fdm.fdm_spark_environmentinfo_di
         |				WHERE
         |					dt >= '${startDt}'
         |					AND dt <= '${endDt}'
         |			)
         |			a3
         |		ON
         |			a1.dt = a3.dt
         |			AND a1.job_id = a3.appid
         |			AND a1.bee_source = a3.beesource
         |	)
         |	a
         |JOIN
         |	(
         |		SELECT
         |			b1.task_id,
         |			b1.task_owner,
         |			b1.bgname,
         |			b1.dept_1_name,
         |			b1.dept_2_name,
         |			b1.dept_3_name,
         |			b1.dept_4_name,
         |			b1.datatype,
         |			b1.task_cate,
         |			b1.task_type_name,
         |			b1.market_name,
         |			b1.priority
         |		FROM
         |			(
         |				SELECT
         |					task_id,
         |					task_owner,
         |					bgname,
         |					dept_1_name,
         |					dept_2_name,
         |					dept_3_name,
         |					dept_4_name,
         |					datatype,
         |					task_cate,
         |					task_type_name,
         |					market_name,
         |					priority
         |				FROM
         |					adm.adm_m99_task_detail_info_da
         |				WHERE
         |					dt = '${endDt}'
         |					AND datatype = 'BUFFALO4'
         |					AND task_cate IN('工作流任务', 'oozie任务', '标准任务')
         |					AND is_delete = 0
         |				GROUP BY
         |					task_id,
         |					bgname,
         |					task_owner,
         |					dept_1_name,
         |					dept_2_name,
         |					dept_3_name,
         |					dept_4_name,
         |					datatype,
         |					task_cate,
         |					task_type_name,
         |					market_name,
         |					priority
         |			)
         |			b1
         |	)
         |	b ON a.task_id = b.task_id
         | """.stripMargin
    spark.sql(sql)
  }
}
