package com.jd.bdp.engine

import org.apache.spark.sql.{DataFrame, Row, SaveMode, SparkSession}
import com.jd.bdp.engine.StrConstant.{buffaloInstanceSQL, compareDualRunTimeSql, completedOriginTasks, connectionProperties, dualRunResultCheckSQL, dualRunTasksSQL, errorCodeSQL, explainCheckSql, jdbcPassword, jdbcUrl, jdbcUser, latestInsId, randomFunctionSQL, shuffleHeavyTasksSQL, taskCriticalPath, tempConnectionProperties, tempJdbcUrl, upgradedAndRollbackTasksSQL}
import com.jd.bdp.engine.DateTimeConverter
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.net.HttpURLConnection
import java.net.URL

import scala.io.Source

import org.apache.log4j.Logger
import java.net.{HttpURLConnection, URL, URLEncoder}
import java.sql.{Connection, DriverManager}

import com.alibaba.fastjson.JSON
import java.time.LocalDate

import org.apache.spark.sql.functions.when
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{IntegerType, LongType, StringType, StructField, StructType}
import org.apache.spark.storage.StorageLevel

/**
<h3>1 运行环境 和 提交方式</h3>
<pre>
export JDHXXXXX_CLUSTER_NAME=cairne;
export JDHXXXXX_USER=dd_edw;
export JDHXXXXX_QUEUE=bdp_jdw_dd_edw_bdp_spark;
export TEAM_USER=dd_edw_system_optimization;
source /software/servers/env/env.sh
<br/>
export SPARK_HOME=/software/servers/cairne/dd_edw/spark_3.4
current_datetime=$(date +%Y%m%d_%H%M)
<br/>
spark-submit --queue root.bdp_jdw_dd_edw_test --class com.jd.bdp.engine.V34UpgradeAssessment \
DualRunAnalysis3_0-1.0-SNAPSHOT.jar hdfs://ns17/tmp/wuguoxiao_v34_${current_datetime} false true
</pre>
<h3>2 任务清单会导出成csv并保存至 hdfs://ns17/tmp/ 目录下（默认）</h3>

<h3>3 发布脚本</h3>
每天定时定时任务会从以下地址获取最新版本的包，<b>请注意修改脚本后一定要发布至以下地址</b><br/>
http://storage.jd.local/moneta/Dual-Run/DualRunAnalysis3_0-1.0-SNAPSHOT.jar

 */
object V34UpgradeAssessment {
  val logger: Logger = Logger.getLogger(this.getClass)
  val shufflePartition = 600

  def addRandomFunction(spark: SparkSession, addErrorCodeDF: DataFrame, primaryKey: String): DataFrame = {
    val devFollower = spark.read.jdbc(tempJdbcUrl,
      s"(${randomFunctionSQL}) AS tmp", tempConnectionProperties)
    val devFollowerDF = addErrorCodeDF.join(devFollower, addErrorCodeDF(primaryKey) === devFollower("task_id"), "left").drop(devFollower("task_id"))
    devFollowerDF
  }

  def addFollower(spark: SparkSession, addErrorCodeDF: DataFrame, primaryKey: String): DataFrame = {
    val devFollower = spark.read.jdbc(tempJdbcUrl,
      s"(select task_id, dev_follower from dev_follower) AS tmp", tempConnectionProperties)
    val devFollowerDF = addErrorCodeDF.join(devFollower, addErrorCodeDF(primaryKey) === devFollower("task_id"), "left").drop(devFollower("task_id"))
    devFollowerDF
  }

  def addErrorCodeCategory(spark: SparkSession, dualRunTasks: DataFrame, addUpgradeRollbackDF: DataFrame) = {
    spark.table("tmp.lvfulong_fail_message_for_input_tasks").repartition(150, col("double_run_task_id"))
      .createOrReplaceTempView("lvfulong_fail_message_for_input_tasks_repartition")
    val errorCode = spark.sql(
      """
        |select double_run_task_id, count(1) as cn,
        |case
        |    when error_log like '%The table name is too long%' then 'The table name is too long'
        |    when error_log like '%java.net.UnknownHostException%' then 'ns not found'
        |    when error_log like '%LOCATION_ALREADY_EXISTS%' then 'LOCATION_ALREADY_EXISTS'
        |    when error_log like '%FileNotFoundException%Jar%' then 'not found jar'
        |    when error_log like '%mismatched input%' then 'mismatched input'
        |    when error_log like '%Failed to move to trash%' then 'Failed to move to trash'
        |    when error_log like '%FileNotFoundException%File file:%' then 'FileNotFoundException: File file:'
        |    when error_log like '%Destination directory  has not be cleaned up%' then 'Destination directory  has not be cleaned up'
        |    when error_log like '%extraneous input%' then 'extraneous input'
        |    when error_log like '%nondeterministic expressions are only allowed in%' then 'nondeterministic expressions are only allowed in'
        |    when error_log like '%DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE%' then 'DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE'
        |    when error_log like '%mismatched input \'call\' expecting%' then 'mismatched input \'call\' expecting'
        |    when error_log like '%[Driver] ERROR SparkSqlCluster%' then 'SparkSqlCluster'
        |    when error_log like '%not found in database%' then 'not found in database'
        |    when error_log like '%Caused by: MetaException(message:JD Secure[create database%' then 'create default database'
        |    when error_log like '%Operation not allowed: ROW FORMAT DELIMITED is only compatible with%' then 'ROW FORMAT DELIMITED'
        |    when error_log like '%ROW FORMAT SERDE is incompatible with format%' then 'ROW FORMAT SERD'
        |    when error_log like '%WRONG_NUM_ARGS%' then 'WRONG_NUM_ARGS'
        |    when error_log like '%requires that the data to be inserted have the same number of columns as the target table%' then 'different columns'
        |    when error_log like '%different row number after join%' then 'result not same'
        |    when error_log like '%files that exceeds the threshold%' then 'files that exceeds the threshold'
        |    when error_log like '%当前实例分配的客户端的内存已达到上限%' then '内存已达到上限'
        |    when error_log like '%java.lang.ClassNotFoundException: org.apache.hudi.hadoop.hive.HoodieStorageHandler%' then 'HoodieStorageHandler'
        |    when error_log like '%Error in query: Can not load class%' then 'class not load'
        |    when error_log like '%Error in query: Undefined function%' then 'function not found'
        |    when error_log like '%Failed to find data source: hudi%' then 'hudi table'
        |    when error_log like '%Caused by: java.lang.ClassNotFoundException: hudi.DefaultSource%' then 'hudi datasource'
        |    when error_log like '%Found duplicate keys%' then 'Found duplicate keys'
        |    when error_log like '%PATH_NOT_FOUND%' then 'PATH_NOT_FOUND'
        |    when error_log like '%Table not found%' then 'table not found'
        |    when error_log like '%Table or view not found%' then 'Table or view not found'
        |    when error_log like '%Can not create the managed table%' then 'Can not create the managed table'
        |    when error_log like '%Invalid argument to --conf%' then 'Invalid argument to --conf'
        |    when error_log like '%Error while trying to get column names%' then 'Error while trying to get column names'
        |    when error_log like '%is not in the NS white%' then 'is not in the NS white'
        |    when error_log like '%Could not open input file for reading%' then 'Could not open input file for reading'
        |    when error_log like '%Input path does not exist%' then 'Input path does not exist'
        |    when error_log like '%Spark-sql-cluster only supports%' then 'Spark-sql-cluster only supports'
        |    when error_log like '%任务执行完成，退出码：128%' then '任务执行完成，退出码：128'
        |    when error_log like '%has not be cleaned up%' then 'has not be cleaned up'
        |    when error_log like '%[PARSE_SYNTAX_ERROR] Syntax error at or near \'<\'%' then '[PARSE_SYNTAX_ERROR] Syntax error at or near \'<\''
        |    when error_log like '%[PARSE_SYNTAX_ERROR] Syntax error at or near \'<=\'%' then '[PARSE_SYNTAX_ERROR] Syntax error at or near \'<=\''
        |    when error_log like '%[PARSE_SYNTAX_ERROR] Syntax error at or near \'insert\'%' then '[PARSE_SYNTAX_ERROR] Syntax error at or near \'insert\''
        |    when error_log like '%Cannot resolve function%' then 'Cannot resolve function'
        |    when error_log like '%Could not find CoarseGrainedScheduler%' then 'Could not find CoarseGrainedScheduler'
        |    when error_log like '%代表两张表的数据均为空%' then '代表两张表的数据均为空'
        |    when error_log like '%different row number:%' then 'different row number:'
        |    when error_log like '%进行了终止操作%' then '进行了终止操作'
        |    when error_log like '%暂时仅支持10k集群%' then '暂时仅支持10k集群'
        |    when error_log like '%com.jd.compute.compare.Result%' then 'com.jd.compute.compare.Result'
        |    when error_log like '%Caused by: MetaException(message:Could not connect to meta store using any of the URIs provided. Most recent failure: org.apache.thrift.transport.TTransportException: java.net.ConnectException: Connection refused (Connection refused)%' then 'Caused by: MetaException(message:Could not connect to meta store using any of the URIs provided. Most recent failure: org.apache.thrift.transport.TTransportException: java.net.ConnectException: Connection refused (Connection refused)'
        |    when error_log like '%TABLE_OR_VIEW_NOT_FOUND%' then 'TABLE_OR_VIEW_NOT_FOUND'
        |    when error_log like '%Unable to fetch table%' then 'Unable to fetch table'
        |    when error_log like '%org.apache.hive.storage.jdbc.JdbcSerDe%' then 'org.apache.hive.storage.jdbc.JdbcSerDe'
        |    when error_log like '%org.apache.hadoop.hive.ql.metadata.HiveException: File does not exist:%' then 'org.apache.hadoop.hive.ql.metadata.HiveException: File does not exist:'
        |    when error_log like '%没找到表名%' then '没找到表名'
        |    when error_log like '%java.lang.IncompatibleClassChangeError%' then 'java.lang.IncompatibleClassChangeError'
        |    when error_log like '%The dataset to be written must be partitioned when dynamicPartitionOverwrite is true%' then 'The dataset to be written must be partitioned when dynamicPartitionOverwrite is true'
        |    when error_log like '%获取用户密钥信息异常%' then '获取用户密钥信息异常'
        |    when error_log like '%HiveIcebergSerDe%' then 'HiveIcebergSerDe'
        |    when error_log like '%Unclosed character class near index%' then 'Unclosed character class near index'
        |    when error_log like '%HiveIcebergStorageHandler%' then 'HiveIcebergStorageHandler'
        |    when error_log like '%NoSuchDatabaseException%' then 'NoSuchDatabaseException'
        |    when error_log like '%java.lang.ExceptionInInitializerError%' then 'java.lang.ExceptionInInitializerError'
        |    when error_log like '%Blacklisting behavior can be configured via spark.blacklist%' then 'Blacklisting behavior can be configured via spark.blacklist'
        |    when error_log like '%boosting spark.yarn.executor.memoryOverhead%' then 'boosting spark.yarn.executor.memoryOverhead'
        |    when error_log like '%Permission denied: user=%' then 'Permission denied: user='
        |    when error_log like '%Invalid call to toAttribute on unresolved object%' then 'Invalid call to toAttribute on unresolved object'
        |    when error_log like '%Window function row_number() requires window to be ordered%' then 'Window function row_number() requires window to be ordered'
        |    when error_log like '%HoodieParquetInputFormat%' then 'HoodieParquetInputFormat'
        |    when error_log like '%tar: Ignoring unknown extended header keyword%' then 'tar: Ignoring unknown extended header keyword'
        |    when error_log like '%脚本版本验证失败%' then 'script problem'
        |    when error_log like '%java.lang.IllegalArgumentException: spark.sql.files.maxPartitionBytes should be long%' then 'maxPartitionBytes should be long'
        |    when error_log like '%illegal cyclic reference involving object InterfaceAudience%' then 'illegal cyclic reference'
        |    when error_log like '%no viable alternative at input%' then 'no viable alternative at input'
        |    when error_log like '%Invalid call to%on unresolved object%' then 'call on unresolved object'
        |    when error_log like '%AnalysisException: No handler for%' then 'No handler for'
        |    when error_log like '%no viable alternative at input%' then 'no viable alternative at input'
        |    when error_log like '%A column or function parameter with name%cannot be resolved%' then 'A column or function parameter cannot be resolved'
        |    when error_log like '%FileNotFoundException%' then 'FileNotFoundException other'
        |    when error_log like '%FAILED: Execution Error, return code%' then 'Hive Execution Error'
        |    when error_log like '%Syntax error at or near%' then 'Syntax error at or near'
        |    when error_log like '%NoViableAltException%' then 'NoViableAltException'
        |    when error_log like '%AnalysisException: cannot resolve%' then 'AnalysisException: cannot resolve'
        |    when error_log like '%FAILED: Execution Error, return code%' then 'FAILED: Execution Error, return code'
        |    when error_log like '%character%not supported here%' then 'character not supported'
        |    when error_log like '%NullPointerException%' then 'NullPointerException'
        |    when error_log like '%返回状态为 2%' then '返回状态为 2'
        |    when error_log like '%返回状态为 1%' then '返回状态为 1'
        |    when error_log like '%Not enough memory to build and broadcast the table%' then 'Not enough memory to build and broadcast the table'
        |    when error_log like '%Path does not exist%' then 'Path does not exist'
        |    when error_log like '%does not exist%' then 'does not exist'
        |    when error_log like '%Cannot load class%' then 'Cannot load class'
        |    when error_log like '%cannot be used as a grouping expression because its data type%' then 'cannot be used as a grouping expression because its data type'
        |    when error_log like '%Futures timed out after%' then 'cannot be used as a grouping expression because its data type'
        |    when error_log like '%Exception thrown in awaitResult%' then 'Exception thrown in awaitResult'
        |    when error_log like '%Illegal repetition near index%' then 'Illegal repetition near index'
        |    when error_log like '%Attribute(s) with the same name appear in the operation: data_type%' then 'Attribute(s) with the same name appear in the operation'
        |    when error_log like '%java.lang.InterruptedException: sleep interrupted%' then 'sleep interrupted'
        |    when error_log like '%Cannot recognize hive type string%' then 'Cannot recognize hive type string'
        |    when error_log like '%due to data type mismatch%' then 'due to data type mismatch'
        |    when error_log like '%exceeding physical memory limits%' then 'exceeding physical memory limit'
        |    when error_log like '%is based on columns which are not participating in the GROUP BY clause%' then 'is based on columns which are not participating in the GROUP BY clause (other)'
        |    when error_log like '%Operation category READ is not supported in state standby%' then 'not supported in state standby'
        |    when error_log like '%killing job with: job%' then 'killing job with: job'
        |    when error_log like '%already exists in database%' then 'already exists in database'
        |    when error_log like '%Not a file%' then 'multiple partitions'
        |    when error_log like '%are currently not supported%' then 'are currently not supported'
        |    when error_log like '%File does not exist%' then 'File does not exist'
        |    when error_log like '%Found duplicate column(s) in the table definition%' then 'Found duplicate column(s) in the table definition'
        |    when error_log like '%does not have enough number of replicas%' then 'does not have enough number of replicas'
        |    when error_log like '%Not replicated yet%' then 'Not replicated yet'
        |    when error_log like '%No handler for UDF/UDAF/UDTF%' then 'No handler for UDF/UDAF/UDTF'
        |    when error_log like '%Job aborted due to stage failure%' then 'Job aborted due to stage failure'
        |    when error_log like '%is ambiguous, could be%' then 'is ambiguous, could be'
        |    when error_log like '%AnalysisErrorAt.failAnalysis%' then 'AnalysisErrorAt.failAnalysis'
        |    when error_log like '%doesn\'t have the samenumber of columns with the query column names%' then 'doesn\'t have the samenumber of columns with the query column names'
        |    when error_log like '%ERROR TransportResponseHandler: Still have 1 requests outstanding when connection from%' then 'ERROR TransportResponseHandler: Still have 1 requests outstanding when connection from'
        |    when error_log like '%Unable to instantiate org.apache.hadoop.hive.ql.metadata.SessionHiveMetaStoreClient%' then 'Unable to instantiate org.apache.hadoop.hive.ql.metadata.SessionHiveMetaStoreClient'
        |    when error_log like '%Failed to connect to the MetaStore Server%' then 'Failed to connect to the MetaStore Server'
        |    when error_log like '%The following partitions already exists in table%' then 'The following partitions already exists in table'
        |    when error_log like '%Unrecognized option: -XX:%' then 'Unrecognized option: -XX:'
        |    when error_log like '%ERROR 404: Not Found%' then 'ERROR 404: Not Found'
        |    else 'others'
        |end as reason
        |from lvfulong_fail_message_for_input_tasks_repartition
        |where
        |task_status='fail'
        |group by double_run_task_id, reason order by cn desc
        |""".stripMargin)
    val taskErrorCode = dualRunTasks.join(errorCode, dualRunTasks("dual_run_id") === errorCode("double_run_task_id"), "left").drop(dualRunTasks("dual_run_id")).drop(errorCode("double_run_task_id")).drop(dualRunTasks("name"))
    val originTaskErrorCode = taskErrorCode.groupBy("origin_buffalo_id").agg(
      count("*").alias("error_cnt"),
      concat_ws("|", collect_set("reason")).alias("reasons"))
    val addErrorCodeDF = addUpgradeRollbackDF.join(originTaskErrorCode, addUpgradeRollbackDF("task_id") === originTaskErrorCode("origin_buffalo_id"), "left").drop(originTaskErrorCode("origin_buffalo_id"))

    val errorCodeMysql = spark.read.jdbc(tempJdbcUrl,
      s"(${errorCodeSQL}) AS tmp", tempConnectionProperties)
    val maxInstanceId = errorCodeMysql.groupBy("origin_task_id", "double_run_task_id").agg(max("double_run_instance_id").alias("latest_instance_id")).select("latest_instance_id")
    val error01 = errorCodeMysql.join(maxInstanceId, errorCodeMysql("double_run_instance_id") === maxInstanceId("latest_instance_id"), "left").drop(maxInstanceId("latest_instance_id"))
    val error02 = error01.groupBy("origin_task_id")
      .agg(
        concat_ws("|", collect_set("double_run_task_id")).alias("dual_run_ids"),
        concat_ws("|", collect_set("error_description")).alias("dual_run_errors"),
        concat_ws("|", collect_set(concat_ws("-", col("error_description"), col("already_fix")))).alias("dual_run_error_fix")
      )
    addErrorCodeDF.join(error02, addErrorCodeDF("task_id") === error02("origin_task_id"), "left").drop(error02("origin_task_id"))
  }

  def getRequestVCore(spark: SparkSession, aggTask: DataFrame, startDateStr: String, endDateStr: String, field: String): DataFrame = {
    val startDate = LocalDate.parse(startDateStr)
    val endDate = LocalDate.parse(endDateStr)
    import java.time.temporal.ChronoUnit
    val daysBetween = ChronoUnit.DAYS.between(startDate, endDate)
    val upgrade9month = spark.table("gdm.gdm_m99_job_run_log_da")
      .filter(s"bee_source in ('BUFFALO4', '9N_BUFFALO4') and dt > '$startDate' AND dt <= '$endDate' AND job_type IN('spark', 'mr') AND finishedstatus IN('FINISHED', 'SUCCEEDED')")
      .groupBy("bee_source", "task_id").agg(round(sum("req_vcore_c_s") / 3600 / 24 / daysBetween, 2).alias(field))
    aggTask.join(upgrade9month, aggTask("bee_source") === upgrade9month("bee_source") and aggTask("task_id") === upgrade9month("task_id"), "left").drop(upgrade9month("bee_source")).drop(upgrade9month("task_id"))
  }

  def getRequestVCoreFollowingUpgrade(spark: SparkSession, df: DataFrame, upgradeDowngradeDT: String, field: String): DataFrame = {
    //    val df = Seq((942941, "2024-11-16", "UPGRADED")).toDF("origin_task_id", "update_dt", "upgrade_status")
    val startDate = LocalDate.parse("2024-11-01")
    val endDate = LocalDate.now()
    val upgrade9month = spark.table("gdm.gdm_m99_job_run_log_da")
      .filter(s"bee_source in ('BUFFALO4', '9N_BUFFALO4') and dt > '$startDate' AND dt <= '$endDate' AND job_type IN('spark', 'mr') AND finishedstatus IN('FINISHED', 'SUCCEEDED')")
      .groupBy("bee_source", "task_id", "dt").agg(round(sum("req_vcore_c_s") / 3600 / 24, 2).alias("req_vcore_day"))

    val task = upgrade9month.groupBy("bee_source", "task_id", "dt").agg(sum("req_vcore_day").alias("req_vcore_day"))
    val join = df.join(task, task("task_id") === df("origin_task_id") and df("upgrade_status") === "UPGRADED", "left")
    join.filter(task("dt") > df(upgradeDowngradeDT) and task("dt") <= date_add(df(upgradeDowngradeDT), 7))
      .groupBy(df("origin_task_id").alias("o_task_id"))
      .agg(round(sum(task("req_vcore_day")) / countDistinct(task("dt")), 2).alias(field))
  }

  def addIsRSS(spark: SparkSession, aggTask_allHT: DataFrame): DataFrame = {
    val today: LocalDate = LocalDate.now()
    val tPlus1: LocalDate = today.plusDays(-1)
    val tPlus7: LocalDate = today.plusDays(-7)
    //    val tPlus7: LocalDate = today.plusDays(-2)
    val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    val isRssDF = spark.sql(
      s"""
         |SELECT
         |	buffaloEnvTaskDefId,
         |	(instr(c.spark_config_all, 'spark.shuffle.manager -> remote') > 0
         |			AND instr(c.spark_config_all, 'spark.shuffle.rss.enabled -> true') > 0) AS isRSS
         |FROM
         |	fdm.fdm_spark_environmentinfo_di as c
         |WHERE
         |	beeSource = 'BUFFALO4'
         |	AND dt >= '${tPlus7.format(formatter)}'
         |	AND dt <= '${tPlus1.format(formatter)}'
         |GROUP BY
         |	buffaloEnvTaskDefId,
         |	(instr(c.spark_config_all, 'spark.shuffle.manager -> remote') > 0
         |			AND instr(c.spark_config_all, 'spark.shuffle.rss.enabled -> true') > 0)
         |""".stripMargin)
    val isRssDF2 = isRssDF.filter("isRSS = true").groupBy("buffaloEnvTaskDefId").agg(count("*").alias("used_rss_last_week"))
    aggTask_allHT.join(isRssDF2, aggTask_allHT("task_id") === isRssDF2("buffaloEnvTaskDefId"), "left").drop(isRssDF2("buffaloEnvTaskDefId"))
  }

  def saveAsMySQL(writeToMySQL: Boolean, devFollowerDF: DataFrame): Unit = {
    if (writeToMySQL) {
      // 摸底数据存入MySQL
      val tableName = "v34_upgrade_assessment_2"
      devFollowerDF.repartition(10).write.format("jdbc")
        .option("url", "***************************************************************************")
        .option("dbtable", tableName)
        .option("driver", "com.mysql.jdbc.Driver")
        .option("user", "salt")
        .option("password", "salt")
        .mode("overwrite")
        .save()
    }
  }

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.shuffle.targetPostShuffleInputSize", "41943040")
      .config("spark.dynamicAllocation.initialExecutors", "200")
      .config("spark.dynamicAllocation.minExecutors", "200")
      .config("spark.dynamicAllocation.maxExecutors", "2000")
      .config("spark.sql.shuffle.partitions", shufflePartition)
      .config("spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version", 2)
      .appName("V34UpgradeAssessment App")
      .enableHiveSupport()
      .getOrCreate()

    //    val args = Seq()
    import spark.implicits._

    // 格式化日期时间
    val defaultPath = "hdfs://ns17/tmp/v34_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))

    val defaultVal = Seq(defaultPath, false, false)
    val args2 = args.padTo(defaultVal.length, "")
    val handleDefaultArgs = args2.zip(defaultVal).map { case (param, default) =>
      if (param == null || param.isEmpty) default else param
    }
    val outputDir = handleDefaultArgs(0).toString
    val scoreEnabled = handleDefaultArgs(1).toString.toBoolean
    val writeToMySQL = handleDefaultArgs(2).toString.toBoolean

    val scorePartition = shufflePartition * 4
    val today: LocalDate = LocalDate.now()
    val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    val beforeYesterday = today.minusDays(2).format(formatter)
    val tMinus1: LocalDate = today.minusDays(1)
    val tMinus14: LocalDate = today.minusDays(14)

    val convertDt = udf { (update_time: String) => DateTimeConverter.convert(update_time)}

    val upgradeDowngradeDT = "upgradeDowngradeDT"
    val upgradedAndRollbackTasks = spark.read.jdbc(tempJdbcUrl,
      s"(${upgradedAndRollbackTasksSQL}) AS tmp", tempConnectionProperties)
      .select(col("origin_task_id"),
        col("status").alias("upgrade_status"),
        col("update_time").alias("upgradeDowngradeDate"))
      .withColumn(upgradeDowngradeDT, convertDt($"upgradeDowngradeDate"))

    // Job 表中 Spark 和 hive 任务
    val date2024716 = "2024-07-16"
    val date2024730 = "2024-07-30"
    val date2024801 = "2024-08-01"
    val date20241205 = "2024-12-05"

    // Generate SQL aggregates based on TaskId
    def generateSQL(m99RunLogTableName: String, days: Integer): String = {
      s"""
         |SELECT
         |	bee_source,
         |	task_id,
         |	AVG(total_long_s / 60) AS task_duration_minutes,
         |	ROUND(SUM(req_vcore_c_s / 3600 / 24) / $days, 2) AS req_vcore_day,
         |	ROUND(SUM(total_long_s / 60) / $days, 2) AS sum_exe_min,
         |	ROUND(COUNT(1) / $days, 2) AS job_num
         |FROM
         |	(
         |		SELECT
         |			r1.task_id,
         |			r1.bee_source,
         |			r1.job_id,
         |			r1.req_vcore_c_s,
         |			r1.exe_long_s,
         |			r1.total_long_s,
         |			r1.job_user,
         |			r1.dt
         |		FROM
         |			(
         |			${m99RunLogTableName}
         |			)
         |			r1
         |	)
         |GROUP BY
         |	bee_source,
         |	task_id
         |""".stripMargin
    }

    def getAppendDF(incrementalDt: String): DataFrame = {
      // The number of days in which incremental tasks are counted
      val incrementalDays = 7

      val sql =
        s"""
          |select task_id,run_dt as dt from tmp.wangzhehan_all_data_statistic_new where dt='${incrementalDt}'
          |and bee_source='BUFFALO4' and job_type='spark' and is_spark34='0' and sql_submit='1'
          |""".stripMargin

      spark.sql(sql).createOrReplaceTempView("append_sql_with_dt")

      spark.table("append_sql_with_dt").show

      val m99JobRunSql =
        """
          |				SELECT
          |					job_type,
          |					bee_source,
          |					job_id,
          |					job_user,
          |					task_id,
          |					bee_businessid,
          |					bee_sn,
          |					req_mem_mbs_s,
          |					used_mem_mbs_s,
          |					req_vcore_c_s,
          |					exe_long_s,
          |					total_long_s,
          |					dt,
          |					finishedstatus,
          |					session_id,
          |					hql
          |				FROM
          |					gdm.gdm_m99_job_run_log_da
          |				WHERE
          |					job_type IN('spark', 'mr')
          |					AND finishedstatus IN('FINISHED', 'SUCCEEDED')
          |""".stripMargin
      spark.sql(m99JobRunSql).createOrReplaceTempView("m99_job_run_log_successed")

      spark.sql(
        s"""
           |select b.*
           |from append_sql_with_dt as a
           |inner join m99_job_run_log_successed as b
           |on a.task_id = b.task_id
           |and b.dt > date_sub(a.dt, $incrementalDays)
           |and b.dt <= a.dt
           |and b.bee_source = 'BUFFALO4'
           |""".stripMargin).createOrReplaceTempView("gdm_run_log_append")

      // Check whether the number of data records is 7
      // Because only the last 7 days are counted
      spark.table("gdm_run_log_append").where("task_id = '1675398'")
        .groupBy("task_id", "dt").count().show

      def generateRunLogQuery(startDate: String, endDate: String): String = {
        s"""
           |				SELECT
           |					job_type,
           |					bee_source,
           |					job_id,
           |					job_user,
           |					task_id,
           |					bee_businessid,
           |					bee_sn,
           |					req_mem_mbs_s,
           |					used_mem_mbs_s,
           |					req_vcore_c_s,
           |					exe_long_s,
           |					total_long_s,
           |					dt,
           |					finishedstatus,
           |					session_id,
           |					hql
           |				FROM
           |					gdm.gdm_m99_job_run_log_da
           |				WHERE
           |					dt >= '${startDate}'
           |					AND dt <= '${endDate}'
           |					AND job_type IN('spark', 'mr')
           |					AND finishedstatus IN('FINISHED', 'SUCCEEDED')
           |""".stripMargin
      }

      spark.sql(generateRunLogQuery(date2024716, date2024730))
        .createOrReplaceTempView("gdm_run_log")

      val jobAppendDF = spark.sql(generateSQL("gdm_run_log_append", 7))
      jobAppendDF.persist()
      // Verify duplicate data
      jobAppendDF.where("task_id = '1675398'").show
      jobAppendDF.groupBy("task_id").agg(count("*").alias("cnt_dt")).where("cnt_dt > 1").show
      jobAppendDF.withColumn("dt", lit(incrementalDt)).write.format("orc").mode("overwrite").partitionBy("dt").saveAsTable("tmp.wuguoxiao_run_log_append")
      jobAppendDF.unpersist()

      // Detect duplicates in incremental tasks
      spark.table("tmp.wuguoxiao_run_log_append").groupBy("task_id", "dt").agg(count("*").alias("cnt_dt")).where("cnt_dt > 1").show
      spark.table("tmp.wuguoxiao_run_log_append").printSchema()
      // |-- bee_source: string (nullable = true)
      // |-- task_id: string (nullable = true)
      // |-- task_duration_minutes: double (nullable = true)
      // |-- req_vcore_day: double (nullable = true)
      // |-- sum_exe_min: double (nullable = true)
      // |-- job_num: double (nullable = true)
      // |-- dt: string (nullable = true)
      spark.sql(
        """
          |SELECT
          |	bee_source,
          |	task_id,
          |	AVG(task_duration_minutes) AS task_duration_minutes,
          |	AVG(req_vcore_day) AS req_vcore_day,
          |	AVG(sum_exe_min) AS sum_exe_min,
          |	AVG(job_num) AS job_num
          |FROM
          |	tmp.wuguoxiao_run_log_append
          |GROUP BY
          |	bee_source,
          |	task_id,
          |	dt
          | """.stripMargin)
    }

    val jobBeforeAug = spark.sql(generateSQL("gdm_run_log", 15))

    val jobSinceAugSql =
      s"""
         |SELECT
         |	tb.bee_source,
         |	tb.task_id,
         |	AVG(total_long_s / 60) AS task_duration_minutes,
         |	ROUND(AVG(req_vcore_c_s / 3600 / 24), 2) AS req_vcore_day,
         |	ROUND(AVG(total_long_s / 60), 2) AS sum_exe_min,
         |	ROUND(COUNT(1) / COUNT(DISTINCT dt), 2) AS job_num
         |FROM
         |	(
         |		SELECT
         |			r1.task_id,
         |			r1.bee_source,
         |			r1.job_id,
         |			r1.req_vcore_c_s,
         |			r1.exe_long_s,
         |			r1.total_long_s,
         |			r1.job_user,
         |			r1.dt
         |		FROM
         |			(
         |				SELECT
         |					job_type,
         |					bee_source,
         |					job_id,
         |					job_user,
         |					job_name,
         |					task_id,
         |					bee_businessid,
         |					req_mem_mbs_s,
         |					used_mem_mbs_s,
         |					req_vcore_c_s,
         |					exe_long_s,
         |					total_long_s,
         |					dt
         |				FROM
         |					gdm.gdm_m99_job_run_log_da xx
         |				WHERE
         |					dt >= '${date2024801}'
         |					AND dt <= '${date20241205}'
         |					AND job_type IN('spark', 'mr')
         |					AND finishedstatus IN('FINISHED', 'SUCCEEDED')
         |					AND EXISTS
         |					(
         |						SELECT
         |							1
         |						FROM
         |							tmp.lvfulong_new_create_task_sine_0801 yy
         |						WHERE
         |							xx.task_id = yy.task_id
         |					)
         |			)
         |			r1
         |	)
         |	tb
         |JOIN tmp.lvfulong_new_create_task_from_0801 tb1
         |ON
         |	tb.task_id = tb1.task_id
         |	AND tb.dt >= tb1.create_dt
         |	AND tb.dt <= date_add(tb1.create_dt, 14)
         |GROUP BY
         |	tb.bee_source,
         |	tb.task_id
         |""".stripMargin
    val jobSinceAug = spark.sql(jobSinceAugSql)
    // As of 2015-01-21, the number of statistical jobs is 678591
    var job = jobBeforeAug.union(jobSinceAug)

    val appendDF = getAppendDF(tMinus1.format(formatter))
    job = job.union(appendDF)

    job.filter("bee_source in ('BUFFALO4', '9N_BUFFALO4')").select("task_id").distinct().createOrReplaceTempView("distinct_task_id")

    spark.sql("select distinct task_id as task_id_hive from tmp.lvfulong_jisuan_not_hive_task_jobs").createOrReplaceTempView("distinct_not_hive_task")
    spark.sql(
      """
        |select
        |    task_id_hive
        |from distinct_not_hive_task as a
        |where not exists (
        |    select 1
        |    from distinct_task_id as b
        |    where a.task_id_hive = b.task_id
        |)
        |""".stripMargin)
      .createOrReplaceTempView("not_hive_task")

    spark.sql(
      """
        |				SELECT
        |					job_type,
        |					bee_source,
        |					job_id,
        |					job_user,
        |					job_name,
        |					task_id,
        |					bee_businessid,
        |					req_mem_mbs_s,
        |					used_mem_mbs_s,
        |					req_vcore_c_s,
        |					exe_long_s,
        |					total_long_s,
        |					dt
        |				FROM
        |					gdm.gdm_m99_job_run_log_da xx
        |				WHERE
        |					dt >= '2024-08-01'
        |					AND dt <= '2024-12-05'
        |					AND job_type IN('spark', 'mr')
        |					AND finishedstatus IN('FINISHED', 'SUCCEEDED')
        |					AND EXISTS
        |					(
        |						SELECT
        |							1
        |						FROM
        |							not_hive_task yy
        |						WHERE
        |							xx.task_id = yy.task_id_hive
        |					)
        |""".stripMargin).createOrReplaceTempView("run_log_da_not_hive_task")

    spark.sql("""
        |select
        |   task_id,
        |   min(dt) as create_dt
        |from run_log_da_not_hive_task
        |group by task_id
        |""".stripMargin).createOrReplaceTempView("not_hive_task_created_dt")

    val notHiveTaskDF = spark.sql(
      s"""
         |SELECT
         |	tb.bee_source,
         |	tb.task_id,
         |	AVG(total_long_s / 60) AS task_duration_minutes,
         |	ROUND(AVG(req_vcore_c_s / 3600 / 24), 2) AS req_vcore_day,
         |	ROUND(AVG(total_long_s / 60), 2) AS sum_exe_min,
         |	ROUND(COUNT(1) / COUNT(DISTINCT dt), 2) AS job_num
         |FROM
         |	(
         |		SELECT
         |			r1.task_id,
         |			r1.bee_source,
         |			r1.job_id,
         |			r1.req_vcore_c_s,
         |			r1.exe_long_s,
         |			r1.total_long_s,
         |			r1.job_user,
         |			r1.dt
         |		FROM
         |			run_log_da_not_hive_task r1
         |	)
         |	tb
         |JOIN not_hive_task_created_dt tb1
         |ON
         |	tb.task_id = tb1.task_id
         |	AND tb.dt >= tb1.create_dt
         |	AND tb.dt <= date_add(tb1.create_dt, 14)
         |GROUP BY
         |	tb.bee_source,
         |	tb.task_id
         |""".stripMargin)

    val addNoHiveTask = notHiveTaskDF.union(job)

    val upgradeDF1 = upgradedAndRollbackTasks.select(col("origin_task_id").alias("upgrade_task_id"))
    val job2 = addNoHiveTask.join(upgradeDF1, addNoHiveTask("task_id") === upgradeDF1("upgrade_task_id"), "left")

    // 增加研发负责人
    val devFollowerDF: DataFrame = addFollower(spark, job2, "task_id")

    val taskLevelSQL =
      s"""
         |SELECT
         |	task_id,
         |	priority,
         |	status as task_status,
         |	task_owner,
         |	job_cycle,
         |	task_hour,
         |	task_cate as task_first_category,
         |	task_type_name as task_2nd_category,
         |	full_exec_long / 60 as exec_long_first_minutes,
         |	exec_long['1days'] / 60 as exec_long_1day_minutes,
         |	owner_state,
         |	bgname,
         |	dept_1_name,
         |	dept_2_name,
         |	dept_3_name,
         |	dt as task_info_dt
         |FROM
         |	adm.adm_m99_task_detail_info_da
         |WHERE
         |	dt = '${beforeYesterday}'
         |	AND datatype IN('BUFFALO4')
         |	AND is_delete = '0'
         |	AND status NOT IN('冻结', '已升级', '过期')
         |	AND task_cate IN('工作流任务', '标准任务')
         |""".stripMargin
    val taskLevel = spark.sql(taskLevelSQL)
    val addDepartDT = devFollowerDF.join(taskLevel, devFollowerDF("task_id") === taskLevel("task_id"), "left").drop(taskLevel("task_id"))

    val aggTask = addDepartDT //.filter("dev_follower is not null or upgrade_task_id is not null or dept_2_name in ('数据资产与应用部', '智能平台部', '集团数据计算平台部', '供应链产品研发部')")

    // 升级后使用的资源（9月底）
    val upgrade9monthDF = getRequestVCore(spark, aggTask, "2024-09-15", "2024-09-30", "req_vcore_m09")
    // 升级后使用的资源（10月底）
    val upgrade10monthDF = getRequestVCore(spark, upgrade9monthDF, "2024-10-15", "2024-10-30", "req_vcore_m10")
    val upgrade11monthDF = getRequestVCore(spark, upgrade10monthDF, "2024-11-15", "2024-11-30", "req_vcore_m11")

    // Spark 任务是否全是 HiveTask
    val isAllHiveTaskSQL =
      """
        |SELECT
        |	m.task_id,
        |	CASE
        |		WHEN m.source_count = 1
        |			AND m.task_count = m.total_count
        |		THEN 'Y'
        |		ELSE 'N'
        |	END AS all_hivetask
        |FROM
        |	(
        |		SELECT
        |			buffaloEnvTaskDefId AS task_id,
        |			COUNT(DISTINCT spark_sql_source) AS source_count,
        |			COUNT(
        |				CASE
        |					WHEN spark_sql_source = 'HiveTask'
        |					THEN 1
        |				END) AS task_count,
        |			COUNT( *) AS total_count
        |		FROM
        |			fdm.fdm_spark_environmentinfo_di
        |		WHERE
        |			dt >= '2024-07-16'
        |			AND dt <= '2024-07-30'
        |		GROUP BY
        |			buffaloEnvTaskDefId
        |	)
        |	m
        |""".stripMargin
    val isAllHiveTask = spark.sql(isAllHiveTaskSQL)
    val aggTask_allHT = upgrade11monthDF.join(isAllHiveTask, upgrade11monthDF("task_id") === isAllHiveTask("task_id"), "left").drop(isAllHiveTask("task_id"))

    // 增加是否 RSS
    val addRssDF: DataFrame = addIsRSS(spark, aggTask_allHT)

    def getElapsed(dt: String): DataFrame = {
      val elapsedTime040531SQL =
        s"""
           |SELECT
           | task_id,
           | full_exec_long / 60 as exec_long_${dt.replaceAll("-", "_")}
           |FROM
           |	adm.adm_m99_task_detail_info_da
           |WHERE
           |	dt = '${dt}'
           |	AND datatype IN('BUFFALO4')
           |	AND is_delete = '0'
           |	AND status NOT IN('冻结', '已升级', '过期')
           |	AND task_cate IN('工作流任务', '标准任务')
           |""".stripMargin
      spark.sql(elapsedTime040531SQL)
    }

    // 2024-05-31 24年618开门红
    val elapsedTime040531DF = getElapsed("2024-05-31")
    val addElapsedTime040531DF = addRssDF.join(elapsedTime040531DF, addRssDF("task_id") === elapsedTime040531DF("task_id"), "left").drop(elapsedTime040531DF("task_id"))

    // 2023-10-31 23年双11开门红
    val elapsedTime031031DF = getElapsed("2023-10-31")
    val addElapsedTime031031DF = addElapsedTime040531DF.join(elapsedTime031031DF, addElapsedTime040531DF("task_id") === elapsedTime031031DF("task_id"), "left").drop(elapsedTime031031DF("task_id"))

    // 2024-10-31 24年双11开门红
    val elapsedTime041031DF = getElapsed("2024-10-31")
    val addElapsedTime041031DF = addElapsedTime031031DF.join(elapsedTime041031DF, addElapsedTime031031DF("task_id") === elapsedTime041031DF("task_id"), "left").drop(elapsedTime041031DF("task_id"))

    val elapsedTime041111DF = getElapsed("2024-11-11")
    val addElapsedTime041111DF = addElapsedTime041031DF.join(elapsedTime041111DF, addElapsedTime041031DF("task_id") === elapsedTime041111DF("task_id"), "left").drop(elapsedTime041111DF("task_id"))

    val elapsedTime041101DF = getElapsed("2024-11-01")
    val addelapsedTime041101DF = addElapsedTime041111DF.join(elapsedTime041101DF, addElapsedTime041111DF("task_id") === elapsedTime041101DF("task_id"), "left").drop(elapsedTime041101DF("task_id"))

    val elapsedTime031111DF = getElapsed("2023-11-11")
    val addelapsedTime031111DF = addelapsedTime041101DF.join(elapsedTime031111DF, addelapsedTime041101DF("task_id") === elapsedTime031111DF("task_id"), "left").drop(elapsedTime031111DF("task_id"))

    // 增加HiveTask标识
    val hivetaskSQL =
      s"""
         |SELECT
         |	bufflo_id,
         |	IF(SUM(IF(engine = 'hive', 1, 0)) > 0, true, false) AS isHive,
         |	IF(SUM(IF(engine = 'spark', 1, 0)) > 0, true, false) AS isSpark
         |FROM
         |	app.app_hivetask_log
         |WHERE
         |	dt >= '2024-07-15' AND dt <= '2024-12-15'
         |	AND source = 'buffalo4'
         |GROUP BY
         |	bufflo_id
         |""".stripMargin
    val hivetaskDF = spark.sql(hivetaskSQL)
    val addHiveTaskFlag = addelapsedTime031111DF.join(hivetaskDF,
      addelapsedTime031111DF("task_id") === hivetaskDF("bufflo_id"), "left")
      .drop(hivetaskDF("bufflo_id"))

    // spark版本、spark任务类型（sql、jar包、python等）、集市
    val sparkVersionAndType = spark.sql(
      s"""
        |SELECT
        |	beesource,
        |	buffaloenvtaskdefid,
        |	concat_ws(',', collect_set(spark_version)) AS spark_versions,
        |	concat_ws(',', collect_set(spark_app_type)) AS spark_app_types,
        | concat_ws(',', collect_set(market)) AS spark_markets
        |FROM
        |	(
        |		SELECT
        |			regexp_extract(a.appSparkVersion, '(\\\\d+\\\\.\\\\d+)', 1) AS spark_version,
        |			a.market,
        |			b.beeSource,
        |			b.spark_app_type,
        |			b.buffaloenvtaskdefid
        |		FROM
        |			fdm.fdm_spark_appinfo_di AS a
        |		INNER JOIN fdm.fdm_spark_environmentinfo_di AS b
        |		ON
        |			a.appid = b.appid
        |			AND a.appattemptid = b.appattemptid
        |			AND a.dt = b.dt
        |		WHERE
        |			a.dt >= '2024-07-15' AND a.dt <= '2024-12-15'
        |			AND b.dt >= '2024-07-15' AND b.dt <= '2024-12-15'
        |			AND beesource IN('BUFFALO4', '9N_BUFFALO4')
        |	)
        |GROUP BY
        |	beesource,
        |	buffaloenvtaskdefid
        |""".stripMargin).withColumn("only_spark_sql", col("spark_app_types").equalTo("SPARK-SqlShell"))
    val sparkVersionAndTypeDF = addHiveTaskFlag.join(sparkVersionAndType, addHiveTaskFlag("task_id") === sparkVersionAndType("buffaloenvtaskdefid")
      and addHiveTaskFlag("bee_source") === sparkVersionAndType("beesource"), "left")
      .drop(sparkVersionAndType("buffaloenvtaskdefid")).drop(sparkVersionAndType("beesource"))

    // 大 Shuffle 标识
    val shuffleHeavyTasksDF = spark.sql(shuffleHeavyTasksSQL)
    val shuffleHeavyTasksDF2 = sparkVersionAndTypeDF.join(shuffleHeavyTasksDF, sparkVersionAndTypeDF("task_id") === shuffleHeavyTasksDF("task_id"), "left").drop(shuffleHeavyTasksDF("task_id"))

    // 所处阶段：7升级成功、4.1双跑失败、6已进入回归池
    val upgradeDF = spark.sql("select task_id, status from tmp.lvfulong_task_with_status")
    val aggTask_allHt_level_online = shuffleHeavyTasksDF2.join(upgradeDF, shuffleHeavyTasksDF2("task_id") === upgradeDF("task_id"), "left").drop(upgradeDF("task_id"))

    // 任务层级
    val levelDF = spark.sql("select task_id,level from tmp.lvfulong_all_levels_task_table")
    val taskLevelDF = aggTask_allHt_level_online.join(levelDF,
      aggTask_allHt_level_online("task_id") === levelDF("task_id"), "left").drop(levelDF("task_id"))
      .na.fill("1 执行计划待校验", Seq("status"))

    // 是否是数据资产的P95作业
    val shuzi_p95DF = taskLevelDF.withColumn("p95", col("sum_exe_min") > 49.2)

    // 所属基线id
    val baselineSQL = "select task_id,concat_ws('|', collect_set(baseline_id)) as baseline from tmp.tmp_base_line_tasks group by task_id"
    val baseline = spark.sql(baselineSQL)
    val baselineDF = shuzi_p95DF.join(baseline,
      shuzi_p95DF("task_id") === baseline("task_id"), "left").drop(baseline("task_id"))

    // 是否关键路径
    val criticalPath = spark.read.jdbc(tempJdbcUrl,
      s"(${taskCriticalPath}) AS tmp", tempConnectionProperties)
    val addCriticalPath = baselineDF.join(criticalPath, baselineDF("task_id")===criticalPath("taskid"), "left").drop(criticalPath("taskid"))

    // 是否已创建双跑作业
    val dualRunTasks = spark.read.jdbc(jdbcUrl,
      s"(${dualRunTasksSQL}) AS tmp", connectionProperties)
    val dualRunTaskCreatedDF = dualRunTasks.select("origin_buffalo_id").distinct().withColumn("is_double_run_task_created", lit("Y"))
    val addDualRunTaskCreatedDF = addCriticalPath.join(dualRunTaskCreatedDF, addCriticalPath("task_id") === dualRunTaskCreatedDF("origin_buffalo_id"), "left").drop(dualRunTaskCreatedDF("origin_buffalo_id"))

    val sparkGCDF = if(scoreEnabled) {
      // Driver GC、Executor GC
      val inspectionCheckUdf = udf { (taskId: Long, checkNo: Int, beeSource: String) =>
        val startTimeMillis = System.currentTimeMillis()
        var hasWarn = false
        if (beeSource == "BUFFALO4" || beeSource == "9N_BUFFALO4") {
          import java.time.{LocalDateTime, ZoneId}
          val zoneId: ZoneId = ZoneId.systemDefault()
          val now: LocalDateTime = LocalDateTime.now(zoneId)
          val nowInSeconds: Long = now.toEpochSecond(zoneId.getRules.getOffset(now))
          val twoHoursAgo: LocalDateTime = now.minusHours(2)
          val twoHoursAgoInSeconds: Long = twoHoursAgo.toEpochSecond(zoneId.getRules.getOffset(twoHoursAgo))
          val baseUrl = "http://baizepr-hadoop.jd.com/api/v1/query_range"
          val params = Map(
            "query" -> s"check_task_item{task_id='${taskId}',check_no='${checkNo}'}==1",
            "end" -> nowInSeconds.toString,
            "start" -> twoHoursAgoInSeconds.toString,
            "step" -> "14"
          )
          val encodedParams = params.map { case (key, value) =>
            s"${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value, "UTF-8")}"
          }.mkString("&")
          val urlWithParams = s"$baseUrl?$encodedParams"

          val connection = new URL(urlWithParams).openConnection().asInstanceOf[HttpURLConnection]
          connection.setRequestMethod("GET")

          try {
            val response = Source.fromInputStream(connection.getInputStream).mkString
            val jsonObj = JSON.parseObject(response)
            hasWarn = if ("success".equalsIgnoreCase(jsonObj.getString("status"))) {
              val data = jsonObj.getJSONObject("data")
              if (data != null) {
                val result = data.getJSONArray("result")
                result.size() > 0
              } else false
            } else false
            hasWarn
          } finally {
            connection.disconnect()
            logger.info(s"url: ${urlWithParams} hasWarn: ${hasWarn} timeTaken: ${System.currentTimeMillis() - startTimeMillis}")
          }
        } else {
          hasWarn
        }
      }
      // 健康分
      val scoreUdf = udf { (taskId: Long, beeSource: String) =>
        val startTimeMillis = System.currentTimeMillis()
        var score = -1.0d
        if (beeSource == "BUFFALO4" || beeSource == "9N_BUFFALO4") {
          import java.time.{LocalDateTime, ZoneId}
          val zoneId: ZoneId = ZoneId.systemDefault()
          val now: LocalDateTime = LocalDateTime.now(zoneId)
          val nowInSeconds: Long = now.toEpochSecond(zoneId.getRules.getOffset(now))

          val twoHoursAgo: LocalDateTime = now.minusHours(2)
          val twoHoursAgoInSeconds: Long = twoHoursAgo.toEpochSecond(zoneId.getRules.getOffset(twoHoursAgo))
          val baseUrl = "http://baizepr-hadoop.jd.com/api/v1/query_range"
          val params = Map(
            "query" -> s"check_task_score{task_id='${taskId}'}",
            "end" -> nowInSeconds.toString,
            "start" -> twoHoursAgoInSeconds.toString,
            "step" -> "14"
          )
          val encodedParams = params.map { case (key, value) =>
            s"${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value, "UTF-8")}"
          }.mkString("&")
          val urlWithParams = s"$baseUrl?$encodedParams"
          val connection = new URL(urlWithParams).openConnection().asInstanceOf[HttpURLConnection]
          connection.setRequestMethod("GET")

          try {
            val response = Source.fromInputStream(connection.getInputStream).mkString
            val jsonObj = JSON.parseObject(response)
            score = if ("success".equalsIgnoreCase(jsonObj.getString("status"))) {
              val data = jsonObj.getJSONObject("data")
              if (data != null) {
                val result = data.getJSONArray("result")
                if (!result.isEmpty) {
                  val values = result.getJSONObject(0).getJSONArray("values")
                  val scoreArr = values.getJSONArray(values.size() - 1)
                  scoreArr.getDouble(1).doubleValue()
                } else -1.0
              } else -1.0
            } else -1.0
            score
          } finally {
            connection.disconnect()
            logger.info(s"url: ${urlWithParams} score: ${score} timeTake: ${System.currentTimeMillis() - startTimeMillis}")
          }
        } else {
          score
        }
      }
      addDualRunTaskCreatedDF.repartition(scorePartition)
        .withColumn("score", scoreUdf($"task_id", $"bee_source"))
        .withColumn("driver_gc", inspectionCheckUdf($"task_id", lit(2000), $"bee_source"))
        .withColumn("executor_gc", inspectionCheckUdf($"task_id", lit(2001), $"bee_source"))
    } else {
      addDualRunTaskCreatedDF
    }
    // 双跑 2.4/3.4 耗时
    val dualRunTableDF = spark.sql("select buffalo_id,duation_24,duation_34 from tmp.lvfulong_double_run_origin_task_time")
    val addCompareDualRunTime = sparkGCDF.join(dualRunTableDF, sparkGCDF("task_id") === dualRunTableDF("buffalo_id"), "left").drop(dualRunTableDF("buffalo_id"))

    // 检测是否语法解析通过
    val explainCheckSQL = spark.read.jdbc(tempJdbcUrl,
      s"(${explainCheckSql}) AS tmp", tempConnectionProperties)
    val addExplainCheckDF = addCompareDualRunTime.join(explainCheckSQL, addCompareDualRunTime("task_id") === explainCheckSQL("buffalo_id"), "left").drop(explainCheckSQL("buffalo_id"))

    // 最新实例的状态和2.4/3.4耗时
    val lastInstanceTasks = spark.read.jdbc(jdbcUrl, s"(${latestInsId}) AS tmp", connectionProperties).select("dual_run_id", "origin_buffalo_id", "latest_ins_id", "created").withColumn("task_create_dt", convertDt($"created")).drop(col("created"))
    val latestCreate = lastInstanceTasks.groupBy("origin_buffalo_id").agg(max(col("task_create_dt")).alias("task_latest_create_dt"))
    val lastInstance = lastInstanceTasks.join(latestCreate, lastInstanceTasks("origin_buffalo_id") === latestCreate("origin_buffalo_id") and lastInstanceTasks("task_create_dt") === latestCreate("task_latest_create_dt"))
      .drop(latestCreate("origin_buffalo_id")).drop(latestCreate("task_latest_create_dt"))
    val lastInstRDD = lastInstance.rdd.mapPartitions {
      partition =>
        var connection: Connection = null
        try {
          connection = DriverManager.getConnection(jdbcUrl, jdbcUser, jdbcPassword)
          val statement = connection.createStatement()
          partition.map {
            row =>
              val lastInstId = row.getAs[Long]("latest_ins_id")
              val query = s"SELECT run_status, instance_id, run_time FROM b_run_log WHERE instance_id = ${lastInstId}"
              val resultSet = statement.executeQuery(query)
              val (runStatus, instanceId, runTime) = if (resultSet.next())
                (resultSet.getString(1), resultSet.getLong(2), resultSet.getString(3))
              else (null, null, null)

              val durationQuery = s"SELECT sum(if(args like '2_4%', duration, 0)) as duration_24, " +
                s"sum(if(args like '3_4%', duration, 0)) as duration_34 " +
                s"FROM b_run_log WHERE task_ins_id = ${lastInstId} and instance_type = 2"
              val durationResultSet = statement.executeQuery(durationQuery)
              val (duration24, duration34) = if (durationResultSet.next())
                (durationResultSet.getLong(1), durationResultSet.getLong(2))
              else (null, null)
              Row.fromSeq(row.toSeq :+ runStatus :+ duration24 :+ duration34 :+ instanceId :+ runTime)
          }
        } finally {
        }
    }
    val newSchema = StructType(lastInstance.schema.fields :+
      StructField("run_status", StringType, nullable = true) :+
      StructField("duration24", LongType, nullable = true) :+
      StructField("duration34", LongType, nullable = true) :+
      StructField("lastInstId", LongType, nullable = true) :+
      StructField("lastInstRuntime", StringType, nullable = true))
    val runStatusDF = spark.createDataFrame(lastInstRDD, newSchema)
    val allInstSuccess = runStatusDF
      .withColumn("all_inst_success", when(runStatusDF("run_status") === "success", 0).otherwise(1))
      .withColumn("duration_24_subtract_34", col("duration24") - col("duration34"))
      .withColumn("duration_32_divide_24", col("duration34") / col("duration24"))
      .withColumn("inst_run", when(runStatusDF("run_status") === "run", 1).otherwise(0))
      .withColumn("inst_fail", when(runStatusDF("run_status") === "fail", 1).otherwise(0))
    val groupBuffaloID = allInstSuccess.groupBy("origin_buffalo_id")
      .agg(
        sum("all_inst_success").alias("inst_all_success"),
        sum("inst_run").alias("inst_run"),
        sum("inst_fail").alias("inst_fail"),
        sum("duration24").alias("duration24"),
        sum("duration34").alias("duration34"),
        max("lastInstId").alias("maxLatestInstId"),
        max("lastInstRuntime").alias("maxLastInstRuntime"))
      .withColumn("lastInstRunDT", convertDt($"maxLastInstRuntime"))
    val addDurationDF = addExplainCheckDF.join(groupBuffaloID, addExplainCheckDF("task_id") === groupBuffaloID("origin_buffalo_id"), "left")
      .drop(groupBuffaloID("origin_buffalo_id"))

    // 3.4 升级状态（已升级/已回滚）
    val upgrade1 = getRequestVCoreFollowingUpgrade(spark, upgradedAndRollbackTasks, upgradeDowngradeDT, "req_vcore_day_7_days_following_upgrade")
    val reqVcoreFollowingUpgrade = upgradedAndRollbackTasks.join(upgrade1, upgradedAndRollbackTasks("origin_task_id") === upgrade1("o_task_id"), "left").drop(upgrade1("o_task_id"))
    val addUpgradeRollbackDF = addDurationDF.join(reqVcoreFollowingUpgrade, addDurationDF("task_id") === reqVcoreFollowingUpgrade("origin_task_id"), "left")
      .drop(reqVcoreFollowingUpgrade("origin_task_id"))

    // 增加错误码
    val addErrorCodeDF: DataFrame = addErrorCodeCategory(spark, dualRunTasks, addUpgradeRollbackDF)
    // 增加非幂等
    val randomFunctionDF: DataFrame = addRandomFunction(spark, addErrorCodeDF, "task_id")

    val scriptIsSameDF = getScriptVersionDF(spark, tMinus14, tMinus1)
    val addScriptDF = randomFunctionDF.join(scriptIsSameDF, randomFunctionDF("task_id") === scriptIsSameDF("task_id"), "left")
      .drop(scriptIsSameDF("task_id"))

    val resultDF = addScriptDF

    resultDF.persist(StorageLevel.MEMORY_AND_DISK_SER_2)
    // 保存结果至MySQL
    saveAsMySQL(writeToMySQL, resultDF)

    dualRunTasks.write.mode("overwrite").format("hive")
      .saveAsTable("tmp.wuguoxiao_dual_run_info")
    resultDF.write.mode("overwrite").format("hive")
      .saveAsTable("tmp.wuguoxiao_v34_upgrade_assessment_20250111")
    resultDF.repartition(1).write.mode("overwrite").format("csv").option("header", "true").save(outputDir)
    logger.info("Result in csv file at " + outputDir)
    spark.close()
  }

  def getScriptVersionDF(spark: SparkSession,
                       tMinus14: LocalDate,
                       tMinus1: LocalDate): DataFrame = {
    val scriptIsSame = spark.sql(
      s"""
         |SELECT
         |	c.task_id,
         |	c.version AS old_version,
         |	d.version AS now_version,
         |	CASE
         |		WHEN c.version = d.version
         |		THEN 1
         |		ELSE 0
         |	END AS is_same
         |FROM
         |	(
         |		SELECT
         |			task_id,
         |			version
         |		FROM
         |			(
         |				SELECT
         |					MAX(current_version_num) AS version,
         |					task_id
         |				FROM
         |					adm.adm_m99_task_detail_info_da a
         |				WHERE
         |					dt >= '2024-07-16'
         |					AND dt <= '2024-07-30'
         |					AND EXISTS
         |					(
         |						SELECT
         |							1
         |						FROM
         |							tmp.lvfulong_spark_upgrade_big_graph b
         |						WHERE
         |							upgrade_status = 'UPGRADED'
         |							AND a.task_id = b.task_id
         |					)
         |				GROUP BY
         |					task_id
         |			)
         |	)
         |	c
         |JOIN
         |	(
         |		SELECT
         |			task_id,
         |			version
         |		FROM
         |			(
         |				SELECT
         |					MAX(current_version_num) AS version,
         |					task_id
         |				FROM
         |					adm.adm_m99_task_detail_info_da a
         |				WHERE
         |					dt >= '${tMinus14}'
         |					AND dt <= '${tMinus1}'
         |					AND EXISTS
         |					(
         |						SELECT
         |							1
         |						FROM
         |							tmp.lvfulong_spark_upgrade_big_graph b
         |						WHERE
         |							upgrade_status = 'UPGRADED'
         |							AND a.task_id = b.task_id
         |					)
         |				GROUP BY
         |					task_id
         |			)
         |	)
         |	d
         |ON
         |	c.task_id = d.task_id
         |""".stripMargin)
    scriptIsSame
  }
}
