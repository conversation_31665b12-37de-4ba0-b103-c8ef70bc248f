package com.jd.bdp.engine

import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession

object DeleteDualRunTables {
  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "30g")
      .config("spark.executor.memoryOverhead", "4g")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.shuffle.targetPostShuffleInputSize", "41943040")
      .config("spark.dynamicAllocation.initialExecutors", "400")
      .config("spark.dynamicAllocation.minExecutors", "200")
      .config("spark.dynamicAllocation.maxExecutors", "2000")
      .appName("DeleteDualRunTables App")
      .enableHiveSupport()
      .getOrCreate()

    val tables = spark.sql("show tables in wangriyu_test")
    val dual = tables.filter("tableName like '%\\_spark\\_%' or tableName like '%\\_hive\\_%' or tableName like '%\\_read' or tableName like '%\\_write'")

    dual.collect().par.foreach { row =>
      val database = row.getAs[String]("database")
      val tableName = row.getAs[String]("tableName")
      try {
        if(!tableName.contains("_hudi_copy_hive")){
          spark.sql(s"DROP TABLE IF EXISTS ${database}.${tableName}")
          logger.info(s"Deleted ${database}.${tableName}")
        }else{
          logger.info(s"=== skip hudi double table: ${database}.${tableName}")
        }

      } catch {
        case e: Exception =>
          println(s"Failed to drop table: ${database}.${tableName}. Error: ${e.getMessage}")
      }
    }
    spark.close()
  }
}
