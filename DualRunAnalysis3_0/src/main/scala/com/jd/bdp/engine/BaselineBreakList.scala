package com.jd.bdp.engine


import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

import org.apache.log4j.Logger

import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.col


/**
export JDHXXXXX_CLUSTER_NAME=cairne;
export JDHXXXXX_USER=dd_edw;
export JDHXXXXX_QUEUE=bdp_jdw_dd_edw_bdp_spark;
export TEAM_USER=dd_edw_system_optimization;
source /software/servers/env/env.sh

export SPARK_HOME=/software/servers/cairne/dd_edw/spark_3.4
spark-submit --queue root.bdp_jdw_dd_edw_test --class com.jd.bdp.engine.BaselineBreakList DualRunAnalysis3_0-1.0-SNAPSHOT.jar

 */
object BaselineBreakList {
  val logger: Logger = Logger.getLogger(this.getClass)

  def main(args: Array[String]): Unit = {

    val spark = SparkSession.builder()
      .config("spark.executor.cores", "8")
      .config("spark.executor.memory", "24g")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.shuffle.targetPostShuffleInputSize", "41943040")
      .config("spark.dynamicAllocation.initialExecutors", "400")
      .config("spark.dynamicAllocation.minExecutors", "200")
      .config("spark.dynamicAllocation.maxExecutors", "2000")
      .appName("BaselineBreakList App")
      .enableHiveSupport()
      .getOrCreate()

//    val args = Seq()
    val defaultPath = "hdfs://ns17/tmp/baseline_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))

    val defaultVal = Seq(defaultPath)
    val args2 = args.padTo(defaultVal.length, "")
    val handleDefaultArgs = args2.zip(defaultVal).map { case (param, default) =>
      if (param == null || param.isEmpty) default else param
    }
    val outputDir = handleDefaultArgs(0).toString

    val breakList = spark.sql(
      """
      |WITH
      |	base AS
      |	(
      |		SELECT
      |			baseline_id,
      |			baseline_name,
      |			MAX((CAST(split(target_end_time, ':') [0] AS INT) * 60) + CAST(split(target_end_time, ':') [1] AS INT)) AS target_minutes,
      |			AVG((hour(CAST(end_time AS TIMESTAMP)) * 60) + minute(CAST(end_time AS TIMESTAMP))) AS base_m7_complete_minutes
      |		FROM
      |			app.app_jdr_bms_baseline_monitor_warn_a_s_d
      |		WHERE
      |			dt >= '2024-07-16'
      |			AND dt <= '2024-07-31'
      |			AND baseline_id IN(12996, 12997, 12973, 12974, 14270, 1367, 1366, 739, 959, 805, 957, 15799, 9350, 1392, 907, 743, 908, 678, 1264, 1262, 9351, 1792, 9352, 911, 677, 9353, 1390, 738, 1669, 745, 9501, 9047, 7285, 7410, 7682, 628, 1052, 749, 1349, 9349, 958, 16348, 16439, 16438, 16437, 16436, 16435, 16434, 16433, 16432, 7283, 7284, 658, 660, 659, 16440, 16441, 16442, 16443, 16420, 9052, 7409, 638)
      |		GROUP BY
      |			baseline_id,
      |			baseline_name
      |	)
      |	,
      |	last_7_day AS
      |	(
      |		SELECT
      |			baseline_id,
      |			AVG((hour(CAST(end_time AS TIMESTAMP)) * 60) + minute(CAST(end_time AS TIMESTAMP))) AS last_7_complete_minutes
      |		FROM
      |			app.app_jdr_bms_baseline_monitor_warn_a_s_d
      |		WHERE
      |			dt >= sysdate( - 7)
      |			AND baseline_id IN(12996, 12997, 12973, 12974, 14270, 1367, 1366, 739, 959, 805, 957, 15799, 9350, 1392, 907, 743, 908, 678, 1264, 1262, 9351, 1792, 9352, 911, 677, 9353, 1390, 738, 1669, 745, 9501, 9047, 7285, 7410, 7682, 628, 1052, 749, 1349, 9349, 958, 16348, 16439, 16438, 16437, 16436, 16435, 16434, 16433, 16432, 7283, 7284, 658, 660, 659, 16440, 16441, 16442, 16443, 16420, 9052, 7409, 638)
      |		GROUP BY
      |			baseline_id
      |	)
      |SELECT
      |	a.*,
      |	b.last_7_complete_minutes,
      | a.base_m7_complete_minutes - b.last_7_complete_minutes as baseline_improve_minutes
      |FROM
      |	base AS a
      |INNER JOIN last_7_day AS b
      |ON
      |	a.baseline_id = b.baseline_id
        |""".stripMargin)
    val breaklistOrderDF = breakList.orderBy(col("baseline_improve_minutes").desc)

    breaklistOrderDF.repartition(1).write.format("csv").option("header", "true").save(outputDir)
    logger.info("Result in csv file at " + outputDir)

    spark.stop()
  }

}
