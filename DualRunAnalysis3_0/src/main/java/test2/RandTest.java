package test2;

import java.util.Random;

public class RandTest extends Random{

    @Override
    protected int next(int bits) {
        return super.next(bits);
    }

    @Override
    public synchronized void setSeed(long seed) {
        super.setSeed(seed);
    }

    public static void main(String[] args) {
        RandTest randTest = new RandTest();
        randTest.setSeed(10);
        double v = randTest.nextDouble();
        System.out.println("v = " + v);
    }
}
