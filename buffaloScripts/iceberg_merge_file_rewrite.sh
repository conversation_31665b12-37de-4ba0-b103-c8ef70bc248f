#!/bin/bash


###参数说明
##  $1 集群
##  $2 账号
##  $3 队列
##  $4 teamuser
##  $5 实际使用队列
##  $6 表名 
##  $7 目标大小
##  $8 是否全表重写(true/false) 
##  $9 where条件(可以不填)

export JDHXXXXX_CLUSTER_NAME=$1
export JDHXXXXX_USER=$2
export JDHXXXXX_QUEUE=$3
export TEAM_USER=$4
source /software/servers/env/env.sh

# 使用Append方式合并文件
# 队列使用：
#   10k集群使用 root.bdp_jdw_dd_edw_bdp_spark 队列
#   hope集群使用 bdp_jdw_dd_edw_spark队列

set -x

export SPARK_HOME=${SPARK_HOME}_3.0 

# === export DATALAKE_HOME 开始， 在iceberg新版本上线后删除
# if [ -d '/data0/matao/iceberg/datalake_DEV-TEST_20230419185853' ]; then
#   export DATALAKE_HOME='/data0/matao/iceberg/datalake_DEV-TEST_20230419185853'
# else
#   curl -O http://storage.jd.local/com.bamboo.server.product/8498157/datalake_DEV-TEST_20230419185853.tar.gz
#   tar -zxvf datalake_DEV-TEST_20230419185853.tar.gz
#   export DATALAKE_HOME="$PWD/datalake_DEV-TEST_20230419185853"
# fi
# echo "DATALAKE_HOME => $DATALAKE_HOME"
# === export DATALAKE_HOME 结束

spark-submit \
--conf spark.hadoop.dfs.ha.namenodes.ns22024=nn44045,nn44046,ns22024lfrz1  \
--conf spark.hadoop.dfs.namenode.rpc-address.ns22024.ns22024lfrz1=lfrz-10k-11-131-54-205.hadoop.jd.local:8020 \
--conf spark.hadoop.dfs.namenode.http-address.ns22024.ns22024lfrz1=lfrz-10k-11-131-54-205.hadoop.jd.local:50070 \
--conf spark.hadoop.dfs.namenode.servicerpc-address.ns22024.ns22024lfrz1=lfrz-10k-11-131-54-205.hadoop.jd.local:8021 \
--conf spark.hadoop.dfs.client.failover.observer.read.enable.ns22024=true \
--conf spark.hadoop.dfs.client.failover.observer.auto-msync-period.ns22024=0 \
--queue $5 \
--conf spark.sql.shuffle.partitions=4000 \
--conf spark.driver.memory=20g \
--conf spark.datalake.enabled=true \
--conf spark.dynamicAllocation.maxExecutors=6000 \
--conf spark.dynamicAllocation.initialExecutors=1000 \
--conf spark.dynamicAllocation.minExecutors=1000 \
--conf spark.speculation=true \
--conf spark.speculation.quantile=0.1 \
--conf spark.speculation.task.min.duration=10s \
--conf spark.sql.iceberg.mergeinto.skip.filefilter.enabled=true \
--class com.jd.datalake.rewrite.RewriteDataFileMain ${DATALAKE_HOME}/jars/mergefile/mergefile.jar \
"$6" "$7" "$8" "$9"