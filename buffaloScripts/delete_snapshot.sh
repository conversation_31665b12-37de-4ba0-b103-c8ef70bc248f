#!/bin/bash
set -e

###参数说明
##  $1 集群
##  $2 账号
##  $3 队列
##  $4 teamuser
##  $5 保留快照时间，比如 1 day
##  $6 库名.表名
##  $7 队列名称：10k请填写：root.bdp_jdw_dd_edw_bdp_spark  Hope 请填写：bdp_jdw_dd_edw_spark

export JDHXXXXX_CLUSTER_NAME=$1
export JDHXXXXX_USER=$2
export JDHXXXXX_QUEUE=$3
export TEAM_USER=$4
source /software/servers/env/env.sh

env | grep SPARK_HOME

set -x

export SPARK_HOME=${SPARK_HOME}_3.0 

# === export DATALAKE_HOME 开始， 在iceberg新版本上线后删除
# if [ -d '/data0/matao/iceberg/datalake_DEV-TEST_20230419185853' ]; then
#   export DATALAKE_HOME='/data0/matao/iceberg/datalake_DEV-TEST_20230419185853'
# else
#   curl -O http://storage.jd.local/com.bamboo.server.product/8498157/datalake_DEV-TEST_20230419185853.tar.gz
#   tar -zxvf datalake_DEV-TEST_20230419185853.tar.gz
#   export DATALAKE_HOME="$PWD/datalake_DEV-TEST_20230419185853"
# fi
# echo "DATALAKE_HOME => $DATALAKE_HOME"
# === export DATALAKE_HOME 结束


expireDate=`date -d "$5 ago"  +"%Y-%m-%d %H:%M:%S"`

retain_last=100
if [ -n "$8" ]; then
  # Number of ancestor snapshots to preserve regardless of `older_than` (defaults to 1)
  retain_last=$8
fi

(
cat <<EOF

select * from iceberg_catalog.$6.snapshots;

CALL iceberg_catalog.system.expire_snapshots(
    "$6", 
    TIMESTAMP "${expireDate}", $retain_last, 100, true);

select * from iceberg_catalog.$6.snapshots;

EOF
) > sql.sql

cat sql.sql


spark-sql --master yarn \
--conf spark.submit.deployMode=client \
--queue $7 \
--conf spark.dynamicAllocation.maxExecutors=3000 \
--conf spark.executor.cores=4 \
--conf spark.task.cpus=4 \
--conf spark.executor.memory=40g \
--conf spark.executor.memoryOverhead=10g \
--conf spark.executor.extraJavaOptions="-verbose:gc -XX:OnOutOfMemoryError='kill -9 %p' -XX:+UnlockExperimentalVMOptions -XX:G1NewSizePercent=30 -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:G1HeapRegionSize=32m -XX:MaxGCPauseMillis=200 -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4 -XX:+UseG1GC -XX:+PrintAdaptiveSizePolicy -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime -XX:-PrintCommandLineFlags -XX:+PrintTenuringDistribution -XX:+PrintReferenceGC -Dorg.asynchttpclient.readTimeout=5000 -Dorg.asynchttpclient.connectionTtl=60000 -Dorg.asynchttpclient.acquireFreeChannelTimeout=10000" \
--conf spark.driver.memory=20g \
--conf spark.driver.cores=10 \
--conf spark.sql.shuffle.partitions=3600 \
--conf spark.speculation=true \
--conf spark.isLoadHivercFile=true \
--conf spark.sql.hive.convertMetastoreOrc=true \
--conf spark.sql.adaptive.enabled=true \
--conf spark.sql.tempudf.ignoreIfExists=true \
--conf spark.sql.parser.quotedRegexColumnNames=true \
--conf spark.sql.crossJoin.enabled=true \
--conf spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version=2 \
--conf spark.sql.retry.with.hive=False \
--hiveconf hive.exec.orc.split.strategy=BI \
--conf spark.sql.hive.mergeFiles=true \
--conf spark.datalake.enabled=true \
--conf spark.sql.storeAssignmentPolicy=ANSI \
--conf spark.sql.viewPermission.enabled="true" \
--conf spark.sql.parser.quotedRegexColumnNames.isRegex.enabled="true" \
-f sql.sql