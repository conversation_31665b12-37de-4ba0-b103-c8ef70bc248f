#!/usr/bin/env bash

set -x
set -e

echo "====================START====================="
##source /home/<USER>/envs/dd_edw.env

usage()  
{  
 echo "Usage: $0 LogDate"  
 echo "       Format for LogDate: yyyy-mm-dd"
 exit 1  
} 


if [ $# -ne 1 ] ; then
    usage
else
    LOG_DATE=$1
    LOG_PATH="hdfs://ns100/user/spark/buffalolog/buffalo_logs/$LOG_DATE"
    echo "LogDate:$LOG_DATE"
    echo "LogPath:$LOG_PATH"
fi


hdfs dfs -test -e $LOG_PATH

if [ $? -eq 0 ]; then
    export HADOOP_USER_NAME=hadp
    hdfs dfs -rm -r $LOG_PATH
    if [ $? -eq 0 ]; then
        echo "success!"
    fi
fi

echo "=====================END========================"
