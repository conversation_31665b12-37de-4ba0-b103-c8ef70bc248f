
log4j.rootLogger=debug, customFile, console

log4j.appender.console=com.jd.bdp.engine.LogConsoleAppender
log4j.appender.console.threshold=warn
log4j.appender.console.filter.filter=com.jd.bdp.engine.Log4jFilter
log4j.appender.console.filter.string=com.jd.bdp.engine.StringMatchFilter
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=[%-5p][%-22d{yyyy/MM/dd HH:mm:ssS}][%l] %m%n
log4j.appender.console.ImmediateFlush=TRUE
log4j.appender.console.Target=System.out

log4j.appender.customFile=com.jd.bdp.engine.LogFileAppender
log4j.appender.customFile.threshold=all
log4j.appender.customFile.File=${LOG_DIR}/${SPARK_LOG_FILE_NAME}.log
log4j.appender.customFile.layout=org.apache.log4j.PatternLayout
log4j.appender.customFile.layout.ConversionPattern=[%-5p][%-22d{yyyy/MM/dd HH:mm:ssS}][%l] %m%n

log4j.filter.class.com.jd.Main22=error
log4j.filter.context.contain.c1=CCC

log4j.filter.revise.p2.pattern=(\d+)(\S+)
log4j.filter.revise.p2.format=有问题的字符串%s 有问题的数字是%s
log4j.filter.revise.p2.args=2, 1


log4j.filter.revise.p1.pattern=Trying to connect to metastore.*(\\d+\\.\\d+\\.\\d+\\.\\d+)
log4j.filter.revise.p1.format=正在连接Hive Meta Store,IP是%s，如果时间比较长，请联系xnbdp
log4j.filter.revise.p1.args=1
