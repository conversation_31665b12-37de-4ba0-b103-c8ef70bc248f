<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration debug="true">
    <appender name="console" class="com.jd.bdp.engine.LogConsoleAppender">
        <param name="threshold" value="WARN" />
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern"
                   value="%d{yy/MM/dd HH:mm:ss} [%t] %p %c{1}: %m%n" />
        </layout>
        <filter class="com.jd.bdp.engine.Log4jFilter"/>
        <filter class="com.jd.bdp.engine.StringMatchFilter"/>
    </appender>
    <appender name="file" class="com.jd.bdp.engine.LogFileAppender">
        <param name="threshold" value="ALL" />
        <param name="append" value="false" />
        <param name="file" value="${LOG_DIR}/${LAUNCH_LOG_DATE}/spark_${SPARK_LOG_FILE_NAME}.log" />
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern"
                   value="%d{yy/MM/dd HH:mm:ss} [%t] %p %c: %m%n" />
        </layout>
    </appender>

    <root>
        <level value="INFO" />
        <appender-ref ref="console" />
        <appender-ref ref="file" />
    </root>
</log4j:configuration>