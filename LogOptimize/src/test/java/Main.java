
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Main {
    public static void main(String[] args) {
        System.setProperty("LOG_DIR", "/Users/<USER>/JD/IdeaProjects-git/bag_SparkAPM/Log4jUpdateFilePath/target/AA");
        Logger logger = LoggerFactory.getLogger("com.jd.Main11");
        logger.info("INFO--DDDD");
        logger.warn("WARN--DDDD");
        Logger logger11 = LoggerFactory.getLogger("com.jd.Main22");
        logger11.info("Main22 INFO CCCC");
        logger11.warn("Main22 WARN CCCC");
        logger11.error("Main22 ERROR CCCC");
    }
}
