package com.jd.bdp.engine;

import org.apache.log4j.helpers.LogLog;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ReviseImpl {

    private static final String LOG_4_J_FILTER_REVISE = "log4j.filter.revise.";

    public static Map<String, Revise> getRevise(){
        Map<String, Revise> reviseHashMap = new HashMap<>();
        Properties properties = Singleton.getInstance().getProperties();
        properties.forEach((key, value) -> {
            String keyStr = key + "";
            String valueStr = value + "";
            int logIndexOf = keyStr.indexOf(LOG_4_J_FILTER_REVISE);
            if (logIndexOf != -1) {
                // log4j.filter.revise.p1.pattern=(\d+)(\S+)
                int groupIndex = keyStr.indexOf(".", LOG_4_J_FILTER_REVISE.length() + 1);
                String filterGroup = keyStr.substring(LOG_4_J_FILTER_REVISE.length(), groupIndex);
                String filterName = keyStr.substring(groupIndex + 1);
                Revise revise = reviseHashMap.get(filterGroup);
                if(revise == null) {
                    revise = new Revise();
                    extracted(valueStr, filterName, revise);
                    reviseHashMap.put(filterGroup, revise);
                } else {
                    extracted(valueStr, filterName, revise);
                }
            }
        });
        return reviseHashMap;
    }

    private static void extracted(String valueStr, String filterName, Revise revise) {
        if("format".equalsIgnoreCase(filterName)) {
            revise.setFormat(valueStr);
        } else if("pattern".equalsIgnoreCase(filterName)) {
            revise.setPattern(valueStr);
        } else if("args".equalsIgnoreCase(filterName)) {
            String[] split = valueStr.split(",");
            Integer[] args = new Integer[split.length];
            for (int i = 0; i < split.length; i++) {
                args[i] = Integer.parseInt(split[i].trim());
            }
            revise.setArgs(args);
        }
    }

    public static String extracted(String format, Revise revise) {
        StringBuilder builder = new StringBuilder();
        Matcher matcher = Pattern.compile(revise.getPattern()).matcher(format);
        while (matcher.find()) {
            String[] values = new String[revise.getArgs().length];
            for (int i = 0; i < revise.getArgs().length; i++) {
                values[i] = matcher.group(revise.getArgs()[i]);
            }
            builder.append(String.format(revise.getFormat(), values)).append("\n");
        }
        return builder.toString();
    }

    public static String appendRevise(String format) {
        if(!LogOptimize.REVISE_ENABLED) {
            return "";
        }
        String collect;
        try {
            collect = getRevise().values().stream()
                    .map(revise -> ReviseImpl.extracted(format, revise))
                    .collect(Collectors.joining());
        } catch (Exception e) {
            collect = "An exception["+e.getMessage()
                    +"] occurred while parsing the content of the previous line.\n";
        }
        return collect;
    }
}
