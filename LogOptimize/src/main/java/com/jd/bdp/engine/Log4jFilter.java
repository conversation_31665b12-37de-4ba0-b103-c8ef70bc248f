package com.jd.bdp.engine;

import org.apache.log4j.Level;
import org.apache.log4j.spi.Filter;
import org.apache.log4j.spi.LoggingEvent;

import java.text.SimpleDateFormat;
import java.util.Date;

public class Log4jFilter extends Filter {

    private static final String LOG_4_J_FILTER = "log4j.filter.class.";

    public int decide(LoggingEvent loggingEvent) {
        String loggerName = loggingEvent.getLoggerName();
        String property = Singleton.getInstance().getProperties().getProperty(LOG_4_J_FILTER + loggerName);

        if(!LogOptimize.FILTER_CLASS_ENABLED
                || loggingEvent.getLevel().isGreaterOrEqual(Level.toLevel(property))) {
            return Filter.NEUTRAL;
        } else {
            return Filter.DENY;
        }
    }
}
