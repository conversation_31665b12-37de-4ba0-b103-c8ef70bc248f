package com.jd.bdp.engine;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

public class Singleton {
    private static final String LOG_4_J_PROPERTIES = "log4j-optimize.properties";
    private final Properties properties = new Properties();
    private Singleton() {
        String property = System.getProperty("log4j.optimize.configuration");
        String log4jOptimizeConf;
        if(property != null && !"".equals(property)) {
            log4jOptimizeConf = property;
        } else {
            URL resource = Thread.currentThread().getContextClassLoader().getResource(LOG_4_J_PROPERTIES);
            log4jOptimizeConf = resource.getPath();
        }
        try(FileInputStream fis = new FileInputStream(log4jOptimizeConf)) {
            try(InputStreamReader reader = new InputStreamReader(fis, StandardCharsets.UTF_8)) {
                properties.load(reader);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static class SingletonHolder {
        private static final Singleton instance = new Singleton();
    }

    public static Singleton getInstance() {
        return SingletonHolder.instance;
    }

    public Properties getProperties() {
        return properties;
    }
}
