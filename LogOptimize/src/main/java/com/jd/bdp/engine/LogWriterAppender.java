package com.jd.bdp.engine;

import org.apache.log4j.Layout;
import org.apache.log4j.WriterAppender;
import org.apache.log4j.spi.LoggingEvent;


import static com.jd.bdp.engine.ReviseImpl.appendRevise;

public class LogWriterAppender extends WriterAppender {

    @Override
    protected void subAppend(LoggingEvent event) {
        String format = this.layout.format(event);
        this.qw.write(format + appendRevise(format));
        if (this.layout.ignoresThrowable()) {
            String[] s = event.getThrowableStrRep();
            if (s != null) {
                int len = s.length;
                for(int i = 0; i < len; ++i) {
                    this.qw.write(s[i]);
                    this.qw.write(Layout.LINE_SEP);
                }
            }
        }
        if (this.shouldFlush(event)) {
            this.qw.flush();
        }
    }



}
