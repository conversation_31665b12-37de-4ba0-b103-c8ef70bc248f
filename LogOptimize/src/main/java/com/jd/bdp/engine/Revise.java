package com.jd.bdp.engine;

import java.util.Arrays;

public class Revise {
    private String pattern;
    private String format;
    private Integer[] args = new Integer[0];

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public Integer[] getArgs() {
        return args;
    }

    public void setArgs(Integer[] args) {
        this.args = args;
    }

    @Override
    public String toString() {
        return "Revise{" +
                "pattern='" + pattern + '\'' +
                ", format='" + format + '\'' +
                ", args=" + Arrays.toString(args) +
                '}';
    }
}
