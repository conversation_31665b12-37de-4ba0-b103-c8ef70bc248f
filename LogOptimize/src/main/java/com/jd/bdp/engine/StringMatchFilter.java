package com.jd.bdp.engine;

import org.apache.log4j.spi.Filter;
import org.apache.log4j.spi.LoggingEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringMatchFilter extends Filter {

    private static final Map<String, String> DENY_CONTAIN_MAP = new HashMap<>();
    private static final Map<String, String> DENY_REGEX_MAP = new HashMap<>();

    static {
        Properties properties = Singleton.getInstance().getProperties();
        properties.forEach((key, value) -> {
            if ((key + "").contains("log4j.filter.content.contain")) {
                DENY_CONTAIN_MAP.put(key + "", value + "");
            }
            if ((key + "").contains("log4j.filter.content.regex")) {
                DENY_REGEX_MAP.put(key + "", value + "");
            }
        });
    }

    public int decide(LoggingEvent event) {
        String msg = event.getRenderedMessage();
        if (LogOptimize.FILTER_CONTENT_ENABLED && msg != null) {
            for (String values : DENY_CONTAIN_MAP.values()) {
                if (msg.contains(values)) {
                    return Filter.DENY;
                }
            }
            for (String values : DENY_REGEX_MAP.values()) {
                Matcher matcher = Pattern.compile(values).matcher(msg);
                if(matcher.find()) {
                    return Filter.DENY;
                }
            }
        }
        return Filter.NEUTRAL;
    }
}
