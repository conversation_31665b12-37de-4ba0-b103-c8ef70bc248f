package com.jd.bdp.engine;

import org.apache.log4j.helpers.LogLog;

import java.text.SimpleDateFormat;
import java.util.Date;

public class LogOptimize {

    public static void init() {
        SimpleDateFormat sdfYmd = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdfHms = new SimpleDateFormat("HHmmss.sss");
        Date date = new Date();
        String formatYmd = sdfYmd.format(date);
        String formatHms = sdfHms.format(date);
        System.getenv().forEach((key, value) -> {
            String property = System.getProperty(key);
            if(property == null) {
                System.setProperty(key, value);
            }
        });
        System.setProperty("LAUNCH_LOG_DATE", formatYmd);
        System.setProperty("SPARK_LOG_FILE_NAME", formatHms);

        LOG_OPTIMIZE_ENABLED = "true".equalsIgnoreCase(
                System.getProperty("LOG_OPTIMIZE_ENABLED", "true"));
        FILTER_CLASS_ENABLED = LOG_OPTIMIZE_ENABLED &&
                "true".equalsIgnoreCase(System.getProperty("LOG_FILTER_CLASS_ENABLED", "true"));
        FILTER_CONTENT_ENABLED = LOG_OPTIMIZE_ENABLED &&
                "true".equalsIgnoreCase(System.getProperty("LOG_FILTER_CONTENT_ENABLED", "true"));
        REVISE_ENABLED = LOG_OPTIMIZE_ENABLED &&
                "true".equalsIgnoreCase(System.getProperty("LOG_REVISE_ENABLED", "true"));
        LogLog.warn("LOG_OPTIMIZE_ENABLED: " + LOG_OPTIMIZE_ENABLED + " FILTER_CLASS_ENABLED: " + FILTER_CLASS_ENABLED
        + " FILTER_CONTENT_ENABLED: " + FILTER_CONTENT_ENABLED + " REVISE_ENABLED: " + REVISE_ENABLED);
    }

    public static boolean LOG_OPTIMIZE_ENABLED = false;
    public static boolean FILTER_CLASS_ENABLED = false;
    public static boolean FILTER_CONTENT_ENABLED = false;
    public static boolean REVISE_ENABLED = false;
}
