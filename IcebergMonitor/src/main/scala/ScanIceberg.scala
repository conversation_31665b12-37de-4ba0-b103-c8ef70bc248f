import scala.collection.parallel.ForkJoinTaskSupport

import <PERSON><PERSON><PERSON><PERSON><PERSON>._
import org.apache.hadoop.fs.{FileSystem, Path}

import org.apache.spark.internal.Logging
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

class ScanIceberg(spark: SparkSession, conf: ConfUtil, prometheus: PrometheusUtil) extends Logging {

  private def listClusterToIcebergTable(): Map[String, Array[IcebergTable]] = {
    def getTables(dateSub: Int): Map[String, Array[IcebergTable]] = {
      spark.table("fdm.fdm_dd_edw_hive_table_params")
        .where(s"dt = DATE_SUB(CURRENT_DATE, $dateSub)")
        .groupBy(col("tbl_id"))
        .agg(map_from_arrays(collect_list(col("param_key")), collect_list(col("param_value")))
          .as("map"))
        .where("lower(coalesce(map['table_type'], '')) = 'iceberg'")
        .createOrReplaceTempView("param_map")

      import spark.implicits._
      spark.sql(
        s"""
           |SELECT
           |  param_map.tbl_id AS tableId,
           |  table_info.cluster_name AS clusterName,
           |  table_info.db_name AS databaseName,
           |  table_info.tbl_name AS tableName,
           |  table_info.tbl_master AS tableMasterErp,
           |  table_info.table_location AS tableLocation,
           |  regexp_extract(table_info.table_location, '(?<=^hdfs://)(ns[0-9]+)') AS tableLocationNs,
           |  lower(coalesce(param_map.map['write.upsert.enabled'], 'false')) AS isUpsert
           |FROM
           |  param_map
           |LEFT JOIN
           |  gdm.gdm_m99_metadata_table_info_da table_info
           |ON
           |  param_map.tbl_id = table_info.tbl_id
           |WHERE
           |  table_info.dt = DATE_SUB(CURRENT_DATE, $dateSub)
           |ORDER BY
           |  table_info.cluster_name, table_info.db_name
           |${conf.scanIcebergTableNum.map(num => s"LIMIT $num").getOrElse("")}
           |""".stripMargin
      ).as[IcebergTable].collect().groupBy(_.clusterName)
    }

    val tables = getTables(1)
    if (tables.isEmpty) getTables(2) else tables
  }

  private def icebergTableToScanResult(table: IcebergTable): Option[(IcebergTable, ScanResult)] = try {
    val tableName = s"iceberg_catalog.${table.databaseName}.${table.tableName}"

    val location = new Path(table.tableLocation)
    val locationSummary = FileSystem
      .get(location.toUri, spark.sparkContext.hadoopConfiguration)
      .getContentSummary(location)
    val tableSizeBytes = locationSummary.getLength
    val tableFileNum = locationSummary.getFileCount
    val tableDirectoryNum = locationSummary.getDirectoryCount

    val snapShotNum = spark.sql(s"SELECT 1 FROM $tableName.snapshots").count()

    val smallFileThresholdBytes = conf.scanIcebergSmallFileThresholdBytes

    def sumIfSQL(content: FileContent.Value, field: String, filterSQL: Option[String] = None): String = {
      val filter = filterSQL.map(x => s"AND $x").getOrElse("")
      val sum = s"(SUM(IF(content==${content.id} $filter, $field, 0)))"
      s"IF($sum IS NULL, 0, $sum)"
    }

    def smallFileFilter: Some[String] = Some(s"file_size_in_bytes<$smallFileThresholdBytes")

    import spark.implicits._
    Some(
      table,
      spark.sql(
        s"""
           |SELECT
           | '$tableName' AS tableName,
           | '${table.tableMasterErp}' AS tableMasterErp,
           | $snapShotNum AS snapShotNum,
           | ${sumIfSQL(FileContent.DATA, "file_size_in_bytes")} AS dataFileSizeBytes,
           | ${sumIfSQL(FileContent.DATA, "1")} AS dataFileNum,
           | ${sumIfSQL(FileContent.DATA, "1", smallFileFilter)} AS smallDataFileNum,
           | ${sumIfSQL(FileContent.POSITION_DELETES, "file_size_in_bytes")} AS positionDeleteFileSizeBytes,
           | ${sumIfSQL(FileContent.POSITION_DELETES, "1")} AS positionDeleteFileNum,
           | ${sumIfSQL(FileContent.POSITION_DELETES, "1", smallFileFilter)} AS smallPositionDeleteFileNum,
           | ${sumIfSQL(FileContent.EQUALITY_DELETES, "file_size_in_bytes")} AS equalityDeleteFileSizeBytes,
           | ${sumIfSQL(FileContent.EQUALITY_DELETES, "1")} AS equalityDeleteFileNum,
           | ${sumIfSQL(FileContent.EQUALITY_DELETES, "1", smallFileFilter)} AS smallEqualityDeleteFileNum,
           | $tableSizeBytes AS tableSizeBytes,
           | $tableFileNum AS tableFileNum,
           | $tableDirectoryNum AS tableDirectoryNum
           |FROM
           | $tableName.files
           |""".stripMargin
      ).as[ScanResult].collect().head
    )

  } catch {
    case t: Throwable =>
      logError(s"Scan table $table ERROR", t)
      prometheus.reportErrorIcebergTable(table)
      None
  }

  private def parallelScanIcebergTable(tables: Array[IcebergTable]): Array[(IcebergTable, ScanResult)] = {
    val concurrentNum = conf.scanIcebergConcurrentNum

    val tablesPar = tables.par
    tablesPar.tasksupport = new ForkJoinTaskSupport(new scala.concurrent.forkjoin.ForkJoinPool(concurrentNum))
    tablesPar.map(icebergTableToScanResult).filter(_.nonEmpty).map(_.get).toArray
  }

  def execute(): Seq[(IcebergTable, ScanResult)] = {
    val clusterToHmsUrl = conf.scanIcebergClusterToHmsUrl

    listClusterToIcebergTable().filter { case (cluster, tables) =>
      if (!clusterToHmsUrl.contains(cluster)) {
        logError(s"Unknown cluster: $cluster, table: [${tables.mkString(" ,")}]")
      }
      clusterToHmsUrl.contains(cluster)
    }.map { case (cluster, tables) =>
      val hmsUrl = clusterToHmsUrl(cluster)
      logInfo(s"cluster $cluster, tableNum ${tables.length}")
      spark.conf.set("spark.hadoop.hive.metastore.uris", hmsUrl)
      parallelScanIcebergTable(tables)
    }.toSeq.flatten
  }
}

object ScanIceberg {
  private object FileContent extends Enumeration {
    val DATA = Value(0)
    val POSITION_DELETES = Value(1)
    val EQUALITY_DELETES = Value(2)
  }

  case class IcebergTable(
                           tableId: Long,
                           clusterName: String,
                           databaseName: String,
                           tableName: String,
                           tableMasterErp: String,
                           tableLocation: String,
                           tableLocationNs: String,
                           isUpsert: String)

  case class ScanResult(
                         snapShotNum: Long,
                         dataFileSizeBytes: Long,
                         dataFileNum: Long,
                         smallDataFileNum: Long,
                         positionDeleteFileSizeBytes: Long,
                         positionDeleteFileNum: Long,
                         smallPositionDeleteFileNum: Long,
                         equalityDeleteFileSizeBytes: Long,
                         equalityDeleteFileNum: Long,
                         smallEqualityDeleteFileNum: Long,
                         tableSizeBytes: Long,
                         tableFileNum: Long,
                         tableDirectoryNum: Long)
}
