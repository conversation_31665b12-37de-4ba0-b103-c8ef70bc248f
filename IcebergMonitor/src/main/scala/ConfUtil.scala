import ConfUtil._

import org.apache.spark.sql.SparkSession

object ConfUtil {
  // prometheus - key
  private val PROMETHEUS_TESTING_KEY = "spark.scan.iceberg.prometheus.testing"
  private val PROMETHEUS_ADDRESS_KEY = "spark.scan.iceberg.prometheus.address"
  private val PROMETHEUS_TEST_ADDRESS_KEY = "spark.scan.iceberg.prometheus.test.address"
  private val PROMETHEUS_RSS_SERVICE_ID_KEY = "spark.scan.iceberg.prometheus.service.id"

  // prometheus - default
  private val PROMETHEUS_TESTING_VALUE = "false"
  private val PROMETHEUS_ADDRESS_VALUE = "baizepg.jd.local:2000"
  private val PROMETHEUS_TEST_ADDRESS_VALUE = "t.bz-pg.jd.local"
  private val PROMETHEUS_RSS_SERVICE_ID_VALUE = "iceberg1-b12b-11ed-ba16-246e96ac2938"

  // scan iceberg - key
  private val SCAN_CONCURRENT_NUM_KEY = "spark.scan.iceberg.concurrent.num"
  private val SCAN_CLUSTER_NAME_KEY = "spark.scan.iceberg.cluster.name"
  private val SCAN_CLUSTER_HMS_URL_KEY = "spark.scan.iceberg.cluster.hms.url"
  private val SCAN_SMALL_FILE_THRESHOLD_MB_KEY = "spark.scan.iceberg.small.file.threshold.mb"
  private val SCAN_TABLE_NUM_KEY = "spark.scan.iceberg.table.num"  // for test

  // scan iceberg - default
  private val SCAN_CONCURRENT_NUM_VALUE = "5"
  private val SCAN_CLUSTER_NAME_VALUE = "10k,hope"
  private val SCAN_CLUSTER_HMS_URL_VALUE =
    "10k->thrift://**************:10113,hope->thrift://**************:10112"
  private val SCAN_SMALL_FILE_THRESHOLD_MB_VALUE = "24"
  private val SCAN_TABLE_NUM_VALUE = "-1"  // for test

}

class ConfUtil(spark: SparkSession) {
  private def conf = spark.conf

  // prometheus
  def prometheusAddress: String =
    if (conf.get(PROMETHEUS_TESTING_KEY, PROMETHEUS_TESTING_VALUE).toBoolean) {
      conf.get(PROMETHEUS_TEST_ADDRESS_KEY, PROMETHEUS_TEST_ADDRESS_VALUE)
    } else {
      conf.get(PROMETHEUS_ADDRESS_KEY, PROMETHEUS_ADDRESS_VALUE)
    }

  def prometheusServiceId: String =
    conf.get(PROMETHEUS_RSS_SERVICE_ID_KEY, PROMETHEUS_RSS_SERVICE_ID_VALUE)

  // scan iceberg
  def scanIcebergConcurrentNum: Int =
    conf.get(SCAN_CONCURRENT_NUM_KEY, SCAN_CONCURRENT_NUM_VALUE).toInt

  def scanIcebergClusterToHmsUrl: Map[String, String] =
    spark.conf.get(SCAN_CLUSTER_HMS_URL_KEY, SCAN_CLUSTER_HMS_URL_VALUE)
      .replace(" ", "")
      .split(",")
      .map { clusterToUrl =>
        clusterToUrl.split("->") match {
          case Array(k, v) => (k, v)
          case _ => throw new Exception(s"error conf $SCAN_CLUSTER_HMS_URL_KEY")
        }
      }.filter(x => conf.get(SCAN_CLUSTER_NAME_KEY, SCAN_CLUSTER_NAME_VALUE).contains(x._1))
      .toMap

  def scanIcebergSmallFileThresholdBytes: Long =
    spark.conf.get(SCAN_SMALL_FILE_THRESHOLD_MB_KEY, SCAN_SMALL_FILE_THRESHOLD_MB_VALUE).toLong << 20

  def scanIcebergTableNum: Option[Int] = {
    val num = spark.conf.get(SCAN_TABLE_NUM_KEY, SCAN_TABLE_NUM_VALUE).toInt
    if (num > 0) Some(num) else None
  }
}
