import scala.collection.JavaConverters._

import io.prometheus.client.{CollectorRegistry, Gauge}
import io.prometheus.client.exporter.PushGateway

import org.apache.spark.internal.Logging

class PrometheusUtil(conf: ConfUtil) extends Logging {

  private val pushGateway = new PushGateway(conf.prometheusAddress)
  private val serviceId = conf.prometheusServiceId
  private val serviceIdName = "serviceId"
  private val groupingKey = Map(serviceIdName -> serviceId).asJava

  private val labelNames = classOf[ScanIceberg.IcebergTable].getDeclaredFields.map(_.getName) :+ serviceIdName
  private def labelValues(table: ScanIceberg.IcebergTable): Seq[String] =
    table.productIterator.map(_.toString).toSeq :+ serviceId

  private def pushRegistryToGateway(registry: CollectorRegistry): Unit = {
    pushGateway.push(registry, "IcebergMonitor", groupingKey)
  }

  def reportScanResults(scanResults: Seq[(ScanIceberg.IcebergTable, ScanIceberg.ScanResult)]): Unit = {
    val reportFields = classOf[ScanIceberg.ScanResult].getDeclaredFields.map(_.getName)

    val registry = new CollectorRegistry()
    val gauges = reportFields.map { field =>
      new Gauge.Builder().name(field).help(field).labelNames(labelNames: _*).create()
    }
    scanResults.foreach { case (table, result) =>
      gauges.zipWithIndex.foreach { case (gauge, index) =>
        gauge.labels(labelValues(table): _*).set(result.productElement(index).asInstanceOf[Long])
      }
    }
    gauges.foreach { g => g.register(registry); 0 }
    pushRegistryToGateway(registry)
    logInfo(
      s"""
         | report scanResults size ${scanResults.size}
         | value ([${labelNames.filter(_ != serviceIdName).mkString(", ")}]: [${reportFields.mkString(" ,")}]) :
         | ${scanResults.map(x => s"[${x._1}]: [${x._2}]").mkString("\n")}
         |""".stripMargin)
  }

  def reportErrorIcebergTable(table: ScanIceberg.IcebergTable): Unit = {

    val registry = new CollectorRegistry()
    val gauge = new Gauge.Builder().name("errorTable").help("errorTable")
      .labelNames(labelNames: _*).create()
    gauge.labels(labelValues(table): _*).set(1L)
    gauge.register(registry)
    pushRegistryToGateway(registry)
    logInfo(s"report errorTable $table")
  }
}
