# 打包流水线：

http://jagile.jd.com/upipe/pipelines/1218/buildList/425173

# 运行：

```
spark-submit --conf spark.datalake.enabled=true --class Main IcebergMonitor-1.0-SNAPSHOT-shaded.jar
```

# 配置项

## prometheus配置

| 配置                                       | 说明                                                         | 默认值                               |
| ------------------------------------------ | ------------------------------------------------------------ | ------------------------------------ |
| spark.scan.iceberg.prometheus.testing      | 当false时，发送到线上正式prometheus。当true时，发送到测试环境prometheus。 | false                                |
| spark.scan.iceberg.prometheus.address      | 线上正式prometheus地址                                       | baizepg.jd.local:2000                |
| spark.scan.iceberg.prometheus.test.address | 测试环境prometheus地址                                       | t.bz-pg.jd.local                     |
| spark.scan.iceberg.prometheus.service.id   | prometheus上Iceberg的service.id                              | iceberg1-b12b-11ed-ba16-246e96ac2938 |




## scan iceberg配置

| 配置                                       | 说明                                                         | 默认值                                                       |
| ------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| spark.scan.iceberg.concurrent.num          | 扫描的表级别并发度                                           | 5                                                            |
| spark.scan.iceberg.cluster.name            | 对于不在指定集群内的表，只会打印一条日志，不会执行扫描逻辑   | 10k,hope                                                     |
| spark.scan.iceberg.cluster.hms.url         | 集群名到使用hms url的对应关系                                | 10k->thrift://172.21.213.165:10113,hope->thrift://172.21.213.165:10112 |
| spark.scan.iceberg.small.file.threshold.mb | 统计小文件数量的文件大小阈值，小于这个大小的文件将被统计为小文件（单位MB） | 24                                                           |
| spark.scan.iceberg.table.num               | （测试用）扫描指定数量的表后就停止程序，设置-1时扫描所有表   | -1                                                           |



# 上报信息

### 全部指标的label都是统一的：

```
tableId, clusterName, databaseName, tableName, tableMasterErp, tableLocation, tableLocationNs, isUpsert, serviceId
```

### 具体指标：

| 指标名                      | 说明                                                         |
| --------------------------- | ------------------------------------------------------------ |
| errorTable                  | 有一些用户测试后把表location的所有文件（包括元数据文件）直接用hdfs命令删除了 |
| snapShotNum                 | 表快照总量                                                   |
| dataFileSizeBytes           | 当前快照数据文件总大小                                       |
| dataFileNum                 | 当前快照数据文件总数量                                       |
| smallDataFileNum            | 当前快照数据文件小文件数量                                   |
| positionDeleteFileSizeBytes | 当前快照位置删除文件总大小                                   |
| positionDeleteFileNum       | 当前快照位置删除文件总数量                                   |
| smallPositionDeleteFileNum  | 当前快照位置删除文件小文件数量                               |
| equalityDeleteFileSizeBytes | 当前快照相等删除文件总大小                                   |
| equalityDeleteFileNum       | 当前快照相等删除文件总数量                                   |
| smallEqualityDeleteFileNum  | 当前快照相等删除文件小文件数量                               |
| tableSizeBytes              | 表location总大小                                             |
| tableFileNum                | 表location下总文件数量                                       |
| tableDirectoryNum           | 表location下总目录数量                                       |

