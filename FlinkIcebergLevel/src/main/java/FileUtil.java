
import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by root on 15-11-6.
 */
public class FileUtil {

    public static void createDirectory(String paths) {
        int i = paths.lastIndexOf("/");
        if (i == -1) {
            System.out.println("FileUtil.createDirectory InitDirectory Error");
            return;
        }
        File file = new File(paths.substring(0, i));
        if (!file.exists()) {
            file.mkdirs();
        } else {
            if (file.isFile()) {
                file.delete();
                file.mkdirs();
            }
        }
    }

    public static boolean fileExists(String path) {
        File file = new File(path);
        if (file.exists()) {
            if (file.isDirectory()) {
                boolean delete = file.delete();
                return false;
            }

            if (file.isFile()) {
                return true;
            }
        }
        return false;
    }

    public static List<String> readFile(String filepath, Integer maxLine) {
        List<String> lines = new ArrayList<>();
        try {
            FileReader reader = new FileReader(filepath);
            BufferedReader br = new BufferedReader(reader);
            String str;
            int i = 0;
            while ((str = br.readLine()) != null) {
                if (maxLine != null && i++ > maxLine) {
                    break;
                }
                lines.add(str);
            }
            br.close();
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return lines;
    }

    public static List<String> readFile(String filepath) {
        return readFile(filepath, null);
    }

    public static CommonResult readContent(String filePath) {
        StringBuilder builder = new StringBuilder();
        File file = new File(filePath);
        System.out.println("file.getAbsolutePath() = " + file.getAbsolutePath());
        if (!file.isFile()) {
            return new CommonResult("path[" + filePath + "] is not a file or directory not found.");
        }
        FileReader fileReader;
        try {
            fileReader = new FileReader(file);
        } catch (FileNotFoundException e) {
            return new CommonResult("path[" + filePath + "] " + e.getMessage());
        }
        BufferedReader bufferedReader = new BufferedReader(fileReader);
        String line;
        try {
            while ((line = bufferedReader.readLine()) != null) {
                builder.append(line).append(System.lineSeparator());
            }
        } catch (IOException e) {
            return new CommonResult("path[" + filePath + "] " + e.getMessage());
        }
        return new CommonResult(true, builder.toString());
    }

    public static boolean writeFile(String filePath, String content, Boolean append) {
        return writeFile(filePath, content, append, "utf-8");
    }
    static Map<String,FileOutputStream> pathFileOutPutStreamMap = new HashMap<>();
    public static boolean writeFile(String filePath, String content, Boolean append, String encode) {
        if (append == null) {
            append = false;
        }
        FileOutputStream fileOutputStream1;
        try {
            fileOutputStream1 = new FileOutputStream(filePath, append);
            fileOutputStream1.write(content.getBytes(encode));
            fileOutputStream1.flush();
            fileOutputStream1.close();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static class CommonResult {
        private boolean isSuccess = false;
        private String errorMessage;
        private String content;

        public CommonResult(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public CommonResult(boolean isSuccess, String content) {
            this.isSuccess = isSuccess;
            this.content = content;
        }

        public boolean isSuccess() {
            return isSuccess;
        }

        public void setSuccess(boolean success) {
            isSuccess = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        @Override
        public String toString() {
            return "CommonResult{" +
                    "isSuccess=" + isSuccess +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", content='" + content + '\'' +
                    '}';
        }
    }


}
