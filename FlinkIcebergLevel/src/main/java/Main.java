import com.alibaba.fastjson.JSONObject;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;


public class Main {
    public static void main(String[] args) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String url = "http://baizepg.jd.local:2000/api/put?service_id=jrc14294-154c-4ff8-b3f3-c9b8eb346b86&data_fmt=opentsdb";

        FileUtil.CommonResult commonResult = FileUtil.readContent("flink_task.txt");
        for (String row : commonResult.getContent().split(System.lineSeparator())) {
            String[] split = row.split(",");
            String flinkId = split[0];
            String level = split[1];
            extracted(httpclient, url, flinkId, level);
        }
    }

    private static void extracted(CloseableHttpClient httpclient, String url, String flinkId, String level) {
        JSONObject jsonobj = new JSONObject();
        jsonobj.put("metric", "flink_task_info");
        jsonobj.put("timestamp", System.currentTimeMillis());
        jsonobj.put("value", "1");
        JSONObject tags = new JSONObject();
        tags.put("flink_id", flinkId);
        tags.put("level", level);
        jsonobj.put("tags", tags);
        System.out.println("jsonobj.toJSONString() = " + jsonobj.toJSONString());
        String s = SimpleHttpClient.sendJsonRequest(httpclient, jsonobj, url);
        System.out.println(flinkId + " = " + s);
    }
}
