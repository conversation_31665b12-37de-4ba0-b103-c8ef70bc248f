package com.jd.bdp.hms.proxy;
 import com.facebook.fb303.fb_status;
 import org.apache.hadoop.hive.metastore.api.*;
 import org.apache.hadoop.hive.metastore.api.ThriftHiveMetastore.Client;
 import org.apache.hadoop.hive.metastore.api.ThriftHiveMetastore.Iface;
 import org.apache.thrift.TException;
 import org.apache.thrift.protocol.TBinaryProtocol;
 import org.apache.thrift.server.TServer;
 import org.apache.thrift.server.TSimpleServer;
 import org.apache.thrift.transport.TServerSocket;
 import org.apache.thrift.transport.TSocket;
 import org.apache.thrift.transport.TTransport;
 import java.util.List;
 import java.util.Map;
 public class HiveMetastoreProxy implements Iface {
     private final Iface realMetastoreClient;
     public HiveMetastoreProxy(Iface realMetastoreClient) {
         this.realMetastoreClient = realMetastoreClient;
     }
     public static void main(String[] args) throws Exception {
         String hmsHost = args[1];
         Integer hmsPort = Integer.parseInt(args[2]);
         Integer listenPort = Integer.parseInt(args[0]);
         // 连接到后端真实的 Hive Metastore
         TTransport transport = new TSocket(hmsHost, hmsPort);
         transport.open();
         TBinaryProtocol protocol = new TBinaryProtocol(transport);
         Client realMetastoreClient = new ThriftHiveMetastore.Client(protocol);
         // 创建代理服务
         HiveMetastoreProxy proxyService = new HiveMetastoreProxy(realMetastoreClient);
         // 启动 Thrift 服务器来接收请求
         TServerSocket serverTransport = new TServerSocket(listenPort);
         TServer server = new TSimpleServer(new TServer.Args(serverTransport).processor(new ThriftHiveMetastore.Processor<>(proxyService)));
         System.out.println("Starting the Hive Metastore Proxy server...");
         server.serve();
     }
     @Override
     public String getMetaConf(String key, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.getMetaConf(key, jdhive_flag);
     }
     @Override
     public void setMetaConf(String key, String value, String jdhive_flag) throws MetaException, TException {
         realMetastoreClient.setMetaConf(key, value, jdhive_flag);
     }
     @Override
     public void create_database(Database database, String jdhive_flag) throws AlreadyExistsException, InvalidObjectException, MetaException, TException {
         realMetastoreClient.create_database(database, jdhive_flag);
     }
     @Override
     public Database get_database(String name, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         System.out.println("get_database: name = " + name + "\tjdhive_flag = " + jdhive_flag);
         return realMetastoreClient.get_database(name, jdhive_flag);
     }
     @Override
     public void drop_database(String name, boolean deleteData, boolean cascade, String jdhive_flag) throws NoSuchObjectException, InvalidOperationException, MetaException, TException {
         realMetastoreClient.drop_database(name, deleteData, cascade, jdhive_flag);
     }
     @Override
     public List<String> get_databases(String pattern, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_databases(pattern, jdhive_flag);
     }
     @Override
     public List<String> get_all_databases(String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_all_databases(jdhive_flag);
     }
     @Override
     public void alter_database(String dbname, Database db, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         realMetastoreClient.alter_database(dbname, db, jdhive_flag);
     }
     @Override
     public Type get_type(String name, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_type(name, jdhive_flag);
     }
     @Override
     public boolean create_type(Type type, String jdhive_flag) throws AlreadyExistsException, InvalidObjectException, MetaException, TException {
         return realMetastoreClient.create_type(type, jdhive_flag);
     }
     @Override
     public boolean drop_type(String type, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.drop_type(type, jdhive_flag);
     }
     @Override
     public Map<String, Type> get_type_all(String name, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_type_all(name, jdhive_flag);
     }
     @Override
     public List<FieldSchema> get_fields(String db_name, String table_name, String jdhive_flag) throws MetaException, UnknownTableException, UnknownDBException, TException {
         return realMetastoreClient.get_fields(db_name, table_name, jdhive_flag);
     }
     @Override
     public List<FieldSchema> get_fields_with_environment_context(String db_name, String table_name, EnvironmentContext environment_context, String jdhive_flag) throws MetaException, UnknownTableException, UnknownDBException, TException {
         return realMetastoreClient.get_fields_with_environment_context(db_name, table_name, environment_context, jdhive_flag);
     }
     @Override
     public List<FieldSchema> get_schema(String db_name, String table_name, String jdhive_flag) throws MetaException, UnknownTableException, UnknownDBException, TException {
         return realMetastoreClient.get_schema(db_name, table_name, jdhive_flag);
     }
     @Override
     public List<FieldSchema> get_schema_with_environment_context(String db_name, String table_name, EnvironmentContext environment_context, String jdhive_flag) throws MetaException, UnknownTableException, UnknownDBException, TException {
         return realMetastoreClient.get_schema_with_environment_context(db_name, table_name, environment_context, jdhive_flag);
     }
     @Override
     public void create_table(Table tbl, String jdhive_flag) throws AlreadyExistsException, InvalidObjectException, MetaException, NoSuchObjectException, TException {
         realMetastoreClient.create_table(tbl, jdhive_flag);
     }
     @Override
     public void create_table_with_environment_context(Table tbl, EnvironmentContext environment_context, String jdhive_flag) throws AlreadyExistsException, InvalidObjectException, MetaException, NoSuchObjectException, TException {
         realMetastoreClient.create_table_with_environment_context(tbl, environment_context, jdhive_flag);
     }
     @Override
     public void create_table_with_constraints(Table tbl, List<SQLPrimaryKey> primaryKeys, List<SQLForeignKey> foreignKeys, String jdhive_flag) throws AlreadyExistsException, InvalidObjectException, MetaException, NoSuchObjectException, TException {
         realMetastoreClient.create_table_with_constraints(tbl, primaryKeys, foreignKeys, jdhive_flag);
     }
     @Override
     public void drop_constraint(DropConstraintRequest req, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         realMetastoreClient.drop_constraint(req, jdhive_flag);
     }
     @Override
     public void add_primary_key(AddPrimaryKeyRequest req, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         realMetastoreClient.add_primary_key(req, jdhive_flag);
     }
     @Override
     public void add_foreign_key(AddForeignKeyRequest req, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         realMetastoreClient.add_foreign_key(req, jdhive_flag);
     }
     @Override
     public void drop_table(String dbname, String name, boolean deleteData, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         realMetastoreClient.drop_table(dbname, name, deleteData, jdhive_flag);
     }
     @Override
     public void drop_table_with_environment_context(String dbname, String name, boolean deleteData, EnvironmentContext environment_context, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         realMetastoreClient.drop_table_with_environment_context(dbname, name, deleteData, environment_context, jdhive_flag);
     }
     @Override
     public List<String> get_tables(String db_name, String pattern, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_tables(db_name, pattern, jdhive_flag);
     }
     @Override
     public List<String> get_tables_by_type(String db_name, String pattern, String tableType, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_tables_by_type(db_name, pattern, tableType, jdhive_flag);
     }
     @Override
     public List<TableMeta> get_table_meta(String db_patterns, String tbl_patterns, List<String> tbl_types, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_table_meta(db_patterns, tbl_patterns, tbl_types, jdhive_flag);
     }
     @Override
     public List<String> get_all_tables(String db_name, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_all_tables(db_name, jdhive_flag);
     }
     @Override
     public Table get_table(String dbname, String tbl_name, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         // 拦截逻辑
         System.out.println("Intercepting get_table request for database: " + dbname + ", table: " + tbl_name
                 + ", jdhive_flag = " + jdhive_flag);
         if("spark_team_temp_1111".equals(tbl_name) && "yxx".equals(dbname)) {
             dbname = "yxx_test";
         }
         // 转发请求到实际的 Hive Metastore
         Table table = realMetastoreClient.get_table(dbname, tbl_name, jdhive_flag);
         System.out.println("get_table response is " + table);
         return table;
     }
     @Override
     public Table get_table_from_view(String dbname, String tbl_name, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_table_from_view(dbname, tbl_name, jdhive_flag);
     }
     @Override
     public List<Table> get_table_objects_by_name(String dbname, List<String> tbl_names, String jdhive_flag) throws TException {
         return realMetastoreClient.get_table_objects_by_name(dbname, tbl_names, jdhive_flag);
     }
     @Override
     public GetTableResult get_table_req(GetTableRequest req, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_table_req(req, jdhive_flag);
     }
     @Override
     public GetTablesResult get_table_objects_by_name_req(GetTablesRequest req, String jdhive_flag) throws MetaException, InvalidOperationException, UnknownDBException, TException {
         return realMetastoreClient.get_table_objects_by_name_req(req, jdhive_flag);
     }
     @Override
     public List<String> get_table_names_by_filter(String dbname, String filter, short max_tables, String jdhive_flag) throws MetaException, InvalidOperationException, UnknownDBException, TException {
         return realMetastoreClient.get_table_names_by_filter(dbname, filter, max_tables, jdhive_flag);
     }
     @Override
     public void alter_table(String dbname, String tbl_name, Table new_tbl, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_table(dbname, tbl_name, new_tbl, jdhive_flag);
     }
     @Override
     public void alter_table_with_environment_context(String dbname, String tbl_name, Table new_tbl, EnvironmentContext environment_context, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_table_with_environment_context(dbname, tbl_name, new_tbl, environment_context, jdhive_flag);
     }
     @Override
     public void alter_table_with_cascade(String dbname, String tbl_name, Table new_tbl, boolean cascade, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_table_with_cascade(dbname, tbl_name, new_tbl, cascade, jdhive_flag);
     }
     @Override
     public Partition add_partition(Partition new_part, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.add_partition(new_part, jdhive_flag);
     }
     @Override
     public Partition add_partition_with_environment_context(Partition new_part, EnvironmentContext environment_context, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.add_partition_with_environment_context(new_part, environment_context, jdhive_flag);
     }
     @Override
     public int add_partitions(List<Partition> new_parts, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.add_partitions(new_parts, jdhive_flag);
     }
     @Override
     public int add_partitions_pspec(List<PartitionSpec> new_parts, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.add_partitions_pspec(new_parts, jdhive_flag);
     }
     @Override
     public Partition append_partition(String db_name, String tbl_name, List<String> part_vals, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.append_partition(db_name, tbl_name, part_vals, jdhive_flag);
     }
     @Override
     public AddPartitionsResult add_partitions_req(AddPartitionsRequest request, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.add_partitions_req(request, jdhive_flag);
     }
     @Override
     public Partition append_partition_with_environment_context(String db_name, String tbl_name, List<String> part_vals, EnvironmentContext environment_context, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.append_partition_with_environment_context(db_name, tbl_name, part_vals, environment_context, jdhive_flag);
     }
     @Override
     public Partition append_partition_by_name(String db_name, String tbl_name, String part_name, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.append_partition_by_name(db_name, tbl_name, part_name, jdhive_flag);
     }
     @Override
     public Partition append_partition_by_name_with_environment_context(String db_name, String tbl_name, String part_name, EnvironmentContext environment_context, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.append_partition_by_name_with_environment_context(db_name, tbl_name, part_name, environment_context, jdhive_flag);
     }
     @Override
     public boolean drop_partition(String db_name, String tbl_name, List<String> part_vals, boolean deleteData, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.drop_partition(db_name, tbl_name, part_vals, deleteData, jdhive_flag);
     }
     @Override
     public boolean drop_partition_with_environment_context(String db_name, String tbl_name, List<String> part_vals, boolean deleteData, EnvironmentContext environment_context, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.drop_partition_with_environment_context(db_name, tbl_name, part_vals, deleteData, environment_context, jdhive_flag);
     }
     @Override
     public boolean drop_partition_by_name(String db_name, String tbl_name, String part_name, boolean deleteData, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.drop_partition_by_name(db_name, tbl_name, part_name, deleteData, jdhive_flag);
     }
     @Override
     public boolean drop_partition_by_name_with_environment_context(String db_name, String tbl_name, String part_name, boolean deleteData, EnvironmentContext environment_context, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.drop_partition_by_name_with_environment_context(db_name, tbl_name, part_name, deleteData, environment_context, jdhive_flag);
     }
     @Override
     public DropPartitionsResult drop_partitions_req(DropPartitionsRequest req, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.drop_partitions_req(req, jdhive_flag);
     }
     @Override
     public Partition get_partition(String db_name, String tbl_name, List<String> part_vals, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition(db_name, tbl_name, part_vals, jdhive_flag);
     }
     @Override
     public Partition get_partition_from_view(String db_name, String tbl_name, List<String> part_vals, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition_from_view(db_name, tbl_name, part_vals, jdhive_flag);
     }
     @Override
     public Partition exchange_partition(Map<String, String> partitionSpecs, String source_db, String source_table_name, String dest_db, String dest_table_name, String jdhive_flag) throws MetaException, NoSuchObjectException, InvalidObjectException, InvalidInputException, TException {
         return realMetastoreClient.exchange_partition(partitionSpecs, source_db, source_table_name, dest_db, dest_table_name, jdhive_flag);
     }
     @Override
     public List<Partition> exchange_partitions(Map<String, String> partitionSpecs, String source_db, String source_table_name, String dest_db, String dest_table_name, String jdhive_flag) throws MetaException, NoSuchObjectException, InvalidObjectException, InvalidInputException, TException {
         return realMetastoreClient.exchange_partitions(partitionSpecs, source_db, source_table_name, dest_db, dest_table_name, jdhive_flag);
     }
     @Override
     public Partition get_partition_with_auth(String db_name, String tbl_name, List<String> part_vals, String user_name, List<String> group_names, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition_with_auth(db_name, tbl_name, part_vals, user_name, group_names, jdhive_flag);
     }
     @Override
     public Partition get_partition_with_auth_from_view(String db_name, String tbl_name, List<String> part_vals, String user_name, List<String> group_names, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition_with_auth_from_view(db_name, tbl_name, part_vals, user_name, group_names, jdhive_flag);
     }
     @Override
     public Partition get_partition_by_name(String db_name, String tbl_name, String part_name, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition_by_name(db_name, tbl_name, part_name, jdhive_flag);
     }
     @Override
     public Partition get_partition_by_name_from_view(String db_name, String tbl_name, String part_name, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition_by_name_from_view(db_name, tbl_name, part_name, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions(String db_name, String tbl_name, short max_parts, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_partitions(db_name, tbl_name, max_parts, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions_from_view(String db_name, String tbl_name, short max_parts, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_partitions_from_view(db_name, tbl_name, max_parts, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions_with_auth(String db_name, String tbl_name, short max_parts, String user_name, List<String> group_names, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_partitions_with_auth(db_name, tbl_name, max_parts, user_name, group_names, jdhive_flag);
     }
     @Override
     public List<PartitionSpec> get_partitions_pspec(String db_name, String tbl_name, int max_parts, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_partitions_pspec(db_name, tbl_name, max_parts, jdhive_flag);
     }
     @Override
     public List<String> get_partition_names(String db_name, String tbl_name, short max_parts, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_partition_names(db_name, tbl_name, max_parts, jdhive_flag);
     }
     @Override
     public List<String> get_partition_names_from_view(String db_name, String tbl_name, short max_parts, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_partition_names_from_view(db_name, tbl_name, max_parts, jdhive_flag);
     }
     @Override
     public PartitionValuesResponse get_partition_values(PartitionValuesRequest request) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition_values(request);
     }
     @Override
     public List<Partition> get_partitions_ps(String db_name, String tbl_name, List<String> part_vals, short max_parts, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partitions_ps(db_name, tbl_name, part_vals, max_parts, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions_ps_with_auth(String db_name, String tbl_name, List<String> part_vals, short max_parts, String user_name, List<String> group_names, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_partitions_ps_with_auth(db_name, tbl_name, part_vals, max_parts, user_name, group_names, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions_ps_with_auth_from_view(String db_name, String tbl_name, List<String> part_vals, short max_parts, String user_name, List<String> group_names, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_partitions_ps_with_auth_from_view(db_name, tbl_name, part_vals, max_parts, user_name, group_names, jdhive_flag);
     }
     @Override
     public List<String> get_partition_names_ps(String db_name, String tbl_name, List<String> part_vals, short max_parts, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition_names_ps(db_name, tbl_name, part_vals, max_parts, jdhive_flag);
     }
     @Override
     public List<String> get_partition_names_ps_from_view(String db_name, String tbl_name, List<String> part_vals, short max_parts, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partition_names_ps_from_view(db_name, tbl_name, part_vals, max_parts, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions_by_filter(String db_name, String tbl_name, String filter, short max_parts, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partitions_by_filter(db_name, tbl_name, filter, max_parts, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions_by_filter_from_view(String db_name, String tbl_name, String filter, short max_parts, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partitions_by_filter_from_view(db_name, tbl_name, filter, max_parts, jdhive_flag);
     }
     @Override
     public List<PartitionSpec> get_part_specs_by_filter(String db_name, String tbl_name, String filter, int max_parts, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_part_specs_by_filter(db_name, tbl_name, filter, max_parts, jdhive_flag);
     }
     @Override
     public PartitionsByExprResult get_partitions_by_expr(PartitionsByExprRequest req, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partitions_by_expr(req, jdhive_flag);
     }
     @Override
     public PartitionsByExprResult get_partitions_by_expr_from_view(PartitionsByExprRequest req, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partitions_by_expr_from_view(req, jdhive_flag);
     }
     @Override
     public int get_num_partitions_by_filter(String db_name, String tbl_name, String filter, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_num_partitions_by_filter(db_name, tbl_name, filter, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions_by_names(String db_name, String tbl_name, List<String> names, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partitions_by_names(db_name, tbl_name, names, jdhive_flag);
     }
     @Override
     public List<Partition> get_partitions_by_names_from_view(String db_name, String tbl_name, List<String> names, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_partitions_by_names_from_view(db_name, tbl_name, names, jdhive_flag);
     }
     @Override
     public void alter_partition(String db_name, String tbl_name, Partition new_part, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_partition(db_name, tbl_name, new_part, jdhive_flag);
     }
     @Override
     public void alter_partitions(String db_name, String tbl_name, List<Partition> new_parts, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_partitions(db_name, tbl_name, new_parts, jdhive_flag);
     }
     @Override
     public void alter_partitions_with_environment_context(String db_name, String tbl_name, List<Partition> new_parts, EnvironmentContext environment_context, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_partitions_with_environment_context(db_name, tbl_name, new_parts, environment_context, jdhive_flag);
     }
     @Override
     public void alter_partition_with_environment_context(String db_name, String tbl_name, Partition new_part, EnvironmentContext environment_context, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_partition_with_environment_context(db_name, tbl_name, new_part, environment_context, jdhive_flag);
     }
     @Override
     public void rename_partition(String db_name, String tbl_name, List<String> part_vals, Partition new_part, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.rename_partition(db_name, tbl_name, part_vals, new_part, jdhive_flag);
     }
     @Override
     public boolean partition_name_has_valid_characters(List<String> part_vals, boolean throw_exception, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.partition_name_has_valid_characters(part_vals, throw_exception, jdhive_flag);
     }
     @Override
     public String get_config_value(String name, String defaultValue, String jdhive_flag) throws ConfigValSecurityException, TException {
         return realMetastoreClient.get_config_value(name, defaultValue, jdhive_flag);
     }
     @Override
     public List<String> partition_name_to_vals(String part_name, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.partition_name_to_vals(part_name, jdhive_flag);
     }
     @Override
     public Map<String, String> partition_name_to_spec(String part_name, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.partition_name_to_spec(part_name, jdhive_flag);
     }
     @Override
     public void markPartitionForEvent(String db_name, String tbl_name, Map<String, String> part_vals, PartitionEventType eventType, String jdhive_flag) throws MetaException, NoSuchObjectException, UnknownDBException, UnknownTableException, UnknownPartitionException, InvalidPartitionException, TException {
         realMetastoreClient.markPartitionForEvent(db_name, tbl_name, part_vals, eventType, jdhive_flag);
     }
     @Override
     public boolean isPartitionMarkedForEvent(String db_name, String tbl_name, Map<String, String> part_vals, PartitionEventType eventType, String jdhive_flag) throws MetaException, NoSuchObjectException, UnknownDBException, UnknownTableException, UnknownPartitionException, InvalidPartitionException, TException {
         return realMetastoreClient.isPartitionMarkedForEvent(db_name, tbl_name, part_vals, eventType, jdhive_flag);
     }
     @Override
     public Index add_index(Index new_index, Table index_table, String jdhive_flag) throws InvalidObjectException, AlreadyExistsException, MetaException, TException {
         return realMetastoreClient.add_index(new_index, index_table, jdhive_flag);
     }
     @Override
     public void alter_index(String dbname, String base_tbl_name, String idx_name, Index new_idx, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_index(dbname, base_tbl_name, idx_name, new_idx, jdhive_flag);
     }
     @Override
     public boolean drop_index_by_name(String db_name, String tbl_name, String index_name, boolean deleteData, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.drop_index_by_name(db_name, tbl_name, index_name, deleteData, jdhive_flag);
     }
     @Override
     public Index get_index_by_name(String db_name, String tbl_name, String index_name, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_index_by_name(db_name, tbl_name, index_name, jdhive_flag);
     }
     @Override
     public List<Index> get_indexes(String db_name, String tbl_name, short max_indexes, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_indexes(db_name, tbl_name, max_indexes, jdhive_flag);
     }
     @Override
     public List<String> get_index_names(String db_name, String tbl_name, short max_indexes, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_index_names(db_name, tbl_name, max_indexes, jdhive_flag);
     }
     @Override
     public PrimaryKeysResponse get_primary_keys(PrimaryKeysRequest request, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_primary_keys(request, jdhive_flag);
     }
     @Override
     public ForeignKeysResponse get_foreign_keys(ForeignKeysRequest request, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_foreign_keys(request, jdhive_flag);
     }
     @Override
     public boolean update_table_column_statistics(ColumnStatistics stats_obj, String jdhive_flag) throws NoSuchObjectException, InvalidObjectException, MetaException, InvalidInputException, TException {
         return realMetastoreClient.update_table_column_statistics(stats_obj, jdhive_flag);
     }
     @Override
     public boolean update_partition_column_statistics(ColumnStatistics stats_obj, String jdhive_flag) throws NoSuchObjectException, InvalidObjectException, MetaException, InvalidInputException, TException {
         return realMetastoreClient.update_partition_column_statistics(stats_obj, jdhive_flag);
     }
     @Override
     public ColumnStatistics get_table_column_statistics(String db_name, String tbl_name, String col_name, String jdhive_flag) throws NoSuchObjectException, MetaException, InvalidInputException, InvalidObjectException, TException {
         return realMetastoreClient.get_table_column_statistics(db_name, tbl_name, col_name, jdhive_flag);
     }
     @Override
     public ColumnStatistics get_partition_column_statistics(String db_name, String tbl_name, String part_name, String col_name, String jdhive_flag) throws NoSuchObjectException, MetaException, InvalidInputException, InvalidObjectException, TException {
         return realMetastoreClient.get_partition_column_statistics(db_name, tbl_name, part_name, col_name, jdhive_flag);
     }
     @Override
     public TableStatsResult get_table_statistics_req(TableStatsRequest request, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_table_statistics_req(request, jdhive_flag);
     }
     @Override
     public PartitionsStatsResult get_partitions_statistics_req(PartitionsStatsRequest request, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_partitions_statistics_req(request, jdhive_flag);
     }
     @Override
     public AggrStats get_aggr_stats_for(PartitionsStatsRequest request, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         return realMetastoreClient.get_aggr_stats_for(request, jdhive_flag);
     }
     @Override
     public boolean set_aggr_stats_for(SetPartitionsStatsRequest request, String jdhive_flag) throws NoSuchObjectException, InvalidObjectException, MetaException, InvalidInputException, TException {
         return realMetastoreClient.set_aggr_stats_for(request, jdhive_flag);
     }
     @Override
     public boolean delete_partition_column_statistics(String db_name, String tbl_name, String part_name, String col_name, String jdhive_flag) throws NoSuchObjectException, MetaException, InvalidObjectException, InvalidInputException, TException {
         return realMetastoreClient.delete_partition_column_statistics(db_name, tbl_name, part_name, col_name, jdhive_flag);
     }
     @Override
     public boolean delete_table_column_statistics(String db_name, String tbl_name, String col_name, String jdhive_flag) throws NoSuchObjectException, MetaException, InvalidObjectException, InvalidInputException, TException {
         return realMetastoreClient.delete_table_column_statistics(db_name, tbl_name, col_name, jdhive_flag);
     }
     @Override
     public void create_function(Function func, String jdhive_flag) throws AlreadyExistsException, InvalidObjectException, MetaException, NoSuchObjectException, TException {
         realMetastoreClient.create_function(func, jdhive_flag);
     }
     @Override
     public void drop_function(String dbName, String funcName, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         realMetastoreClient.drop_function(dbName, funcName, jdhive_flag);
     }
     @Override
     public void alter_function(String dbName, String funcName, Function newFunc, String jdhive_flag) throws InvalidOperationException, MetaException, TException {
         realMetastoreClient.alter_function(dbName, funcName, newFunc, jdhive_flag);
     }
     @Override
     public List<String> get_functions(String dbName, String pattern, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_functions(dbName, pattern, jdhive_flag);
     }
     @Override
     public Function get_function(String dbName, String funcName, String jdhive_flag) throws MetaException, NoSuchObjectException, TException {
         return realMetastoreClient.get_function(dbName, funcName, jdhive_flag);
     }
     @Override
     public GetAllFunctionsResponse get_all_functions(String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_all_functions(jdhive_flag);
     }
     @Override
     public boolean create_role(Role role, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.create_role(role, jdhive_flag);
     }
     @Override
     public boolean drop_role(String role_name, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.drop_role(role_name, jdhive_flag);
     }
     @Override
     public List<String> get_role_names(String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_role_names(jdhive_flag);
     }
     @Override
     public boolean grant_role(String role_name, String principal_name, PrincipalType principal_type, String grantor, PrincipalType grantorType, boolean grant_option, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.grant_role(role_name, principal_name, principal_type, grantor, grantorType, grant_option, jdhive_flag);
     }
     @Override
     public boolean revoke_role(String role_name, String principal_name, PrincipalType principal_type, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.revoke_role(role_name, principal_name, principal_type, jdhive_flag);
     }
     @Override
     public List<Role> list_roles(String principal_name, PrincipalType principal_type, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.list_roles(principal_name, principal_type, jdhive_flag);
     }
     @Override
     public GrantRevokeRoleResponse grant_revoke_role(GrantRevokeRoleRequest request, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.grant_revoke_role(request, jdhive_flag);
     }
     @Override
     public GetPrincipalsInRoleResponse get_principals_in_role(GetPrincipalsInRoleRequest request, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_principals_in_role(request, jdhive_flag);
     }
     @Override
     public GetRoleGrantsForPrincipalResponse get_role_grants_for_principal(GetRoleGrantsForPrincipalRequest request, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_role_grants_for_principal(request, jdhive_flag);
     }
     @Override
     public PrincipalPrivilegeSet get_privilege_set(HiveObjectRef hiveObject, String user_name, List<String> group_names, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_privilege_set(hiveObject, user_name, group_names, jdhive_flag);
     }
     @Override
     public List<HiveObjectPrivilege> list_privileges(String principal_name, PrincipalType principal_type, HiveObjectRef hiveObject, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.list_privileges(principal_name, principal_type, hiveObject, jdhive_flag);
     }
     @Override
     public boolean grant_privileges(PrivilegeBag privileges, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.grant_privileges(privileges, jdhive_flag);
     }
     @Override
     public boolean revoke_privileges(PrivilegeBag privileges, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.revoke_privileges(privileges, jdhive_flag);
     }
     @Override
     public GrantRevokePrivilegeResponse grant_revoke_privileges(GrantRevokePrivilegeRequest request, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.grant_revoke_privileges(request, jdhive_flag);
     }
     @Override
     public List<String> set_ugi(String user_name, List<String> group_names) throws MetaException, TException {
         return realMetastoreClient.set_ugi(user_name, group_names);
     }
     @Override
     public String get_delegation_token(String token_owner, String renewer_kerberos_principal_name, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.get_delegation_token(token_owner, renewer_kerberos_principal_name, jdhive_flag);
     }
     @Override
     public long renew_delegation_token(String token_str_form, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.renew_delegation_token(token_str_form, jdhive_flag);
     }
     @Override
     public void cancel_delegation_token(String token_str_form, String jdhive_flag) throws MetaException, TException {
         realMetastoreClient.cancel_delegation_token(token_str_form, jdhive_flag);
     }
     @Override
     public boolean add_token(String token_identifier, String delegation_token, String jdhive_flag) throws TException {
         return realMetastoreClient.add_token(token_identifier, delegation_token, jdhive_flag);
     }
     @Override
     public boolean remove_token(String token_identifier, String jdhive_flag) throws TException {
         return realMetastoreClient.remove_token(token_identifier, jdhive_flag);
     }
     @Override
     public String get_token(String token_identifier, String jdhive_flag) throws TException {
         return realMetastoreClient.get_token(token_identifier, jdhive_flag);
     }
     @Override
     public List<String> get_all_token_identifiers(String jdhive_flag) throws TException {
         return realMetastoreClient.get_all_token_identifiers(jdhive_flag);
     }
     @Override
     public int add_master_key(String key, String jdhive_flag) throws MetaException, TException {
         return realMetastoreClient.add_master_key(key, jdhive_flag);
     }
     @Override
     public void update_master_key(int seq_number, String key, String jdhive_flag) throws NoSuchObjectException, MetaException, TException {
         realMetastoreClient.update_master_key(seq_number, key, jdhive_flag);
     }
     @Override
     public boolean remove_master_key(int key_seq, String jdhive_flag) throws TException {
         return realMetastoreClient.remove_master_key(key_seq, jdhive_flag);
     }
     @Override
     public List<String> get_master_keys(String jdhive_flag) throws TException {
         return realMetastoreClient.get_master_keys(jdhive_flag);
     }
     @Override
     public GetOpenTxnsResponse get_open_txns(String jdhive_flag) throws TException {
         return realMetastoreClient.get_open_txns(jdhive_flag);
     }
     @Override
     public GetOpenTxnsInfoResponse get_open_txns_info(String jdhive_flag) throws TException {
         return realMetastoreClient.get_open_txns_info(jdhive_flag);
     }
     @Override
     public OpenTxnsResponse open_txns(OpenTxnRequest rqst, String jdhive_flag) throws TException {
         return realMetastoreClient.open_txns(rqst, jdhive_flag);
     }
     @Override
     public void abort_txn(AbortTxnRequest rqst, String jdhive_flag) throws NoSuchTxnException, TException {
         realMetastoreClient.abort_txn(rqst, jdhive_flag);
     }
     @Override
     public void abort_txns(AbortTxnsRequest rqst) throws NoSuchTxnException, TException {
         realMetastoreClient.abort_txns(rqst);
     }
     @Override
     public void commit_txn(CommitTxnRequest rqst, String jdhive_flag) throws NoSuchTxnException, TxnAbortedException, TException {
         realMetastoreClient.commit_txn(rqst, jdhive_flag);
     }
     @Override
     public LockResponse lock(LockRequest rqst, String jdhive_flag) throws NoSuchTxnException, TxnAbortedException, TException {
         return realMetastoreClient.lock(rqst, jdhive_flag);
     }
     @Override
     public LockResponse check_lock(CheckLockRequest rqst, String jdhive_flag) throws NoSuchTxnException, TxnAbortedException, NoSuchLockException, TException {
         return realMetastoreClient.check_lock(rqst, jdhive_flag);
     }
     @Override
     public void unlock(UnlockRequest rqst, String jdhive_flag) throws NoSuchLockException, TxnOpenException, TException {
         realMetastoreClient.unlock(rqst, jdhive_flag);
     }
     @Override
     public ShowLocksResponse show_locks(ShowLocksRequest rqst, String jdhive_flag) throws TException {
         return realMetastoreClient.show_locks(rqst, jdhive_flag);
     }
     @Override
     public void heartbeat(HeartbeatRequest ids, String jdhive_flag) throws NoSuchLockException, NoSuchTxnException, TxnAbortedException, TException {
         realMetastoreClient.heartbeat(ids, jdhive_flag);
     }
     @Override
     public HeartbeatTxnRangeResponse heartbeat_txn_range(HeartbeatTxnRangeRequest txns, String jdhive_flag) throws TException {
         return realMetastoreClient.heartbeat_txn_range(txns, jdhive_flag);
     }
     @Override
     public void compact(CompactionRequest rqst, String jdhive_flag) throws TException {
         realMetastoreClient.compact(rqst, jdhive_flag);
     }
     @Override
     public CompactionResponse compact2(CompactionRequest rqst) throws TException {
         return realMetastoreClient.compact2(rqst);
     }
     @Override
     public ShowCompactResponse show_compact(ShowCompactRequest rqst, String jdhive_flag) throws TException {
         return realMetastoreClient.show_compact(rqst, jdhive_flag);
     }
     @Override
     public void add_dynamic_partitions(AddDynamicPartitions rqst, String jdhive_flag) throws NoSuchTxnException, TxnAbortedException, TException {
         realMetastoreClient.add_dynamic_partitions(rqst, jdhive_flag);
     }
     @Override
     public NotificationEventResponse get_next_notification(NotificationEventRequest rqst, String jdhive_flag) throws TException {
         return realMetastoreClient.get_next_notification(rqst, jdhive_flag);
     }
     @Override
     public CurrentNotificationEventId get_current_notificationEventId(String jdhive_flag) throws TException {
         return realMetastoreClient.get_current_notificationEventId(jdhive_flag);
     }
     @Override
     public FireEventResponse fire_listener_event(FireEventRequest rqst, String jdhive_flag) throws TException {
         return realMetastoreClient.fire_listener_event(rqst, jdhive_flag);
     }
     @Override
     public void flushCache() throws TException {
         realMetastoreClient.flushCache();
     }
     @Override
     public GetFileMetadataByExprResult get_file_metadata_by_expr(GetFileMetadataByExprRequest req) throws TException {
         return realMetastoreClient.get_file_metadata_by_expr(req);
     }
     @Override
     public GetFileMetadataResult get_file_metadata(GetFileMetadataRequest req) throws TException {
         return realMetastoreClient.get_file_metadata(req);
     }
     @Override
     public PutFileMetadataResult put_file_metadata(PutFileMetadataRequest req) throws TException {
         return realMetastoreClient.put_file_metadata(req);
     }
     @Override
     public ClearFileMetadataResult clear_file_metadata(ClearFileMetadataRequest req) throws TException {
         return realMetastoreClient.clear_file_metadata(req);
     }
     @Override
     public CacheFileMetadataResult cache_file_metadata(CacheFileMetadataRequest req) throws TException {
         return realMetastoreClient.cache_file_metadata(req);
     }
     @Override
     public String getName() throws TException {
         return realMetastoreClient.getName();
     }
     @Override
     public String getVersion() throws TException {
         return realMetastoreClient.getVersion();
     }
     @Override
     public fb_status getStatus() throws TException {
         return realMetastoreClient.getStatus();
     }
     @Override
     public String getStatusDetails() throws TException {
         return realMetastoreClient.getStatusDetails();
     }
     @Override
     public Map<String, Long> getCounters() throws TException {
         return realMetastoreClient.getCounters();
     }
     @Override
     public long getCounter(String s) throws TException {
         return realMetastoreClient.getCounter(s);
     }
     @Override
     public void setOption(String s, String s1) throws TException {
         realMetastoreClient.setOption(s, s1);
     }
     @Override
     public String getOption(String s) throws TException {
         return realMetastoreClient.getOption(s);
     }
     @Override
     public Map<String, String> getOptions() throws TException {
         return realMetastoreClient.getOptions();
     }
     @Override
     public String getCpuProfile(int i) throws TException {
         return realMetastoreClient.getCpuProfile(i);
     }
     @Override
     public long aliveSince() throws TException {
         return realMetastoreClient.aliveSince();
     }
     @Override
     public void reinitialize() throws TException {
         realMetastoreClient.reinitialize();
     }
     @Override
     public void shutdown() throws TException {
         realMetastoreClient.shutdown();
     }
 }