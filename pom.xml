<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jd</groupId>
    <artifactId>SparkAPM</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>TroubleshootingPlatform</module>
        <module>CompareTool</module>
        <module>NMUtil</module>
        <module>SparkToMySQL</module>
        <module>LogOptimize</module>
        <module>UploadLocalFileToHDFS</module>
        <module>HdfsToIceberg</module>
        <module>IcebergMonitor</module>
        <module>ServiceHealthMonitor</module>
        <module>OrcTools</module>
        <module>DualRunAnalysis</module>
        <module>DualRunAnalysis3_0</module>
        <module>RegressionTest/RegressionHudiAndIcebergReadWriteTest</module>
        <module>RSSReport</module>
        <module>OrcRead</module>
        <module>SparkEventLogParser</module>
    </modules>
</project>
