package com.jd;

//import jdk.internal.org.objectweb.asm.*;
//import org.objectweb.asm.*;
//
import java.lang.instrument.ClassFileTransformer;
//import org.objectweb.asm.*;
//import java.lang.reflect.Method;
import java.security.ProtectionDomain;


import org.objectweb.asm.*;
import java.lang.reflect.Method;

public class HdfsFileSystemTransformer implements ClassFileTransformer {

    @Override
    public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined, ProtectionDomain protectionDomain, byte[] classfileBuffer) {
        if (className.equals("org/apache/hadoop/fs/FileSystem")) {
            // 如果是需要修改的类
            return modifyFileSystemClass(classfileBuffer);
        }
        return classfileBuffer; // 如果不是目标类，直接返回原字节码
    }

    private byte[] modifyFileSystemClass(byte[] classfileBuffer) {
        // 使用 ASM 解析字节码
        ClassReader classReader = new ClassReader(classfileBuffer);
        ClassWriter classWriter = new ClassWriter(ClassWriter.COMPUTE_FRAMES);
        ClassVisitor classVisitor = new ClassVisitor(Opcodes.ASM7, classWriter) {

            @Override
            public MethodVisitor visitMethod(int access, String name, String descriptor, String signature, String[] exceptions) {
                MethodVisitor mv = super.visitMethod(access, name, descriptor, signature, exceptions);

                // 找到 getContentSummary 方法
                if ("getContentSummary".equals(name)) {
                    return new MethodVisitor(Opcodes.ASM7, mv) {

                        @Override
                        public void visitCode() {
                            // 在方法开始处插入代码：new RuntimeException().printStackTrace();
                            mv.visitTypeInsn(Opcodes.NEW, "java/lang/RuntimeException");
                            mv.visitInsn(Opcodes.DUP);
                            mv.visitMethodInsn(Opcodes.INVOKESPECIAL, "java/lang/RuntimeException", "<init>", "()V", false);
                            mv.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/RuntimeException", "printStackTrace", "()V", false);
                            super.visitCode();
                        }
                    };
                }
                return mv;
            }
        };
        classReader.accept(classVisitor, 0);
        return classWriter.toByteArray();
    }
}