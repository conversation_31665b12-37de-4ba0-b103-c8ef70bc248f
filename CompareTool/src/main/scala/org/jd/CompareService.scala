package org.jd

import org.apache.spark.rdd.RDD
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types._
import org.jd.ComparatorUtils.createDataTypeComparators

object CompareService extends Serializable {

  def compareTwoTable(df1: DataFrame, df2: DataFrame, rows: Long, eps: Double, partitionNum: Int): Array[Result] = {

    /** nominalColumns: hashable cols, for repartition */
    val nominalColumns = df1.schema.fields.filter { field =>
      field.dataType match {
        case IntegerType => true
        case StringType => true
        case BooleanType => true
        case DateType => true
        case ByteType => true
        case TimestampType => true
        case LongType => true
        case ShortType => true
        case _ => false
      }
    }.map(_.name)

    val extraColumns = df1.schema.fields.filter { field =>
      field.dataType match {
        case DoubleType => true
        case DecimalType() => true
        case FloatType => true
        case _ => false
      }
    }.map(_.name)

    /** comparableColumns, for sort within each partition */
    val comparableColumns = nominalColumns ++ extraColumns

    val rdd1 = df1
      .repartition(partitionNum, nominalColumns.map(col): _*)
      .sortWithinPartitions(comparableColumns.map(col): _*).rdd

    val rdd2 = df2
      .repartition(partitionNum, nominalColumns.map(col): _*)
      .sortWithinPartitions(comparableColumns.map(col): _*).rdd

    val fieldNum = df1.schema.fields.length
    val comparators = createDataTypeComparators(df1.schema, eps)

//    /** add index for rdd */
//    def zipWithIndex[U](rdd: RDD[U]) = rdd.zipWithIndex().map { case (row, idx) => (idx, row) }
//
//    val rdd1Idx = zipWithIndex(rdd1)
//    val rdd2Idx = zipWithIndex(rdd2)

    val rs: Array[Result] = rdd1.zip(rdd2).mapPartitions { iter =>
      var resultInfo: Result = null
      iter.foreach({ case (row1, row2) =>

        for (i <- 0 until fieldNum) {
          var rowRes: Result = comparators(i).compare(row1, row2, i, rows)
          if (rowRes != null) {
            resultInfo = rowRes
          }
        }
      })
      Iterator.single(resultInfo)
    }.collect()
    rs
  }
}
