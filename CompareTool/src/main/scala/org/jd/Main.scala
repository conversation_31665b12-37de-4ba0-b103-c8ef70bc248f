package org.jd

import org.apache.spark.sql.SparkSession

object Main {

  def main(args: Array[String]): Unit = {

    var tableName1 = args(0) // dev.xxx
    var tableName2 = args(1)
    var buffaloId = args(2)
    var logId=args(3)
    var rtol = args(4)
    var partitions = args(5)
    var eps = rtol.trim.toDouble
    var partitionNum = partitions.trim.toInt

    if (eps < Double.MinPositiveValue) {
      System.out.println(new Result(-1, "relative tolerance is meaningless").toString)
      System.exit(-1)
    }

    if (partitionNum > Int.MaxValue) {
      System.out.println(new Result(-1, "partitionNum is bigger than Int.MaxValue").toString)
      System.exit(-1)
    } else if (partitionNum <= 0) {
      partitionNum = 200
    }

    val spark = SparkSession
      .builder()
      .appName(s"buffaloId: $buffaloId -- logId: $logId")
      .enableHiveSupport()
      .getOrCreate()

    val df = spark.sql("")
//    df.map()

    var startTime = System.currentTimeMillis()
    try {

      val df1 = spark.table(tableName1)
      val df2 = spark.table(tableName2)

      /** 1st. compare schema */
      val hasEqualSchema = df1.schema.fields.length == df2.schema.fields.length && df1.schema.toString().equalsIgnoreCase(df2.schema.toString())
      if (!hasEqualSchema) {
        System.out.println(new Result(CompareConfig.DiffSchema, s"different schema").toString)
        System.exit(CompareConfig.DiffSchema)
      }

      /** 2nd. compare row-number */
      val df1Rows = df1.count()
      val df2Rows = df2.count()

      if (df1Rows != df2Rows) {
        System.out.println(new Result(CompareConfig.DiffRowNum, s"different row number: ${df1Rows},${df2Rows}").toString)
        System.exit(CompareConfig.DiffRowNum)
      } else if (df1Rows == df2Rows && df1Rows == 0) {
        System.out.println(new Result(CompareConfig.EmptyTable, "empty table").toString)
        System.exit(CompareConfig.EmptyTable)
      }

      /** 3th. compare table */
      var status: Array[Result] = CompareService.compareTwoTable(df1, df2, df1Rows, eps, partitionNum)

      /** filter unequal row */
      var resultInfo = status.filterNot(res => res == null)

      val unequalNums = resultInfo.length
      unequalNums match {
        case 0 => {
          System.out.println(new Result(0, "equal").toString)
          System.exit(0)
        }
        case v1 if 1 until 5 contains v1 => {
          for (i <- 1 to unequalNums) {
            System.out.println(resultInfo(unequalNums - i).toString)
            System.exit(0)
          }
        }
        case _ => {
          for (i <- 1 to 5) {
            System.out.println(resultInfo(unequalNums - i).toString)
            System.exit(0)
          }
        }
      }
    } catch {
      case e: Exception => {
        val t = e.getCause
        val causeInfo = if (t == null) e.getMessage
        else t.toString
        System.out.println(new Result(CompareConfig.RunException, causeInfo).toString)
        System.exit(-1)
      }
    }

    var endTime = System.currentTimeMillis()
    var timeStep = endTime - startTime
    System.out.println("\n")
    System.out.println("compare runtime: " + timeStep / 60000 + " min\n")
  }
}
