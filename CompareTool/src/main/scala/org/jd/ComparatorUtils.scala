package org.jd

import org.apache.spark.sql.Row
import org.apache.spark.sql.types._
import org.json4s.DefaultFormats
import org.json4s.jackson.Json


object ComparatorUtils {

  trait DataTypeComparator {
    def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result
  }

  case class BooleanTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (BooleanType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (BooleanType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getBoolean(index)
          val v2 = row2.getBoolean(index)
          if (v1 != v2) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; { $row1 }, { $row2 }; in [BooleanType: $v1, $v2] is different")
          } else {
            null
          }
        }
      }
    }
  }

  case class LongTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (LongType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (LongType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getLong(index)
          val v2 = row2.getLong(index)
          if (v1 != v2) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [LongType: $v1, $v2] is different---{ $row1 }, { $row2 };")
          } else {
            null
          }
        }
      }
    }
  }

  case class ShortTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (ShortType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (ShortType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getShort(index)
          val v2 = row2.getShort(index)
          if (v1 != v2) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [ShortType: $v1, $v2] is different---{ $row1 }, { $row2 };")
          } else {
            null
          }
        }
      }
    }
  }

  case class DoubleTypeComparator(eps: Double) extends DataTypeComparator {
    require(eps >= 0)

    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (DoubleType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (DoubleType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getDouble(index)
          val v2 = row2.getDouble(index)
          if (v1 != scala.Double.NaN && v2 != scala.Double.NaN) {
            val absX = math.abs(v1)
            val absY = math.abs(v2)
            val diff = math.abs(v1 - v2)
            if (v1 == v2 || diff < eps * math.min(absX, absY)) {
              null
            } else {
              new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [DoubleType: $v1, $v2] is different---{ $row1 }, { $row2 };")
            }
          } else if(v1 == scala.Double.NaN && v2 == scala.Double.NaN) {
            null
          } else {
            new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; one of tables in [DoubleType: $v1, $v2] is Double.NaN")
          }
        }
      }
    }
  }

  case class FloatTypeComparator(eps: Double) extends DataTypeComparator {
    require(eps >= 0)

    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (FloatType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (FloatType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getFloat(index)
          val v2 = row2.getFloat(index)
          if (v1 != scala.Float.NaN && v2 != scala.Float.NaN) {
            val absX = math.abs(v1)
            val absY = math.abs(v2)
            val diff = math.abs(v1 - v2)
            if (v1 == v2 || (diff < eps * math.min(absX, absY))) {
              null
            } else {
              new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; { $row1 }, { $row2 }; in [FloatType: $v1, $v2] is different")
            }
          } else if(v1 == scala.Float.NaN && v2 == scala.Float.NaN) {
            null
          } else {
            new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; one of table in [FloatType: $v1,$v2] is Float.NaN")
          }
        }
      }
    }
  }

  case class StringTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (StringType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (StringType) in columns has null value")
        case (false, false) => {
          val str1 = row1.getString(index)
          val str2 = row2.getString(index)
          if (str1 != str2) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [StringType: $str1, $str2] is different---{ $row1 }, { $row2 };")
          } else {
            null
          }
        }
      }
    }
  }

  case class IntegerTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (IntegerType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (IntegerType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getInt(index)
          val v2 = row2.getInt(index)
          if (v1 != v2) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [IntegerType: $v1, $v2] is different---{ $row1 }, { $row2 };")
          } else {
            null
          }
        }
      }
    }
  }

  case class DecimalTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (DecimalType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (DecimalType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getDecimal(index).setScale(CompareConfig.DECIMAL_PLACES, Decimal.ROUND_HALF_UP) //取小数点后4位，四舍五入
          val v2 = row2.getDecimal(index).setScale(CompareConfig.DECIMAL_PLACES, Decimal.ROUND_HALF_UP)
          if (v1.compareTo(v2) != 0) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [DecimalType: $v1, $v2] is different---{ $row1 }, { $row2 };")
          } else {
            null
          }
        }
      }
    }
  }

  case class TimestampTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (TimestampType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (TimestampType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getTimestamp(index)
          val v2 = row2.getTimestamp(index)
          if (v1.compareTo(v2) != 0) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [TimestampType: $v1, $v2] is different---{ ${row1.fieldIndex("index")} }, { $row2 };")
          } else {
            null
          }
        }
      }
    }
  }

  case class ByteTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (ByteType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (ByteType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getByte(index)
          val v2 = row2.getByte(index)
          if (v1.compareTo(v2) != 0) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [ByteType: $v1, $v2] is different---{ $row1 }, { $row2 };")
          } else {
            null
          }
        }
      }
    }
  }

  case class DateTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      (row1.isNullAt(index), row2.isNullAt(index)) match {
        case (true, true) => null
        case (true, false) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the first table in (DateType) in columns has null value")
        case (false, true) => new Result(CompareConfig.NullAndNaN, s"table has $rowNum rows; the second table in (DateType) in columns has null value")
        case (false, false) => {
          val v1 = row1.getDate(index)
          val v2 = row2.getDate(index)
          if (v1.compareTo(v2) != 0) {
            new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [DateType: $v1, $v2] is different---{ $row1 }, { $row2 };")
          } else {
            null
          }
        }
      }
    }
  }

  //  case class MapTypeComparator() extends DataTypeComparator {
  //    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
  //      var t1 = row1.getMap(index)
  //      var t2 = row2.getMap(index)
  //      var json1 = Json(DefaultFormats).write(t1)
  //      var json2 = Json(DefaultFormats).write(t2)
  //      if(json1.self.equals(json2.self)){
  //        null
  //      } else {
  //        new Result(CompareConfig.UnEqualRow, s"table has $rowNum rows; in [MapType: $t1, $t2] is different---{ $row1 }, { $row2 };")
  //      }
  //    }
  //  }

  /** OtherComplexType : default false */
  case class OtherComplexTypeComparator() extends DataTypeComparator {
    override def compare(row1: Row, row2: Row, index: Int, rowNum: Long): Result = {
      //new Result(CompareConfig.ComplexType, s"table1,2 have ${rowNum} rows, complexType: default false")
      null
    }
  }

  /**
   * createDataTypeComparators provide datatype function
   *
   * @param struct    : StructType => df.schema
   * @param getRelTol : Double => Comparison accuracy
   * @return : Array[DataTypeComparator]
   */
  def createDataTypeComparators(struct: StructType, eps: Double): Array[DataTypeComparator] = {
    struct.fields.map { field =>
      field.dataType match {
        case BooleanType => BooleanTypeComparator()
        case IntegerType => IntegerTypeComparator()
        case LongType => LongTypeComparator()
        case ShortType => ShortTypeComparator()
        case DoubleType => DoubleTypeComparator(eps)
        case StringType => StringTypeComparator()
        case d: DecimalType => DecimalTypeComparator()
        case FloatType => FloatTypeComparator(eps)
        case DateType => DateTypeComparator()
        case ByteType => ByteTypeComparator()
        case TimestampType => TimestampTypeComparator()
        //        case m: MapType => MapTypeComparator()
        case _ => OtherComplexTypeComparator()
      }
    }
  }

}
