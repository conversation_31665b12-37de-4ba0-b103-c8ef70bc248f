package org.jd

object CompareConfig {

  /** PARTITION_NUMBER: customize repartition */
//  val PARTITION_NUMBER: Int = 2000

  /** compare loss: Comparison accuracy [Double],[Float] */
//  val eps: Double = 1e-3

  /** DECIMAL_PLACES: set BigDecimal digits */
  val DECIMAL_PLACES: Int = 4

  /** ERROR CODE */
  val UnEqualRow: Int = 2
  val NullAndNaN: Int = 3
  val ComplexType: Int = 4 //array <String>
  val DiffRowNum: Int = 5
  val DiffSchema: Int = 6
  val EmptyTable: Int = 7
  val RunException: Int = 9
}
