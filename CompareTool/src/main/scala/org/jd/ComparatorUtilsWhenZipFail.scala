//package org.jd
//
//import org.apache.spark.sql.Row
//import org.apache.spark.sql.types._
//
//object ComparatorUtilsWhenZipFail {
//
//  /**
//   * DataTypeComparator: [trait] => common interface for column.datatype
//   */
//  trait DataTypeComparator extends Serializable {
//    def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result
//  }
//
//  case class BooleanTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getBoolean(index)
//          val v2 = row2.getBoolean(index)
//          if (v1 != v2) {
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [BooleanType: $v1, $v2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//  case class LongTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getLong(index)
//          val v2 = row2.getLong(index)
//          if (v1 != v2) {
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [LongType: $v1, $v2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//  case class ShortTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getShort(index)
//          val v2 = row2.getShort(index)
//          if (v1 != v2) {
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [ShortType: $v1, $v2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//  case class DoubleTypeComparator(relTol: Double) extends DataTypeComparator {
//    require(relTol >= 0)
//
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getDouble(index)
//          val v2 = row2.getDouble(index)
//          if (v1 != scala.Double.NaN && v2 != scala.Double.NaN) {
//            if (math.abs(v1 - v2) > relTol) {
//              throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [DoubleType: $v1, $v2] is different")
//            } else {
//              new Result(0, "equal")
//            }
//          } else {
//            throw new ResultException(-1, "Double.NaN")
//          }
//        }
//      }
//    }
//  }
//
//  case class FloatTypeComparator(relTol: Double) extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getFloat(index)
//          val v2 = row2.getFloat(index)
//          if (v1 != scala.Float.NaN && v2 != scala.Float.NaN) {
//            if (v1 != v2 || Math.abs(v1 - v2) > relTol) {
//              throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [FloatType: $v1, $v2] is different")
//            } else {
//              new Result(0, "equal")
//            }
//          } else {
//            throw new ResultException(-1, "Float.NaN")
//          }
//        }
//      }
//    }
//  }
//
//  case class StringTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val str1 = row1.getString(index)
//          val str2 = row2.getString(index)
//          if (str1 != str2) {
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [StringType: $str1, $str2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//  case class IntegerTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getInt(index)
//          val v2 = row2.getInt(index)
//          if (v1 != v2) {
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [IntegerType: $v1, $v2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//  case class DecimalTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getDecimal(index).setScale(CompareConfig.DECIMAL_PLACES, Decimal.ROUND_HALF_UP)//取小数点后4位，四舍五入
//          val v2 = row2.getDecimal(index).setScale(CompareConfig.DECIMAL_PLACES, Decimal.ROUND_HALF_UP)
//          if (v1.compareTo(v2) !=0 ) {
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [DecimalType: $v1, $v2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//  case class TimestampTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getTimestamp(index)
//          val v2 = row2.getTimestamp(index)
//          if ( v1.compareTo(v2) != 0 ){
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [TimestampType: $v1, $v2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//  case class ByteTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getByte(index)
//          val v2 = row2.getByte(index)
//          if ( v1.compareTo(v2) != 0 ){
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [ByteType: $v1, $v2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//  case class DateTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      (row1.isNullAt(index), row2.isNullAt(index)) match {
//        case (true, true) => new Result(0, "equal")
//        case (true, false) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, true) => throw new ResultException(-1, "one of table1,2 has null value")
//        case (false, false) => {
//          val v1 = row1.getDate(index)
//          val v2 = row2.getDate(index)
//          if ( v1.compareTo(v2) != 0 ){
//            throw new ResultException(-1, s"$rowNum1: {{ $row1 }}, {{ $row2 }} in [DateType: $v1, $v2] is different")
//          } else {
//            new Result(0, "equal")
//          }
//        }
//      }
//    }
//  }
//
//
//  /** OtherComplexType : default false */
//  case class OtherComplexTypeComparator() extends DataTypeComparator {
//    override def compare(row1: Row, row2: Row, index: Int, rowNum1: Long): Result = {
//      throw new ResultException(-1, "table1,2 have complexType, default false")
//    }
//  }
//
//  /**
//   * createDataTypeComparators provide datatype function
//   *
//   * @param struct    : StructType => df.schema
//   * @param getRelTol : Double => Comparison accuracy
//   * @return : Array[DataTypeComparator]
//   */
//  def createDataTypeComparators(struct: StructType, getRelTol: Double): Array[DataTypeComparator] = {
//    struct.fields.map { field =>
//      field.dataType match {
//        case BooleanType => BooleanTypeComparator()
//        case IntegerType => IntegerTypeComparator()
//        case LongType => LongTypeComparator()
//        case ShortType => ShortTypeComparator()
//        case DoubleType => DoubleTypeComparator(getRelTol)
//        case StringType => StringTypeComparator()
//        case _: DecimalType => DecimalTypeComparator()
//        case FloatType => FloatTypeComparator(getRelTol)
//        case DateType => DateTypeComparator()
//        case ByteType => ByteTypeComparator()
//        case TimestampType => TimestampTypeComparator()
//        case _ => OtherComplexTypeComparator()
//      }
//    }
//  }
//}
