export JDHXXXXX_CLUSTER_NAME=cairne
export JDHXXXXX_QUEUE=dmx.dmx_adm_l
export JDHXXXXX_USER=jdt_edw
export TEAM_USER=dmx_adm
export BEE_HIVETASK_EXEC_ENGINE=spark;
export JDHXXXXX_ENGINE_VERSION=3.4;
export BEE_BDP_TASK_LEVEL=20;
source /software/servers/env/env.sh

cat <<EOF >> cluster.py
import os, sys
import datetime, time
sys.path.append(os.getenv('HIVE_TASK'))
from HiveTask import HiveTask
ht = HiveTask()

sql = """
create table if not exists dmc_tmp.spark_team_temp_wuguoxiao_20250121_keji_cluster(id string) stored as orcfile;
insert into dmc_tmp.spark_team_temp_wuguoxiao_20250121_keji_cluster values('111111');
select * from dmc_tmp.spark_team_temp_wuguoxiao_20250121_keji_cluster;
drop table if exists dmc_tmp.spark_team_temp_wuguoxiao_20250121_keji_cluster;
"""

ht.exec_sql(schema_name = 'dmc_tmp', table_name = 'spark_team_temp_wuguoxiao_20250121_keji_cluster', sql = sql,  exec_engine='spark' ,spark_args=[])
EOF

python3 cluster.py